package com.siteweb.tcs.south.omc.unitweb.handler;

import com.siteweb.tcs.south.omc.unitweb.UnitWebCmdEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-19 13:14
 **/
@Component
public class UnitWebHandlerObserver {
    private static final Map<UnitWebCmdEnum, UnitWebHandler> handlerMap = new HashMap<>();

    public void registerHandler(UnitWebCmdEnum type,UnitWebHandler unitWebHandler){
        handlerMap.put(type,unitWebHandler);
    }

    public static void handlerMessage(UnitWebCmdEnum type,Integer equipmentId,String msg){
        if (handlerMap.containsKey(type)) {
            handlerMap.get(type).handle(equipmentId,msg);
        }else{
            //未识别
        }
    }
}
