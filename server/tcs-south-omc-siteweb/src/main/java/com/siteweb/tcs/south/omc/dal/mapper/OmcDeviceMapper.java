package com.siteweb.tcs.south.omc.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.south.omc.dal.entity.OmcDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
@Repository
public interface OmcDeviceMapper extends BaseMapper<OmcDevice> {
    OmcDevice selectByDeviceName(@Param("deviceName") String deviceName);
    List<OmcDevice> findActiveDevices(@Param("minCreateTime") LocalDateTime minCreateTime);
} 