package com.siteweb.tcs.south.omc.unitweb.entiry;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-19 13:38
 **/
@Data
@AllArgsConstructor
public class ControlData {
    public Integer CtrlId;
    public String Name;
    public String BaseTypeId;
    public long SigId;
    public double FloatValue;
    public String StrValue;
    public double MaxValue;
    public double MinValue;
    public int Enable;
    public int Visible;
    public int CmdType;
    public int CtrlType;
    public int DataType;
    public String Meanings;
}
