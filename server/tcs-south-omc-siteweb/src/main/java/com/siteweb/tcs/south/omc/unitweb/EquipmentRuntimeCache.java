package com.siteweb.tcs.south.omc.unitweb;

import com.siteweb.tcs.south.omc.unitweb.entiry.ControlData;
import com.siteweb.tcs.south.omc.unitweb.entiry.EquipmentData;
import com.siteweb.tcs.south.omc.unitweb.entiry.EventData;
import com.siteweb.tcs.south.omc.unitweb.entiry.SignalData;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-19 13:46
 **/

public class EquipmentRuntimeCache {
    private static final Map<Integer, EquipmentData> equipmentDataMap = new ConcurrentHashMap<>();

    public static boolean addEquipmentSignal(Integer equipmentId, Map<Integer, SignalData> signalDataMap) {
        if(!equipmentDataMap.containsKey(equipmentId)){
            EquipmentData equipmentData = new EquipmentData();
            equipmentData.setEquipmentId(equipmentId);
            equipmentData.setSignalDataMap(signalDataMap);
            equipmentDataMap.put(equipmentId,equipmentData);
        }else{
            EquipmentData equipmentData = equipmentDataMap.get(equipmentId);
            equipmentData.getSignalDataMap().putAll(signalDataMap);
        }
        return true;
    }

    public static boolean addEquipmentEvent(Integer equipmentId, Map<Integer, EventData> eventDataMap) {
        if(!equipmentDataMap.containsKey(equipmentId)){
            EquipmentData equipmentData = new EquipmentData();
            equipmentData.setEquipmentId(equipmentId);
            equipmentData.setEventDataMap(eventDataMap);
            equipmentDataMap.put(equipmentId,equipmentData);
        }else{
            EquipmentData equipmentData = equipmentDataMap.get(equipmentId);
            equipmentData.getEventDataMap().putAll(eventDataMap);
        }
        return true;
    }

    public static boolean addEquipmentControl(Integer equipmentId, Map<Integer, ControlData> controlDataMap) {
        if(!equipmentDataMap.containsKey(equipmentId)){
            EquipmentData equipmentData = new EquipmentData();
            equipmentData.setEquipmentId(equipmentId);
            equipmentData.setControlDataMap(controlDataMap);
            equipmentDataMap.put(equipmentId,equipmentData);
        }else{
            EquipmentData equipmentData = equipmentDataMap.get(equipmentId);
            equipmentData.getControlDataMap().putAll(controlDataMap);
        }
        return true;
    }

    public static EquipmentData getEquipmentData(Integer equipmentId){
        if(equipmentDataMap.containsKey(equipmentId)) return equipmentDataMap.get(equipmentId);
        return null;
    }
}
