package com.siteweb.tcs.south.omc.unitweb.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.south.omc.unitweb.EquipmentRuntimeCache;
import com.siteweb.tcs.south.omc.unitweb.UnitWebCmdEnum;
import com.siteweb.tcs.south.omc.unitweb.entiry.EventData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: tcs2
 * @description: 实时告警解析器
 * @author: xsx
 * @create: 2025-06-19 12:54
 **/
@Component
public class GetEventListHandler implements UnitWebHandler {

    private static final UnitWebCmdEnum type = UnitWebCmdEnum.cmd_get_eventlist;

    @Autowired
    private UnitWebHandlerObserver unitWebHandlerObserver;

    @PostConstruct
    private void registerHandler(){
        unitWebHandlerObserver.registerHandler(type,this);
    }

    @Override
    public void handle(Integer equipmentId,String msg) {
        String[] lines = msg.split("\\r?\\n");
        Map<Integer, EventData> resultMap = new HashMap<>();

        for (String line : lines) {
            String[] parts = line.split("`");

            Map<String, String> kvMap = Arrays.stream(parts)
                    .map(p -> p.split("=", 2))
                    .filter(kv -> kv.length == 2)
                    .collect(Collectors.toMap(kv -> kv[0], kv -> StringUtils.isBlank(kv[1])?"":kv[1]));

            try {
                EventData event = new EventData(
                        Integer.parseInt(kvMap.getOrDefault("EventId", "0")),
                        kvMap.getOrDefault("Name", ""),
                        Double.parseDouble(kvMap.getOrDefault("FloatValue", "0")),
                        kvMap.getOrDefault("ValueType", ""),
                        kvMap.getOrDefault("Value", ""),
                        Integer.parseInt(kvMap.getOrDefault("CondId", "0")),
                        kvMap.getOrDefault("Meaning", ""),
                        Integer.parseInt(kvMap.getOrDefault("Level", "0")),
                        kvMap.getOrDefault("StartOper", ""),
                        kvMap.getOrDefault("StartValue", ""),
                        Integer.parseInt(kvMap.getOrDefault("StartDelay", "0")),
                        kvMap.getOrDefault("EndOper", ""),
                        kvMap.getOrDefault("EndValue", ""),
                        Integer.parseInt(kvMap.getOrDefault("EndDelay", "0")),
                        kvMap.getOrDefault("BaseTypeId", ""),
                        Integer.parseInt(kvMap.getOrDefault("State", "0")),
                        Integer.parseInt(kvMap.getOrDefault("TrigValue", "0")),
                        kvMap.getOrDefault("StartTime", ""),
                        kvMap.getOrDefault("EndTime", ""),
                        Integer.parseInt(kvMap.getOrDefault("Valid", "0"))
                );
                resultMap.put(event.getEventId(),event);
            } catch (Exception e) {
                System.err.println("解析失败行：" + line);
            }
            if(CollectionUtil.isNotEmpty(resultMap)){
                EquipmentRuntimeCache.addEquipmentEvent(equipmentId,resultMap);
            }
        }
    }
}
