package com.siteweb.tcs.south.omc.config;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.util.PluginResourceHelper;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.DistributeFilePath;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Configuration
@EnableConfigurationProperties
@MapperScan(basePackages = {"com.siteweb.tcs.south.omc.dal.mapper"}, sqlSessionFactoryRef = "omcSqlSessionFactory")
public class DataSourceConfig {
    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;
    @Value("${plugin.middleware.siteweb-persistent.primary}")
    private String sitewebPersistentResourceId;
    @Value("${spring.plugins.runtime-mode:production}")
    private String runtimeMode;
    @Value("${plugin.distribution}")
    private List<String> distributeFilePath;

    @Autowired
    private ResourceRegistry resourceRegistry;
    @Autowired
    private ServiceRegistry serviceRegistry;
    @Value("${plugin.id}")
    private String pluginId;
    @Autowired
    private PluginResourceHelper pluginResourceHelper;
    @Autowired
    private PluginContext pluginContext;

    @Bean(name = "omcDataSourceProperties")
    @ConfigurationProperties(prefix = "plugin.datasource.omc")
    public DataSourceProperties omcDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean(name = "omcDataSource")
    public DataSource omcDataSource(@Qualifier("omcDataSourceProperties") DataSourceProperties dataSourceProperties) {
        return resourceRegistry.getDataSource(dbResourceId, pluginId);
    }

    @Bean(name = "omcTransactionManager")
    public DataSourceTransactionManager omcTransactionManager(@Qualifier("omcDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "omcSqlSessionFactory")
    public SqlSessionFactory omcSqlSessionFactory(@Qualifier("omcDataSource") DataSource dataSource) throws Exception {
        ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
        try{
            // 切换为插件的 ClassLoader
            Thread.currentThread().setContextClassLoader(pluginContext.getPluginClassLoader());
            MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
            bean.setDataSource(dataSource);
//            pluginContext.resolveResources("")
            bean.setMapperLocations(pluginResourceHelper.getMapperResourcesByPluginPath(pluginId, pluginContext.getPluginClassLoader()));
            GlobalConfig globalConfig = new GlobalConfig();
            globalConfig.setBanner(false);
            globalConfig.setDbConfig(new GlobalConfig.DbConfig());
            bean.setGlobalConfig(globalConfig);

            return bean.getObject();

        } finally {
            Thread.currentThread().setContextClassLoader(originalClassLoader);
        }
    }
    @Bean(name = "omcSitewebPersistentService")
    public SitewebPersistentService omcSitewebPersistentService() throws Exception {
        SitewebPersistentService sitewebPersistentService = serviceRegistry.getSitewebPersistentService(sitewebPersistentResourceId, pluginId, pluginId, "south-cmcc-plugin");
        List<String[]> pathString = extractPaths(distributeFilePath);
        if (CollUtil.isNotEmpty(pathString)){
            List<DistributeFilePath> distributeFilePaths = new ArrayList<>();
            for (String[] path : pathString) {
                distributeFilePaths.add(new DistributeFilePath(path[0], path[1]));
            }
            sitewebPersistentService.updatePluginFileConfig(distributeFilePaths);
        }
        return sitewebPersistentService;
    }
    public List<String[]> extractPaths(List<String> originLines) {
        if (originLines == null || originLines.isEmpty() || originLines.get(0).length() < 2){
            return null;
        }

        String[] lines = originLines.get(0).substring(1, originLines.get(0).length() - 1).split(",");
        List<String[]> result = new ArrayList<>();
        // 正则：filePath:xxx,targetPath:yyy
        Pattern pattern = Pattern.compile("^\\s*filePath:([^,]+) targetPath:(.+)\\s*$");

        for (String line : lines) {
            Matcher matcher = pattern.matcher(line);
            if (matcher.matches()) {
                String filePath = matcher.group(1).trim();
                String targetPath = matcher.group(2).trim();
                result.add(new String[]{filePath, targetPath});
            }
        }
        return result;
    }
}