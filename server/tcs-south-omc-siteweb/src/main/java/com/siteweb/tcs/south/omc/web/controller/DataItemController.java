package com.siteweb.tcs.south.omc.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.DataItemCreateDTO;
import com.siteweb.tcs.siteweb.dto.DataItemUpdateDTO;
import com.siteweb.tcs.siteweb.util.StrSplitUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据字典值控制器
 * 通过 SitewebPersistentService.getConfigAPI() 调用相关方法
 */
@Slf4j
@RestController
@RequestMapping("/dataitems")
public class DataItemController {

    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 根据entryId查询数据项
     */
    @GetMapping
    public ResponseEntity<ResponseResult> getDataItems(@RequestParam(value = "entryId", required = false) Integer entryId) {
        try {
            List<?> result = sitewebPersistentService.getConfigAPI().findByEntryIdForDataItem(entryId);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to get data items by entryId: {}", entryId, e);
            return ResponseHelper.failed("查询数据项失败: " + e.getMessage());
        }
    }

    /**
     * 更新数据项
     */
    @PutMapping
    public ResponseEntity<ResponseResult> updateDataItem(@RequestBody DataItemUpdateDTO dataItemUpdateDTO) {
        try {
            boolean result = sitewebPersistentService.getConfigAPI().updateForDataItem(dataItemUpdateDTO);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to update data item", e);
            return ResponseHelper.failed("更新数据项失败: " + e.getMessage());
        }
    }

    /**
     * 创建数据项
     */
    @PostMapping
    public ResponseEntity<ResponseResult> createDataItem(@RequestBody DataItemCreateDTO dataItemCreateDTO) {
        try {
            int result = sitewebPersistentService.getConfigAPI().createDataItemForDataItem(dataItemCreateDTO);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to create data item", e);
            return ResponseHelper.failed("创建数据项失败: " + e.getMessage());
        }
    }

    /**
     * 删除数据项
     */
    @DeleteMapping(params = "entryItemIds")
    public ResponseEntity<ResponseResult> deleteDataItem(@RequestParam String entryItemIds) {
        try {
            int result = sitewebPersistentService.getConfigAPI().deleteByEntryItemIdsForDataItem(entryItemIds);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("Failed to delete data items: {}", entryItemIds, e);
            return ResponseHelper.failed("删除数据项失败: " + e.getMessage());
        }
    }

    /**
     * 获取不同标准下的设备种类下的key，如维谛标准下的电池设备key为24,而联通标准下的电池key有24(蓄电池) 26(低压进线柜) 36
     *
     * @param originCategoryKey 原设备种类下的key，如24
     */
    @GetMapping("/equipmentcategorys")
    public ResponseEntity<ResponseResult> getBusinessCategoryFromOriginCategory(Integer originCategoryKey) {
        return ResponseHelper.successful(sitewebPersistentService.getConfigAPI().findBusinessCategoryFromOriginCategoryForCategoryIdMap(originCategoryKey));
    }
}
