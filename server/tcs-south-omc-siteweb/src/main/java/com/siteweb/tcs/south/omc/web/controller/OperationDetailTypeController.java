package com.siteweb.tcs.south.omc.web.controller;

/**
 * @Description:
 */
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/operationdetailtype")
public class OperationDetailTypeController {
    @Qualifier("omcSitewebPersistentService")
    @Autowired
    private SitewebPersistentService SitewebPersistentService;

    @GetMapping
    public ResponseEntity<ResponseResult> findAll(){
        return ResponseHelper.successful(SitewebPersistentService.getConfigAPI().getOperationTypesForDeviceManagement());
    }
}