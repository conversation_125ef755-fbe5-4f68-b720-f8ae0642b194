<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.omc.dal.mapper.OmcDeviceMapper">
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.south.omc.dal.entity.OmcDevice">
        <id column="device_id" property="deviceId" />
        <result column="device_name" property="deviceName" />
        <result column="device_type" property="deviceType" />
        <result column="device_status" property="deviceStatus" />
        <result column="ip_address" property="ipAddress" />
        <result column="port" property="port" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        device_id, device_name, device_type, device_status, ip_address, port, username, password, create_time, update_time, is_deleted
    </sql>

    <select id="selectByDeviceName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_omc_device
        WHERE device_name = #{deviceName}
        AND is_deleted = 0
    </select>

    <select id="findActiveDevices" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tcs_omc_device
        WHERE create_time >= #{minCreateTime}
        AND is_deleted = 0
    </select>

    <insert id="insert" parameterType="com.siteweb.tcs.south.omc.dal.entity.OmcDevice" useGeneratedKeys="true" keyProperty="deviceId">
        INSERT INTO tcs_omc_device (device_name, device_type, device_status, ip_address, port, username, password, create_time, update_time, is_deleted)
        VALUES (#{deviceName}, #{deviceType}, #{deviceStatus}, #{ipAddress}, #{port}, #{username}, #{password}, #{createTime}, #{updateTime}, #{deleted})
    </insert>

    <update id="updateById" parameterType="com.siteweb.tcs.south.omc.dal.entity.OmcDevice">
        UPDATE tcs_omc_device
        <set>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="port != null">port = #{port},</if>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            update_time = #{updateTime}
        </set>
        WHERE device_id = #{deviceId}
        AND is_deleted = 0
    </update>

    <update id="deleteById">
        UPDATE tcs_omc_device
        SET is_deleted = 1, update_time = NOW()
        WHERE device_id = #{id}
    </update>
</mapper> 