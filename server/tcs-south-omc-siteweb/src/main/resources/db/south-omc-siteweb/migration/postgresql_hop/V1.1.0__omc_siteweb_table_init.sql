-- 文件表
CREATE TABLE IF NOT EXISTS diskfile (
                          fileid BIGSERIAL PRIMARY KEY,
                          filepath VARCHAR(128) NOT NULL,
                          filename VARCHAR(128) NOT NULL,
                          status INT,
                          createtime TIMESTAMP
);

CREATE TABLE IF NOT EXISTS tsl_sampler (
                             SamplerId SERIAL PRIMARY KEY,
                             SamplerName VARCHAR(128) NOT NULL,
                             SamplerType SMALLINT NOT NULL,
                             ProtocolCode VARCHAR(32) NOT NULL,
                             DLLCode VARCHAR(255) NOT NULL,
                             DLLVersion VARCHAR(32) NOT NULL,
                             ProtocolFilePath VARCHAR(255) NOT NULL,
                             DLLFilePath VARCHAR(255) NOT NULL,
                             DllPath VARCHAR(255) NOT NULL,
                             Setting VARCHAR(255) DEFAULT NULL,
                             Description VARCHAR(255) DEFAULT NULL,
                             SoCode VARCHAR(255) NOT NULL,
                             SoPath VARCHAR(255) NOT NULL,
                             UploadProtocolFile BOOLEAN DEFAULT FALSE,
                             CONSTRAINT uniqueProtocolcode UNIQUE (ProtocolCode)
);

CREATE TABLE IF NOT EXISTS tsl_samplerunit (
                                 Id SERIAL PRIMARY KEY,
                                 SamplerUnitId INT NOT NULL,
                                 PortId INT NOT NULL,
                                 MonitorUnitId INT NOT NULL,
                                 SamplerId INT NOT NULL,
                                 ParentSamplerUnitId INT NOT NULL,
                                 SamplerType INT NOT NULL,
                                 SamplerUnitName VARCHAR(128) NOT NULL,
                                 Address INT NOT NULL,
                                 SpUnitInterval DOUBLE PRECISION DEFAULT NULL,
                                 DllPath VARCHAR(128) DEFAULT NULL,
                                 ConnectState INT NOT NULL,
                                 UpdateTime TIMESTAMP NOT NULL,
                                 PhoneNumber VARCHAR(128) DEFAULT NULL,
                                 Description VARCHAR(255) DEFAULT NULL
);
CREATE INDEX IF NOT EXISTS IDX_SamplerUnit_1 ON tsl_samplerunit(MonitorUnitId, SamplerUnitId);
CREATE INDEX IF NOT EXISTS IDX_SamplerUnit_MonitorUnitId ON tsl_samplerunit(MonitorUnitId, PortId, SamplerUnitId);
CREATE INDEX IF NOT EXISTS IDX_SamplerUnit_SamplerUnitId ON tsl_samplerunit(SamplerUnitId);


CREATE TABLE IF NOT EXISTS tbl_equipmenttemplate (
                                       EquipmentTemplateId SERIAL PRIMARY KEY,
                                       EquipmentTemplateName VARCHAR(128) NOT NULL,
                                       ParentTemplateId INT NOT NULL,
                                       Memo VARCHAR(255) NOT NULL,
                                       ProtocolCode VARCHAR(32) NOT NULL,
                                       EquipmentCategory INT NOT NULL,
                                       EquipmentType INT NOT NULL,
                                       Property VARCHAR(255) DEFAULT NULL,
                                       Description VARCHAR(255) DEFAULT NULL,
                                       EquipmentStyle VARCHAR(128) DEFAULT NULL,
                                       Unit VARCHAR(255) DEFAULT NULL,
                                       Vendor VARCHAR(255) DEFAULT NULL,
                                       Photo VARCHAR(255) DEFAULT NULL,
                                       EquipmentBaseType INT DEFAULT NULL,
                                       StationCategory INT DEFAULT NULL,
                                       ExtendField1 VARCHAR(255) DEFAULT NULL
);

-- Data tables
CREATE TABLE IF NOT EXISTS tbl_dataentry (
  EntryId INT NOT NULL,
  EntryCategory INT DEFAULT NULL,
  EntryName VARCHAR(128) DEFAULT NULL,
  EntryTitle VARCHAR(128) DEFAULT NULL,
  EntryAlias VARCHAR(255) DEFAULT NULL,
  Enable BOOLEAN NOT NULL DEFAULT TRUE,
  Description VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (EntryId)
);

CREATE TABLE IF NOT EXISTS tbl_dataitem (
  EntryItemId INT NOT NULL,
  ParentEntryId INT NOT NULL DEFAULT 0,
  ParentItemId INT NOT NULL DEFAULT 0,
  EntryId INT NOT NULL,
  ItemId INT NOT NULL,
  ItemValue VARCHAR(128) NOT NULL,
  ItemAlias VARCHAR(255) DEFAULT NULL,
  Enable BOOLEAN NOT NULL DEFAULT TRUE,
  IsSystem BOOLEAN NOT NULL DEFAULT TRUE,
  IsDefault BOOLEAN NOT NULL DEFAULT FALSE,
  Description VARCHAR(255) DEFAULT NULL,
  ExtendField1 VARCHAR(255) DEFAULT NULL,
  ExtendField2 VARCHAR(255) DEFAULT NULL,
  ExtendField3 VARCHAR(255) DEFAULT NULL,
  ExtendField4 VARCHAR(255) DEFAULT NULL,
  ExtendField5 VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (EntryItemId),
  CONSTRAINT tbl_dataitem_Idx1 UNIQUE (EntryId, ItemId)
);
CREATE INDEX IF NOT EXISTS IDX_DataItem_EntryId ON tbl_dataitem(EntryId);

-- Primary key identity table
CREATE TABLE IF NOT EXISTS tbl_primarykeyidentity (
                                        TableId INT NOT NULL,
                                        TableName VARCHAR(30) DEFAULT NULL,
                                        Description VARCHAR(255) DEFAULT NULL,
                                        PRIMARY KEY (TableId)
);

-- Primary key value table
CREATE TABLE IF NOT EXISTS tbl_primarykeyvalue (
                                     TableId INT NOT NULL,
                                     PostalCode INT NOT NULL,
                                     MinValue INT DEFAULT NULL,
                                     CurrentValue INT DEFAULT NULL,
                                     PRIMARY KEY (PostalCode, TableId)
);

CREATE TABLE IF NOT EXISTS tbl_operationdetail (
                                     UserId INT NOT NULL,
                                     UserName VARCHAR(128) DEFAULT NULL,
                                     ObjectId VARCHAR(128) DEFAULT NULL,
                                     ObjectType INT NOT NULL,
                                     PropertyName VARCHAR(128) DEFAULT NULL,
                                     OperationTime TIMESTAMP NOT NULL,
                                     OperationType VARCHAR(64) NOT NULL,
                                     OldValue VARCHAR(4000) DEFAULT NULL,
                                     NewValue VARCHAR(4000) DEFAULT NULL
);

-- Signal tables
CREATE TABLE IF NOT EXISTS tbl_signal (
                            Id SERIAL PRIMARY KEY,
                            EquipmentTemplateId INT NOT NULL,
                            SignalId INT NOT NULL,
                            Enable BOOLEAN NOT NULL,
                            Visible BOOLEAN NOT NULL,
                            Description VARCHAR(255) DEFAULT NULL,
                            SignalName VARCHAR(128) NOT NULL,
                            SignalCategory INT NOT NULL,
                            SignalType INT NOT NULL,
                            ChannelNo INT NOT NULL,
                            ChannelType INT NOT NULL,
                            Expression TEXT,
                            DataType INT DEFAULT NULL,
                            ShowPrecision VARCHAR(20) DEFAULT NULL,
                            Unit VARCHAR(64) DEFAULT NULL,
                            StoreInterval DOUBLE PRECISION DEFAULT NULL,
                            AbsValueThreshold DOUBLE PRECISION DEFAULT NULL,
                            PercentThreshold DOUBLE PRECISION DEFAULT NULL,
                            StaticsPeriod INT DEFAULT NULL,
                            BaseTypeId DECIMAL(12,0) DEFAULT NULL,
                            ChargeStoreInterVal DOUBLE PRECISION DEFAULT NULL,
                            ChargeAbsValue DOUBLE PRECISION DEFAULT NULL,
                            DisplayIndex INT NOT NULL,
                            MDBSignalId INT DEFAULT NULL,
                            ModuleNo INT NOT NULL DEFAULT 0
);

CREATE INDEX IF NOT EXISTS IDX_TBLSignal_1 ON tbl_signal(EquipmentTemplateId);
CREATE INDEX IF NOT EXISTS IDX_TBLSignal_2 ON tbl_signal(EquipmentTemplateId, SignalId);
CREATE INDEX IF NOT EXISTS IDX_TBLSignal_3 ON tbl_signal(EquipmentTemplateId, BaseTypeId);
CREATE INDEX IF NOT EXISTS IDX_BaseTypeId ON tbl_signal(BaseTypeId);

CREATE TABLE IF NOT EXISTS tbl_signalmeanings (
                                    Id SERIAL PRIMARY KEY,
                                    EquipmentTemplateId INT NOT NULL,
                                    SignalId INT NOT NULL,
                                    StateValue SMALLINT NOT NULL,
                                    Meanings VARCHAR(255) DEFAULT NULL,
                                    BaseCondId DECIMAL(12,0) DEFAULT NULL,
                                    CONSTRAINT CLUSTERED UNIQUE (EquipmentTemplateId, SignalId, StateValue)
);
CREATE INDEX IF NOT EXISTS IDX_TBLSignalMeanings_1 ON tbl_signalmeanings(EquipmentTemplateId, SignalId);

CREATE TABLE IF NOT EXISTS tbl_signalproperty (
                                    Id SERIAL PRIMARY KEY,
                                    EquipmentTemplateId INT NOT NULL,
                                    SignalId INT NOT NULL,
                                    SignalPropertyId INT NOT NULL,
                                    CONSTRAINT index_27 UNIQUE (EquipmentTemplateId, SignalId, SignalPropertyId)
);
CREATE INDEX IF NOT EXISTS IDX_TBLSignalProperty_1 ON tbl_signalproperty(EquipmentTemplateId, SignalId);

-- Event tables
CREATE TABLE IF NOT EXISTS tbl_event (
                           Id SERIAL PRIMARY KEY,
                           EquipmentTemplateId INT NOT NULL,
                           EventId INT NOT NULL,
                           EventName VARCHAR(128) NOT NULL,
                           StartType INT NOT NULL,
                           EndType INT NOT NULL,
                           StartExpression TEXT,
                           SuppressExpression TEXT,
                           EventCategory INT NOT NULL,
                           SignalId INT DEFAULT NULL,
                           Enable BOOLEAN NOT NULL,
                           Visible BOOLEAN NOT NULL,
                           Description VARCHAR(255) DEFAULT NULL,
                           DisplayIndex INT DEFAULT NULL,
                           ModuleNo INT NOT NULL DEFAULT 0
);

CREATE INDEX IF NOT EXISTS IDX_TBLEvent_1 ON tbl_event(EquipmentTemplateId);
CREATE INDEX IF NOT EXISTS IDX_TBLEvent_2 ON tbl_event(EquipmentTemplateId, EventId);
CREATE INDEX IF NOT EXISTS IDX_Event_Signal_1 ON tbl_event(EquipmentTemplateId, SignalId);

CREATE TABLE IF NOT EXISTS tbl_eventcondition (
                                    Id SERIAL PRIMARY KEY,
                                    EventConditionId INT NOT NULL,
                                    EquipmentTemplateId INT NOT NULL,
                                    EventId INT NOT NULL,
                                    StartOperation VARCHAR(4) NOT NULL,
                                    StartCompareValue DOUBLE PRECISION NOT NULL,
                                    StartDelay INT NOT NULL,
                                    EndOperation VARCHAR(4) DEFAULT NULL,
                                    EndCompareValue DOUBLE PRECISION DEFAULT NULL,
                                    EndDelay INT DEFAULT NULL,
                                    Frequency INT DEFAULT NULL,
                                    FrequencyThreshold INT DEFAULT NULL,
                                    Meanings VARCHAR(255) DEFAULT NULL,
                                    EquipmentState INT DEFAULT NULL,
                                    BaseTypeId DECIMAL(12,0) DEFAULT NULL,
                                    EventSeverity INT NOT NULL,
                                    StandardName INT DEFAULT NULL
);

CREATE INDEX IF NOT EXISTS IDX_TBLEventCondition_1 ON tbl_eventcondition(EquipmentTemplateId, EventId);

-- Control tables
CREATE TABLE IF NOT EXISTS tbl_control (
                             Id SERIAL PRIMARY KEY,
                             EquipmentTemplateId INT NOT NULL,
                             ControlId INT NOT NULL,
                             ControlName VARCHAR(128) NOT NULL,
                             ControlCategory INT NOT NULL,
                             CmdToken TEXT NOT NULL,
                             BaseTypeId DECIMAL(12,0) DEFAULT NULL,
                             ControlSeverity INT NOT NULL,
                             SignalId INT DEFAULT NULL,
                             TimeOut DOUBLE PRECISION DEFAULT NULL,  -- 修改为 DOUBLE PRECISION
                             Retry INT DEFAULT NULL,
                             Description VARCHAR(255) DEFAULT NULL,
                             Enable BOOLEAN NOT NULL,
                             Visible BOOLEAN NOT NULL,
                             DisplayIndex INT NOT NULL,
                             CommandType INT NOT NULL,
                             ControlType SMALLINT DEFAULT NULL,
                             DataType SMALLINT DEFAULT NULL,
                             MaxValue DOUBLE PRECISION NOT NULL,  -- 修改为 DOUBLE PRECISION
                             MinValue DOUBLE PRECISION NOT NULL,  -- 修改为 DOUBLE PRECISION
                             DefaultValue DOUBLE PRECISION DEFAULT NULL,  -- 修改为 DOUBLE PRECISION
                             ModuleNo INT NOT NULL DEFAULT 0
);


CREATE INDEX IF NOT EXISTS IDX_TBLControl_1 ON tbl_control(EquipmentTemplateId);
CREATE INDEX IF NOT EXISTS IDX_TBLControl_2 ON tbl_control(EquipmentTemplateId, ControlId);


-- Equipment tables
CREATE TABLE IF NOT EXISTS tbl_equipment (
                               StationId INT NOT NULL,
                               EquipmentId SERIAL PRIMARY KEY,
                               EquipmentName VARCHAR(128) NOT NULL,
                               EquipmentNo VARCHAR(128) NOT NULL,
                               EquipmentModule VARCHAR(128) DEFAULT NULL,
                               EquipmentStyle VARCHAR(128) DEFAULT NULL,
                               AssetState INT DEFAULT NULL,
                               Price DOUBLE PRECISION DEFAULT NULL,
                               UsedLimit DOUBLE PRECISION DEFAULT NULL,
                               UsedDate TIMESTAMP DEFAULT NULL,
                               BuyDate TIMESTAMP DEFAULT NULL,
                               Vendor VARCHAR(255) DEFAULT NULL,
                               Unit VARCHAR(255) DEFAULT NULL,
                               EquipmentCategory INT NOT NULL,
                               EquipmentType INT NOT NULL,
                               EquipmentClass INT DEFAULT NULL,
                               EquipmentState INT NOT NULL,
                               EventExpression VARCHAR(255) DEFAULT NULL,
                               StartDelay DOUBLE PRECISION DEFAULT NULL,
                               EndDelay DOUBLE PRECISION DEFAULT NULL,
                               Property VARCHAR(255) DEFAULT NULL,
                               Description VARCHAR(255) DEFAULT NULL,
                               EquipmentTemplateId INT DEFAULT NULL,
                               HouseId INT DEFAULT NULL,
                               MonitorUnitId INT NOT NULL,
                               WorkStationId INT DEFAULT NULL,
                               SamplerUnitId INT NOT NULL,
                               DisplayIndex INT NOT NULL,
                               ConnectState INT NOT NULL,
                               UpdateTime TIMESTAMP NOT NULL,
                               ParentEquipmentId VARCHAR(255) DEFAULT NULL,
                               RatedCapacity VARCHAR(255) DEFAULT NULL,
                               InstalledModule TEXT NOT NULL,
                               ProjectName VARCHAR(255) DEFAULT NULL,
                               ContractNo VARCHAR(255) DEFAULT NULL,
                               InstallTime TIMESTAMP DEFAULT NULL,
                               EquipmentSN VARCHAR(255) DEFAULT NULL,
                               SO VARCHAR(255) DEFAULT NULL,
                               ResourceStructureId INT DEFAULT 0,
                               ExtValue TEXT DEFAULT NULL,
                               photo VARCHAR(255) DEFAULT NULL
);

CREATE INDEX IF NOT EXISTS IDC_EquipmentId_MonitorUnit_ID ON tbl_equipment(MonitorUnitId);
CREATE INDEX IF NOT EXISTS IDX_EquipmentId_1 ON tbl_equipment(MonitorUnitId, SamplerUnitId);
CREATE INDEX IF NOT EXISTS IDX_Equipment_ResourceStructureId ON tbl_equipment(ResourceStructureId);
CREATE INDEX IF NOT EXISTS IDX_Equipment_StationId ON tbl_equipment(StationId, HouseId);
CREATE INDEX IF NOT EXISTS IDX_EquipmentTemplateId ON tbl_equipment(EquipmentTemplateId);

CREATE TABLE IF NOT EXISTS tsl_monitorunit (
                                 MonitorUnitId INT PRIMARY KEY,
                                 MonitorUnitName VARCHAR(128) NOT NULL,
                                 MonitorUnitCategory INT NOT NULL,
                                 MonitorUnitCode VARCHAR(128) NOT NULL,
                                 WorkStationId INT DEFAULT NULL,
                                 StationId INT DEFAULT NULL,
                                 IpAddress VARCHAR(128) DEFAULT NULL,
                                 RunMode INT DEFAULT NULL,
                                 ConfigFileCode VARCHAR(32) DEFAULT NULL,
                                 ConfigUpdateTime TIMESTAMP DEFAULT NULL,
                                 SampleConfigCode VARCHAR(32) DEFAULT NULL,
                                 SoftwareVersion VARCHAR(64) DEFAULT NULL,
                                 Description VARCHAR(255) DEFAULT NULL,
                                 StartTime TIMESTAMP DEFAULT NULL,
                                 HeartbeatTime TIMESTAMP DEFAULT NULL,
                                 ConnectState INT NOT NULL DEFAULT 2,
                                 UpdateTime TIMESTAMP NOT NULL,
                                 IsSync BOOLEAN NOT NULL DEFAULT TRUE,
                                 SyncTime TIMESTAMP DEFAULT NULL,
                                 IsConfigOK BOOLEAN NOT NULL DEFAULT TRUE,
                                 ConfigFileCode_Old VARCHAR(32) DEFAULT NULL,
                                 SampleConfigCode_Old VARCHAR(32) DEFAULT NULL,
                                 AppCongfigId INT DEFAULT NULL,
                                 CanDistribute BOOLEAN NOT NULL,
                                 Enable BOOLEAN NOT NULL,
                                 ProjectName VARCHAR(255) DEFAULT NULL,
                                 ContractNo VARCHAR(255) DEFAULT NULL,
                                 InstallTime TIMESTAMP DEFAULT NULL,
                                 FSU BOOLEAN DEFAULT FALSE
);
CREATE INDEX IF NOT EXISTS IDX_MonitorUnit_WorkStationId ON tsl_monitorunit(WorkStationId);
CREATE INDEX IF NOT EXISTS IDX_MonitorUnit_StationId ON tsl_monitorunit(StationId);


CREATE TABLE IF NOT EXISTS tsl_port (
                          Id SERIAL PRIMARY KEY,
                          PortId INT NOT NULL,
                          MonitorUnitId INT NOT NULL,
                          PortNo INT NOT NULL,
                          PortName VARCHAR(128) NOT NULL,
                          PortType INT NOT NULL,
                          Setting VARCHAR(255) NOT NULL,
                          PhoneNumber VARCHAR(128) DEFAULT NULL,
                          LinkSamplerUnitId INT DEFAULT NULL,
                          Description VARCHAR(255) DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS tbl_controlmeanings (
                                     Id SERIAL PRIMARY KEY,
                                     EquipmentTemplateId INT NOT NULL,
                                     ControlId INT NOT NULL,
                                     ParameterValue SMALLINT NOT NULL,
                                     Meanings VARCHAR(255) DEFAULT NULL,
                                     BaseCondId DECIMAL(12,0) DEFAULT NULL
);

-- Table: tbl_standardtype
CREATE TABLE IF NOT EXISTS tbl_standardtype (
                                  StandardId INT NOT NULL,
                                  StandardName VARCHAR(255) NOT NULL,
                                  StandardAlias VARCHAR(255) NOT NULL,
                                  Remark VARCHAR(255) DEFAULT NULL,
                                  PRIMARY KEY (StandardId)
);

CREATE TABLE IF NOT EXISTS tbl_sysconfig (
                               ConfigKey VARCHAR(255) NOT NULL,
                               ConfigValue VARCHAR(1024) DEFAULT NULL
);
CREATE INDEX IDX_SysConfig_1 ON tbl_sysconfig(ConfigKey);

CREATE TABLE IF NOT EXISTS tsl_monitorunitconfig (
                                       Id SERIAL PRIMARY KEY,
                                       AppConfigId INT NOT NULL,
                                       SiteWebTimeOut INT NOT NULL,
                                       RetryTimes INT NOT NULL,
                                       HeartBeat INT NOT NULL,
                                       EquipmentTimeOut INT NOT NULL,
                                       PortInterruptCount INT NOT NULL,
                                       PortInitializeInternal INT NOT NULL,
                                       MaxPortInitializeTimes INT NOT NULL,
                                       PortQueryTimeOut INT NOT NULL,
                                       DataSaveTimes INT NOT NULL,
                                       HistorySignalSavedTimes INT NOT NULL,
                                       HistoryBatterySavedTimes INT NOT NULL,
                                       HistoryEventSavedTimes INT NOT NULL,
                                       CardRecordSavedCount INT NOT NULL,
                                       ControlLog BOOLEAN NOT NULL,
                                       IpAddressDS VARCHAR(128) DEFAULT NULL
);
-- websocket转taskstatus持久化
CREATE TABLE IF NOT EXISTS task_status (
    id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(64) NOT NULL,
    task_type VARCHAR(32) NOT NULL,
    monitor_unit_id TEXT,
    status VARCHAR(32) NOT NULL,
    current_step VARCHAR(255),
    progress INT DEFAULT 0,
    message TEXT,
    is_final BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expire_time TIMESTAMP
);

CREATE TABLE IF NOT EXISTS tbl_categoryidmap (
                                   BusinessId INT NOT NULL,
                                   CategoryTypeId INT NOT NULL,
                                   OriginalCategoryId INT NOT NULL,
                                   BusinessCategoryId INT NOT NULL,
                                   PRIMARY KEY (BusinessCategoryId, BusinessId, CategoryTypeId, OriginalCategoryId)
);


CREATE TABLE IF NOT EXISTS tsl_monitorunitextend (
	monitorunitid int4 NOT NULL,
	description varchar(255) NULL,
  Extend_Filed1 varchar(255) DEFAULT NULL,
  Extend_Filed2 varchar(255) DEFAULT NULL,
  Extend_Filed3 varchar(255) DEFAULT NULL,
  Extend_Filed4 varchar(255) DEFAULT NULL,
  Extend_Filed5 varchar(255) DEFAULT NULL,
	CONSTRAINT tsl_monitorunitextend_pkey PRIMARY KEY (monitorunitid)
);

