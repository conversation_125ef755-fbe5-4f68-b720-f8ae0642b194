<template>
  <div class="device-log-container">
    <!-- 搜索工具栏 -->
    <div class="search-toolbar">
      <div class="search-left">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[
            new Date(0, 0, 0, 0, 0, 0),
            new Date(0, 0, 0, 23, 59, 59)
          ]"
          class="date-picker"
        />
        <el-button type="primary" @click="getList">
          <el-icon class="mr-1"><Search /></el-icon>
          查询
        </el-button>
      </div>
      <div class="search-right">
        <el-input
          v-model="searchText"
          placeholder="输入关键词搜索..."
          clearable
          class="search-input"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 变更记录表格 -->
    <div class="table-container">
      <el-auto-resizer v-loading="loading">
        <template #default="{ height, width }">
          <el-table-v2
            ref="tableRef"
            :columns="columns"
            :data="filteredTableData"
            :width="width"
            :height="Math.max(height, 600)"
            row-key="id"
            fixed
          />
        </template>
      </el-auto-resizer>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pageIndex"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, onMounted, watch } from "vue";
import {
  ElMessage,
  ElIcon,
  ElPopover,
  ElInput,
  ElCheckbox,
  ElButton
} from "element-plus";
import { Search, Filter } from "@element-plus/icons-vue";
import { getEquipmentOperationLog } from "@/api/device-management";
import type { Column, HeaderCellSlotProps } from "element-plus";
import { formatDateTime } from "@/utils/dateTime";

interface Props {
  equipmentId: string;
  active: boolean;
  tabIndex?: number;
  objectType: number;
}

interface LogData {
  id: number;
  objectType: string;
  objectId: string;
  propertyName: string;
  oldValue: string | null;
  newValue: string;
  operationType: string;
  operationTime: string;
  userName: string;
  objectName: string | null;
}

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    enabled: boolean;
    value?: any;
    type: "text" | "select";
  };
}

const props = defineProps<Props>();

// 响应式数据
const loading = ref(false);
const tableRef = ref();
const tableData = ref<LogData[]>([]);
const dateRange = ref<[string, string] | null>(null);
const searchText = ref("");

// 分页相关
const pageIndex = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 过滤器状态
const filterState = ref<FilterState>({});

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    propertyName: { enabled: false, value: "", type: "text" },
    oldValue: { enabled: false, value: "", type: "text" },
    newValue: { enabled: false, value: "", type: "text" },
    operationType: { enabled: false, value: "", type: "text" },
    operationTime: { enabled: false, value: "", type: "text" },
    userName: { enabled: false, value: "", type: "text" },
    objectType: { enabled: false, value: "", type: "text" }
  };
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: HeaderCellSlotProps) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].enabled = false;
        filterState.value[filterKey].value = "";
      }
    };

    const renderFilterControl = () => {
      const filter = filterState.value[filterKey];
      if (!filter) return null;

      return (
        <>
          <ElCheckbox v-model={filter.enabled}>启用过滤</ElCheckbox>
          <ElInput
            v-model={filter.value}
            placeholder="输入过滤条件"
            size="small"
            style="margin-top: 8px"
            disabled={!filter.enabled}
          />
        </>
      );
    };

    return (
      <div class="flex items-center justify-center">
        <span class="mr-2 text-xs">{props.column.title}</span>
        <ElPopover ref={popoverRef} trigger="click" width={200}>
          {{
            default: () => (
              <div class="filter-wrapper">
                <div class="filter-group">{renderFilterControl()}</div>
                <div class="el-table-v2__demo-filter">
                  <ElButton text onClick={onFilter}>
                    确认
                  </ElButton>
                  <ElButton text onClick={onReset}>
                    重置
                  </ElButton>
                </div>
              </div>
            ),
            reference: () => (
              <ElIcon class="cursor-pointer">
                <Filter />
              </ElIcon>
            )
          }}
        </ElPopover>
      </div>
    );
  };
};

// 创建只读显示单元格
const createReadOnlyCell = (dataKey: string) => {
  return ({ rowData }: { rowData: LogData }) => {
    const displayValue = rowData[dataKey as keyof LogData];
    const showValue =
      displayValue === null || displayValue === undefined || displayValue === ""
        ? "-"
        : String(displayValue);

    return (
      <div class="cell-content">
        <span title={showValue}>{showValue}</span>
      </div>
    );
  };
};

// 创建时间单元格
const createTimeCell = (dataKey: string) => {
  return ({ rowData }: { rowData: LogData }) => {
    const timeValue = rowData[dataKey as keyof LogData] as string;

    // 使用统一的时间格式化工具
    const formatTime = formatDateTime;

    const formattedTime = formatTime(timeValue);

    return (
      <div class="cell-content">
        <span title={timeValue}>{formattedTime}</span>
      </div>
    );
  };
};

// 创建操作类型单元格
const createOperationTypeCell = (dataKey: string) => {
  return ({ rowData }: { rowData: LogData }) => {
    const operationType = rowData[dataKey as keyof LogData] as string;

    const getTypeColor = (type: string) => {
      switch (type) {
        case "新增":
        case "创建":
          return "text-green-600";
        case "修改":
        case "更新":
          return "text-blue-600";
        case "删除":
          return "text-red-600";
        default:
          return "text-gray-600";
      }
    };

    return (
      <div class="cell-content">
        <span class={getTypeColor(operationType)} title={operationType}>
          {operationType || "-"}
        </span>
      </div>
    );
  };
};

// 定义表格列
const columns = computed<Column<LogData>[]>(() => [
  {
    key: "propertyName",
    dataKey: "propertyName",
    title: "操作模块",
    width: 200,
    cellRenderer: createReadOnlyCell("propertyName"),
    headerCellRenderer: createFilterHeader({ dataKey: "propertyName" })
  },
  {
    key: "oldValue",
    dataKey: "oldValue",
    title: "操作前的值",
    width: 200,
    cellRenderer: createReadOnlyCell("oldValue"),
    headerCellRenderer: createFilterHeader({ dataKey: "oldValue" })
  },
  {
    key: "newValue",
    dataKey: "newValue",
    title: "操作后的值",
    width: 300,
    cellRenderer: createReadOnlyCell("newValue"),
    headerCellRenderer: createFilterHeader({ dataKey: "newValue" })
  },
  {
    key: "operationType",
    dataKey: "operationType",
    title: "操作类型",
    width: 100,
    cellRenderer: createOperationTypeCell("operationType"),
    headerCellRenderer: createFilterHeader({ dataKey: "operationType" })
  },
  {
    key: "operationTime",
    dataKey: "operationTime",
    title: "操作时间",
    width: 220,
    cellRenderer: createTimeCell("operationTime"),
    headerCellRenderer: createFilterHeader({ dataKey: "operationTime" })
  },
  {
    key: "userName",
    dataKey: "userName",
    title: "操作人",
    width: 120,
    cellRenderer: createReadOnlyCell("userName"),
    headerCellRenderer: createFilterHeader({ dataKey: "userName" })
  }
]);

// 应用过滤器的数据
const filteredTableData = computed(() => {
  let data = tableData.value;

  // 首先应用搜索文本过滤
  if (searchText.value && searchText.value.trim() !== "") {
    const searchTerm = searchText.value.toLowerCase();
    data = data.filter(item => {
      return (
        (item.propertyName &&
          item.propertyName.toLowerCase().includes(searchTerm)) ||
        (item.oldValue && item.oldValue.toLowerCase().includes(searchTerm)) ||
        (item.newValue && item.newValue.toLowerCase().includes(searchTerm)) ||
        (item.operationType &&
          item.operationType.toLowerCase().includes(searchTerm)) ||
        (item.userName && item.userName.toLowerCase().includes(searchTerm)) ||
        (item.objectType && item.objectType.toLowerCase().includes(searchTerm))
      );
    });
  }

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (
      filter.enabled &&
      filter.value !== "" &&
      filter.value !== null &&
      filter.value !== undefined
    ) {
      data = data.filter(item => {
        const itemValue = item[key as keyof LogData];
        if (itemValue === null || itemValue === undefined) {
          return (
            filter.value.toLowerCase() === "-" ||
            filter.value.toLowerCase() === "null"
          );
        }
        return itemValue
          .toString()
          .toLowerCase()
          .includes(filter.value.toLowerCase());
      });
    }
  });

  return data;
});

// 方法
const getList = async () => {
  if (!props.equipmentId) {
    console.warn("equipmentId为空，无法获取变更记录");
    return;
  }

  // 如果正在加载中，避免重复请求
  if (loading.value) {
    console.warn("正在加载中，跳过重复请求");
    return;
  }

  loading.value = true;
  try {
    console.log("获取变更记录，参数:", {
      equipmentId: props.equipmentId,
      current: pageIndex.value,
      size: pageSize.value,
      dateRange: dateRange.value
    });

    // 准备时间参数
    let startTime: string | null = null;
    let endTime: string | null = null;
    if (dateRange.value && dateRange.value.length === 2) {
      startTime = dateRange.value[0];
      endTime = dateRange.value[1];
    }

    // 调用API获取变更记录
    const result = await getEquipmentOperationLog(
      props.equipmentId,
      pageIndex.value,
      pageSize.value,
      startTime,
      endTime
    );

    console.log("变更记录API响应:", result);

    // 根据记忆，code: 0表示成功状态
    if (result.code === 0 && result.data) {
      // 处理数据，确保字段名称匹配
      const processedData = (result.data.records || []).map(
        (item: any, index: number) => ({
          id: index + 1, // 使用索引作为ID
          objectType: item.objectType || "设备",
          objectId: item.objectId || "",
          propertyName: item.propertyName || "未知模块",
          oldValue: item.oldValue || "",
          newValue: item.newValue || "",
          operationType: item.operationType || "未知操作",
          operationTime: item.operationTime || "",
          userName: item.userName || "未知用户",
          objectName: item.objectName
        })
      );

      tableData.value = processedData;
      total.value = result.data.total || 0;
      pageIndex.value = result.data.current || 1;
      pageSize.value = result.data.size || 20;

      console.log(`成功获取${processedData.length}条变更记录`);
    } else {
      ElMessage.error(
        "获取变更记录失败: " + (result.msg || result.message || "未知错误")
      );
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error("获取变更记录失败:", error);
    ElMessage.error("获取变更记录失败，请检查网络连接");
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  // 搜索功能通过computed属性filteredTableData自动处理
  console.log("搜索关键词:", searchText.value);
};

// 添加一个标志来防止重复请求
const isInitialized = ref(false);

// 统一的数据加载逻辑
const checkAndLoadData = (source: string = "") => {
  const { active, tabIndex, equipmentId } = props;
  console.log(`checkAndLoadData 被调用 - 来源: ${source}`, {
    active,
    tabIndex,
    equipmentId
  });

  if (active && tabIndex === 4 && equipmentId) {
    console.log("条件满足，开始获取变更记录");
    getList();
  }
};

// 只使用一个综合监听器，避免重复调用
watch(
  [() => props.active, () => props.tabIndex, () => props.equipmentId],
  (
    [active, tabIndex, equipmentId],
    [oldActive, oldTabIndex, oldEquipmentId]
  ) => {
    console.log("props变化监听:", {
      active,
      tabIndex,
      equipmentId,
      oldActive,
      oldTabIndex,
      oldEquipmentId
    });

    // 只有在相关值真正改变且条件满足时才调用
    const shouldLoad =
      active &&
      tabIndex === 4 &&
      equipmentId &&
      (active !== oldActive ||
        tabIndex !== oldTabIndex ||
        equipmentId !== oldEquipmentId);

    if (shouldLoad) {
      checkAndLoadData("props变化");
    }
  },
  { immediate: false } // 不立即执行，避免与onMounted重复
);

// 组件挂载
onMounted(() => {
  console.log("DeviceLog组件挂载:", {
    active: props.active,
    tabIndex: props.tabIndex,
    equipmentId: props.equipmentId,
    objectType: props.objectType
  });

  // 初始化过滤器状态
  initFilterState();

  // 组件挂载时检查是否需要加载数据
  checkAndLoadData("组件挂载");
  isInitialized.value = true;
});
</script>

<style scoped>
.device-log-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #fff;
}

.search-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-right {
  display: flex;
  align-items: center;
}

.date-picker {
  width: 350px;
}

.search-input {
  width: 250px;
}

.table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.cell-content {
  padding: 4px 8px;
  word-break: break-all;
  white-space: pre-wrap;
}

:deep(.el-table .el-table__cell) {
  padding: 4px 0;
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

/* 操作类型颜色样式 */
.text-green-600 {
  color: #16a085;
  font-weight: 500;
}

.text-blue-600 {
  color: #3498db;
  font-weight: 500;
}

.text-red-600 {
  color: #e74c3c;
  font-weight: 500;
}

.text-gray-600 {
  color: #6c757d;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-left {
    justify-content: center;
  }

  .search-right {
    justify-content: center;
  }

  .date-picker {
    width: 100%;
  }

  .search-input {
    width: 100%;
  }
}
</style>
