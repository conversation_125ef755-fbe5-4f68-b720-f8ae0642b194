# 设备模板管理功能分析

## 页面布局结构

### 左侧面板（可调整宽度）
- **标题区域**：显示"设备模板管理"标题
- **工具栏**：包含6个操作按钮
  - 删除模板（垃圾桶图标）
  - 导出模板（导出图标）
  - 复制模板（复制图标）
  - 刷新模板（刷新图标）
  - 隐藏动态配置模板（文件夹图标）
  - 显示动态配置模板（打开文件夹图标）
- **搜索框**：支持首字母或关键字查询
- **树形结构**：显示设备模板分类和模板列表
  - 文件夹图标表示分类
  - 文件图标表示具体模板
  - 支持虚拟滚动（大数据量优化）
  - 支持搜索高亮显示

### 右侧内容区
- **标签页导航**：5个标签页
  1. 设备模板
  2. 信号
  3. 事件
  4. 控制
  5. 变更记录
- **标题栏**：显示当前选中的模板名称
- **搜索框**：在信号、事件、控制标签页显示，用于表格数据搜索

## 主要功能

### 1. 模板树管理
- 以树形结构展示设备模板分类和模板
- 支持动态配置模板的显示/隐藏切换
- 支持关键字搜索，搜索结果高亮显示
- 支持双击展开/收起节点
- 支持节点选择，右侧显示对应内容

### 2. 模板操作功能
- **删除模板**：删除选中的模板（需确认）
- **导出模板**：导出模板为XML文件
- **复制模板**：复制现有模板（需输入新名称和原因）
- **刷新模板**：重新加载模板树数据
- **升级成根模板**：将子模板升级为根模板

### 3. 右键菜单操作
针对模板节点的右键菜单包含：
- 删除模板
- 导出模板
- 复制模板
- 查看引用该模板的设备
- 升级成根模板
- 导出模板配置信息

### 4. 关联设备管理
- 查看使用该模板的设备列表
- 批量切换模板
- 批量分发MU配置
- 导出关联设备信息

### 5. 搜索功能
- 树节点搜索：支持按回车键在匹配结果间切换
- 表格搜索：在信号、事件、控制标签页提供独立搜索

## API接口信息

### 模板树相关
- `GET api/config/equipmenttemplate/tree?hideDynamicConfigTemplate=true` - 获取模板树（隐藏动态配置）
- `GET api/config/equipmenttemplate/tree?hideDynamicConfigTemplate=false` - 获取模板树（显示动态配置）
- `GET api/config/equipmenttemplate/{id}` - 根据ID获取模板详情

### 模板操作相关
- `DELETE api/config/equipmenttemplate/{id}` - 删除模板
- `POST api/config/equipmenttemplate/copy` - 复制模板
- `POST api/config/equipmenttemplate/export?equipmentTemplateId={id}` - 导出模板
- `PUT api/config/equipmenttemplate/upgradetoroottemplate?equipmentTemplateId={id}` - 升级为根模板
- `POST api/config/equipmenttemplate/excel/export?equipmentTemplateId={id}` - 导出模板配置信息

### 数据字典相关
- `GET api/config/dataitems?entryId=8` - 获取设备类型列表
- `GET api/config/dataitems?entryId=7` - 获取设备分类列表
- `GET api/config/dataitems?entryId=9` - 获取属性列表
- `GET api/config/dataitems?entryId=14` - 获取厂商列表
- `GET api/config/dataitems?entryId=12` - 获取电池状态列表
- `GET api/config/dataitems?entryId=23` - 获取告警等级列表
- `GET api/config/dataitems?entryId=24` - 获取告警类型列表
- `GET api/config/dataitems?entryId=25` - 获取启动类型列表
- `GET api/config/dataitems?entryId=26` - 获取结束类型列表

### 关联设备相关
- `GET api/config/equipment/reference?equipmentTemplateId={id}` - 获取模板关联的设备
- `GET api/config/equipment/reference/export?equipmentTemplateId={id}` - 导出关联设备
- `POST api/config/equipment/switchtemplate/checkchange` - 检查模板切换影响
- `POST api/config/equipment/switchtemplate` - 切换模板

### 信号相关
- `GET api/config/signal?equipmentTemplateId={id}` - 获取模板信号列表
- `GET api/config/signal/simplifysignals?equipmentTemplateId={id}` - 获取简化信号列表
- `PUT api/config/signal/update` - 更新信号
- `POST api/config/signal/create` - 创建信号
- `DELETE api/config/signal/batchdelete?eqTemplateId={t_id}&signalIds={s_id}` - 批量删除信号
- `PUT api/config/signal/field/copy` - 批量复制信号字段

### 事件相关
- `GET api/config/event?equipmentTemplateId={id}` - 获取模板事件列表
- `PUT api/config/event/update` - 更新事件
- `PUT api/config/event/update/batch` - 批量更新事件
- `POST api/config/event/create` - 创建事件
- `DELETE api/config/event/delete?eqTemplateId={temp}&eventId={event}` - 删除事件
- `POST api/config/event/linkevent` - 关联事件
- `PUT api/config/event/field/copy` - 批量复制事件字段

### 控制相关
- `GET api/config/control/list?equipmentTemplateId={id}` - 获取控制列表
- `POST api/config/control/create` - 创建控制
- `PUT api/config/control/update` - 更新控制
- `DELETE api/config/control/batchdelete` - 批量删除控制
- `PUT api/config/control/field/copy` - 批量复制控制字段

## 用户操作逻辑

### 1. 页面初始化流程
1. 加载模板树数据
2. 展开第一个节点
3. 自动选中第一个叶子节点
4. 加载该节点的详细信息到右侧

### 2. 模板选择流程
1. 用户点击左侧树节点
2. 保存当前选中状态
3. 清空右侧搜索框
4. 加载选中模板的详细信息
5. 默认显示"设备模板"标签页

### 3. 搜索操作流程
1. 用户在搜索框输入关键字
2. 系统实时过滤树节点，高亮匹配文本
3. 按回车键可在匹配结果间循环切换
4. 自动选中匹配的节点并加载详情

### 4. 模板操作流程
1. **删除**：选中模板 → 点击删除按钮 → 确认对话框 → 执行删除 → 刷新树
2. **复制**：选中模板 → 点击复制按钮 → 输入新名称和原因 → 确认 → 刷新树
3. **导出**：选中模板 → 点击导出按钮 → 自动下载文件
4. **刷新**：点击刷新按钮 → 保持当前展开和选中状态 → 重新加载数据

### 5. 右键菜单操作流程
1. 右键点击树节点
2. 显示上下文菜单
3. 根据节点类型显示不同菜单项
4. 点击菜单项执行对应操作

### 6. 标签页切换流程
1. 用户点击标签页
2. 清空表格搜索框
3. 加载对应标签页的数据
4. 在信号/事件/控制标签页显示搜索框

### 7. 关联设备查看流程
1. 右键选择"查看引用该模板的设备"
2. 打开模态对话框显示设备列表
3. 提供导出、批量切换模板、批量分发配置等操作

### 8. 动态配置切换流程
1. 点击显示/隐藏动态配置按钮
2. 切换显示状态
3. 重新加载模板树数据
4. 保持当前选中状态

## 状态管理

### 页面状态
- `currentNode`: 当前选中的模板节点
- `selectedKeys`: 选中的节点keys
- `expendKeys`: 展开的节点keys
- `selectedTabIndex`: 当前选中的标签页索引
- `searchText`: 树搜索关键字
- `tableSearchText`: 表格搜索关键字
- `showDynamic`: 是否显示动态配置模板

### 交互状态
- `siderWidth`: 左侧面板宽度（支持拖拽调整）
- `treeHeight`: 树组件高度（动态计算）
- `seachChanged`: 搜索内容是否变更
- `mathNodes`: 搜索匹配的节点列表
- `mathIndex`: 当前匹配节点索引

## 数据响应格式

所有API响应统一格式：
```json
{
    "code": 0,           // 0表示成功，非0表示错误
    "timestamp": 1749620121400,
    "data": {},          // 返回数据
    "msg": null          // 错误消息
}
```

页面通过检查 `code` 字段判断请求是否成功，错误时显示 `msg` 中的错误信息。
