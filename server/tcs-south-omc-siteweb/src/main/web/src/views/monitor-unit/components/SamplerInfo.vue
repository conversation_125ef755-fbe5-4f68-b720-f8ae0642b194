<template>
  <div class="sampler-info-container">
    <div class="tree-container" @contextmenu="handleEmptyContextMenu">
      <el-tree
        ref="treeRef"
        v-loading="loading"
        :data="treeData"
        :props="treeProps"
        :expand-on-click-node="false"
        :default-expand-all="true"
        node-key="key"
        highlight-current
        @node-click="handleNodeClick"
        @node-contextmenu="handleNodeContextMenu"
      >
        <template #default="{ node, data }">
          <div class="tree-node-content">
            <el-icon class="node-icon">
              <!-- 端口图标 -->
              <OfficeBuilding v-if="data.samplerUnits" />
              <!-- 采集单元图标 -->
              <Cpu v-else-if="data.equipments" />
              <!-- 设备图标 -->
              <Monitor v-else />
            </el-icon>
            <span
              class="node-label"
              :class="{ 'device-label': data.equipmentId }"
              :title="data.equipmentId ? '单击跳转到设备管理' : ''"
            >
              {{ data.label }}
            </span>
          </div>
        </template>
      </el-tree>

      <el-empty
        v-if="treeData.length === 0 && !loading"
        description="暂无采集信息"
        :image-size="80"
      />
    </div>

    <!-- 添加端口弹框 -->
    <el-dialog
      v-model="showAddPortDialog"
      :title="isEditMode ? '编辑端口' : '添加端口'"
      width="850px"
      :before-close="handleAddPortCancel"
    >
      <AddPortForm
        ref="addPortFormRef"
        :monitor-unit="monitorUnit"
        :port="currentEditPort"
        :is-edit="isEditMode"
        @success="handleAddPortSuccess"
      />
      <template #footer>
        <el-button @click="handleAddPortCancel">取消</el-button>
        <el-button type="primary" @click="handleAddPortConfirm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 添加采集单元弹框 -->
    <el-dialog
      v-model="showAddSamplerDialog"
      :title="isEditMode ? '编辑采集单元' : '添加采集单元'"
      width="500px"
      :before-close="handleAddSamplerCancel"
    >
      <AddSamplerForm
        ref="addSamplerFormRef"
        :monitor-unit="monitorUnit"
        :port="currentEditPort"
        :sampler="currentEditSampler"
        :is-edit="isEditMode"
        @success="handleAddSamplerSuccess"
      />
      <template #footer>
        <el-button @click="handleAddSamplerCancel">取消</el-button>
        <el-button type="primary" @click="handleAddSamplerConfirm"
          >确定</el-button
        >
      </template>
    </el-dialog>

    <!-- 添加设备弹框 -->
    <el-dialog
      v-model="showAddDeviceDialog"
      title="添加设备"
      width="800px"
      :before-close="handleAddDeviceCancel"
    >
      <AddDeviceForm
        ref="addDeviceFormRef"
        :monitor-unit="monitorUnit"
        :current-node="currentNode"
        :mu-list="muList"
        :current-port="currentEditPort"
        @success="handleAddDeviceSuccess"
      />
      <template #footer>
        <el-button @click="handleAddDeviceCancel">取消</el-button>
        <el-button type="primary" @click="handleAddDeviceConfirm"
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Edit,
  Delete,
  Plus,
  OfficeBuilding,
  Cpu,
  Monitor
} from "@element-plus/icons-vue";
import ContextMenu from "@imengyu/vue3-context-menu";
import {
  getMonitorUnitSamplerTree,
  deletePort,
  deleteSamplerUnit,
  deleteEquipment
} from "@/api/monitor-unit";
import AddPortForm from "./AddPortForm.vue";
import AddSamplerForm from "./AddSamplerForm.vue";
import AddDeviceForm from "./AddDeviceForm.vue";

interface Props {
  monitorUnit: any;
  currentNode?: any;
  muList?: any[];
}

const props = defineProps<Props>();

// 路由实例
const router = useRouter();

// 响应式数据
const loading = ref(false);
const treeData = ref<any[]>([]);
const treeRef = ref();

// 弹框控制
const showAddPortDialog = ref(false);
const showAddSamplerDialog = ref(false);
const showAddDeviceDialog = ref(false);
const isEditMode = ref(false);

// 表单引用
const addPortFormRef = ref();
const addSamplerFormRef = ref();
const addDeviceFormRef = ref();

// 当前编辑的数据
const currentEditPort = ref<any>(null);
const currentEditSampler = ref<any>(null);
const currentEditDevice = ref<any>(null);

// 树形组件配置
const treeProps = {
  children: "children",
  label: "label"
};

// 方法
const loadSamplerTree = async () => {
  if (!props.monitorUnit?.monitorUnitId) return;

  try {
    loading.value = true;
    const response = await getMonitorUnitSamplerTree(
      props.monitorUnit.monitorUnitId
    );
    const rawData = response.data || [];
    treeData.value = processTreeData(rawData);
  } catch (error) {
    console.error("加载采集信息失败:", error);
    ElMessage.error("加载采集信息失败");
  } finally {
    loading.value = false;
  }
};

// 刷新采集树
const refreshSamplerTree = () => {
  loadSamplerTree();
};

const processTreeData = (data: any[]): any[] => {
  if (!data) return [];

  return data.map((node: any) => {
    const processedNode: any = {
      key: "",
      label: "",
      children: []
    };

    // 端口节点
    if (node.samplerUnits) {
      processedNode.key = `port_${node.portId}`;
      processedNode.label = node.portName;
      processedNode.samplerUnits = node.samplerUnits;
      processedNode.portId = node.portId;
      processedNode.portName = node.portName;

      if (node.samplerUnits.length > 0) {
        processedNode.children = processTreeData(node.samplerUnits);
      }
    }
    // 采集单元节点
    else if (node.equipments) {
      processedNode.key = `sampler_${node.samplerUnitId}`;
      processedNode.label = node.samplerUnitName;
      processedNode.equipments = node.equipments;
      processedNode.samplerUnitId = node.samplerUnitId;
      processedNode.samplerUnitName = node.samplerUnitName;

      if (node.equipments.length > 0) {
        processedNode.children = processTreeData(node.equipments);
      }
    }
    // 设备节点
    else {
      processedNode.key = `device_${node.equipmentId}`;
      processedNode.label = node.equipmentName;
      processedNode.equipmentId = node.equipmentId;
      processedNode.equipmentName = node.equipmentName;
    }

    return processedNode;
  });
};

const handleNodeClick = (data: any, node: any) => {
  // 如果点击的是设备节点，则跳转到设备管理
  if (data.equipmentId) {
    handleDeviceJump(data);
  }
};

const handleDeviceJump = (device: any) => {
  if (!device.equipmentId) {
    ElMessage.warning("设备ID不存在，无法跳转");
    return;
  }

  // 跳转到设备管理页面
  router.push({
    path: `/siteweb-omc/device-management/${device.equipmentId}`,
    query: {
      title: device.equipmentName || "设备管理"
    }
  });
};

const handleNodeContextMenu = (event: MouseEvent, data: any, node: any) => {
  event.preventDefault();
  event.stopPropagation();

  showNodeContextMenu(event, data);
};

const handleEmptyContextMenu = (event: MouseEvent) => {
  // 检查是否点击在树形组件的空白区域
  const target = event.target as HTMLElement;

  // 如果点击的是节点相关元素，不处理
  if (target.closest(".el-tree-node") || target.closest(".tree-node-content")) {
    return;
  }

  event.preventDefault();
  showEmptyContextMenu(event);
};

// 显示节点右键菜单
const showNodeContextMenu = (event: MouseEvent, data: any) => {
  const hasMonitorUnit = props.monitorUnit;

  // 端口菜单
  if (data.samplerUnits) {
    ContextMenu.showContextMenu({
      zIndex: 10000,
      x: event.x,
      y: event.y,
      items: [
        {
          label: "编辑端口",
          icon: "h:pencil-alt",
          onClick: () => {
            editPort(data);
          }
        },
        {
          label: "删除端口",
          icon: "h:trash",
          onClick: () => {
            handleDeletePort(data);
          }
        },
        {
          label: "添加采集单元",
          icon: "h:plus",
          onClick: () => {
            addSamplerUnit(data);
          }
        },
        {
          label: "添加设备",
          icon: "h:plus",
          onClick: () => {
            addDevice(data);
          }
        }
      ]
    });
  }
  // 采集单元菜单
  else if (data.equipments) {
    ContextMenu.showContextMenu({
      zIndex: 10000,
      x: event.x,
      y: event.y,
      items: [
        {
          label: "编辑采集单元",
          icon: "h:pencil-alt",
          onClick: () => {
            editSamplerUnit(data);
          }
        },
        {
          label: "删除采集单元",
          icon: "h:trash",
          onClick: () => {
            handleDeleteSamplerUnit(data);
          }
        },
        {
          label: "添加设备",
          icon: "h:plus",
          disabled: !hasMonitorUnit,
          onClick: () => {
            addDevice(data);
          }
        }
      ]
    });
  }
  // 设备菜单
  else {
    ContextMenu.showContextMenu({
      zIndex: 10000,
      x: event.x,
      y: event.y,
      items: [
        {
          label: "编辑设备",
          icon: "h:pencil-alt",
          onClick: () => {
            editDevice(data);
          }
        },
        {
          label: "删除设备",
          icon: "h:trash",
          onClick: () => {
            handleDeleteDevice(data);
          }
        }
      ]
    });
  }
};

// 显示空白区域右键菜单
const showEmptyContextMenu = (event: MouseEvent) => {
  const hasMonitorUnit = props.monitorUnit;

  ContextMenu.showContextMenu({
    zIndex: 10000,
    x: event.x,
    y: event.y,
    items: [
      {
        label: "添加端口",
        icon: "h:plus",
        disabled: !hasMonitorUnit,
        onClick: () => {
          addPort();
        }
      },
      {
        label: "添加设备",
        icon: "h:plus",
        disabled: !hasMonitorUnit,
        onClick: () => {
          addDevice(null);
        }
      }
    ]
  });
};

// 端口操作方法
const addPort = () => {
  if (!props.monitorUnit) {
    ElMessage.warning("未选择监控单元");
    return;
  }

  currentEditPort.value = null;
  isEditMode.value = false;
  showAddPortDialog.value = true;
};

const editPort = (port: any) => {
  currentEditPort.value = port;
  isEditMode.value = true;
  showAddPortDialog.value = true;
};

const handleDeletePort = (port: any) => {
  ElMessageBox.confirm(`确认要删除端口 ${port.portName} 吗？`, "确认删除端口", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      try {
        await deletePort(port.portId);
        ElMessage.success("删除成功！");
        setTimeout(() => {
          loadSamplerTree();
        }, 500);
      } catch (error) {
        console.error("删除端口失败:", error);
        ElMessage.error("删除端口失败");
      }
    })
    .catch(() => {
      // 取消删除
    });
};

const handleAddPortConfirm = () => {
  if (addPortFormRef.value) {
    addPortFormRef.value.submit();
  }
};

const handleAddPortSuccess = () => {
  showAddPortDialog.value = false;
  ElMessage.success(isEditMode.value ? "编辑端口成功！" : "新增端口成功！");
  loadSamplerTree();
};

const handleAddPortCancel = () => {
  showAddPortDialog.value = false;
  currentEditPort.value = null;
  isEditMode.value = false;
};

// 采集单元操作方法
const addSamplerUnit = (port: any) => {
  if (!props.monitorUnit) {
    ElMessage.warning("未选择监控单元");
    return;
  }

  currentEditPort.value = port;
  currentEditSampler.value = null;
  isEditMode.value = false;
  showAddSamplerDialog.value = true;
};

const editSamplerUnit = (samplerUnit: any) => {
  currentEditSampler.value = samplerUnit;
  currentEditPort.value = null;
  isEditMode.value = true;
  showAddSamplerDialog.value = true;
};

const handleDeleteSamplerUnit = (samplerUnit: any) => {
  ElMessageBox.confirm(
    `确认要删除采集单元 [${samplerUnit.samplerUnitId}]${samplerUnit.samplerUnitName} 吗？`,
    "确认删除采集单元",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(async () => {
      try {
        await deleteSamplerUnit(samplerUnit.samplerUnitId);
        ElMessage.success("删除成功！");
        setTimeout(() => {
          loadSamplerTree();
        }, 500);
      } catch (error) {
        console.error("删除采集单元失败:", error);
        ElMessage.error("删除采集单元失败");
      }
    })
    .catch(() => {
      // 取消删除
    });
};

const handleAddSamplerConfirm = () => {
  if (addSamplerFormRef.value) {
    addSamplerFormRef.value.submit();
  }
};

const handleAddSamplerSuccess = () => {
  showAddSamplerDialog.value = false;
  ElMessage.success(
    isEditMode.value ? "编辑采集单元成功！" : "新增采集单元成功！"
  );
  loadSamplerTree();
};

const handleAddSamplerCancel = () => {
  showAddSamplerDialog.value = false;
  currentEditPort.value = null;
  currentEditSampler.value = null;
  isEditMode.value = false;
};

// 设备操作方法
const addDevice = (node: any) => {
  if (!props.monitorUnit) {
    ElMessage.warning("未选择监控单元");
    return;
  }

  currentEditPort.value = node?.samplerUnits ? node : null;
  showAddDeviceDialog.value = true;
};

const editDevice = (device: any) => {
  handleDeviceJump(device);
};

const handleDeleteDevice = (device: any) => {
  ElMessageBox.confirm(`确认要删除设备 ${device.equipmentName} 吗？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      try {
        await deleteEquipment(device.equipmentId);
        ElMessage.success("删除成功！");
        setTimeout(() => {
          loadSamplerTree();
        }, 500);
      } catch (error) {
        console.error("删除设备失败:", error);
        ElMessage.error("删除设备失败");
      }
    })
    .catch(() => {
      // 取消删除
    });
};

const handleAddDeviceConfirm = () => {
  if (addDeviceFormRef.value) {
    addDeviceFormRef.value.submit();
  }
};

const handleAddDeviceSuccess = () => {
  showAddDeviceDialog.value = false;
  ElMessage.success("新增设备成功！");
  loadSamplerTree();
};

const handleAddDeviceCancel = () => {
  showAddDeviceDialog.value = false;
  currentEditPort.value = null;
};

// 监听props变化
watch(
  () => props.monitorUnit,
  () => {
    loadSamplerTree();
  },
  { immediate: true }
);

// 生命周期
onMounted(() => {
  loadSamplerTree();
});

// 暴露方法给父组件调用
defineExpose({
  refreshSamplerTree,
  addPort
});
</script>

<style scoped>
.sampler-info-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tree-container {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.tree-node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-icon {
  font-size: 16px;
  color: #409eff;
}

.node-label {
  font-size: 14px;
  color: #333;
}

.device-label {
  color: #409eff;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.device-label:hover {
  color: #337ecc;
  text-decoration: underline;
}

.el-tree {
  --el-tree-node-hover-bg-color: #f0f9ff;
}

.el-tree :deep(.el-tree-node__content:hover) {
  background-color: #f0f9ff;
}

.el-tree :deep(.is-current > .el-tree-node__content) {
  background-color: #e1f5fe;
}

.el-tree :deep(.el-tree-node__content) {
  height: 32px;
}

.el-tree :deep(.el-tree-node__expand-icon) {
  color: #409eff;
}
</style>
