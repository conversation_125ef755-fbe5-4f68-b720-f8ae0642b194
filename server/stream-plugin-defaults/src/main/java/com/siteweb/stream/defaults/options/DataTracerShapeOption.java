package com.siteweb.stream.defaults.options;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.siteweb.tcs.common.expression.ValueExpression;
import com.siteweb.stream.common.stream.StreamShapeOption;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.slf4j.event.Level;

/**
 * <AUTHOR> (2025-02-22)
 **/

@Data
@EqualsAndHashCode(callSuper = true)
public class DataTracerShapeOption extends StreamShapeOption {

    private Boolean enabled;

    /**
     * 要打印的数据
     */
    private ValueExpression property;

    /**
     * 输出目标
     */
    private OutputTarget output;

    /**
     * 输出为日志时 日志等级
     */
    private Level logLevel;


    /**
     * 输出为日志时，日志输出格式
     */
    private String format = "";


    public enum OutputTarget {
        LOGGER("logger"),
        DEBUGGER("debugger"),
        ;

        OutputTarget(String code) {
            this.value = code;
        }


        private final String value;

        @JsonValue
        public String getValue() {
            return this.value;
        }

        @JsonCreator
        public static OutputTarget fromInt(String i) {
            for (OutputTarget status : OutputTarget.values()) {
                if (status.value.equals(i)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("No enum constant with ordinal " + i);
        }
    }


}
