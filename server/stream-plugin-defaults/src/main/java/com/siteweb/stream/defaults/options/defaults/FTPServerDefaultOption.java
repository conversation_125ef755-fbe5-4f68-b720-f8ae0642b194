package com.siteweb.stream.defaults.options.defaults;

import com.siteweb.stream.common.stream.AbstractShapeDefaultOption;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.defaults.options.FTPServerShapeOption;


/**
 * <AUTHOR> (2025-04-24)
 **/

public class FTPServerDefaultOption implements AbstractShapeDefaultOption {
    @Override
    public StreamShapeOption option() {
        FTPServerShapeOption option = new FTPServerShapeOption();

        return option;
    }
}
