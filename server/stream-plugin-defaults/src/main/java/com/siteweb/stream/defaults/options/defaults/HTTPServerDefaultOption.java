package com.siteweb.stream.defaults.options.defaults;

import com.siteweb.stream.common.stream.AbstractShapeDefaultOption;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.defaults.options.HTTPServerShapeOption;

/**
 * <AUTHOR> (2025-04-24)
 **/

public class HTTPServerDefaultOption implements AbstractShapeDefaultOption {
    @Override
    public StreamShapeOption option() {
        HTTPServerShapeOption option = new HTTPServerShapeOption();

        return option;
    }
}
