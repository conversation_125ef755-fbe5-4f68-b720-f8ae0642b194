# CMCC 插件功能分析与业务流程文档

## 1. 项目概述

### 1.1 插件介绍
CMCC 南向接入插件是TCS系统中用于对接中国移动动力环境集中监控系统的核心组件，实现了中国移动B接口技术规范(2016版)的完整协议栈。该插件负责FSU(现场采集单元)的接入、管理、数据采集、告警处理和控制下发等功能。

### 1.2 技术架构
- **后端框架**: Spring Boot + Pekko Actor + MyBatis Plus
- **前端框架**: Vue 3 + TypeScript + Element Plus
- **通信协议**: HTTP/SOAP + FTP + XML
- **数据库**: 支持多种数据库（MySQL、PostgreSQL、H2等）
- **插件体系**: 基于PF4J的热插拔插件系统

### 1.3 核心特性
- 支持FSU自动和手动入网
- 实时数据采集与处理
- 告警信息上报与管理
- 设备远程控制
- 配置同步与备份
- 标准化管理
- 多租户支持

## 2. 架构设计

### 2.1 后端架构

#### 2.1.1 主要模块划分
```
tcs-south-cmcc/
├── src/main/java/com/siteweb/tcs/south/cmcc/
│   ├── SouthCmccPlugin.java          # 插件主入口
│   ├── CMCCWebService.java           # HTTP服务
│   ├── FsuLauncher.java              # FSU启动器
│   ├── config/                       # 配置管理
│   ├── connector/                    # 连接器层
│   │   ├── letter/                   # 协议报文
│   │   ├── protocol/                 # 协议定义
│   │   ├── process/                  # 业务处理
│   │   ├── commands/                 # 命令管理
│   │   ├── ftp/                      # FTP功能
│   │   └── services/                 # 业务服务
│   ├── dal/                          # 数据访问层
│   │   ├── entity/                   # 实体类
│   │   ├── mapper/                   # 数据映射
│   │   └── provider/                 # 数据提供者
│   ├── stream/                       # 流计算集成
│   ├── web/                          # Web控制器
│   └── util/                         # 工具类
```

#### 2.1.2 核心组件说明

**SouthCmccPlugin**: 插件生命周期管理，负责插件的启动、停止和资源管理。

**CMCCWebService**: HTTP服务器，接收FSU的SOAP请求，实现B接口的通信协议。

**CmccFSUEntity**: FSU实体Actor，每个FSU对应一个Actor实例，处理FSU的所有业务逻辑。

**CmccFsuMessageProcessor**: 消息处理器，处理各种B接口协议消息。

**CmccFsuControlProcessor**: 控制处理器，处理设备控制命令的下发。

### 2.2 前端架构

#### 2.2.1 页面结构
```
web/src/views/
├── fsu-list/                         # FSU列表管理
│   ├── index.vue                     # 主列表页面
│   ├── FsuDetail.vue                 # FSU详情页面
│   └── components/                   # 组件
│       ├── DeviceManagementComponent.vue    # 设备管理
│       ├── BackupManagementComponent.vue    # 备份管理
│       ├── OperateLogComponent.vue          # 操作日志
│       ├── ProtocolDebugComponent.vue       # 协议调试
│       └── StandardizationRateComponent.vue # 标准化率
├── standard/                         # 标准管理
└── standard-config/                  # 标准配置
```

#### 2.2.2 API接口层
```typescript
// API模块划分
src/api/
├── fsu.ts                           # FSU管理API
├── cmcc-device.ts                   # 设备管理API
├── region.ts                        # 区域管理API
├── standard.ts                      # 标准化API
└── operation-log.ts                 # 操作日志API
```

## 3. 业务流程分析

### 3.1 FSU注册流程

#### 3.1.1 自动入网流程
```mermaid
sequenceDiagram
    participant FSU as FSU设备
    participant WebService as CMCCWebService
    participant Entity as CmccFSUEntity
    participant Service as CmccFsuAccessService
    participant DB as 数据库

    FSU->>WebService: 发送LOGIN消息
    WebService->>Entity: 转发消息到FSU Entity
    
    alt FSU未在数据库中
        Entity->>Service: 检查自动入网配置
        alt 开启自动入网
            Service->>DB: 创建待处理FSU记录
            Service->>DB: 自动批准入网
            Entity->>FSU: 返回登录成功
        else 关闭自动入网
            Entity->>DB: 仅创建待处理FSU记录
            Entity->>FSU: 返回等待批准
        end
    else FSU已存在
        Entity->>Entity: 验证用户名密码
        alt 验证成功
            Entity->>FSU: 返回登录成功
            Entity->>Entity: 更新FSU状态为在线
        else 验证失败
            Entity->>FSU: 返回登录失败
        end
    end
```

#### 3.1.2 手动入网流程
1. FSU发送LOGIN消息被记录到pending表
2. 管理员在前端审核待入网FSU
3. 手动配置FSU信息并批准入网
4. 系统创建正式FSU记录
5. 下次FSU登录时可正常通过验证

#### 3.1.3 关键代码位置
- 入网处理：`CmccFsuAccessService:79` (handleNewFsuNetworkAccess方法)
- LOGIN消息处理：`CmccFSUEntity:330` (handleLogin方法)
- 前端FSU管理：`fsu-list/index.vue`

### 3.2 信号获取流程

#### 3.2.1 实时数据上报
```mermaid
sequenceDiagram
    participant FSU as FSU设备
    participant WebService as CMCCWebService
    participant Entity as CmccFSUEntity
    participant Stream as 流计算引擎
    participant Hub as 数据总线

    FSU->>WebService: 发送SEND_DATA消息
    WebService->>Entity: 解析并转发消息
    Entity->>Stream: 将数据送入流计算引擎
    Stream->>Stream: 数据处理与转换
    Stream->>Hub: 标准化后的数据
    Hub->>Hub: 数据存储与分发
    Entity->>FSU: 返回ACK确认
```

#### 3.2.2 主动查询流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant Controller as 控制器
    participant Entity as CmccFSUEntity
    participant Http as HTTP请求器
    participant FSU as FSU设备

    User->>Frontend: 触发数据查询
    Frontend->>Controller: 调用API
    Controller->>Entity: 发送用户命令
    Entity->>Http: 构建HTTP请求
    Http->>FSU: 发送GET_DATA请求
    FSU->>Http: 返回数据
    Http->>Entity: 解析响应
    Entity->>Controller: 返回处理结果
    Controller->>Frontend: 响应数据
    Frontend->>User: 显示结果
```

#### 3.2.3 数据处理组件
- 数据接收：`SendDataMessage` 协议报文
- 流计算集成：`GetDataShape` 流计算节点
- HTTP请求器：`CmccXmlHttpRequester`
- 消息处理：`CmccFsuMessageProcessor:287` (processUserCommand方法)

### 3.3 告警上报流程

#### 3.3.1 告警处理链路
```mermaid
sequenceDiagram
    participant FSU as FSU设备
    participant WebService as CMCCWebService
    participant Entity as CmccFSUEntity
    participant Stream as 流计算引擎
    participant Hub as 数据总线
    participant Alarm as 告警系统

    FSU->>WebService: 发送SEND_ALARM消息
    Note over FSU,WebService: 包含TAlarm告警信息列表
    
    WebService->>Entity: 转发告警消息
    Entity->>Stream: 送入告警处理流
    
    Stream->>Stream: 告警规则匹配
    Stream->>Stream: 告警等级判定
    Stream->>Stream: 告警过滤与去重
    
    Stream->>Hub: 标准化告警数据
    Hub->>Alarm: 触发告警处理
    
    Entity->>FSU: 返回ACK确认
```

#### 3.3.2 告警数据结构
```java
// TAlarm 告警信息结构
public class TAlarm {
    private String deviceId;        // 设备ID
    private String alarmId;         // 告警ID  
    private Integer alarmType;      // 告警类型
    private Integer alarmLevel;     // 告警等级
    private LocalDateTime occurTime;// 发生时间
    private String alarmDesc;       // 告警描述
    private Integer alarmValue;     // 告警值
}
```

#### 3.3.3 关键代码位置
- 告警报文：`SendAlarmMessage` 协议定义
- 流计算集成：`SendAlarmShape` 处理节点
- 告警实体：`CMCCAlarm` 数据模型

### 3.4 控制下发流程

#### 3.4.1 控制命令处理
```mermaid
sequenceDiagram
    participant User as 操作员
    participant Frontend as 前端界面
    participant Controller as 控制器
    participant Hub as 数据总线
    participant Entity as CmccFSUEntity
    participant Processor as 控制处理器
    participant Http as HTTP请求器
    participant FSU as FSU设备

    User->>Frontend: 发起控制操作
    Frontend->>Controller: 提交控制命令
    Controller->>Hub: 转发控制请求
    Hub->>Entity: 路由到目标FSU
    
    Entity->>Processor: 委托控制处理器
    Processor->>Processor: 构建SetPoint消息
    Processor->>Http: 发送HTTP请求
    Http->>FSU: 下发控制命令
    
    FSU->>Http: 返回执行结果
    Http->>Processor: 解析响应
    Processor->>Entity: 返回控制结果
    Entity->>Hub: 上报执行状态
    Hub->>Controller: 响应结果
    Controller->>Frontend: 更新界面
    Frontend->>User: 显示执行结果
```

#### 3.4.2 控制点映射
控制系统通过以下方式实现标准化控制：
1. 前端发起标准控制命令
2. 通过`CMCCControl`表查找FSU本地控制点映射
3. 构建符合B接口规范的`SetPointMessage`
4. 使用HTTP/SOAP协议下发到FSU
5. 处理FSU返回的`SetPointAckMessage`

#### 3.4.3 关键代码位置
- 控制处理器：`CmccFsuControlProcessor:44` (handleGatewayControl方法)
- 控制报文：`SetPointMessage` 协议定义
- 控制映射：`CMCCControl` 实体类

### 3.5 与OMC联动机制

#### 3.5.1 系统集成架构
```mermaid
graph TB
    subgraph "TCS 系统"
        CMCC[CMCC插件]
        Hub[数据总线]
        Core[TCS核心]
        Stream[流计算引擎]
    end
    
    subgraph "外部系统"
        FSU[FSU设备]
        OMC[OMC系统]
        NMS[网管系统]
    end
    
    FSU -->|B接口协议| CMCC
    CMCC --> Hub
    Hub --> Stream
    Hub --> Core
    Core -->|北向接口| OMC
    Core -->|标准协议| NMS
```

#### 3.5.2 数据流转机制

**配置同步流程：**
1. FSU上报配置变更（SEND_DEV_CONF_DATA）
2. CMCC插件解析配置数据
3. 通过`ITcsGatewayService`上报给Hub
4. Hub触发配置变更事件
5. 北向插件将变更推送给OMC

**告警联动流程：**
1. FSU上报告警信息（SEND_ALARM）
2. 流计算引擎处理告警规则
3. 标准化后存储到Hub
4. Hub触发告警事件
5. OMC接收标准化告警信息

#### 3.5.3 关键集成点
- 配置变更处理：`CmccFSUEntity:295` (handleDevConfData方法)
- Hub服务集成：`ITcsGatewayService` 接口
- 生命周期事件：`LifeCycleEventEnum` 枚举

## 4. 数据模型

### 4.1 核心实体

#### 4.1.1 FSU实体 (CMCCFsu)
```java
@TableName("cmcc_fsus")
public class CMCCFsu implements IGateway {
    private String gatewayId;           // FSU ID
    private String gatewayName;         // FSU名称
    private String ipAddress;           // IP地址
    private String fsuPort;            // 端口
    private boolean enable;            // 启用状态
    private ConfigSyncEnum configSync; // 配置同步方式
    private String username;           // 认证用户名
    private String password;           // 认证密码
    private String vendor;             // 厂商
    private String model;              // 型号
    private Long regionId;             // 区域ID
    // FTP配置
    private FTPType ftpType;           // FTP类型
    private String ftpUserName;        // FTP用户名
    private String ftpPassword;        // FTP密码
    // 时间信息
    private LocalDateTime createTime;   // 创建时间
    private LocalDateTime lastLoginDate; // 最后登录时间
}
```

#### 4.1.2 设备实体 (CMCCDevice)
```java
@TableName("cmcc_devices") 
public class CMCCDevice {
    private String deviceId;           // 设备ID
    private String fsuId;             // 所属FSU
    private String deviceName;        // 设备名称
    private String deviceType;        // 设备类型
    private String manufacturer;      // 制造商
    private String model;            // 型号
    private String version;          // 版本
    private Integer status;          // 设备状态
}
```

#### 4.1.3 信号实体 (CMCCSignal)
```java
@TableName("cmcc_signals")
public class CMCCSignal {
    private String signalId;          // 信号ID
    private String fsuId;            // 所属FSU
    private String deviceId;         // 所属设备
    private String signalName;       // 信号名称
    private String signalType;       // 信号类型
    private String unit;             // 单位
    private Double currentValue;     // 当前值
    private LocalDateTime updateTime; // 更新时间
}
```

#### 4.1.4 告警实体 (CMCCAlarm)
```java
@TableName("cmcc_alarms")
public class CMCCAlarm {
    private String alarmId;          // 告警ID
    private String fsuId;           // 所属FSU
    private String deviceId;        // 设备ID
    private String alarmType;       // 告警类型
    private Integer alarmLevel;     // 告警等级
    private String alarmDesc;       // 告警描述
    private LocalDateTime occurTime; // 发生时间
    private LocalDateTime clearTime; // 清除时间
    private Integer alarmStatus;    // 告警状态
}
```

#### 4.1.5 控制实体 (CMCCControl)
```java
@TableName("cmcc_controls")
public class CMCCControl {
    private String controlId;        // 控制ID
    private String fsuId;           // 所属FSU
    private String deviceId;        // 设备ID
    private String spId;            // 设定点ID
    private String controlName;     // 控制名称
    private String controlType;     // 控制类型
    private String spType;          // 设定点类型
    private Integer signalNumber;   // 信号编号
}
```

### 4.2 数据库设计

#### 4.2.1 表结构关系
```mermaid
erDiagram
    cmcc_fsus ||--o{ cmcc_devices : "包含"
    cmcc_devices ||--o{ cmcc_signals : "包含"
    cmcc_devices ||--o{ cmcc_alarms : "产生"
    cmcc_devices ||--o{ cmcc_controls : "支持"
    cmcc_fsus ||--o{ cmcc_pending_fsus : "待处理"
    cmcc_fsus ||--o{ cmcc_operation_logs : "记录"
    
    cmcc_fsus {
        string gateway_id PK
        string gateway_name
        string ip_address
        string fsu_port
        boolean enable
        enum config_sync
        long region_id
        datetime create_time
        datetime update_time
    }
    
    cmcc_devices {
        string device_id PK
        string fsu_id FK
        string device_name
        string device_type
        string manufacturer
        string model
        int status
    }
    
    cmcc_signals {
        bigint id PK
        string signal_id
        string fsu_id FK
        string device_id FK
        string signal_name
        string signal_type
        double current_value
        datetime update_time
    }
```

## 5. 配置管理

### 5.1 插件配置 (plugin.yml)
```yaml
plugin:
  id: south-cmcc-plugin
  name: 中国移动南向接入插件
  description: TCS中国移动南向接入插件
  version: 1.0.0
  provider: Siteweb

  # 中间件资源配置
  middleware:
    database:
      primary: test-postgresql-config-001
    redis:
      primary: tcs-redis
    kafka:
      primary: tcs-kafka
    http-server:
      primary: cmcc-akka-http-server-service

  gateway:
    auto-access:
      enable: false              # 开启FSU自动入网
      default-enable: true       # 入网后自动启用
      default-config-sync: DOWN_TOP # 默认配置同步方式

    default-port: 8080          # FSU的默认端口
    backup:
      max-file-count: 100       # 保留最多备份记录数
      delete-after: 180d        # 备份记录保留时间
      min-file-count: 10        # 最少保留备份数

    sharding:
      name: cmcc-gateway
      count: 10                 # Actor分片数量

  # FSU定时任务配置
  scheduler:
    time_check:
      interval: 60              # 时间同步间隔(秒)
      initial-delay: 30         # 初始延迟(秒)
    get_fsu_info:
      interval: 600             # FSU信息获取间隔(秒)
      initial-delay: 30         # 初始延迟(秒)
```

### 5.2 数据库配置

支持多种数据库类型：
- **H2**: 开发环境默认数据库
- **MySQL**: 生产环境推荐
- **PostgreSQL**: 高性能场景
- **OpenGauss**: 国产化要求
- **达梦**: 国产化要求
- **SQLite**: 轻量级部署

### 5.3 流计算图模板

默认提供`cmcc-default-template.json`流计算图模板，包含：
- 数据接收节点
- 告警处理节点
- 数据转换节点
- 输出节点

## 6. API接口

### 6.1 FSU管理API

#### 6.1.1 基础管理接口
```typescript
// 获取FSU列表
GET /api/thing/south-cmcc-plugin/api/cmcc/2016/fsu/list

// 获取待处理FSU列表  
GET /api/thing/south-cmcc-plugin/api/cmcc/2016/fsu/pending/list

// 根据ID获取FSU详情
GET /api/thing/south-cmcc-plugin/api/cmcc/2016/fsu/get/{fsuId}

// 批准FSU入网
POST /api/thing/south-cmcc-plugin/api/cmcc/2016/fsu/approved
```

#### 6.1.2 命令操作接口
```typescript
// 时间同步
POST /api/thing/south-cmcc-plugin/api/cmcc/2016/fsu/command/timecheck/{fsuId}

// 获取FSU信息
POST /api/thing/south-cmcc-plugin/api/cmcc/2016/fsu/command/getinfo/{fsuId}

// 重启FSU
POST /api/thing/south-cmcc-plugin/api/cmcc/2016/fsu/command/restart/{fsuId}

// 设置登录信息
POST /api/thing/south-cmcc-plugin/api/cmcc/2016/fsu/command/setlogininfo/{fsuId}

// 设置FTP信息
POST /api/thing/south-cmcc-plugin/api/cmcc/2016/fsu/command/setftp/{fsuId}
```

### 6.2 设备管理API

#### 6.2.1 设备信息接口
```typescript
// 获取设备列表
GET /api/thing/south-cmcc-plugin/cmcc-device/list?fsuId={fsuId}

// 获取设备详情
GET /api/thing/south-cmcc-plugin/cmcc-device/{deviceId}

// 获取设备信号
GET /api/thing/south-cmcc-plugin/cmcc-signal/list?fsuId={fsuId}&deviceId={deviceId}

// 获取设备告警
GET /api/thing/south-cmcc-plugin/cmcc-alarm/list?fsuId={fsuId}&deviceId={deviceId}
```

### 6.3 标准化管理API

#### 6.3.1 标准化配置接口
```typescript
// 获取标准化统计
GET /api/thing/south-cmcc-plugin/cmcc-device/standardization/statistics?fsuId={fsuId}

// 获取信号标准字典
GET /api/thing/south-cmcc-plugin/standard/signal-dic

// 获取告警标准字典
GET /api/thing/south-cmcc-plugin/standard/alarm-dic

// 导出标准字典
GET /api/thing/south-cmcc-plugin/standard/export
```

## 7. 协议规范

### 7.1 B接口协议消息

#### 7.1.1 登录消息 (LOGIN)
```xml
<Request>
    <Header>
        <PK_Type>1</PK_Type>
        <FSUID>FSU001</FSUID>
        <Timestamp>2025-01-15T10:30:00</Timestamp>
    </Header>
    <Info>
        <UserName>admin</UserName>
        <PassWord>password_md5_hash</PassWord>
        <FSUID>FSU001</FSUID>
        <FSUIP>*************</FSUIP>
        <FSUMAC>00:11:22:33:44:55</FSUMAC>
        <FSUVER>V1.0.0</FSUVER>
    </Info>
</Request>
```

#### 7.1.2 数据上报消息 (SEND_DATA)
```xml
<Request>
    <Header>
        <PK_Type>13</PK_Type>
        <FSUID>FSU001</FSUID>
        <Timestamp>2025-01-15T10:30:00</Timestamp>
    </Header>
    <Info>
        <FSUID>FSU001</FSUID>
        <Values>
            <DeviceList>
                <Device ID="DEV001">
                    <TSemaphore>
                        <ID>SIG001</ID>
                        <Type>1</Type>
                        <Value>25.5</Value>
                        <Time>2025-01-15T10:30:00</Time>
                    </TSemaphore>
                </Device>
            </DeviceList>
        </Values>
    </Info>
</Request>
```

#### 7.1.3 告警上报消息 (SEND_ALARM)
```xml
<Request>
    <Header>
        <PK_Type>15</PK_Type>
        <FSUID>FSU001</FSUID>
        <Timestamp>2025-01-15T10:30:00</Timestamp>
    </Header>
    <Info>
        <FSUID>FSU001</FSUID>
        <Values>
            <TAlarmList>
                <TAlarm>
                    <DeviceID>DEV001</DeviceID>
                    <AlarmID>ALM001</AlarmID>
                    <AlarmType>1</AlarmType>
                    <AlarmLevel>2</AlarmLevel>
                    <OccurTime>2025-01-15T10:30:00</OccurTime>
                    <AlarmDesc>温度超高</AlarmDesc>
                    <AlarmValue>45</AlarmValue>
                </TAlarm>
            </TAlarmList>
        </Values>
    </Info>
</Request>
```

#### 7.1.4 控制下发消息 (SET_POINT)
```xml
<Request>
    <Header>
        <PK_Type>19</PK_Type>
        <FSUID>FSU001</FSUID>
        <Timestamp>2025-01-15T10:30:00</Timestamp>
    </Header>
    <Info>
        <FSUID>FSU001</FSUID>
        <Value>
            <DeviceList>
                <Device ID="DEV001">
                    <TSemaphore>
                        <ID>CTL001</ID>
                        <Type>3</Type>
                        <SetupVal>1</SetupVal>
                        <SignalNumber>1</SignalNumber>
                        <Time>2025-01-15T10:30:00</Time>
                    </TSemaphore>
                </Device>
            </DeviceList>
        </Value>
    </Info>
</Request>
```

### 7.2 协议类型映射

#### 7.2.1 消息类型 (PK_TypeName)
| 类型码 | 消息名称 | 说明 | 方向 |
|-------|----------|------|------|
| 1 | LOGIN | 登录请求 | FSU→SC |
| 2 | LOGIN_ACK | 登录响应 | SC→FSU |
| 13 | SEND_DATA | 数据上报 | FSU→SC |
| 14 | SEND_DATA_ACK | 数据确认 | SC→FSU |
| 15 | SEND_ALARM | 告警上报 | FSU→SC |
| 16 | SEND_ALARM_ACK | 告警确认 | SC→FSU |
| 19 | SET_POINT | 控制下发 | SC→FSU |
| 20 | SET_POINT_ACK | 控制确认 | FSU→SC |
| 7 | TIME_CHECK | 时间同步 | SC→FSU |
| 8 | TIME_CHECK_ACK | 时间同步确认 | FSU→SC |

#### 7.2.2 信号类型 (TSemaphore.Type)
| 类型码 | 类型名称 | 说明 |
|-------|----------|------|
| 1 | 开关量 | 数字量信号 |
| 2 | 模拟量 | 模拟量信号 |
| 3 | 控制量 | 控制输出 |
| 4 | 累计量 | 累积计数 |

## 8. 测试指南

### 8.1 FSU注册测试

#### 8.1.1 测试场景
1. **自动入网测试**
   - 配置自动入网开启
   - 使用新FSU ID发送LOGIN消息
   - 验证FSU自动创建并上线

2. **手动入网测试**
   - 配置自动入网关闭
   - FSU发送LOGIN消息后检查待处理列表
   - 管理员手动批准入网
   - 验证FSU正常上线

3. **认证测试**
   - 测试用户名密码错误情况
   - 测试重复登录
   - 测试登录状态维护

#### 8.1.2 测试工具
可以使用以下工具进行测试：
```bash
# 使用curl模拟LOGIN请求
curl -X POST http://localhost:8550/services/LSCService \
  -H "Content-Type: text/xml" \
  -d '<?xml version="1.0" encoding="UTF-8"?>
      <Request>
        <Header><PK_Type>1</PK_Type><FSUID>TEST001</FSUID></Header>
        <Info>
          <UserName>admin</UserName>
          <PassWord>admin</PassWord>
          <FSUID>TEST001</FSUID>
          <FSUIP>*************</FSUIP>
        </Info>
      </Request>'
```

### 8.2 信号获取测试

#### 8.2.1 数据上报测试
1. **实时数据测试**
   - FSU定时上报SEND_DATA消息
   - 验证数据正确解析和存储
   - 检查流计算引擎处理

2. **主动查询测试**
   - 通过前端触发数据查询
   - 验证HTTP请求正确发送
   - 检查响应数据解析

#### 8.2.2 测试数据
```xml
<!-- 模拟SEND_DATA消息 -->
<Request>
  <Header><PK_Type>13</PK_Type><FSUID>TEST001</FSUID></Header>
  <Info>
    <FSUID>TEST001</FSUID>
    <Values>
      <DeviceList>
        <Device ID="UPS001">
          <TSemaphore>
            <ID>TEMP01</ID><Type>2</Type>
            <Value>25.5</Value><Unit>℃</Unit>
            <Time>2025-01-15T10:30:00</Time>
          </TSemaphore>
        </Device>
      </DeviceList>
    </Values>
  </Info>
</Request>
```

### 8.3 告警上报测试

#### 8.3.1 告警处理测试
1. **告警生成测试**
   - 模拟FSU发送SEND_ALARM消息
   - 验证告警正确解析
   - 检查告警规则处理

2. **告警联动测试**
   - 验证告警触发后续流程
   - 检查北向系统接收
   - 测试告警清除机制

### 8.4 控制下发测试

#### 8.4.1 控制命令测试
1. **控制点映射测试**
   - 验证标准控制点到FSU控制点的映射
   - 测试控制命令构建
   - 检查控制结果返回

2. **控制执行测试**
   - 从前端发起控制操作
   - 验证HTTP请求下发
   - 检查FSU执行结果

## 9. 运维监控

### 9.1 系统监控

#### 9.1.1 FSU状态监控
- FSU在线状态
- 最后心跳时间
- 数据上报频率
- 告警产生统计

#### 9.1.2 性能监控
- HTTP请求响应时间
- 消息处理速度
- Actor系统性能
- 数据库连接状态

### 9.2 日志管理

#### 9.2.1 日志分类
- **业务日志**: FSU登录、数据处理、告警处理
- **系统日志**: 插件启停、异常信息
- **性能日志**: 处理时间、资源使用
- **协议日志**: 报文收发记录

#### 9.2.2 日志配置
```yaml
logging:
  level:
    com.siteweb.tcs.south.cmcc: INFO
    com.siteweb.tcs.south.cmcc.connector: DEBUG
    com.siteweb.tcs.south.cmcc.process: DEBUG
```

### 9.3 故障处理

#### 9.3.1 常见问题
1. **FSU连接失败**
   - 检查网络连通性
   - 验证HTTP服务状态
   - 确认端口配置

2. **数据处理异常**
   - 检查流计算引擎状态
   - 验证数据格式
   - 查看错误日志

3. **控制下发失败**
   - 检查控制点映射
   - 验证FSU控制能力
   - 确认权限配置

## 10. 扩展开发

### 10.1 添加新协议消息

#### 10.1.1 定义协议报文
```java
@JacksonXmlRootElement(localName = "Request")
public class CustomMessage extends MobileBRequestMessage {
    public CustomMessage() {
        super(PK_TypeName.CUSTOM);
    }
    
    // 定义Info内部类
    @Getter @Setter
    public static class Info {
        private String customField;
    }
}
```

#### 10.1.2 添加消息处理
```java
// 在CmccFSUEntity中添加处理逻辑
if (PK_TypeName.CUSTOM.equals(pkType)) {
    handleCustomMessage(message);
    return true;
}
```

### 10.2 扩展前端功能

#### 10.2.1 添加新页面
```vue
<!-- 新建组件 -->
<template>
  <div class="custom-component">
    <!-- 页面内容 -->
  </div>
</template>

<script setup lang="ts">
// 组件逻辑
</script>
```

#### 10.2.2 添加API接口
```typescript
// 在api文件中添加新接口
export function customApi(params: any): Promise<ApiResponse<any>> {
  return http.post('/api/custom/endpoint', { data: params });
}
```

### 10.3 流计算扩展

#### 10.3.1 自定义Shape节点
```java
@Shape(
    name = "custom-shape",
    displayName = "自定义处理节点"
)
public class CustomShape extends AbstractShape {
    
    @ShapeInlet(name = "input")
    private Inlet<StreamMessage> input = Inlet.create("input");
    
    @ShapeOutlet(name = "output") 
    private Outlet<StreamMessage> output = Outlet.create("output");
    
    @Override
    public GraphStage<Shape> createGraphStage() {
        return new CustomGraphStage();
    }
}
```

## 11. 总结

CMCC南向接入插件是TCS系统中功能完善的南向协议适配器，完整实现了中国移动B接口技术规范的各项功能。该插件具有以下特点：

### 11.1 核心优势
1. **协议完整性**: 完整实现B接口规范的所有消息类型
2. **高可用性**: 基于Actor模型的分布式架构
3. **易扩展性**: 插件化设计，支持热插拔
4. **标准化**: 支持设备信号告警的标准化管理
5. **用户友好**: 提供完整的Web管理界面

### 11.2 适用场景
- 中国移动动力环境监控系统集成
- 大规模FSU设备接入和管理
- 实时数据采集和告警处理
- 设备远程控制和运维
- 多层级系统集成

### 11.3 技术价值
该插件的实现可为后续开发CTCC、CUCC等其他运营商插件提供重要参考，其架构设计和业务流程具有很强的复用价值。

---

*本文档基于CMCC插件源码分析生成，为CTCC插件开发提供参考依据。*