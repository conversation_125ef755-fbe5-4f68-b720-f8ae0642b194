-- CMCC 设备初始化表和FSU初始化表创建脚本 (MySQL)
-- 创建日期: 2024
-- 版本: V1.0.3

-- 创建设备信息表
CREATE TABLE `cmcc_device_init` (
  `SiteWebMuId` INT NOT NULL,
  `DeviceId` BIGINT NOT NULL COMMENT 'CMCC设备ID',
  `DeviceName` VARCHAR(128) NOT NULL COMMENT 'CMCC设备名称',
  `RoomId` BIGINT NOT NULL COMMENT 'CMCC机房ID',
  `RoomName` VARCHAR(128) NOT NULL COMMENT 'CMCC机房名称',
  `DeviceType` INT NOT NULL COMMENT 'CMCC设备类型',
  `DeviceSubType` INT NOT NULL COMMENT 'CMCC设备子类型',
  `Model` VARCHAR(128) DEFAULT NULL COMMENT '型号',
  `Brand` VARCHAR(128) DEFAULT NULL COMMENT '品牌',
  `RatedCapacity` DOUBLE DEFAULT 0 COMMENT '额定容量',
  `Version` VARCHAR(128) DEFAULT NULL COMMENT '版本',
  `BeginRunTime` DATETIME DEFAULT NULL COMMENT '投运时间',
  `DevDescribe` TEXT COMMENT '设备描述',
  `SiteWebEquipId` INT NOT NULL COMMENT 'SiteWeb设备ID',
  `NamePrefix` VARCHAR(128) DEFAULT NULL COMMENT '名称前缀',
  `ExtendField` JSON DEFAULT NULL COMMENT '拓展字段',
  PRIMARY KEY (`SiteWebEquipId`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CMCC设备信息表';

-- 创建FSU初始化表
CREATE TABLE `cmcc_fsu_init` (
    `FSUID` VARCHAR(32) NOT NULL COMMENT '移动FSU唯一标识',
    `SiteWebMuId` INT NOT NULL,
    `FSUPort` INT DEFAULT NULL COMMENT '移动FSU端口',
    `RoomId` VARCHAR(32) DEFAULT NULL COMMENT '房间ID',
    `Type76DeviceName` VARCHAR(255) DEFAULT NULL COMMENT '移动FSU自诊断设备名称',
    `RoomName` VARCHAR(255) DEFAULT NULL COMMENT '房间名称',
    `FSUTYPE` INT DEFAULT NULL COMMENT 'FSU类型',
    `SiteID` VARCHAR(32) DEFAULT NULL COMMENT '站点ID',
    `SiteName` VARCHAR(255) DEFAULT NULL COMMENT '站点名称',
    `FTPUser` VARCHAR(64) DEFAULT NULL COMMENT 'FTP用户名',
    `FTPPwd` VARCHAR(64) DEFAULT NULL COMMENT 'FTP密码',
    `LoginUser` VARCHAR(64) DEFAULT NULL COMMENT '登录用户名',
    `LoginPwd` VARCHAR(64) DEFAULT NULL COMMENT '登录密码',
    `EnableACL` BOOLEAN DEFAULT NULL COMMENT '是否启用ACL',
    `PlatFormNo` INT DEFAULT NULL COMMENT '平台编号',
    `SCIP` VARCHAR(64) DEFAULT NULL COMMENT '主平台IP',
    `SCPort` INT DEFAULT NULL COMMENT '主平台端口',
    `SCURLSuffix` VARCHAR(255) DEFAULT NULL COMMENT '主平台URL后缀',
    `SCIP_BAK` VARCHAR(64) DEFAULT NULL COMMENT '主平台备用IP',
    `SCSwitchMode` INT DEFAULT NULL COMMENT '主平台切换模式',
    `PlatFormName` VARCHAR(64) DEFAULT NULL COMMENT '平台名称',
    `LastPlatFormName` VARCHAR(64) DEFAULT NULL COMMENT '最后的平台名称',
    `SCIP1` VARCHAR(64) DEFAULT NULL COMMENT '备用平台IP',
    `SCPort1` INT DEFAULT NULL COMMENT '备用平台端口',
    `SCURLSuffix1` VARCHAR(255) DEFAULT NULL COMMENT '备用平台URL后缀',
    `SCIP_BAK1` VARCHAR(64) DEFAULT NULL COMMENT '备用平台备用IP',
    `SCSwitchMode1` INT DEFAULT NULL COMMENT '备用平台切换模式',
    `PlatFormName1` VARCHAR(64) DEFAULT NULL COMMENT '备用平台名称',
    `LastPlatFormName1` VARCHAR(64) DEFAULT NULL COMMENT '备用最后平台名称',
    `Type76DeviceID` VARCHAR(32) DEFAULT '760300000000001' COMMENT '移动自诊断设备id1',
    `ExtendField` JSON DEFAULT NULL COMMENT '拓展字段',
    PRIMARY KEY (`SiteWebMuId`),
    UNIQUE KEY `uk_cmcc_fsu_init_fsu_id` (`FSUID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CMCC Type76自诊断设备配置表';
