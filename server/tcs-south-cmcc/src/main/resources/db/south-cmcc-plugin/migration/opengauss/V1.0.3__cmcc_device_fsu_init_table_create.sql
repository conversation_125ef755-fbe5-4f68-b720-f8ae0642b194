-- CMCC 设备初始化表和FSU初始化表创建脚本 (OpenGauss)
-- 创建日期: 2024
-- 版本: V1.0.3

-- 创建设备信息表
CREATE TABLE cmcc_device_init (
    SiteWebMuId INT NOT NULL,
    DeviceId BIGINT NOT NULL,
    DeviceName VARCHAR(128) NOT NULL,
    RoomId BIGINT NOT NULL,
    RoomName VARCHAR(128) NOT NULL,
    DeviceType INTEGER NOT NULL,
    DeviceSubType INTEGER NOT NULL,
    Model VARCHAR(128) DEFAULT NULL,
    Brand VARCHAR(128) DEFAULT NULL,
    RatedCapacity DOUBLE PRECISION DEFAULT 0,
    Version VARCHAR(128) DEFAULT NULL,
    BeginRunTime TIMESTAMP DEFAULT NULL,
    DevDescribe TEXT,
    SiteWebEquipId INTEGER NOT NULL,
    NamePrefix VARCHAR(128) DEFAULT NULL,
    ExtendField TEXT DEFAULT NULL,
    PRIMARY KEY (SiteWebEquipId)
);

-- 添加表注释
COMMENT ON TABLE cmcc_device_init IS 'CMCC设备信息表';
COMMENT ON COLUMN cmcc_device_init.DeviceId IS 'CMCC设备ID';
COMMENT ON COLUMN cmcc_device_init.DeviceName IS 'CMCC设备名称';
COMMENT ON COLUMN cmcc_device_init.RoomId IS 'CMCC机房ID';
COMMENT ON COLUMN cmcc_device_init.RoomName IS 'CMCC机房名称';
COMMENT ON COLUMN cmcc_device_init.DeviceType IS 'CMCC设备类型';
COMMENT ON COLUMN cmcc_device_init.DeviceSubType IS 'CMCC设备子类型';
COMMENT ON COLUMN cmcc_device_init.Model IS '型号';
COMMENT ON COLUMN cmcc_device_init.Brand IS '品牌';
COMMENT ON COLUMN cmcc_device_init.RatedCapacity IS '额定容量';
COMMENT ON COLUMN cmcc_device_init.Version IS '版本';
COMMENT ON COLUMN cmcc_device_init.BeginRunTime IS '投运时间';
COMMENT ON COLUMN cmcc_device_init.DevDescribe IS '设备描述';
COMMENT ON COLUMN cmcc_device_init.SiteWebEquipId IS 'SiteWeb设备ID';
COMMENT ON COLUMN cmcc_device_init.NamePrefix IS '名称前缀';
COMMENT ON COLUMN cmcc_device_init.ExtendField IS '拓展字段';

-- 创建FSU初始化表
CREATE TABLE cmcc_fsu_init (
    FSUID VARCHAR(32) NOT NULL,
    SiteWebMuId INT NOT NULL,
    FSUPort INTEGER DEFAULT NULL,
    RoomId VARCHAR(32) DEFAULT NULL,
    Type76DeviceName VARCHAR(255) DEFAULT NULL,
    RoomName VARCHAR(255) DEFAULT NULL,
    FSUTYPE INTEGER DEFAULT NULL,
    SiteID VARCHAR(32) DEFAULT NULL,
    SiteName VARCHAR(255) DEFAULT NULL,
    FTPUser VARCHAR(64) DEFAULT NULL,
    FTPPwd VARCHAR(64) DEFAULT NULL,
    LoginUser VARCHAR(64) DEFAULT NULL,
    LoginPwd VARCHAR(64) DEFAULT NULL,
    EnableACL BOOLEAN DEFAULT NULL,
    PlatFormNo INTEGER DEFAULT NULL,
    SCIP VARCHAR(64) DEFAULT NULL,
    SCPort INTEGER DEFAULT NULL,
    SCURLSuffix VARCHAR(255) DEFAULT NULL,
    SCIP_BAK VARCHAR(64) DEFAULT NULL,
    SCSwitchMode INTEGER DEFAULT NULL,
    PlatFormName VARCHAR(64) DEFAULT NULL,
    LastPlatFormName VARCHAR(64) DEFAULT NULL,
    SCIP1 VARCHAR(64) DEFAULT NULL,
    SCPort1 INTEGER DEFAULT NULL,
    SCURLSuffix1 VARCHAR(255) DEFAULT NULL,
    SCIP_BAK1 VARCHAR(64) DEFAULT NULL,
    SCSwitchMode1 INTEGER DEFAULT NULL,
    PlatFormName1 VARCHAR(64) DEFAULT NULL,
    LastPlatFormName1 VARCHAR(64) DEFAULT NULL,
    Type76DeviceID VARCHAR(32) DEFAULT '760300000000001',
    ExtendField TEXT DEFAULT NULL,
    PRIMARY KEY (SiteWebMuId)
);
-- 唯一约束
ALTER TABLE cmcc_fsu_init ADD CONSTRAINT uk_cmcc_fsu_init_fsu_id UNIQUE (FSUID);

-- 添加表注释
COMMENT ON TABLE cmcc_fsu_init IS 'CMCC Type76自诊断设备配置表';
COMMENT ON COLUMN cmcc_fsu_init.FSUID IS '移动FSU唯一标识';
COMMENT ON COLUMN cmcc_fsu_init.FSUPort IS '移动FSU端口';
COMMENT ON COLUMN cmcc_fsu_init.RoomId IS '房间ID';
COMMENT ON COLUMN cmcc_fsu_init.Type76DeviceName IS '移动FSU自诊断设备名称';
COMMENT ON COLUMN cmcc_fsu_init.RoomName IS '房间名称';
COMMENT ON COLUMN cmcc_fsu_init.FSUTYPE IS 'FSU类型';
COMMENT ON COLUMN cmcc_fsu_init.SiteID IS '站点ID';
COMMENT ON COLUMN cmcc_fsu_init.SiteName IS '站点名称';
COMMENT ON COLUMN cmcc_fsu_init.FTPUser IS 'FTP用户名';
COMMENT ON COLUMN cmcc_fsu_init.FTPPwd IS 'FTP密码';
COMMENT ON COLUMN cmcc_fsu_init.LoginUser IS '登录用户名';
COMMENT ON COLUMN cmcc_fsu_init.LoginPwd IS '登录密码';
COMMENT ON COLUMN cmcc_fsu_init.EnableACL IS '是否启用ACL';
COMMENT ON COLUMN cmcc_fsu_init.PlatFormNo IS '平台编号';
COMMENT ON COLUMN cmcc_fsu_init.SCIP IS '主平台IP';
COMMENT ON COLUMN cmcc_fsu_init.SCPort IS '主平台端口';
COMMENT ON COLUMN cmcc_fsu_init.SCURLSuffix IS '主平台URL后缀';
COMMENT ON COLUMN cmcc_fsu_init.SCIP_BAK IS '主平台备用IP';
COMMENT ON COLUMN cmcc_fsu_init.SCSwitchMode IS '主平台切换模式';
COMMENT ON COLUMN cmcc_fsu_init.PlatFormName IS '平台名称';
COMMENT ON COLUMN cmcc_fsu_init.LastPlatFormName IS '最后的平台名称';
COMMENT ON COLUMN cmcc_fsu_init.SCIP1 IS '备用平台IP';
COMMENT ON COLUMN cmcc_fsu_init.SCPort1 IS '备用平台端口';
COMMENT ON COLUMN cmcc_fsu_init.SCURLSuffix1 IS '备用平台URL后缀';
COMMENT ON COLUMN cmcc_fsu_init.SCIP_BAK1 IS '备用平台备用IP';
COMMENT ON COLUMN cmcc_fsu_init.SCSwitchMode1 IS '备用平台切换模式';
COMMENT ON COLUMN cmcc_fsu_init.PlatFormName1 IS '备用平台名称';
COMMENT ON COLUMN cmcc_fsu_init.LastPlatFormName1 IS '备用最后平台名称';
COMMENT ON COLUMN cmcc_fsu_init.Type76DeviceID IS '移动自诊断设备id1';
COMMENT ON COLUMN cmcc_fsu_init.ExtendField IS '拓展字段';
