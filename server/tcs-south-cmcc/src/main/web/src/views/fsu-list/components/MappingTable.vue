<template>
  <div class="mapping-container">
    <!-- 未映射项目表格 -->
    <div class="table-section rounded-lg border border-gray-200 dark:border-gray-700">
      <div v-if="unimplementedItems.length === 0" class="text-center py-8 text-gray-500">
        <el-icon class="w-12 h-12 mx-auto mb-4 text-green-400"><CircleCheckFilled /></el-icon>
        <p class="text-green-600">太棒了！所有标准项目都已映射</p>
      </div>
      <div v-else class="table-container">
        <el-table-v2
          :columns="unimappedTableColumns"
          :data="unimplementedItems"
          :width="700"
          :height="300"
          :header-height="50"
          :row-height="50"
          fixed
        />
      </div>
    </div>

    <!-- 已映射项目 -->
    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
      <div class="p-4 border-b border-green-200 dark:border-green-800">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <el-icon class="mr-2 text-green-600"><CircleCheckFilled /></el-icon>
            <h3 class="font-semibold text-green-700 dark:text-green-400">
              已映射标准项目 ({{ implementedItems.length }})
            </h3>
          </div>
          <el-tag type="success" size="small">已完成</el-tag>
        </div>
        <p class="text-sm text-green-600 dark:text-green-400 mt-1">
          以下标准项目已成功建立映射关系
        </p>
      </div>
      <div class="p-4">
        <div v-if="implementedItems.length === 0" class="text-center py-8 text-gray-500">
          <el-icon class="w-12 h-12 mx-auto mb-4 text-gray-300"><DocumentCopy /></el-icon>
          <p>暂无已映射的标准项目</p>
        </div>
        <div v-else class="space-y-3">
          <div 
            v-for="(item, index) in implementedItems" 
            :key="index" 
            class="p-3 bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700"
          >
            <div class="flex items-center justify-between mb-2">
              <div class="flex-1">
                <div class="font-medium text-gray-900 dark:text-gray-100">{{ item.standardName }}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">{{ item.standardId }}</div>
              </div>
              <el-tag type="success" size="small">
                已映射 ({{ item.actualItemCount }})
              </el-tag>
            </div>
            <!-- 实际映射项目列表 -->
            <div v-if="item.actualItems && item.actualItems.length > 0" class="ml-4 space-y-1">
              <div 
                v-for="(actualItem, actualIndex) in item.actualItems" 
                :key="actualIndex" 
                class="flex items-center gap-2 text-sm"
              >
                <div class="w-2 h-2 bg-green-400 rounded-full flex-shrink-0"></div>
                <span class="text-gray-700 dark:text-gray-300">{{ actualItem.actualName }}</span>
                <span class="text-gray-500 dark:text-gray-400">({{ actualItem.actualId }})</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import { WarningFilled, CircleCheckFilled, DocumentCopy } from '@element-plus/icons-vue'
import { ElTag } from 'element-plus'
import type { StandardizedItem, UnimplementedStandardItem } from '@/api/cmcc-device'
import type { Column } from 'element-plus'

interface Props {
  implementedItems: StandardizedItem[]
  unimplementedItems: UnimplementedStandardItem[]
  type: string
}

defineProps<Props>()

// 未映射表格列配置
const unimappedTableColumns = computed((): Column<UnimplementedStandardItem>[] => [
  {
    key: 'index',
    title: '序号',
    dataKey: 'index',
    width: 80,
    align: 'center',
    cellRenderer: ({ rowIndex }) => rowIndex + 1,
  },
  {
    key: 'standardName',
    title: '标准项目名称',
    dataKey: 'standardName',
    width: 300,
    align: 'left',
    cellRenderer: ({ rowData }) => h('div', { 
      class: 'font-medium text-gray-900 dark:text-gray-100' 
    }, rowData.standardName),
  },
  {
    key: 'standardId',
    title: '标准ID',
    dataKey: 'standardId',
    width: 200,
    align: 'left',
    cellRenderer: ({ rowData }) => h('div', { 
      class: 'text-sm text-gray-500 dark:text-gray-400' 
    }, rowData.standardId),
  },
  {
    key: 'status',
    title: '状态',
    dataKey: 'status',
    width: 120,
    align: 'center',
    cellRenderer: () => h(ElTag, { 
      type: 'danger', 
      size: 'small' 
    }, () => '未映射'),
  },
])
</script>

<style scoped>
/* 组件特定样式 */
.mapping-container {
  height: 100%;
  max-height: calc(100vh - 400px);
  overflow: auto;
}

.table-section {
  flex-shrink: 0;
  margin-bottom: 1.5rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

/* 表格容器样式 */
.table-container {
  height: 300px;
  width: 100%;
  max-height: min(300px, 40vh); /* 限制最大高度为300px或视窗高度的40%，取较小值 */
  min-height: 200px; /* 确保最小高度 */
}

/* 虚拟表格样式优化 */
.table-container :deep(.el-table-v2__header) {
  background-color: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color-light);
}

.table-container :deep(.el-table-v2__header-cell) {
  background-color: var(--el-fill-color-lighter);
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.table-container :deep(.el-table-v2__row) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.table-container :deep(.el-table-v2__row:hover) {
  background-color: var(--el-fill-color-light);
}

.table-container :deep(.el-table-v2__cell) {
  border-right: 1px solid var(--el-border-color-lighter);
}

/* 深色主题适配 */
.dark .table-container :deep(.el-table-v2__header) {
  background-color: var(--el-fill-color-darker);
  border-bottom-color: var(--el-border-color-darker);
}

.dark .table-container :deep(.el-table-v2__header-cell) {
  background-color: var(--el-fill-color-darker);
  color: var(--el-text-color-primary);
}

.dark .table-container :deep(.el-table-v2__row) {
  border-bottom-color: var(--el-border-color-darker);
}

.dark .table-container :deep(.el-table-v2__row:hover) {
  background-color: var(--el-fill-color-dark);
}

.dark .table-container :deep(.el-table-v2__cell) {
  border-right-color: var(--el-border-color-darker);
}
</style>