<template>
  <div class="h-full flex flex-col">
    <div v-if="selectedDevice && standardizationData" class="flex-1 flex flex-col">

          <!-- 统计概览卡片 -->
          <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <!-- 告警映射统计 -->
              <div class="stats-card rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <el-icon class="mr-2" style="color: var(--el-color-danger);"><Warning /></el-icon>
                    <span class="font-medium" style="color: var(--el-text-color-primary);">告警映射</span>
                  </div>
                  <span :class="getStatusColor(standardizationData.alarmUsage.standardImplementationRate)" class="font-bold">
                    {{ standardizationData.alarmUsage.standardImplementationRate.toFixed(1) }}%
                  </span>
                </div>
                <el-progress 
                  :percentage="standardizationData.alarmUsage.standardImplementationRate" 
                  :color="getProgressColor(standardizationData.alarmUsage.standardImplementationRate)"
                  :stroke-width="8"
                />
                <div class="flex justify-between text-xs mt-2" style="color: var(--el-text-color-secondary);">
                  <span>已实现: {{ standardizationData.alarmUsage.implementedStandardCount }}</span>
                  <span>总计: {{ standardizationData.alarmUsage.totalStandardCount }}</span>
                </div>
              </div>

              <!-- 信号映射统计 -->
              <div class="stats-card rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <el-icon class="mr-2" style="color: var(--el-color-success);"><Connection /></el-icon>
                    <span class="font-medium" style="color: var(--el-text-color-primary);">信号映射</span>
                  </div>
                  <span :class="getStatusColor(standardizationData.signalUsage.standardImplementationRate)" class="font-bold">
                    {{ standardizationData.signalUsage.standardImplementationRate.toFixed(1) }}%
                  </span>
                </div>
                <el-progress 
                  :percentage="standardizationData.signalUsage.standardImplementationRate" 
                  :color="getProgressColor(standardizationData.signalUsage.standardImplementationRate)"
                  :stroke-width="8"
                />
                <div class="flex justify-between text-xs mt-2" style="color: var(--el-text-color-secondary);">
                  <span>已实现: {{ standardizationData.signalUsage.implementedStandardCount }}</span>
                  <span>总计: {{ standardizationData.signalUsage.totalStandardCount }}</span>
                </div>
              </div>

              <!-- 控制映射统计 -->
              <div class="stats-card rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <el-icon class="mr-2" style="color: var(--el-color-warning);"><Setting /></el-icon>
                    <span class="font-medium" style="color: var(--el-text-color-primary);">控制映射</span>
                  </div>
                  <span :class="getStatusColor(standardizationData.controlUsage.standardImplementationRate)" class="font-bold">
                    {{ standardizationData.controlUsage.standardImplementationRate.toFixed(1) }}%
                  </span>
                </div>
                <el-progress 
                  :percentage="standardizationData.controlUsage.standardImplementationRate" 
                  :color="getProgressColor(standardizationData.controlUsage.standardImplementationRate)"
                  :stroke-width="8"
                />
                <div class="flex justify-between text-xs mt-2" style="color: var(--el-text-color-secondary);">
                  <span>已实现: {{ standardizationData.controlUsage.implementedStandardCount }}</span>
                  <span>总计: {{ standardizationData.controlUsage.totalStandardCount }}</span>
                </div>
              </div>
            </div>
          </div>


          <!-- 详细映射关系Tab页 -->
          <div class="flex-1 overflow-hidden">
            <el-tabs v-model="activeTab" class="h-full tabs-container">
              <el-tab-pane name="overview" label="概览">
                <div class="p-4 h-full overflow-auto">
                  <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                      <h3 class="font-semibold text-red-700 mb-2">告警未映射项目</h3>
                      <div class="text-2xl font-bold text-red-600">
                        {{ standardizationData.alarmUsage.unimplementedStandardItems.length }}
                      </div>
                      <div class="text-sm text-gray-500">
                        共 {{ standardizationData.alarmUsage.totalStandardCount }} 个标准项目
                      </div>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h3 class="font-semibold text-yellow-700 mb-2">信号未映射项目</h3>
                      <div class="text-2xl font-bold text-yellow-600">
                        {{ standardizationData.signalUsage.unimplementedStandardItems.length }}
                      </div>
                      <div class="text-sm text-gray-500">
                        共 {{ standardizationData.signalUsage.totalStandardCount }} 个标准项目
                      </div>
                    </div>
                    
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                      <h3 class="font-semibold text-red-700 mb-2">控制未映射项目</h3>
                      <div class="text-2xl font-bold text-red-600">
                        {{ standardizationData.controlUsage.unimplementedStandardItems.length }}
                      </div>
                      <div class="text-sm text-gray-500">
                        共 {{ standardizationData.controlUsage.totalStandardCount }} 个标准项目
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="alarm">
                <template #label>
                  <div class="flex items-center">
                    告警映射
                    <el-badge 
                      :value="standardizationData.alarmUsage.unimplementedStandardItems.length" 
                      :max="99"
                      class="ml-2"
                    />
                  </div>
                </template>
                <div class="tab-content">
                  <div v-if="loading" class="text-center py-8">
                    <el-icon class="animate-spin mr-2"><Loading /></el-icon>
                    加载中...
                  </div>
                  <div v-else>
                    <!-- 映射关系内容将在下一步实现 -->
                    <MappingTable 
                      :implemented-items="standardizationData.alarmUsage.implementedStandardItems"
                      :unimplemented-items="standardizationData.alarmUsage.unimplementedStandardItems"
                      type="告警"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="signal">
                <template #label>
                  <div class="flex items-center">
                    信号映射
                    <el-badge 
                      :value="standardizationData.signalUsage.unimplementedStandardItems.length" 
                      :max="99"
                      class="ml-2"
                    />
                  </div>
                </template>
                <div class="tab-content">
                  <div v-if="loading" class="text-center py-8">
                    <el-icon class="animate-spin mr-2"><Loading /></el-icon>
                    加载中...
                  </div>
                  <div v-else>
                    <MappingTable 
                      :implemented-items="standardizationData.signalUsage.implementedStandardItems"
                      :unimplemented-items="standardizationData.signalUsage.unimplementedStandardItems"
                      type="信号"
                    />
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane name="control">
                <template #label>
                  <div class="flex items-center">
                    控制映射
                    <el-badge 
                      :value="standardizationData.controlUsage.unimplementedStandardItems.length" 
                      :max="99"
                      type="danger"
                      class="ml-2"
                    />
                  </div>
                </template>
                <div class="tab-content">
                  <div v-if="loading" class="text-center py-8">
                    <el-icon class="animate-spin mr-2"><Loading /></el-icon>
                    加载中...
                  </div>
                  <div v-else>
                    <MappingTable 
                      :implemented-items="standardizationData.controlUsage.implementedStandardItems"
                      :unimplemented-items="standardizationData.controlUsage.unimplementedStandardItems"
                      type="控制"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
    
    <!-- 未选择设备时的提示 -->
    <div v-else class="flex-1 flex items-center justify-center">
      <div class="text-center text-gray-500">
        <el-icon class="w-20 h-20 mx-auto mb-6 text-gray-300"><Monitor /></el-icon>
        <p class="text-xl font-medium mb-2">请选择设备</p>
        <p class="text-gray-400">请选择一个设备来查看标准化详情</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Monitor,
  Connection,
  Warning,
  Setting,
  Loading
} from '@element-plus/icons-vue'
import MappingTable from './MappingTable.vue'
import {
  getDeviceStandardizationUsage,
  type CMCCDevice,
  type DeviceStandardizationUsage
} from '@/api/cmcc-device'

// Props
interface Props {
  fsuData?: any
  selectedDevice?: CMCCDevice | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'device-select': [device: CMCCDevice]
}>()

// 响应式数据
const loading = ref(false)
const detailLoading = ref(false)
const selectedDevice = ref<CMCCDevice | null>(props.selectedDevice || null)
const standardizationData = ref<DeviceStandardizationUsage | null>(null)
const activeTab = ref('overview')

// 计算属性 - 获取状态颜色
const getStatusColor = (rate: number) => {
  if (rate >= 80) return 'text-green-600'
  if (rate >= 60) return 'text-yellow-600'
  return 'text-red-600'
}

// 计算属性 - 获取进度条颜色
const getProgressColor = (rate: number) => {
  if (rate >= 80) return 'var(--el-color-success)'
  if (rate >= 60) return 'var(--el-color-warning)'
  return 'var(--el-color-danger)'
}


// 方法
const selectDevice = (device: CMCCDevice) => {
  selectedDevice.value = device
  activeTab.value = 'overview'
  // 发射事件给父组件
  emit('device-select', device)
  // 加载标准化详情
  loadStandardizationData(device.deviceId)
}

const loadStandardizationData = async (deviceId: string) => {
  detailLoading.value = true
  try {
    const response = await getDeviceStandardizationUsage(deviceId)
    if (response.state) {
      standardizationData.value = response.data
      console.log('加载标准化数据成功:', deviceId)
    } else {
      console.error('获取标准化数据失败:', response.err_msg)
      ElMessage.error(response.err_msg || '获取标准化数据失败')
      standardizationData.value = null
    }
  } catch (error) {
    console.error('获取标准化数据异常:', error)
    ElMessage.error('网络异常，获取标准化数据失败')
    standardizationData.value = null
  } finally {
    detailLoading.value = false
  }
}

// 监听props变化
watch(() => props.selectedDevice, (newDevice) => {
  if (newDevice && newDevice !== selectedDevice.value) {
    selectDevice(newDevice)
  }
}, { immediate: true })

// 监听activeTab变化，确保数据已加载
watch(activeTab, (newTab) => {
  // 如果切换到映射相关的tab且数据未加载，重新加载数据
  if ((newTab === 'alarm' || newTab === 'signal' || newTab === 'control') && 
      selectedDevice.value && !standardizationData.value) {
    loadStandardizationData(selectedDevice.value.deviceId)
  }
})

// 组件挂载
onMounted(() => {
  if (props.selectedDevice) {
    selectDevice(props.selectedDevice)
  }
})
</script>

<style scoped>
/* 统计卡片样式 */
.stats-card {
  background-color: var(--el-fill-color-lighter);
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
}

.stats-card:hover {
  background-color: var(--el-fill-color);
  border-color: var(--el-border-color);
}

/* 状态颜色类 */
.text-green-600 {
  color: var(--el-color-success) !important;
}

.text-yellow-600 {
  color: var(--el-color-warning) !important;
}

.text-red-600 {
  color: var(--el-color-danger) !important;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-text-color-placeholder);
}

/* 响应式布局优化 */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
}

/* 深色主题适配 */
.dark .stats-card {
  background-color: var(--el-fill-color-darker);
  border-color: var(--el-border-color-darker);
}

.dark .stats-card:hover {
  background-color: var(--el-fill-color-dark);
  border-color: var(--el-border-color);
}

/* Tab容器样式 */
.tabs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tabs-container :deep(.el-tabs__header) {
  margin-bottom: 0;
  flex-shrink: 0;
  order: -1; /* 确保header在最上面 */
}

.tabs-container :deep(.el-tabs__nav-wrap) {
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0 16px;
}

.tabs-container :deep(.el-tabs__nav-scroll) {
  overflow: visible;
}

.tabs-container :deep(.el-tabs__nav) {
  white-space: nowrap;
  min-width: max-content;
}

.tabs-container :deep(.el-tabs__item) {
  flex-shrink: 0;
  white-space: nowrap;
}

.tabs-container :deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
  padding: 16px 0 0 0;
}

.tabs-container :deep(.el-tab-pane) {
  height: 100%;
  overflow: hidden;
}

/* Tab内容区域样式 */
.tab-content {
  height: 100%;
  max-height: calc(100vh - 300px); /* 减去页面头部等固定高度 */
  padding: 16px;
  overflow: auto;
}

/* Tab导航滚动条样式 */
.tabs-container :deep(.el-tabs__nav-wrap)::-webkit-scrollbar {
  height: 6px;
}

.tabs-container :deep(.el-tabs__nav-wrap)::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.tabs-container :deep(.el-tabs__nav-wrap)::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 3px;
}

.tabs-container :deep(.el-tabs__nav-wrap)::-webkit-scrollbar-thumb:hover {
  background: var(--el-text-color-placeholder);
}
</style>