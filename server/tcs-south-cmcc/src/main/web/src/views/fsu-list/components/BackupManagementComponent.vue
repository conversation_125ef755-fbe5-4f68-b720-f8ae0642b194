<template>
  <div class="p-4">
    <!-- 工具栏 -->
    <div class="flex items-center mb-4">
      <el-button @click="refreshBackupList" :loading="loading" size="small" plain>
        <el-icon class="mr-1"><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 备份文件列表 -->
    <el-table
      :data="backupList"
      :loading="loading"
      class="compact-table"
      empty-text="暂无备份文件"
      style="width: 100%"
    >
      <el-table-column prop="backupTime" label="备份时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.backupTime) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="reason" label="备份原因" min-width="120">
        <template #default="{ row }">
          <el-tag size="small" type="info">{{ row.reason }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="fileName" label="文件名" min-width="200">
        <template #default="{ row }">
          <span class="font-mono text-sm">{{ row.fileName }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="fileSize" label="文件大小" width="120">
        <template #default="{ row }">
          {{ formatFileSize(row.fileSize) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button
            @click="handleDownload(row)"
            type="primary"
            size="small"
            plain
          >
            <el-icon class="mr-1"><Download /></el-icon>
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 简化的空状态 -->
    <div v-if="!loading && backupList.length === 0" class="text-center py-8">
      <p class="text-gray-500 mb-2">暂无备份文件</p>
      <p class="text-sm text-gray-400">点击"备份FSU"按钮创建备份</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import {
  Refresh,
  Download
} from '@element-plus/icons-vue'
import {
  getBackupList,
  downloadBackup,
  type BackupRecord
} from '@/api/fsu'

// Props
const props = defineProps<{
  fsuData: {
    gatewayId: string;
    gatewayName: string;
  }
}>()

// 响应式数据
const loading = ref(false)
const backupList = ref<BackupRecord[]>([])

// 计算属性
const fsuId = computed(() => props.fsuData?.gatewayId)

// 统一错误处理函数
const handleApiError = (error: any, operation: string) => {
  console.error(`${operation}失败:`, error)
  if (error?.response?.data?.err_msg) {
    ElMessage.error(error.response.data.err_msg)
  } else if (error?.message) {
    ElMessage.error(`${operation}失败: ${error.message}`)
  } else {
    ElMessage.error(`${operation}失败，请稍后重试`)
  }
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return '--'
  return moment(dateTimeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (!bytes || bytes === 0) return '0 B'
  
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  const size = (bytes / Math.pow(1024, i)).toFixed(1)
  
  return `${size} ${sizes[i]}`
}

// 加载备份列表
const loadBackupList = async (silent = false) => {
  if (!fsuId.value) {
    if (!silent) ElMessage.error('FSU ID参数缺失')
    return
  }

  if (!silent) loading.value = true
  
  try {
    const response = await getBackupList(fsuId.value)
    if (response.state && response.data) {
      backupList.value = response.data.sort((a, b) => 
        new Date(b.backupTime).getTime() - new Date(a.backupTime).getTime()
      )
      if (!silent) {
        console.log('备份列表加载成功:', backupList.value.length)
      }
    } else {
      backupList.value = []
      if (!silent) {
        ElMessage.error(response.err_msg || '获取备份列表失败')
      }
    }
  } catch (error) {
    backupList.value = []
    if (!silent) {
      handleApiError(error, '获取备份列表')
    }
  } finally {
    if (!silent) loading.value = false
  }
}

// 刷新备份列表
const refreshBackupList = async () => {
  await loadBackupList(false)
  ElMessage.success('备份列表已刷新')
}

// 下载备份文件
const handleDownload = (record: BackupRecord) => {
  if (!fsuId.value) {
    ElMessage.error('FSU ID参数缺失')
    return
  }

  try {
    downloadBackup(fsuId.value, record.fileName)
    ElMessage.success(`正在下载 ${record.fileName}`)
  } catch (error) {
    console.error('下载备份文件失败:', error)
    ElMessage.error('下载失败，请稍后重试')
  }
}

// 暴露方法给父组件
defineExpose({
  refreshBackupList: loadBackupList
})

// 生命周期
onMounted(async () => {
  console.log('备份管理组件已加载，FSU ID:', fsuId.value)
  if (fsuId.value) {
    await loadBackupList(true)
  }
})
</script>

<style scoped>
/* 紧凑表格样式 */
.compact-table :deep(.el-table th),
.compact-table :deep(.el-table td) {
  padding: 8px 12px;
  font-size: 12px;
}

/* 按钮样式 */
:deep(.el-button--small) {
  padding: 4px 8px;
  font-size: 12px;
}

/* 标签样式 */
:deep(.el-tag) {
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .compact-table :deep(.el-table th),
  .compact-table :deep(.el-table td) {
    padding: 6px 8px;
  }
}
</style>