<template>
  <div class="virtual-device-list h-[800px] flex flex-col">
    <!-- 头部搜索区域 -->
    <div class="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <el-icon class="w-5 h-5 mr-2 text-blue-600"><Monitor /></el-icon>
          设备列表
        </h3>
        <el-tag type="info" size="small">
          共 {{ devices.length }} 台设备
        </el-tag>
      </div>
      
      <!-- 搜索框 -->
      <el-input
        v-model="searchText"
        placeholder="搜索设备名称或ID..."
        size="small"
        clearable
        class="w-full"
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 虚拟滚动列表区域 -->
    <div 
      ref="parentRef" 
      class="flex-1 overflow-auto h-[800px]"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="p-4 text-center text-gray-500">
        <el-icon class="animate-spin mr-2"><Loading /></el-icon>
        加载中...
      </div>
      
      <!-- 空数据状态 -->
      <div v-else-if="filteredDevices.length === 0" class="p-4 text-center text-gray-500">
        <el-icon class="w-12 h-12 mx-auto mb-4 text-gray-300"><Monitor /></el-icon>
        <p v-if="devices.length === 0">暂无设备数据</p>
        <p v-else>未找到匹配的设备</p>
      </div>

      <!-- 虚拟滚动容器 -->
      <div
        v-else
        :style="{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }"
      >
        <!-- 虚拟项 -->
        <div
          v-for="virtualRow in virtualizer.getVirtualItems()"
          :key="virtualRow.index"
          :style="{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: `${virtualRow.size}px`,
            transform: `translateY(${virtualRow.start}px)`
          }"
        >
          <DeviceListItem
            :device="filteredDevices[virtualRow.index]"
            :is-selected="selectedDevice?.id === filteredDevices[virtualRow.index]?.id"
            @click="onDeviceClick"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useVirtualizer } from '@tanstack/vue-virtual'
import { ElIcon, ElTag, ElInput } from 'element-plus'
import { Monitor, Search, Loading } from '@element-plus/icons-vue'
import DeviceListItem from './DeviceListItem.vue'
import type { CMCCDevice } from '@/api/cmcc-device'

interface Props {
  devices: CMCCDevice[]
  selectedDevice: CMCCDevice | null
  loading?: boolean
}

interface Emits {
  (e: 'device-select', device: CMCCDevice): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 搜索相关
const searchText = ref('')
const searchDebounceTimer = ref<NodeJS.Timeout>()

// 容器引用和高度
const parentRef = ref<HTMLElement>()
const containerHeight = ref(600)

// 过滤后的设备列表
const filteredDevices = ref<CMCCDevice[]>([])

// 虚拟滚动配置
const virtualizer = useVirtualizer(
  computed(() => ({
    count: filteredDevices.value.length,
    getScrollElement: () => parentRef.value,
    estimateSize: () => 100, // 调整为新的设备项高度
    overscan: 5, // 预渲染额外的项目数量
    measureElement: (element) => {
      // 动态测量实际高度以提高精确性
      return element.getBoundingClientRect().height
    },
  }))
)

// 初始化过滤列表
const initFilteredDevices = () => {
  filteredDevices.value = [...props.devices]
}

// 防抖搜索处理
const handleSearch = () => {
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }
  
  searchDebounceTimer.value = setTimeout(() => {
    filterDevices()
  }, 300)
}

// 过滤设备
const filterDevices = () => {
  if (!searchText.value.trim()) {
    filteredDevices.value = [...props.devices]
    return
  }
  
  const searchLower = searchText.value.toLowerCase()
  filteredDevices.value = props.devices.filter(device => 
    device.deviceName.toLowerCase().includes(searchLower) ||
    device.deviceId.toLowerCase().includes(searchLower) ||
    device.brand.toLowerCase().includes(searchLower) ||
    device.model.toLowerCase().includes(searchLower)
  )
}

// 设备点击处理
const onDeviceClick = (device: CMCCDevice) => {
  emit('device-select', device)
}

// 容器高度自适应
const updateContainerHeight = () => {
  if (parentRef.value) {
    const rect = parentRef.value.getBoundingClientRect()
    containerHeight.value = rect.height || 600
  }
}

// 监听设备列表变化
watch(() => props.devices, () => {
  initFilteredDevices()
  // 重置搜索
  if (searchText.value) {
    nextTick(() => {
      filterDevices()
    })
  }
}, { immediate: true })

// 监听搜索文本变化
watch(() => props.loading, (isLoading) => {
  if (!isLoading && props.devices.length > 0) {
    nextTick(() => {
      initFilteredDevices()
    })
  }
})

// 组件挂载时初始化
onMounted(() => {
  initFilteredDevices()
  updateContainerHeight()
  
  // 监听窗口大小变化
  window.addEventListener('resize', updateContainerHeight)
})

// 组件卸载时清理
onUnmounted(() => {
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }
  window.removeEventListener('resize', updateContainerHeight)
})

// 暴露方法
defineExpose({
  scrollToDevice: (deviceId: string) => {
    const index = filteredDevices.value.findIndex(device => device.id === deviceId)
    if (index !== -1) {
      virtualizer.scrollToIndex(index, { align: 'center' })
    }
  },
  clearSearch: () => {
    searchText.value = ''
    filterDevices()
  }
})
</script>

<style scoped>
.virtual-device-list {
  background-color: var(--el-bg-color);
  border-radius: 8px;
}

/* 自定义滚动条样式 */
:deep(.el-scrollbar__wrap) {
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-darker) var(--el-fill-color-lighter);
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-text-color-placeholder);
}
</style>