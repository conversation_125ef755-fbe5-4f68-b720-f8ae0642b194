<template>
  <div
    class="device-item p-4 border-b border-gray-100 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
    :class="{
      'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700': isSelected
    }"
    @click="handleClick"
  >
    <!-- 设备名称和选中状态 -->
    <div class="flex items-center justify-between mb-3">
      <div class="font-bold text-gray-900 dark:text-white text-sm leading-tight">
        {{ device.deviceName }}
      </div>
      <!-- 选中状态指示器 -->
      <div v-if="isSelected" class="flex items-center text-blue-600">
        <el-icon class="w-4 h-4">
          <Check />
        </el-icon>
      </div>
    </div>
    
    <!-- 设备ID -->
    <div class="text-xs text-gray-500 mb-3 font-mono">
      ID: {{ device.deviceId }}
    </div>
    
    <!-- 房间信息 -->
    <div class="flex items-center text-xs text-gray-600 dark:text-gray-400">
      <el-icon class="w-3 h-3 mr-1 flex-shrink-0 text-orange-500">
        <Location />
      </el-icon>
      <span class="truncate">{{ device.roomName }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElIcon } from 'element-plus'
import { Location, Check } from '@element-plus/icons-vue'
import type { CMCCDevice } from '@/api/cmcc-device'

interface Props {
  device: CMCCDevice
  isSelected?: boolean
}

interface Emits {
  (e: 'click', device: CMCCDevice): void
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false
})

const emit = defineEmits<Emits>()

// 点击处理
const handleClick = () => {
  emit('click', props.device)
}
</script>

<style scoped>
.device-item {
  min-height: 100px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.device-item.selected {
  border-left: 3px solid var(--el-color-primary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .device-item {
    padding: 12px;
    min-height: 90px;
  }
}
</style>