<template>
  <div class="p-4 bg-gray-50 dark:bg-gray-900" style="height: calc(100vh - 48px)">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 h-full p-4 flex flex-col min-h-0">
      <!-- 标题栏和返回按钮 -->
      <div class="flex items-center justify-between mb-4 flex-shrink-0">
        <div class="flex items-center">
          <div class="w-1 h-6 bg-primary rounded-full mr-3" />
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">站点设置详情</h2>
        </div>
        <el-button 
          type="primary" 
          :icon="ArrowLeft" 
          @click="goBackToFsuSetting"
          size="small"
        >
          返回监控单元列表
        </el-button>
      </div>

      <div class="flex-1 min-h-0 flex flex-col gap-4" v-loading="loading">
        <!-- FSU配置信息区域 (严格占1/3) -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col" style="flex: 1; min-height: 0;">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-md font-semibold text-gray-900 dark:text-white">FSU配置信息</h3>
            <div class="flex items-center space-x-3">
              <el-button 
                type="success" 
                size="small" 
                @click="openPullConfigDialog"
                :icon="Download"
              >
                拉取底端配置
              </el-button>
              <div class="space-x-2">
                <el-button 
                  size="small" 
                  @click="cancelFsuEdit"
                  v-if="isEditingFsu"
                >
                  取消
                </el-button>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="saveFsuConfig"
                  :loading="savingFsu"
                  v-if="isEditingFsu"
                >
                  保存
                </el-button>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="startEditFsu"
                  v-else
                >
                  编辑
                </el-button>
              </div>
            </div>
          </div>
          
          <div class="flex-1 overflow-auto min-h-0">
            <!-- 调试信息 -->
            
            <div v-if="fsuData || isEditingFsu" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <!-- Siteweb监控单元ID -->
              <div class="flex flex-col">
                <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">Siteweb监控单元ID</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ monitorUnitInfo?.monitorUnitId || monitorUnitId || '-' }}</span>
              </div>
              
              <!-- Siteweb监控单元名称 -->
              <div class="flex flex-col">
                <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">Siteweb监控单元名称</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ monitorUnitInfo?.monitorUnitName || '-' }}</span>
              </div>
              
              <!-- FSU ID -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">FSU ID</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.fsuId"
                  size="small"
                  placeholder="请输入FSU ID"
                  :class="{ 'border-red-300': !editFsuForm.fsuId && hasValidationError }"
                  @blur="clearFsuValidationError"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.fsuId || '-' }}</span>
              </div>
              
              <!-- FSU端口 -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">FSU端口</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-input-number 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.fsuPort"
                  size="small"
                  placeholder="请输入FSU端口 (1-65534)"
                  :min="1"
                  :max="65534"
                  class="w-full"
                  :class="{ 'border-red-300': (!editFsuForm.fsuPort || editFsuForm.fsuPort <= 0) && hasValidationError }"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.fsuPort || '-' }}</span>
              </div>
              
              <!-- 机房ID -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">机房ID</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.roomId"
                  size="small"
                  placeholder="请输入机房ID"
                  :class="{ 'border-red-300': (!editFsuForm.roomId || editFsuForm.roomId.toString().trim() === '') && hasValidationError }"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.roomId || '-' }}</span>
              </div>
              
              <!-- 机房名称 -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">机房名称</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.roomName"
                  size="small"
                  placeholder="请输入机房名称"
                  :class="{ 'border-red-300': (!editFsuForm.roomName || editFsuForm.roomName.trim() === '') && hasValidationError }"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.roomName || '-' }}</span>
              </div>
              
              <!-- 机房类型 -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">机房类型</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-select 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.fsuType"
                  size="small"
                  placeholder="请选择机房类型"
                  class="w-full"
                  :class="{ 'border-red-300': (!editFsuForm.fsuType || editFsuForm.fsuType <= 0) && hasValidationError }"
                >
                  <el-option
                    v-for="item in stationTypes"
                    :key="item.itemId"
                    :label="item.itemValue"
                    :value="item.itemId"
                  />
                </el-select>
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ getStationTypeLabel(fsuData?.fsuType) || '-' }}
                </span>
              </div>
              
              <!-- 站点ID -->
              <div class="flex flex-col">
                <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">站点ID</span>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.siteId"
                  size="small"
                  placeholder="请输入站点ID（不允许中文）"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.siteId || '-' }}</span>
              </div>
              
              <!-- 站点名称 -->
              <div class="flex flex-col">
                <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">站点名称</span>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.siteName"
                  size="small"
                  placeholder="请输入站点名称"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.siteName || '-' }}</span>
              </div>
              
              <!-- FTP用户 -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">FTP用户</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.ftpUser"
                  size="small"
                  placeholder="请输入FTP用户名"
                  :class="{ 'border-red-300': (!editFsuForm.ftpUser || editFsuForm.ftpUser.trim() === '') && hasValidationError }"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.ftpUser || '-' }}</span>
              </div>
              
              <!-- FTP密码 -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">FTP密码</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.ftpPwd"
                  type="password"
                  size="small"
                  placeholder="请输入FTP密码"
                  show-password
                  :class="{ 'border-red-300': (!editFsuForm.ftpPwd || editFsuForm.ftpPwd.trim() === '') && hasValidationError }"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.ftpPwd ? '******' : '-' }}</span>
              </div>
              
              <!-- 登录用户 -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">登录用户</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.loginUser"
                  size="small"
                  placeholder="请输入登录用户名"
                  :class="{ 'border-red-300': (!editFsuForm.loginUser || editFsuForm.loginUser.trim() === '') && hasValidationError }"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.loginUser || '-' }}</span>
              </div>
              
              <!-- 登录密码 -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">登录密码</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.loginPwd"
                  type="password"
                  size="small"
                  placeholder="请输入登录密码"
                  show-password
                  :class="{ 'border-red-300': (!editFsuForm.loginPwd || editFsuForm.loginPwd.trim() === '') && hasValidationError }"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.loginPwd ? '******' : '—' }}</span>
              </div>
              
              <!-- 只响应SC请求 -->
              <div class="flex flex-col">
                <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">只响应SC请求</span>
                <el-switch 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.enableACL"
                  size="small"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.enableACL ? '是' : '否' }}</span>
              </div>
              
              <!-- SC地址 -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">SC地址</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.scAddress"
                  size="small"
                  placeholder="请输入SC地址（格式：ip:port/）"
                  :class="{ 'border-red-300': (!editFsuForm.scAddress || editFsuForm.scAddress.trim() === '') && hasValidationError }"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ formatScAddress(fsuData?.scip, fsuData?.scPort) || '-' }}</span>
              </div>
              
              <!-- 备份SC地址 -->
              <div class="flex flex-col">
                <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">备份SC地址</span>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.scipBak"
                  size="small"
                  placeholder="请输入备份SC地址（多个IP用逗号分隔）"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.scipBak || '-' }}</span>
              </div>
              
              <!-- SC切换方式 -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">SC切换方式</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-select 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.scSwitchMode"
                  size="small"
                  placeholder="请选择SC切换方式"
                  class="w-full"
                  :class="{ 'border-red-300': (!editFsuForm.scSwitchMode || editFsuForm.scSwitchMode < 0) && hasValidationError }"
                >
                  <el-option label="不主动切换回主SC" :value="1" />
                  <el-option label="自动切换回主SC" :value="2" />
                </el-select>
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ fsuData?.scSwitchMode === 1 ? '不主动切换回主SC' : '自动切换回主SC' }}
                </span>
              </div>
              
              <!-- 平台数量 -->
              <div class="flex flex-col">
                <div class="flex items-center mb-1">
                  <span class="text-sm text-gray-500 dark:text-gray-400">平台数量</span>
                  <span class="text-red-500 ml-1">*</span>
                </div>
                <el-select 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.platFormNo"
                  size="small"
                  placeholder="请选择平台数量"
                  class="w-full"
                  :class="{ 'border-red-300': (!editFsuForm.platFormNo || editFsuForm.platFormNo <= 0) && hasValidationError }"
                >
                  <el-option label="1" :value="1" />
                  <el-option label="2" :value="2" />
                </el-select>
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.platFormNo || 1 }}</span>
              </div>
              
              <!-- 平台名 -->
              <div class="flex flex-col">
                <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">平台名</span>
                <el-input 
                  v-if="isEditingFsu"
                  v-model="editFsuForm.platFormName"
                  size="small"
                  placeholder="请输入平台名"
                />
                <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.platFormName || '-' }}</span>
              </div>
              
              <!-- 当平台数量为2时显示的字段 -->
              <template v-if="editFsuForm.platFormNo === 2 || (!isEditingFsu && fsuData?.platFormNo === 2)">
                <!-- SC地址2 -->
                <div class="flex flex-col">
                  <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">SC地址2</span>
                  <el-input 
                    v-if="isEditingFsu"
                    v-model="editFsuForm.scAddress1"
                    size="small"
                    placeholder="请输入SC地址2（格式：ip1:port1/）"
                  />
                  <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ formatScAddress(fsuData?.scip1, fsuData?.scPort1) || '-' }}</span>
                </div>
                
                <!-- 备份SC地址2 -->
                <div class="flex flex-col">
                  <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">备份SC地址2</span>
                  <el-input 
                    v-if="isEditingFsu"
                    v-model="editFsuForm.scipBak1"
                    size="small"
                    placeholder="请输入备份SC地址2（多个IP用逗号分隔）"
                  />
                  <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.scipBak1 || '-' }}</span>
                </div>
                
                <!-- 平台名2 -->
                <div class="flex flex-col">
                  <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">平台名2</span>
                  <el-input 
                    v-if="isEditingFsu"
                    v-model="editFsuForm.platFormName1"
                    size="small"
                    placeholder="请输入平台名2"
                  />
                  <span v-else class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.platFormName1 || '-' }}</span>
                </div>
              </template>
              
              <!-- 设备数量 (只读) -->
              <div class="flex flex-col">
                <span class="text-sm text-gray-500 dark:text-gray-400 mb-1">设备数量</span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ fsuData?.deviceNum || 0 }}</span>
              </div>
            </div>
            <div v-else class="text-center text-gray-500 dark:text-gray-400 py-8">
              暂无FSU配置信息
            </div>
          </div>
        </div>

        <!-- 设备列表区域 (严格占2/3) -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 flex flex-col" style="flex: 2; min-height: 0;">
          <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
            <div class="flex items-center justify-between">
              <h3 class="text-md font-semibold text-gray-900 dark:text-white">设备列表</h3>
              <el-button 
                size="small" 
                @click="clearFilters"
                v-if="Object.values(filterForm).some(val => val)"
              >
                清空筛选
              </el-button>
            </div>
            
            <!-- 筛选表单 - 对齐表格列宽 -->
            <div class="mt-3 flex">
              <div style="width: 140px; padding-right: 8px;">
                <el-input
                  v-model="filterForm.siteWebEquipId"
                  placeholder="筛选SiteWeb设备ID"
                  size="small"
                  clearable
                />
              </div>
              <div style="min-width: 160px; padding-right: 8px; flex: 1;">
                <el-input
                  v-model="filterForm.siteWebEquipName"
                  placeholder="筛选SiteWeb设备名称"
                  size="small"
                  clearable
                />
              </div>
              <div style="width: 120px; padding-right: 8px;">
                <el-input
                  v-model="filterForm.cmccDeviceId"
                  placeholder="筛选CMCC设备ID"
                  size="small"
                  clearable
                />
              </div>
              <div style="min-width: 140px; padding-right: 8px; flex: 1;">
                <el-input
                  v-model="filterForm.cmccDeviceName"
                  placeholder="筛选CMCC设备名称"
                  size="small"
                  clearable
                />
              </div>
              <div style="width: 120px;">
                <el-select
                  v-model="filterForm.configStatus"
                  placeholder="筛选配置状态"
                  size="small"
                  clearable
                  style="width: 100%;"
                >
                  <el-option
                    v-for="option in configStatusOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </div>
            </div>
          </div>
          <div class="flex-1 min-h-0 overflow-auto">
            <el-table 
              :data="filteredDeviceTableData" 
              style="width: 100%"
              @row-click="handleRowClick"
              class="cursor-pointer"
            >
              <el-table-column prop="siteWebEquipId" label="SiteWeb设备ID" width="140" />
              <el-table-column prop="siteWebEquipName" label="SiteWeb设备名称" min-width="160" />
              <el-table-column prop="cmccDeviceId" label="CMCC设备ID" width="120">
                <template #default="{ row }">
                  {{ row.cmccDeviceId || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="cmccDeviceName" label="CMCC设备名称" min-width="140">
                <template #default="{ row }">
                  {{ row.cmccDeviceName || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="配置状态" width="120">
                <template #default="{ row }">
                  <el-tag 
                    :type="row.configStatus === '已配置' ? 'success' : row.configStatus === '待配置' ? 'warning' : 'danger'"
                    size="small"
                  >
                    {{ row.configStatus }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 设备编辑弹框 -->
    <el-dialog 
      v-model="dialogVisible" 
      title="设备配置编辑"
      width="700px"
      :close-on-click-modal="false"
      top="5vh"
    >
      <div class="max-h-[70vh] overflow-auto">
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- SiteWeb设备ID -->
            <div class="flex flex-col">
              <span class="text-sm text-gray-500 mb-1">SiteWeb设备ID</span>
              <el-input 
                v-model="editDeviceForm.siteWebEquipId"
                size="small"
                disabled
                placeholder="自动获取"
              />
            </div>
            
            <!-- SiteWeb设备名称 -->
            <div class="flex flex-col">
              <span class="text-sm text-gray-500 mb-1">SiteWeb设备名称</span>
              <el-input 
                v-model="editDeviceForm.siteWebEquipName"
                size="small"
                disabled
                placeholder="自动获取"
              />
            </div>
            
            <!-- 设备ID -->
            <div class="flex flex-col">
              <div class="flex items-center mb-1">
                <span class="text-sm text-gray-500">设备ID</span>
                <span class="text-red-500 ml-1">*</span>
              </div>
              <el-input-number 
                v-model="editDeviceForm.deviceId"
                size="small"
                placeholder="请输入设备ID（不允许中文）"
                :min="1"
                class="w-full"
                :class="{ 'border-red-300': (!editDeviceForm.deviceId || editDeviceForm.deviceId <= 0) && hasDeviceValidationError }"
              />
            </div>
            
            <!-- 设备名称 -->
            <div class="flex flex-col">
              <div class="flex items-center mb-1">
                <span class="text-sm text-gray-500">设备名称</span>
                <span class="text-red-500 ml-1">*</span>
              </div>
              <el-input 
                v-model="editDeviceForm.deviceName"
                size="small"
                placeholder="请输入设备名称"
                :class="{ 'border-red-300': (!editDeviceForm.deviceName || editDeviceForm.deviceName.trim() === '') && hasDeviceValidationError }"
              />
            </div>
            
            <!-- 机房ID -->
            <div class="flex flex-col">
              <span class="text-sm text-gray-500 mb-1">机房ID</span>
              <el-input-number 
                v-model="editDeviceForm.roomId"
                size="small"
                placeholder="请输入机房ID"
                class="w-full"
              />
            </div>
            
            <!-- 机房名称 -->
            <div class="flex flex-col">
              <span class="text-sm text-gray-500 mb-1">机房名称</span>
              <el-input 
                v-model="editDeviceForm.roomName"
                size="small"
                placeholder="请输入机房名称"
              />
            </div>
            
            <!-- 设备类型 -->
            <div class="flex flex-col">
              <div class="flex items-center mb-1">
                <span class="text-sm text-gray-500">设备类型</span>
                <span class="text-red-500 ml-1">*</span>
              </div>
              <el-select 
                v-model="editDeviceForm.deviceType"
                size="small"
                placeholder="请选择设备类型"
                class="w-full"
                @change="onDeviceTypeChange"
                :class="{ 'border-red-300': (!editDeviceForm.deviceType || editDeviceForm.deviceType <= 0) && hasDeviceValidationError }"
              >
                <el-option
                  v-for="item in deviceTypes.filter(d => d.parentItemId === 0)"
                  :key="item.itemId"
                  :label="item.itemValue"
                  :value="item.itemId"
                />
              </el-select>
            </div>
            
            <!-- 设备子类型 -->
            <div class="flex flex-col">
              <div class="flex items-center mb-1">
                <span class="text-sm text-gray-500">设备子类型</span>
                <span class="text-red-500 ml-1">*</span>
              </div>
              <el-select 
                v-model="editDeviceForm.deviceSubType"
                size="small"
                placeholder="请选择设备子类型"
                class="w-full"
                :disabled="!editDeviceForm.deviceType"
                :class="{ 'border-red-300': (!editDeviceForm.deviceSubType || editDeviceForm.deviceSubType <= 0) && hasDeviceValidationError }"
              >
                <el-option
                  v-for="item in deviceSubTypes"
                  :key="item.itemId"
                  :label="item.itemValue"
                  :value="item.itemId"
                />
              </el-select>
            </div>
            
            <!-- 品牌 -->
            <div class="flex flex-col">
              <span class="text-sm text-gray-500 mb-1">品牌</span>
              <el-input 
                v-model="editDeviceForm.brand"
                size="small"
                placeholder="请输入品牌"
              />
            </div>
            
            <!-- 额定容量 -->
            <div class="flex flex-col">
              <span class="text-sm text-gray-500 mb-1">额定容量</span>
              <el-input 
                v-model="editDeviceForm.ratedCapacity"
                size="small"
                placeholder="请输入额定容量（仅数字）"
              />
            </div>
            
            <!-- 版本 -->
            <div class="flex flex-col">
              <span class="text-sm text-gray-500 mb-1">版本</span>
              <el-input 
                v-model="editDeviceForm.version"
                size="small"
                placeholder="请输入版本"
              />
            </div>
            
            <!-- 运行开始时间 -->
            <div class="flex flex-col">
              <span class="text-sm text-gray-500 mb-1">运行开始时间</span>
              <el-date-picker 
                v-model="editDeviceForm.beginRunTime"
                type="datetime"
                size="small"
                placeholder="请选择运行开始时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="w-full"
              />
            </div>
          </div>
          
          <!-- 设备描述 -->
          <div class="flex flex-col">
            <span class="text-sm text-gray-500 mb-1">设备描述</span>
            <el-input 
              v-model="editDeviceForm.devDescribe"
              type="textarea"
              :rows="3"
              size="small"
              placeholder="请输入设备描述"
            />
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="saveDeviceConfig"
            :loading="savingDevice"
          >
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 拉取底端配置对话框 -->
    <el-dialog
      v-model="showPullConfigDialog"
      title="拉取底端配置"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="pullConfigForm" label-width="120px">
        <el-form-item label="FTP主机地址" required>
          <el-input 
            v-model="pullConfigForm.ftpHost" 
            placeholder="自动获取监控单元IP地址"
            disabled
          />
        </el-form-item>
        <el-form-item label="FTP端口">
          <el-input-number v-model="pullConfigForm.ftpPort" :min="1" :max="65535" placeholder="请输入FTP端口" />
        </el-form-item>
        <el-form-item label="FTP用户名" required>
          <el-input v-model="pullConfigForm.ftpUsername" placeholder="请输入FTP用户名" />
        </el-form-item>
        <el-form-item label="FTP密码" required>
          <el-input v-model="pullConfigForm.ftpPassword" type="password" placeholder="请输入FTP密码" show-password />
        </el-form-item>
        <el-form-item label="协议类型">
          <el-select v-model="pullConfigForm.protocolType" placeholder="请选择协议类型">
            <el-option 
              v-for="option in protocolTypeOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button @click="closePullConfigDialog">取消</el-button>
          <el-button 
            type="primary" 
            @click="handlePullConfig"
            :loading="isPulling"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 覆盖配置确认对话框 -->
    <el-dialog
      v-model="showOverrideDialog"
      title="覆盖配置确认"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="pulledFsuData" class="space-y-4">
        <!-- FSU配置信息展示 -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">FSU配置信息</h4>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-500 dark:text-gray-400">FSU ID:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.fsuId }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">FSU端口:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.fsuPort }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">机房ID:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.roomId }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">机房名称:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.roomName }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">站点ID:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.siteId }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">站点名称:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.siteName }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">机房类型:</span>
              <span class="ml-2 font-medium">{{ getStationTypeLabel(pulledFsuData.fsuType as any) || '-' }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">只响应SC请求:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.enableACL ? '是' : '否' }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">SC地址:</span>
              <span class="ml-2 font-medium">{{ formatScAddress(pulledFsuData.scip as any, pulledFsuData.scPort as any) || '-' }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">备份SC地址:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.scipBak || '-' }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">SC切换方式:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.scSwitchMode === 1 ? '不主动切换回主SC' : '自动切换回主SC' }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">平台数量:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.platFormNo || 1 }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">平台名:</span>
              <span class="ml-2 font-medium">{{ pulledFsuData.platFormName || '-' }}</span>
            </div>
            <template v-if="pulledFsuData.platFormNo === 2">
              <div>
                <span class="text-gray-500 dark:text-gray-400">SC地址2:</span>
                <span class="ml-2 font-medium">{{ formatScAddress(pulledFsuData.scip1 as any, pulledFsuData.scPort1 as any) || '-' }}</span>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">备份SC地址2:</span>
                <span class="ml-2 font-medium">{{ pulledFsuData.scipBak1 || '-' }}</span>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">平台名2:</span>
                <span class="ml-2 font-medium">{{ pulledFsuData.platFormName1 || '-' }}</span>
              </div>
            </template>
          </div>
        </div>

        <!-- 设备列表展示 -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3">
            设备列表 ({{ pulledFsuData.cmccDeviceInitDTOList?.length || 0 }}个)
          </h4>
          <div class="max-h-60 overflow-auto">
            <div v-if="pulledFsuData.cmccDeviceInitDTOList && pulledFsuData.cmccDeviceInitDTOList.length > 0" class="space-y-2">
              <div 
                v-for="device in pulledFsuData.cmccDeviceInitDTOList" 
                :key="device.deviceId"
                class="bg-white dark:bg-gray-600 rounded p-3 border border-gray-200 dark:border-gray-500"
              >
                <div class="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">SiteWeb设备ID:</span>
                    <span class="ml-2 font-medium">{{ device.siteWebEquipId }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">CMCC设备ID:</span>
                    <span class="ml-2 font-medium">{{ device.deviceId }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">设备名称:</span>
                    <span class="ml-2 font-medium">{{ device.deviceName }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">设备类型:</span>
                    <span class="ml-2 font-medium">{{ getDeviceTypeLabelById(device.deviceType as any) }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">设备子类:</span>
                    <span class="ml-2 font-medium">{{ getSubDeviceTypeLabelById(device.deviceSubType as any) }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">品牌:</span>
                    <span class="ml-2 font-medium">{{ device.brand }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">型号:</span>
                    <span class="ml-2 font-medium">{{ device.model }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500 dark:text-gray-400">额定容量:</span>
                    <span class="ml-2 font-medium">{{ device.ratedCapacity }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-gray-500 dark:text-gray-400 text-center py-4">
              暂无设备信息
            </div>
          </div>
        </div>

        <!-- 设备过滤提示 -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-blue-800 dark:text-blue-200">
                <strong>提示：</strong>系统将自动过滤设备列表，只保留在监控单元设备列表中的设备进行保存。其他设备将被忽略。
              </p>
            </div>
          </div>
        </div>

        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>警告：</strong>覆盖操作将删除当前的所有FSU配置和设备配置，并替换为拉取的新配置。此操作不可撤销，请确认后再执行。
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button @click="closeOverrideDialog">取消</el-button>
          <el-button 
            type="danger" 
            @click="handleOverrideConfig"
            :loading="isOverriding"
          >
            覆盖
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="showDeleteConfirmDialog"
      title="删除无效映射确认"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="text-center">
        <div class="mb-4">
          <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <p class="text-lg font-medium text-gray-900 dark:text-white mb-2">检测到无效映射</p>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
          该设备在SiteWeb系统中不存在，但在CMCC配置中有记录。<br/>
          是否删除该无效映射？
        </p>
      </div>
      
      <template #footer>
        <div class="flex justify-center space-x-3">
          <el-button @click="cancelDelete">取消</el-button>
          <el-button 
            type="danger" 
            @click="handleDeleteInvalidMapping"
            :loading="isDeleting"
          >
            删除
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { ArrowLeft, Download } from "@element-plus/icons-vue";
import { 
  getMonitorUnitDevices, 
  getFsuWithDevices,
  getStationType,
  getDeviceType,
  getSubDeviceType,
  saveOrUpdateFsu,
  saveOrUpdateDevice,
  getMonitorUnits,
  pullInitConfig,
  overrideFsuConfig,
  deleteDeviceBySiteWebEquipId,
  type Equipment, 
  type CMCCFsuInitDTO,
  type CMCCDeviceInitDTO,
  type DictionaryItem,
  type MonitorUnit,
  type CmccPullInitConfigDTO
} from "@/api/monitor-unit";

defineOptions({ name: "FsuSettingDeviceList" });

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const deviceList = ref<Equipment[]>([]);
const fsuData = ref<CMCCFsuInitDTO | null>(null);
const monitorUnitInfo = ref<MonitorUnit | null>(null);

// 表单验证错误状态
const hasValidationError = ref(false);
const hasDeviceValidationError = ref(false);

// 实时验证函数 - 当字段值改变时清除错误状态
const clearFsuValidationError = () => {
  if (hasValidationError.value) {
    // 简单检查是否有基本内容，如果有就清除错误状态
    const form = editFsuForm.value;
    if (form.fsuId?.trim() && form.fsuPort && form.loginUser?.trim() && 
        form.loginPwd?.trim() && form.ftpUser?.trim() && form.ftpPwd?.trim() &&
        form.fsuType && form.scAddress?.trim() && form.roomId && 
        form.roomName?.trim() && form.platFormNo && form.scSwitchMode !== null) {
      hasValidationError.value = false;
    }
  }
};

const clearDeviceValidationError = () => {
  if (hasDeviceValidationError.value) {
    const form = editDeviceForm.value;
    if (form.deviceId && form.deviceName?.trim() && form.deviceType && form.deviceSubType) {
      hasDeviceValidationError.value = false;
    }
  }
};



// 拉取配置相关状态
const showPullConfigDialog = ref(false);
const pullConfigForm = ref<CmccPullInitConfigDTO>({
  ftpHost: '',
  ftpUsername: '',
  ftpPassword: '',
  ftpPort: 21,
  protocolType: 'FTP'
});
const pulledFsuData = ref<CMCCFsuInitDTO | null>(null);
const showOverrideDialog = ref(false);
const isPulling = ref(false);
const isOverriding = ref(false);

// FTP协议类型选项
const protocolTypeOptions = [
  { label: 'FTP', value: 'FTP' },
  { label: 'SFTP', value: 'SFTP' }
];

const dialogVisible = ref(false);
const selectedDeviceConfig = ref<CMCCDeviceInitDTO | null>(null);
const selectedEquipment = ref<Equipment | null>(null);

// 字典数据
const stationTypes = ref<DictionaryItem[]>([]);
const deviceTypes = ref<DictionaryItem[]>([]);
const allDeviceSubTypes = ref<DictionaryItem[]>([]); // 存储所有设备子类
const deviceSubTypes = ref<DictionaryItem[]>([]); // 当前过滤后的设备子类

// FSU编辑相关状态
const isEditingFsu = ref(false);
const savingFsu = ref(false);
const editFsuForm = ref({
  fsuId: '',
  fsuPort: null as number | null,
  roomId: null as number | null,
  roomName: '',
  fsuType: null as number | null,
  siteId: '',
  siteName: '',
  ftpUser: '',
  ftpPwd: '',
  loginUser: '',
  loginPwd: '',
  enableACL: false,
  scAddress: '', // SC地址 (ip:port/格式)
  scip: '',
  scPort: null as number | null,
  scipBak: '', // 备份SC地址
  scSwitchMode: 1, // SC切换方式，默认1
  platFormNo: 1, // 平台数量，默认1
  platFormName: '', // 平台名
  scAddress1: '', // SC地址2 (ip1:port1/格式)
  scip1: '',
  scPort1: null as number | null,
  scipBak1: '', // 备份SC地址2
  platFormName1: '' // 平台名2
});

// 设备编辑相关状态
const savingDevice = ref(false);
const editDeviceForm = ref({
  siteWebEquipId: null as number | null,
  siteWebEquipName: '',
  deviceId: null as number | null,
  deviceName: '',
  roomId: null as number | null,
  roomName: '',
  deviceType: null as number | null,
  deviceSubType: null as number | null,
  brand: '',
  ratedCapacity: '',
  version: '',
  beginRunTime: '',
  devDescribe: ''
});

// 监听表单变化，自动清除验证错误状态
watch(() => editFsuForm.value, clearFsuValidationError, { deep: true });
watch(() => editDeviceForm.value, clearDeviceValidationError, { deep: true });

// 设备列表筛选相关状态
const filterForm = ref({
  siteWebEquipId: '',
  siteWebEquipName: '',
  cmccDeviceId: '',
  cmccDeviceName: '',
  configStatus: ''
});

// 删除确认对话框
const showDeleteConfirmDialog = ref(false);
const deviceToDelete = ref<number | null>(null);
const isDeleting = ref(false);

// 配置状态选项
const configStatusOptions = [
  { label: '全部', value: '' },
  { label: '已配置', value: '已配置' },
  { label: '待配置', value: '待配置' },
  { label: '配置缺失', value: '配置缺失' }
];

// 设备列表综合数据类型
interface DeviceTableRow {
  siteWebEquipId: number;
  siteWebEquipName: string;
  cmccDeviceId?: number | null;
  cmccDeviceName?: string;
  configStatus: '已配置' | '待配置' | '配置缺失';
  equipment?: Equipment; // 原始 equipment 数据
  cmccDevice?: CMCCDeviceInitDTO; // CMCC 设备配置数据
}

// 综合设备列表数据
const deviceTableData = ref<DeviceTableRow[]>([]);

// 过滤后的设备列表数据
const filteredDeviceTableData = computed(() => {
  let filtered = deviceTableData.value;
  
  // SiteWeb设备ID筛选
  if (filterForm.value.siteWebEquipId) {
    filtered = filtered.filter(row => 
      row.siteWebEquipId.toString().includes(filterForm.value.siteWebEquipId)
    );
  }
  
  // SiteWeb设备名称筛选
  if (filterForm.value.siteWebEquipName) {
    filtered = filtered.filter(row => 
      row.siteWebEquipName.toLowerCase().includes(filterForm.value.siteWebEquipName.toLowerCase())
    );
  }
  
  // CMCC设备ID筛选
  if (filterForm.value.cmccDeviceId) {
    filtered = filtered.filter(row => 
      row.cmccDeviceId?.toString().includes(filterForm.value.cmccDeviceId) || false
    );
  }
  
  // CMCC设备名称筛选
  if (filterForm.value.cmccDeviceName) {
    filtered = filtered.filter(row => 
      row.cmccDeviceName?.toLowerCase().includes(filterForm.value.cmccDeviceName.toLowerCase()) || false
    );
  }
  
  // 配置状态筛选
  if (filterForm.value.configStatus) {
    filtered = filtered.filter(row => row.configStatus === filterForm.value.configStatus);
  }
  
  return filtered;
});

const monitorUnitId = Number(route.params.monitorUnitId);

// 调试路由参数
console.log('路由参数:', route.params);
console.log('monitorUnitId:', monitorUnitId);

// 返回监控单元列表页面
const goBackToFsuSetting = () => {
  router.push('/south-cmcc/fsu-setting');
};

// 获取机房类型标签
const getStationTypeLabel = (fsuType: number | undefined): string => {
  if (!fsuType) return '';
  const stationType = stationTypes.value.find(item => item.itemId === fsuType);
  return stationType?.itemValue || '';
};

// 设备类型与子类中文名称
const getDeviceTypeLabelById = (id?: number | null): string => {
  if (!id && id !== 0) return '';
  const item = deviceTypes.value.find(d => d.itemId === id);
  return item?.itemValue || String(id ?? '');
};

const getSubDeviceTypeLabelById = (id?: number | null): string => {
  if (!id && id !== 0) return '';
  const item = allDeviceSubTypes.value.find(d => d.itemId === id);
  return item?.itemValue || String(id ?? '');
};

// 格式化SC地址显示 (ip:port/格式)
const formatScAddress = (scip: string | undefined, scPort: number | undefined): string => {
  if (!scip || !scPort) return '';
  return `${scip}:${scPort}/`;
};

// 解析SC地址输入 (ip:port/格式 -> scip和scPort)
const parseScAddress = (scAddress: string): { scip: string; scPort: number | null } => {
  if (!scAddress) return { scip: '', scPort: null };
  
  const match = scAddress.match(/^(.+):(\d+)\/$/);
  if (match) {
    return { scip: match[1], scPort: parseInt(match[2]) };
  }
  return { scip: '', scPort: null };
};

// 设备类型改变时更新子类型选项
const onDeviceTypeChange = (typeId: number) => {
  editDeviceForm.value.deviceSubType = null;
  // 根据设备大类的itemId筛选设备子类（设备子类的parentItemId等于设备大类的itemId）
  deviceSubTypes.value = allDeviceSubTypes.value.filter(item => item.parentItemId === typeId);
};

// 校验函数
const validateFsuForm = (): boolean => {
  const form = editFsuForm.value;
  
  // 必填字段校验
  if (!form.fsuId || form.fsuId.trim() === '') {
    ElMessage.error('FSU ID不能为空');
    return false;
  }
  
  if (!form.fsuPort || form.fsuPort <= 0) {
    ElMessage.error('FSU端口不能为空');
    return false;
  }
  
  if (!form.loginUser || form.loginUser.trim() === '') {
    ElMessage.error('登录用户不能为空');
    return false;
  }
  
  if (!form.loginPwd || form.loginPwd.trim() === '') {
    ElMessage.error('登录密码不能为空');
    return false;
  }
  
  if (!form.ftpUser || form.ftpUser.trim() === '') {
    ElMessage.error('FTP用户不能为空');
    return false;
  }
  
  if (!form.ftpPwd || form.ftpPwd.trim() === '') {
    ElMessage.error('FTP密码不能为空');
    return false;
  }
  
  if (!form.fsuType || form.fsuType <= 0) {
    ElMessage.error('机房类型不能为空');
    return false;
  }
  
  if (!form.scAddress || form.scAddress.trim() === '') {
    ElMessage.error('SC地址不能为空');
    return false;
  }
  
  if (!form.roomId || form.roomId <= 0) {
    ElMessage.error('机房ID不能为空');
    return false;
  }
  
  if (!form.roomName || form.roomName.trim() === '') {
    ElMessage.error('机房名称不能为空');
    return false;
  }
  
  if (!form.platFormNo || form.platFormNo <= 0) {
    ElMessage.error('平台数量不能为空');
    return false;
  }
  
  if (!form.scSwitchMode || form.scSwitchMode <= 0) {
    ElMessage.error('SC切换方式不能为空');
    return false;
  }
  
  // FSU端口范围校验
  if (form.fsuPort < 1 || form.fsuPort > 65534) {
    ElMessage.error('FSU端口必须在1-65534范围内');
    return false;
  }
  
  // 站点ID校验 - 不允许中文
  if (form.siteId && /[\u4e00-\u9fa5]/.test(form.siteId)) {
    ElMessage.error('站点ID不允许包含中文字符');
    return false;
  }
  
  // SC地址校验 - 必须符合ip:port/格式
  if (form.scAddress) {
    const { scip, scPort } = parseScAddress(form.scAddress);
    if (!scip || !scPort) {
      ElMessage.error('SC地址格式错误，应为ip:port/格式');
      return false;
    }
  }
  
  return true;
};

const validateDeviceForm = (): boolean => {
  const form = editDeviceForm.value;
  
  // 必填字段校验
  if (!form.deviceId || form.deviceId <= 0) {
    ElMessage.error('设备ID不能为空');
    return false;
  }
  
  if (!form.deviceName || form.deviceName.trim() === '') {
    ElMessage.error('设备名称不能为空');
    return false;
  }
  
  if (!form.deviceType || form.deviceType <= 0) {
    ElMessage.error('设备类型不能为空');
    return false;
  }
  
  if (!form.deviceSubType || form.deviceSubType <= 0) {
    ElMessage.error('设备子类型不能为空');
    return false;
  }
  
  // 设备ID校验 - 不允许中文
  if (form.deviceId && /[\u4e00-\u9fa5]/.test(form.deviceId.toString())) {
    ElMessage.error('设备ID不允许包含中文字符');
    return false;
  }
  
  // 设备ID唯一性校验 - 在同一个FSU下必须唯一
  if (form.deviceId && fsuData.value?.cmccDeviceInitDTOList) {
    const existingDevice = fsuData.value.cmccDeviceInitDTOList.find(device => 
      device.deviceId === form.deviceId && 
      device.siteWebEquipId !== form.siteWebEquipId // 排除当前编辑的设备
    );
    
    if (existingDevice) {
      ElMessage.error(`设备ID ${form.deviceId} 已存在，同一个FSU下的设备ID必须唯一`);
      return false;
    }
  }
  
  // 额定容量校验 - 仅数字
  if (form.ratedCapacity && !/^-?\d+(\.\d+)?$/.test(form.ratedCapacity)) {
    ElMessage.error('额定容量只能输入数字');
    return false;
  }
  
  return true;
};

// 获取配置状态
const getConfigStatus = (equipmentId: number): string => {
  if (!fsuData.value?.cmccDeviceInitDTOList) return "未配置";
  
  const isConfigured = fsuData.value.cmccDeviceInitDTOList.some(
    device => device.siteWebEquipId === equipmentId
  );
  
  return isConfigured ? "已配置" : "未配置";
};

// 构建设备表格数据
const buildDeviceTableData = () => {
  const tableData: DeviceTableRow[] = [];
  const equipmentMap = new Map<number, Equipment>();
  const cmccDeviceMap = new Map<number, CMCCDeviceInitDTO>();
  
  // 构建 equipment 映射
  deviceList.value.forEach(equipment => {
    equipmentMap.set(equipment.equipmentId, equipment);
  });
  
  // 构建 CMCC 设备映射
  if (fsuData.value?.cmccDeviceInitDTOList) {
    fsuData.value.cmccDeviceInitDTOList.forEach(cmccDevice => {
      if (cmccDevice.siteWebEquipId) {
        cmccDeviceMap.set(cmccDevice.siteWebEquipId, cmccDevice);
      }
    });
  }
  
  // 先处理所有 SiteWeb 设备
  deviceList.value.forEach(equipment => {
    const cmccDevice = cmccDeviceMap.get(equipment.equipmentId);
    const configStatus: DeviceTableRow['configStatus'] = cmccDevice ? '已配置' : '待配置';
    
    tableData.push({
      siteWebEquipId: equipment.equipmentId,
      siteWebEquipName: equipment.equipmentName,
      cmccDeviceId: cmccDevice?.deviceId || null,
      cmccDeviceName: cmccDevice?.deviceName || undefined,
      configStatus,
      equipment,
      cmccDevice
    });
  });
  
  // 再处理只在 CMCC 中存在的设备（配置缺失）
  if (fsuData.value?.cmccDeviceInitDTOList) {
    fsuData.value.cmccDeviceInitDTOList.forEach(cmccDevice => {
      if (cmccDevice.siteWebEquipId && !equipmentMap.has(cmccDevice.siteWebEquipId)) {
        tableData.push({
          siteWebEquipId: cmccDevice.siteWebEquipId,
          siteWebEquipName: `[缺失] ${cmccDevice.siteWebEquipId}`,
          cmccDeviceId: cmccDevice.deviceId || null,
          cmccDeviceName: cmccDevice.deviceName || undefined,
          configStatus: '配置缺失',
          equipment: undefined,
          cmccDevice
        });
      }
    });
  }
  
  deviceTableData.value = tableData;
};

// 清空筛选
const clearFilters = () => {
  filterForm.value = {
    siteWebEquipId: '',
    siteWebEquipName: '',
    cmccDeviceId: '',
    cmccDeviceName: '',
    configStatus: ''
  };
};

// 删除无效映射
const handleDeleteInvalidMapping = async () => {
  if (!deviceToDelete.value) return;
  
  isDeleting.value = true;
  try {
    const res = await deleteDeviceBySiteWebEquipId(deviceToDelete.value);
    if (res.code === 0) {
      ElMessage.success('删除成功');
      showDeleteConfirmDialog.value = false;
      deviceToDelete.value = null;
      // 重新加载数据
      await loadFsuWithDevices();
    } else {
      ElMessage.error(res.message || '删除失败');
    }
  } catch (error) {
    console.error('删除设备失败:', error);
    ElMessage.error('删除设备失败');
  } finally {
    isDeleting.value = false;
  }
};

// 取消删除
const cancelDelete = () => {
  showDeleteConfirmDialog.value = false;
  deviceToDelete.value = null;
};

// 处理行点击事件
const handleRowClick = (row: DeviceTableRow) => {
  if (row.configStatus === '配置缺失') {
    // 配置缺失的设备，显示删除确认对话框
    deviceToDelete.value = row.siteWebEquipId;
    showDeleteConfirmDialog.value = true;
    return;
  }
  
  // 正常设备，进入编辑模式
  if (row.equipment) {
    selectedEquipment.value = row.equipment;
  }
  selectedDeviceConfig.value = row.cmccDevice || null;
  
  // 填充编辑表单
  if (row.cmccDevice) {
    // 有配置数据，回显到编辑表单
    editDeviceForm.value = {
      siteWebEquipId: row.siteWebEquipId,
      siteWebEquipName: row.siteWebEquipName,
      deviceId: row.cmccDevice.deviceId || null,
      deviceName: row.cmccDevice.deviceName || row.siteWebEquipName,
      roomId: row.cmccDevice.roomId || null,
      roomName: row.cmccDevice.roomName || '',
      deviceType: row.cmccDevice.deviceType || null,
      deviceSubType: row.cmccDevice.deviceSubType || null,
      brand: row.cmccDevice.brand || '',
      ratedCapacity: row.cmccDevice.ratedCapacity ? row.cmccDevice.ratedCapacity.toString() : '',
      version: row.cmccDevice.version || '',
      beginRunTime: row.cmccDevice.beginRunTime || '',
      devDescribe: row.cmccDevice.devDescribe || ''
    };
    
    // 如果有设备类型，加载对应的子类型
    if (row.cmccDevice.deviceType) {
      onDeviceTypeChange(row.cmccDevice.deviceType);
    }
  } else {
    // 无配置数据，使用默认值
    editDeviceForm.value = {
      siteWebEquipId: row.siteWebEquipId,
      siteWebEquipName: row.siteWebEquipName,
      deviceId: null,
      deviceName: row.siteWebEquipName,
      roomId: null,
      roomName: '',
      deviceType: null,
      deviceSubType: null,
      brand: '',
      ratedCapacity: '',
      version: '',
      beginRunTime: '',
      devDescribe: ''
    };
  }
  
  // 显示编辑弹框
  dialogVisible.value = true;
};

// FSU编辑相关方法
const startEditFsu = () => {
  if (!fsuData.value) return;
  
  // 回显FSU配置数据到编辑表单
  editFsuForm.value = {
    fsuId: fsuData.value.fsuId || '',
    fsuPort: fsuData.value.fsuPort || null,
    roomId: fsuData.value.roomId || null,
    roomName: fsuData.value.roomName || '',
    fsuType: fsuData.value.fsuType || null,
    siteId: fsuData.value.siteId || '',
    siteName: fsuData.value.siteName || '',
    ftpUser: fsuData.value.ftpUser || '',
    ftpPwd: fsuData.value.ftpPwd || '',
    loginUser: fsuData.value.loginUser || '',
    loginPwd: fsuData.value.loginPwd || '',
    enableACL: fsuData.value.enableACL || false,
    scAddress: formatScAddress(fsuData.value.scip, fsuData.value.scPort),
    scip: fsuData.value.scip || '',
    scPort: fsuData.value.scPort || null,
    scipBak: fsuData.value.scipBak || '',
    scSwitchMode: fsuData.value.scSwitchMode || 1,
    platFormNo: fsuData.value.platFormNo || 1,
    platFormName: fsuData.value.platFormName || '',
    scAddress1: formatScAddress(fsuData.value.scip1, fsuData.value.scPort1),
    scip1: fsuData.value.scip1 || '',
    scPort1: fsuData.value.scPort1 || null,
    scipBak1: fsuData.value.scipBak1 || '',
    platFormName1: fsuData.value.platFormName1 || ''
  };
  
  console.log('开始编辑FSU，编辑表单数据:', editFsuForm.value);
  isEditingFsu.value = true;
};

const cancelFsuEdit = () => {
  isEditingFsu.value = false;
  editFsuForm.value = {
    fsuId: '',
    fsuPort: null,
    roomId: null,
    roomName: '',
    fsuType: null,
    siteId: '',
    siteName: '',
    ftpUser: '',
    ftpPwd: '',
    loginUser: '',
    loginPwd: '',
    enableACL: false,
    scAddress: '',
    scip: '',
    scPort: null,
    scipBak: '',
    scSwitchMode: 1,
    platFormNo: 1,
    platFormName: '',
    scAddress1: '',
    scip1: '',
    scPort1: null,
    scipBak1: '',
    platFormName1: ''
  };
};

const saveFsuConfig = async () => {
  if (!fsuData.value) return;
  
  // 设置验证错误状态，显示边框样式
  hasValidationError.value = true;
  
  // 校验表单
  if (!validateFsuForm()) {
    return;
  }
  
  // 验证通过，清除错误状态
  hasValidationError.value = false;
  
  try {
    savingFsu.value = true;
    
          // 解析SC地址
      const { scip, scPort } = parseScAddress(editFsuForm.value.scAddress);
      const { scip: scip1, scPort: scPort1 } = parseScAddress(editFsuForm.value.scAddress1);
      
      // 构造完整的DTO对象
      const fsuDto: CMCCFsuInitDTO = {
        ...fsuData.value,
        fsuId: editFsuForm.value.fsuId,
        fsuPort: editFsuForm.value.fsuPort,
        roomId: editFsuForm.value.roomId,
        roomName: editFsuForm.value.roomName,
        fsuType: editFsuForm.value.fsuType,
        siteId: editFsuForm.value.siteId,
        siteName: editFsuForm.value.siteName,
        ftpUser: editFsuForm.value.ftpUser,
        ftpPwd: editFsuForm.value.ftpPwd,
        loginUser: editFsuForm.value.loginUser,
        loginPwd: editFsuForm.value.loginPwd,
        enableACL: editFsuForm.value.enableACL,
        scip: scip,
        scPort: scPort,
        scipBak: editFsuForm.value.scipBak,
        scSwitchMode: editFsuForm.value.scSwitchMode,
        platFormNo: editFsuForm.value.platFormNo,
        platFormName: editFsuForm.value.platFormName,
        scip1: scip1,
        scPort1: scPort1,
        scipBak1: editFsuForm.value.scipBak1,
        platFormName1: editFsuForm.value.platFormName1,
        siteWebMuId: fsuData.value.siteWebMuId
      };
    
    // 调用API保存FSU配置
    const response = await saveOrUpdateFsu(fsuDto);
    
    if (response.code === 0) {
      // 更新本地数据
      if (fsuData.value) {
        fsuData.value.fsuId = editFsuForm.value.fsuId;
        fsuData.value.fsuPort = editFsuForm.value.fsuPort;
        fsuData.value.roomId = editFsuForm.value.roomId;
        fsuData.value.roomName = editFsuForm.value.roomName;
        fsuData.value.fsuType = editFsuForm.value.fsuType;
        fsuData.value.siteId = editFsuForm.value.siteId;
        fsuData.value.siteName = editFsuForm.value.siteName;
        fsuData.value.ftpUser = editFsuForm.value.ftpUser;
        fsuData.value.ftpPwd = editFsuForm.value.ftpPwd;
        fsuData.value.loginUser = editFsuForm.value.loginUser;
        fsuData.value.loginPwd = editFsuForm.value.loginPwd;
        fsuData.value.enableACL = editFsuForm.value.enableACL;
        fsuData.value.scip = scip;
        fsuData.value.scPort = scPort;
        fsuData.value.scipBak = editFsuForm.value.scipBak;
        fsuData.value.scSwitchMode = editFsuForm.value.scSwitchMode;
        fsuData.value.platFormNo = editFsuForm.value.platFormNo;
        fsuData.value.platFormName = editFsuForm.value.platFormName;
        fsuData.value.scip1 = scip1;
        fsuData.value.scPort1 = scPort1;
        fsuData.value.scipBak1 = editFsuForm.value.scipBak1;
        fsuData.value.platFormName1 = editFsuForm.value.platFormName1;
      }
      ElMessage.success('FSU配置保存成功');
      isEditingFsu.value = false;
      // 重新构建设备表格数据
      buildDeviceTableData();
    } else {
      ElMessage.error(response.message || 'FSU配置保存失败');
    }
  } catch (error: any) {
    console.error('保存FSU配置失败:', error);
    ElMessage.error(error.response?.data?.message || '保存FSU配置失败');
  } finally {
    savingFsu.value = false;
  }
};

// 设备编辑相关方法（现在弹框始终是编辑模式，不需要单独的启动编辑方法）

const saveDeviceConfig = async () => {
  // 设置验证错误状态，显示边框样式
  hasDeviceValidationError.value = true;
  
  // 校验表单
  if (!validateDeviceForm()) {
    return;
  }
  
  // 验证通过，清除错误状态
  hasDeviceValidationError.value = false;
  
  try {
    savingDevice.value = true;
    
    // 构造完整的DTO对象
    const deviceDto: CMCCDeviceInitDTO = {
      deviceId: editDeviceForm.value.deviceId ? Number(editDeviceForm.value.deviceId) : null,
      siteWebMuId: fsuData.value?.siteWebMuId || 0,
      deviceName: editDeviceForm.value.deviceName || '',
      roomId: editDeviceForm.value.roomId ? Number(editDeviceForm.value.roomId) : null,
      roomName: editDeviceForm.value.roomName || '',
      deviceType: editDeviceForm.value.deviceType || null,
      deviceSubType: editDeviceForm.value.deviceSubType || null,
      model: '',
      brand: editDeviceForm.value.brand || '',
      ratedCapacity: editDeviceForm.value.ratedCapacity ? Number(editDeviceForm.value.ratedCapacity) : null,
      version: editDeviceForm.value.version || '',
      beginRunTime: editDeviceForm.value.beginRunTime || '',
      devDescribe: editDeviceForm.value.devDescribe || '',
      siteWebEquipId: editDeviceForm.value.siteWebEquipId || 0,
      namePrefix: ''
    };
    
    // 调用API保存设备配置
    const response = await saveOrUpdateDevice(deviceDto);
    
    if (response.code === 0) {
      ElMessage.success('设备配置保存成功');
      dialogVisible.value = false;
      // 重新加载数据
      await loadFsuWithDevices();
    } else {
      ElMessage.error(response.message || '设备配置保存失败');
    }
  } catch (error: any) {
    console.error('保存设备配置失败:', error);
    ElMessage.error(error.response?.data?.message || '保存设备配置失败');
  } finally {
    savingDevice.value = false;
  }
};

// 加载字典数据
const loadDictionaryData = async () => {
  try {
    // 加载机房类型
    const stationRes = await getStationType();
    if (stationRes.code === 0) {
      stationTypes.value = stationRes.data || [];
    }
    
    // 加载设备类型（大类，parentItemId为0）
    const deviceTypeRes = await getDeviceType();
    if (deviceTypeRes.code === 0) {
      deviceTypes.value = deviceTypeRes.data || [];
    }
    
    // 加载设备子类（所有子类，parentItemId不为0）
    const subDeviceTypeRes = await getSubDeviceType();
    if (subDeviceTypeRes.code === 0) {
      allDeviceSubTypes.value = subDeviceTypeRes.data || [];
    }
  } catch (error) {
    console.error('加载字典数据失败:', error);
  }
};

// 拉取底端配置相关方法
const openPullConfigDialog = () => {
  showPullConfigDialog.value = true;
  // 自动填充监控单元的IP地址作为FTP主机地址
  pullConfigForm.value = {
    ftpHost: monitorUnitInfo.value?.ipAddress || '',
    ftpUsername: '',
    ftpPassword: '',
    ftpPort: 21,
    protocolType: 'FTP'
  };
};

const closePullConfigDialog = () => {
  showPullConfigDialog.value = false;
};

const handlePullConfig = async () => {
  if (!pullConfigForm.value.ftpHost) {
    ElMessage.warning('FTP主机地址未能自动获取，请检查监控单元配置');
    return;
  }
  
  if (!pullConfigForm.value.ftpUsername || !pullConfigForm.value.ftpPassword) {
    ElMessage.warning('请填写FTP用户名和密码');
    return;
  }
  
  isPulling.value = true;
  try {
    const res = await pullInitConfig(pullConfigForm.value);
    if (res.code === 0 && res.data) {
      pulledFsuData.value = res.data;
      showOverrideDialog.value = true;
      closePullConfigDialog();
      ElMessage.success('拉取配置成功');
    } else {
      ElMessage.error(res.message || '拉取配置失败');
    }
  } catch (error) {
    console.error('拉取配置失败:', error);
    ElMessage.error('拉取配置失败');
  } finally {
    isPulling.value = false;
  }
};

const handleOverrideConfig = async () => {
  if (!pulledFsuData.value) {
    ElMessage.error('没有可覆盖的配置数据');
    return;
  }
  
  isOverriding.value = true;
  try {
    // 获取监控单元下的所有设备列表
    const equipmentRes = await getMonitorUnitDevices(monitorUnitId);
    if (equipmentRes.code !== 0 || !equipmentRes.data) {
      throw new Error('获取监控单元设备列表失败');
    }
    
    const validEquipments = equipmentRes.data;
    const validEquipmentIds = validEquipments.map(eq => eq.equipmentId);
    
    // 过滤设备列表，只保留在监控单元设备列表中的设备
    if (pulledFsuData.value.cmccDeviceInitDTOList) {
      pulledFsuData.value.cmccDeviceInitDTOList = pulledFsuData.value.cmccDeviceInitDTOList.filter(device => {
        return device.siteWebEquipId && validEquipmentIds.includes(device.siteWebEquipId);
      });
      
      // 更新设备数量
      pulledFsuData.value.deviceNum = pulledFsuData.value.cmccDeviceInitDTOList.length;
      
      console.log('过滤后的设备列表:', pulledFsuData.value.cmccDeviceInitDTOList);
      console.log('有效设备ID列表:', validEquipmentIds);
    }
    
    // 设置监控单元ID
    pulledFsuData.value.siteWebMuId = monitorUnitId;
    
    const res = await overrideFsuConfig(pulledFsuData.value);
    if (res.code === 0 && res.data) {
      ElMessage.success('覆盖配置成功');
      showOverrideDialog.value = false;
      // 重新加载数据
      await loadFsuWithDevices();
      // 刷新页面
      window.location.reload();
    } else {
      ElMessage.error(res.message || '覆盖配置失败');
    }
  } catch (error) {
    console.error('覆盖配置失败:', error);
    ElMessage.error('覆盖配置失败: ' + (error as Error).message);
  } finally {
    isOverriding.value = false;
  }
};

const closeOverrideDialog = () => {
  showOverrideDialog.value = false;
  pulledFsuData.value = null;
};

// 加载监控单元信息
const loadMonitorUnitInfo = async () => {
  if (!monitorUnitId) return;
  
  try {
    const monitorUnitsRes = await getMonitorUnits();
    if (monitorUnitsRes && monitorUnitsRes.code === 0) {
      const monitorUnit = monitorUnitsRes.data.find(unit => unit.monitorUnitId === monitorUnitId);
      if (monitorUnit) {
        monitorUnitInfo.value = monitorUnit;
        console.log('监控单元信息:', monitorUnit);
      }
    }
  } catch (error) {
    console.error('加载监控单元信息失败:', error);
  }
};

// 加载FSU配置及设备信息
const loadFsuWithDevices = async () => {
  if (!monitorUnitId) {
    console.error('monitorUnitId 为空，无法加载数据');
    return;
  }
  
  console.log('开始加载FSU配置，monitorUnitId:', monitorUnitId);
  
  try {
    loading.value = true;
    
    // 获取FSU配置及设备配置信息
    console.log('调用 getFsuWithDevices API...');
    const fsuRes = await getFsuWithDevices(monitorUnitId);
    console.log('FSU配置响应:', fsuRes);
    console.log('响应类型:', typeof fsuRes);
    console.log('响应键:', fsuRes ? Object.keys(fsuRes) : 'null');
    
    // 检查响应格式，可能是直接的DTO或者包装在ApiResponse中
    if (fsuRes && typeof fsuRes === 'object') {
      if ('code' in fsuRes && (fsuRes as any).code === 0 && (fsuRes as any).data) {
        // ApiResponse格式
        fsuData.value = (fsuRes as any).data;
        console.log('FSU数据 (ApiResponse格式):', fsuData.value);
      } else if ('fsuId' in fsuRes || 'siteWebMuId' in fsuRes) {
        // 直接的DTO格式
        fsuData.value = fsuRes as CMCCFsuInitDTO;
        console.log('FSU数据 (直接DTO格式):', fsuData.value);
      } else {
        console.log('FSU响应格式未知:', fsuRes);
      }
    }
    
    if (!fsuData.value) {
      console.log('创建空的FSU DTO对象');
      // 如果没有FSU配置，创建一个空的DTO对象
      fsuData.value = {
        siteWebMuId: monitorUnitId,
        fsuId: '',
        fsuPort: 0,
        roomId: 0,
        type76DeviceName: '',
        roomName: '',
        fsuType: 0,
        siteId: '',
        siteName: '',
        ftpUser: '',
        ftpPwd: '',
        loginUser: '',
        loginPwd: '',
        enableACL: false,
        platFormNo: 0,
        scip: '',
        scPort: 0,
        scurlSuffix: '',
        scipBak: '',
        scSwitchMode: 1,
        platFormName: '',
        lastPlatFormName: '',
        scip1: '',
        scPort1: 0,
        scurlSuffix1: '',
        scipBak1: '',
        scSwitchMode1: 1,
        platFormName1: '',
        lastPlatFormName1: '',
        type76DeviceID: '',
        deviceNum: 0,
        cmccDeviceInitDTOList: []
      };
    }
    
    console.log('最终 fsuData.value:', fsuData.value);
    
    // 获取设备列表（从OMC系统）
    console.log('调用 getMonitorUnitDevices API...');
    const deviceRes = await getMonitorUnitDevices(monitorUnitId);
    console.log('设备列表响应:', deviceRes);
    
    if (deviceRes && deviceRes.code === 0) {
      deviceList.value = deviceRes.data || [];
    } else {
      deviceList.value = [];
    }
    
    console.log('最终设备列表:', deviceList.value);
    
    // 构建设备表格数据
    buildDeviceTableData();
    
  } catch (e) {
    ElMessage.error("获取FSU配置信息失败");
    console.error('加载FSU配置失败:', e);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadDictionaryData();
  loadMonitorUnitInfo();
  loadFsuWithDevices();
  
  // 添加调试信息
  console.log('组件已挂载，monitorUnitId:', monitorUnitId);
});
</script>

<style scoped>
/* 必填字段标识样式 */
.required-field .text-red-500 {
  color: #ef4444;
  font-weight: 500;
}

/* 验证错误时的边框样式 */
.border-red-300 {
  border-color: #fca5a5 !important;
  box-shadow: 0 0 0 1px #fca5a5 !important;
}

/* 错误状态下的输入框样式 */
.el-input.border-red-300 .el-input__wrapper {
  border-color: #fca5a5 !important;
  box-shadow: 0 0 0 1px #fca5a5 !important;
}

.el-input-number.border-red-300 .el-input__wrapper {
  border-color: #fca5a5 !important;
  box-shadow: 0 0 0 1px #fca5a5 !important;
}

.el-select.border-red-300 .el-input__wrapper {
  border-color: #fca5a5 !important;
  box-shadow: 0 0 0 1px #fca5a5 !important;
}

/* 悬停时的效果 */
.el-input.border-red-300:hover .el-input__wrapper,
.el-input-number.border-red-300:hover .el-input__wrapper,
.el-select.border-red-300:hover .el-input__wrapper {
  border-color: #f87171 !important;
  box-shadow: 0 0 0 1px #f87171 !important;
}

/* 聚焦时的效果 */
.el-input.border-red-300.is-focus .el-input__wrapper,
.el-input-number.border-red-300.is-focus .el-input__wrapper,
.el-select.border-red-300.is-focus .el-input__wrapper {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

/* 必填字段标题样式优化 */
.flex.items-center.mb-1 {
  align-items: center;
  margin-bottom: 4px;
}

.flex.items-center.mb-1 .text-red-500 {
  margin-left: 2px;
  font-size: 14px;
  line-height: 1;
}

/* 禁用状态下的样式 */
.el-select.is-disabled .el-input__wrapper,
.el-input-number.is-disabled .el-input__wrapper {
  border-color: #d1d5db !important;
  box-shadow: none !important;
}
</style>


