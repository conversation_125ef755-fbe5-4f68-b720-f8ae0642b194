<template>
  <div class="device-template-log">
    <!-- 搜索工具栏 -->
    <div class="search-toolbar">
      <div class="search-left">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[
            new Date(0, 0, 0, 0, 0, 0),
            new Date(0, 0, 0, 23, 59, 59)
          ]"
          class="date-picker"
        />
        <el-button type="primary" @click="getList">
          <el-icon class="mr-1"><Search /></el-icon>
          查询
        </el-button>
      </div>
      <div class="search-right">
        <el-input
          v-model="searchText"
          placeholder="输入关键词搜索..."
          clearable
          class="search-input"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 变更记录表格 -->
    <div class="table-container">
      <el-table-v2
        ref="tableRef"
        v-loading="loading"
        :columns="tableColumns"
        :data="filteredData"
        :width="tableWidth"
        :height="tableHeight"
        :row-height="40"
        :header-height="40"
        fixed
      />
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pageIndex"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick, h } from "vue";
import {
  ElMessage,
  ElIcon,
  ElPopover,
  ElInput,
  ElCheckbox,
  ElButton,
  ElOption,
  ElSelect
} from "element-plus";
import { Search, Filter } from "@element-plus/icons-vue";
import type { FunctionalComponent } from "vue";
import {
  getEquipmentTemplateOperationLog,
  getOperationTypes
} from "@/api/device-template";

// 日志数据接口
interface LogData {
  id: number;
  objectType: string;
  objectId: string;
  propertyName: string;
  oldValue: string | null;
  newValue: string;
  operationType: string;
  operationTime: string;
  userName: string;
  objectName: string | null;
}

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    value: any[];
    options: Array<{ label: string; value: any }>;
  };
}

interface Props {
  templateData?: any;
  tabIndex?: number;
  muCategory?: number;
  tableSearchText?: string;
  equipmentId?: string | number;
  buttonFlag?: boolean;
  isRootTemplate?: boolean;
}

interface Emits {
  (e: "refresh"): void;
  (e: "selectTab", data: { index: number }): void;
}

const props = withDefaults(defineProps<Props>(), {
  templateData: null,
  tabIndex: 0,
  muCategory: 0,
  tableSearchText: "",
  equipmentId: "",
  buttonFlag: false,
  isRootTemplate: false
});

const emit = defineEmits<Emits>();

// 状态变量
const loading = ref(false);
const tableRef = ref();
const tableData = ref<LogData[]>([]);
const dateRange = ref<[string, string] | null>(null);
const searchText = ref("");

// 分页相关
const pageIndex = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 操作类型选项
const operationTypeList = ref<Array<{ itemId: number; itemValue: string }>>([]);

// 过滤器状态
const filterState = ref<FilterState>({});

// 虚拟表格配置
const tableWidth = ref(1200);
const tableHeight = ref(500);

// 获取容器尺寸并更新表格尺寸
const updateTableSize = () => {
  nextTick(() => {
    // 查找表格容器
    const container = document.querySelector(
      ".device-template-log .table-container"
    ) as HTMLElement;
    if (container) {
      const rect = container.getBoundingClientRect();
      const newWidth = Math.max(800, rect.width - 4); // 减去边框宽度
      const newHeight = Math.max(400, Math.min(600, rect.height - 4)); // 限制最大高度为600px

      // 只有尺寸真正变化时才更新，避免无限循环
      if (
        Math.abs(tableWidth.value - newWidth) > 10 ||
        Math.abs(tableHeight.value - newHeight) > 10
      ) {
        console.log("更新表格尺寸:", {
          oldWidth: tableWidth.value,
          newWidth,
          oldHeight: tableHeight.value,
          newHeight
        });
        tableWidth.value = newWidth;
        tableHeight.value = newHeight;
      }
    }
  });
};

// 监听窗口尺寸变化
let resizeObserver: ResizeObserver | null = null;
let resizeTimer: NodeJS.Timeout | null = null;

// 防抖的尺寸更新函数
const debouncedUpdateTableSize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
  resizeTimer = setTimeout(() => {
    updateTableSize();
  }, 100); // 100ms防抖
};

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    propertyName: {
      value: [],
      options: []
    },
    oldValue: {
      value: [],
      options: []
    },
    newValue: {
      value: [],
      options: []
    },
    operationType: {
      value: [],
      options: []
    },
    operationTime: {
      value: [],
      options: []
    },
    userName: {
      value: [],
      options: []
    }
  };
};

// 更新动态选项
const updateDynamicOptions = () => {
  if (tableData.value.length === 0) return;

  // 更新操作模块选项
  const propertyNames = [
    ...new Set(tableData.value.map(item => item.propertyName).filter(Boolean))
  ];
  filterState.value.propertyName.options = propertyNames.map(name => ({
    label: name,
    value: name
  }));

  // 更新操作前的值选项
  const oldValues = [
    ...new Set(
      tableData.value
        .map(item => item.oldValue)
        .filter(value => value !== null && value !== "")
    )
  ];
  filterState.value.oldValue.options = oldValues.map(value => ({
    label: value,
    value
  }));

  // 更新操作后的值选项
  const newValues = [
    ...new Set(tableData.value.map(item => item.newValue).filter(Boolean))
  ];
  filterState.value.newValue.options = newValues.map(value => ({
    label: value,
    value
  }));

  // 更新操作时间选项（按日期分组）
  const dates = [
    ...new Set(
      tableData.value
        .map(item => item.operationTime?.split(" ")[0])
        .filter(Boolean)
    )
  ];
  filterState.value.operationTime.options = dates.map(date => ({
    label: date,
    value: date
  }));

  // 更新操作人选项
  const users = [
    ...new Set(tableData.value.map(item => item.userName).filter(Boolean))
  ];
  filterState.value.userName.options = users.map(user => ({
    label: user,
    value: user
  }));

  // 更新操作类型选项
  if (operationTypeList.value.length > 0) {
    filterState.value.operationType.options = operationTypeList.value.map(
      item => ({
        label: item.itemValue,
        value: item.itemValue
      })
    );
  } else {
    const operationTypes = [
      ...new Set(
        tableData.value.map(item => item.operationType).filter(Boolean)
      )
    ];
    filterState.value.operationType.options = operationTypes.map(type => ({
      label: type,
      value: type
    }));
  }
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: any) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].value = [];
      }
    };

    return h("div", { class: "flex items-center justify-center" }, [
      h("span", { class: "mr-2 text-xs" }, props.column.title),
      h(
        ElPopover,
        {
          ref: popoverRef,
          trigger: "click",
          width: 250
        },
        {
          default: () =>
            h("div", { class: "filter-wrapper" }, [
              h("div", { class: "filter-group" }, [
                h(
                  ElSelect,
                  {
                    modelValue: filterState.value[filterKey]?.value || [],
                    "onUpdate:modelValue": (value: any[]) => {
                      if (filterState.value[filterKey]) {
                        filterState.value[filterKey].value = value;
                      }
                    },
                    placeholder: "选择过滤条件",
                    size: "small",
                    multiple: true,
                    collapseTags: true,
                    filterable: true,
                    clearable: true,
                    style: { width: "100%" }
                  },
                  {
                    default: () =>
                      (filterState.value[filterKey]?.options || [])
                        .filter(
                          option =>
                            option &&
                            option.value !== undefined &&
                            option.label !== undefined
                        )
                        .map((option: any) =>
                          h(ElOption, {
                            key: option.value,
                            label: option.label,
                            value: option.value
                          })
                        )
                  }
                )
              ]),
              h("div", { class: "el-table-v2__demo-filter" }, [
                h(ElButton, { text: true, onClick: onFilter }, () => "确认"),
                h(ElButton, { text: true, onClick: onReset }, () => "重置")
              ])
            ]),
          reference: () =>
            h(ElIcon, { class: "cursor-pointer" }, () => [h(Filter)])
        }
      )
    ]);
  };
};

// 创建只读显示单元格
const createReadOnlyCell = (dataKey: string) => {
  return ({ rowData }: { rowData: LogData }) => {
    const displayValue = rowData[dataKey as keyof LogData];
    const showValue =
      displayValue === null || displayValue === undefined || displayValue === ""
        ? "-"
        : String(displayValue);

    return h(
      "div",
      {
        class: "cell-content",
        title: showValue
      },
      showValue
    );
  };
};

// 创建时间单元格
const createTimeCell = (dataKey: string) => {
  return ({ rowData }: { rowData: LogData }) => {
    const timeValue = rowData[dataKey as keyof LogData] as string;

    return h(
      "div",
      {
        class: "cell-content",
        title: timeValue
      },
      timeValue || "-"
    );
  };
};

// 创建操作类型单元格
const createOperationTypeCell = (dataKey: string) => {
  return ({ rowData }: { rowData: LogData }) => {
    const operationType = rowData[dataKey as keyof LogData] as string;

    const getTypeClass = (type: string) => {
      switch (type) {
        case "新增":
        case "创建":
          return "text-green-600";
        case "修改":
        case "更新":
          return "text-blue-600";
        case "删除":
          return "text-red-600";
        default:
          return "text-gray-600";
      }
    };

    return h(
      "div",
      {
        class: "cell-content"
      },
      [
        h(
          "span",
          {
            class: getTypeClass(operationType),
            title: operationType
          },
          operationType || "-"
        )
      ]
    );
  };
};

// 表格列配置
const tableColumns = computed(() => [
  {
    key: "propertyName",
    dataKey: "propertyName",
    title: "操作模块",
    width: 150,
    fixed: "left",
    cellRenderer: createReadOnlyCell("propertyName"),
    headerCellRenderer: createFilterHeader({ dataKey: "propertyName" })
  },
  {
    key: "oldValue",
    dataKey: "oldValue",
    title: "操作前的值",
    width: 200,
    cellRenderer: createReadOnlyCell("oldValue"),
    headerCellRenderer: createFilterHeader({ dataKey: "oldValue" })
  },
  {
    key: "newValue",
    dataKey: "newValue",
    title: "操作后的值",
    width: 300,
    cellRenderer: createReadOnlyCell("newValue"),
    headerCellRenderer: createFilterHeader({ dataKey: "newValue" })
  },
  {
    key: "operationType",
    dataKey: "operationType",
    title: "操作类型",
    width: 100,
    cellRenderer: createOperationTypeCell("operationType"),
    headerCellRenderer: createFilterHeader({ dataKey: "operationType" })
  },
  {
    key: "operationTime",
    dataKey: "operationTime",
    title: "操作时间",
    width: 180,
    cellRenderer: createTimeCell("operationTime"),
    headerCellRenderer: createFilterHeader({ dataKey: "operationTime" })
  },
  {
    key: "userName",
    dataKey: "userName",
    title: "操作人",
    width: 120,
    cellRenderer: createReadOnlyCell("userName"),
    headerCellRenderer: createFilterHeader({ dataKey: "userName" })
  }
]);

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value;

  // 首先应用主页面搜索文本过滤
  if (props.tableSearchText && props.tableSearchText.trim() !== "") {
    const searchTerm = props.tableSearchText.toLowerCase();
    data = data.filter(item => {
      return (
        (item.propertyName &&
          item.propertyName.toLowerCase().includes(searchTerm)) ||
        (item.oldValue && item.oldValue.toLowerCase().includes(searchTerm)) ||
        (item.newValue && item.newValue.toLowerCase().includes(searchTerm)) ||
        (item.operationType &&
          item.operationType.toLowerCase().includes(searchTerm)) ||
        (item.userName && item.userName.toLowerCase().includes(searchTerm)) ||
        (item.operationTime &&
          item.operationTime.toLowerCase().includes(searchTerm))
      );
    });
  }

  // 然后应用本地搜索文本过滤
  if (searchText.value && searchText.value.trim() !== "") {
    const searchTerm = searchText.value.toLowerCase();
    data = data.filter(item => {
      return (
        (item.propertyName &&
          item.propertyName.toLowerCase().includes(searchTerm)) ||
        (item.oldValue && item.oldValue.toLowerCase().includes(searchTerm)) ||
        (item.newValue && item.newValue.toLowerCase().includes(searchTerm)) ||
        (item.operationType &&
          item.operationType.toLowerCase().includes(searchTerm)) ||
        (item.userName && item.userName.toLowerCase().includes(searchTerm)) ||
        (item.operationTime &&
          item.operationTime.toLowerCase().includes(searchTerm))
      );
    });
  }

  // 应用列过滤器
  Object.entries(filterState.value).forEach(([key, filter]) => {
    if (filter.value && filter.value.length > 0) {
      data = data.filter(item => {
        const itemValue = item[key as keyof LogData];

        // 特殊处理操作时间字段（按日期匹配）
        if (key === "operationTime" && itemValue) {
          const itemDate = itemValue.toString().split(" ")[0];
          return filter.value.includes(itemDate);
        }

        // 处理空值
        if (itemValue === null || itemValue === undefined || itemValue === "") {
          return (
            filter.value.includes("-") ||
            filter.value.includes("null") ||
            filter.value.includes("")
          );
        }

        return filter.value.includes(itemValue);
      });
    }
  });

  return data;
});

// 加载操作类型列表
const loadOperationTypes = async () => {
  try {
    const res = await getOperationTypes();
    if (res.code === 0) {
      operationTypeList.value = res.data || [];
    }
  } catch (error) {
    console.error("获取操作类型列表失败:", error);
  }
};

// 获取变更记录列表
const getList = async () => {
  if (!props.templateData?.id) {
    console.warn("templateId为空，无法获取变更记录");
    return;
  }

  // 如果正在加载中，避免重复请求
  if (loading.value) {
    console.warn("正在加载中，跳过重复请求");
    return;
  }

  loading.value = true;
  try {
    console.log("获取变更记录，参数:", {
      templateId: props.templateData.id,
      current: pageIndex.value,
      size: pageSize.value,
      dateRange: dateRange.value
    });

    // 准备时间参数
    let startTime: string | null = null;
    let endTime: string | null = null;
    if (dateRange.value && dateRange.value.length === 2) {
      startTime = dateRange.value[0];
      endTime = dateRange.value[1];
    }

    // 调用API获取变更记录
    const result = await getEquipmentTemplateOperationLog(
      props.templateData.id,
      pageIndex.value,
      pageSize.value,
      startTime,
      endTime
    );

    console.log("变更记录API响应:", result);

    // 根据后端响应格式，code: 0表示成功状态
    if (result.code === 0 && result.data) {
      // 处理数据，确保字段名称匹配
      const processedData = (result.data.records || []).map(
        (item: any, index: number) => ({
          id: index + 1, // 使用索引作为ID
          objectType: item.objectType || "设备模板",
          objectId: item.objectId || "",
          propertyName: item.propertyName || "未知模块",
          oldValue: item.oldValue || "",
          newValue: item.newValue || "",
          operationType: item.operationType || "未知操作",
          operationTime: item.operationTime || "",
          userName: item.userName || "未知用户",
          objectName: item.objectName
        })
      );

      tableData.value = processedData;
      total.value = result.data.total || 0;
      pageIndex.value = result.data.current || 1;
      pageSize.value = result.data.size || 20;

      console.log(`成功获取${processedData.length}条变更记录`);

      // 数据加载后更新过滤器选项
      updateDynamicOptions();
    } else {
      ElMessage.error(
        "获取变更记录失败: " + (result.msg || result.message || "未知错误")
      );
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error("获取变更记录失败:", error);
    ElMessage.error("获取变更记录失败，请检查网络连接");
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  // 搜索功能通过computed属性filteredData自动处理
  console.log("搜索关键词:", searchText.value);
};

// 统一的数据加载逻辑
const checkAndLoadData = (source: string = "") => {
  const { templateData, tabIndex } = props;
  console.log(`checkAndLoadData 被调用 - 来源: ${source}`, {
    templateData: templateData?.id,
    tabIndex
  });

  if (templateData?.id && tabIndex === 4) {
    // 变更记录tab的索引是4
    console.log("条件满足，开始获取变更记录");
    getList();
  }
};

// 监听模板数据变化
watch(
  () => props.templateData,
  newData => {
    if (newData && newData.id) {
      if (props.tabIndex === 4) {
        // 变更记录tab的索引是4
        checkAndLoadData("模板数据变化");
      }
    }
  },
  { immediate: true }
);

// 监听标签页变化
watch(
  () => props.tabIndex,
  newIndex => {
    if (newIndex === 4 && props.templateData?.id) {
      // 变更记录tab的索引是4
      checkAndLoadData("标签页变化");
    }
  }
);

// 组件挂载
onMounted(() => {
  console.log("DeviceTemplateLog组件挂载:", {
    templateData: props.templateData,
    tabIndex: props.tabIndex
  });

  // 初始化过滤器状态
  initFilterState();

  // 加载操作类型列表
  loadOperationTypes();

  // 初始化表格尺寸
  updateTableSize();

  // 设置 ResizeObserver 监听容器尺寸变化
  const container = document.querySelector(
    ".device-template-log"
  ) as HTMLElement;
  if (container && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(entries => {
      // 检查是否是真正的尺寸变化
      const entry = entries[0];
      if (entry && entry.contentRect) {
        debouncedUpdateTableSize();
      }
    });
    resizeObserver.observe(container);
  }

  // 监听窗口尺寸变化作为备选方案
  window.addEventListener("resize", debouncedUpdateTableSize);

  // 组件挂载时检查是否需要加载数据
  checkAndLoadData("组件挂载");
});

// 组件卸载时清理监听器
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  if (resizeTimer) {
    clearTimeout(resizeTimer);
    resizeTimer = null;
  }
  window.removeEventListener("resize", debouncedUpdateTableSize);
});
</script>

<style scoped>
.device-template-log {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: #fff;
}

.search-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-right {
  display: flex;
  align-items: center;
}

.date-picker {
  width: 350px;
}

.search-input {
  width: 250px;
}

.table-container {
  flex: 1;
  min-height: 400px;
  max-height: calc(100vh - 200px);
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.cell-content {
  padding: 4px 8px;
  word-break: break-all;
  white-space: pre-wrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

/* 操作类型颜色样式 */
.text-green-600 {
  color: #16a085;
  font-weight: 500;
}

.text-blue-600 {
  color: #3498db;
  font-weight: 500;
}

.text-red-600 {
  color: #e74c3c;
  font-weight: 500;
}

.text-gray-600 {
  color: #6c757d;
  font-weight: 500;
}

/* 全局表格样式 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-left {
    justify-content: center;
  }

  .search-right {
    justify-content: center;
  }

  .date-picker {
    width: 100%;
  }

  .search-input {
    width: 100%;
  }
}
</style>
