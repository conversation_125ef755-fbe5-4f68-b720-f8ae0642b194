<template>
  <div class="device-template-control">
    <!-- 搜索框 -->
    <div class="px-4 pt-3 pb-4">
      <el-input
        v-model="searchText"
        placeholder="搜索控制名称、控制ID、标准化ID..."
        clearable
        :prefix-icon="Search"
        size="default"
      />
    </div>

    <!-- 表格区域 -->
    <div class="control-table-container flex-1 overflow-hidden" :class="{ dragging: props.isDragging }">
      <el-table-v2
        ref="tableRef"
        v-loading="loading"
        :columns="tableColumns"
        :data="filteredData"
        :width="tableWidth"
        :height="tableHeight"
        :row-height="40"
        :header-height="40"
        :row-class="getRowClass"
        :row-event-handlers="rowEventHandlers"
        fixed
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, h } from "vue";
import {
  ElIcon,
  ElPopover,
  ElSelect,
  ElO<PERSON>,
  ElButton,
  ElMessage
} from "element-plus";
import { Filter, Search } from "@element-plus/icons-vue";
import {
  getEquipmentTemplateControl,
  getSignalListByTempId,
  getDataitems,
  type DataDictionaryItem,
  type ControlInfo as BaseControlInfo
} from "@/api/device-template";

// 扩展控制信息接口
export interface ControlInfo extends BaseControlInfo {
  // 添加控制特有字段
  controlSeverity?: number;
  cmdToken?: string;
  timeOut?: number;
  retry?: number;
  commandType?: number;
  maxValue?: number;
  minValue?: number;
  defaultValue?: any;
  baseTypeName?: string;
  baseTypeId?: number | null;
  controlMeaningsList?: any[]; // 参数含义列表
  [key: string]: any; // 允许动态属性
}

interface Props {
  templateData?: any;
  tabIndex?: number;
  muCategory?: number;
  tableSearchText?: string;
  equipmentId?: string | number;
  buttonFlag?: boolean;
  isRootTemplate?: boolean;
  dragData?: any;
  isDragging?: boolean;
}

interface Emits {
  (e: "refresh"): void;
  (e: "selectTab", data: { index: number }): void;
  (e: "drop-standard", data: { targetRow: any; standardData: any; type: string }): void;
  (e: "filter-standard-id", standardIdPrefix: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  templateData: null,
  tabIndex: 0,
  muCategory: 0,
  tableSearchText: "",
  equipmentId: "",
  buttonFlag: false,
  isRootTemplate: false,
  dragData: null,
  isDragging: false
});

const emit = defineEmits<Emits>();

// 过滤器状态接口
interface FilterState {
  [key: string]: {
    value: any[];
    options: Array<{ label: string; value: any }>;
  };
}

// 状态变量
const loading = ref(false);
const tableData = ref<ControlInfo[]>([]);
const searchText = ref("");

// 表格选中相关
const selectedRowIndexes = ref<number[]>([]);
const selectedRows = ref<ControlInfo[]>([]);

// 拖拽目标相关
const dropTargetRowIndex = ref<number>(-1);
const isDropTarget = ref(false);

// 下拉选项数据
const controlSeverityList = ref<DataDictionaryItem[]>([]);
const controlCategoryList = ref<DataDictionaryItem[]>([]);
const commandTypeList = ref<DataDictionaryItem[]>([]);
const controlTypeList = ref<DataDictionaryItem[]>([]);
const dataTypeList = ref<DataDictionaryItem[]>([]);
const signalList = ref<any[]>([]);

// 过滤器状态
const filterState = ref<FilterState>({});

/**
 * 更新表格行数据
 * @param apiType API类型 (signal | event | control)
 * @param rowData 行数据
 */
const updateTableRow = async (apiType: string, rowData: any) => {
  const { http } = await import("@/utils/http");
  return http.request<any>(
    "put",
    `/api/thing/south-omc-siteweb/${apiType}/update`,
    { data: rowData }
  );
};

// 创建只读单元格渲染器
const createReadOnlyCell = (column: any) => {
  return ({ rowData, rowIndex }: any) => {
    const fieldKey = column.dataKey;
    let value = rowData[fieldKey];

    // 获取显示值（处理字典值的显示）
    const getDisplayValue = () => {
      switch (fieldKey) {
        case "controlCategory":
          const categoryOption = controlCategoryList.value.find(
            item => item.itemId === value
          );
          return categoryOption ? categoryOption.itemValue : value || "";
        case "controlSeverity":
          const severityOption = controlSeverityList.value.find(
            item => item.itemId === value
          );
          return severityOption ? severityOption.itemValue : value || "";
        case "commandType":
          const commandOption = commandTypeList.value.find(
            item => item.itemId === value
          );
          return commandOption ? commandOption.itemValue : value || "";
        case "controlType":
          const controlOption = controlTypeList.value.find(
            item => item.itemId === value
          );
          return controlOption ? controlOption.itemValue : value || "";
        case "dataType":
          const dataOption = dataTypeList.value.find(
            item => item.itemId === value
          );
          return dataOption ? dataOption.itemValue : value || "";
        case "signalId":
          const signalOption = signalList.value.find(
            item => item.id === value
          );
          return signalOption ? signalOption.name : value || "";
        case "enable":
        case "visible":
          return value ? "是" : "否";
        case "controlMeaningsList":
          if (Array.isArray(value) && value.length > 0) {
            // 显示参数含义列表，格式：序号1/序号2/序号3
            const displayValues = value
              .map(item => {
                // 优先使用 parameterValue，如果不存在或为null/undefined则使用 stateValue
                const val =
                  item.parameterValue !== null &&
                  item.parameterValue !== undefined
                    ? item.parameterValue
                    : item.stateValue;
                return val !== null && val !== undefined ? val.toString() : "";
              })
              .filter(val => val !== "");

            return displayValues.join("/");
          }
          return "";
        default:
          return value || "";
      }
    };

    const displayValue = getDisplayValue();

    // 特殊处理标准化ID列，添加删除按钮
    if (fieldKey === 'description') {
      return h(
        "div",
        {
          class: "standard-id-cell",
          style: {
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            padding: "4px 8px",
            minHeight: "24px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap"
          }
        },
        [
          h(
            "span",
            {
              style: {
                flex: 1,
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap"
              },
              title: displayValue
            },
            displayValue
          ),
          // 删除按钮（只有当有标准化ID时才显示）
          displayValue ? h(
            ElButton,
            {
              size: "small",
              type: "danger",
              text: true,
              icon: "Delete",
              style: {
                marginLeft: "8px",
                padding: "2px 4px",
                minWidth: "auto"
              },
              onClick: (e: Event) => {
                e.stopPropagation();
                handleRemoveStandardId(rowData);
              }
            },
            () => "删除"
          ) : null
        ]
      );
    }

    return h(
      "div",
      {
        class: "read-only-cell"
      },
      displayValue
    );
  };
};

// 初始化过滤器状态
const initFilterState = () => {
  filterState.value = {
    controlName: {
      value: [],
      options: []
    },
    controlId: {
      value: [],
      options: []
    },
    displayIndex: {
      value: [],
      options: []
    },
    controlCategory: {
      value: [],
      options: []
    },
    controlSeverity: {
      value: [],
      options: []
    },
    cmdToken: {
      value: [],
      options: []
    },
    timeOut: {
      value: [],
      options: []
    },
    retry: {
      value: [],
      options: []
    },
    commandType: {
      value: [],
      options: []
    },
    controlType: {
      value: [],
      options: []
    },
    dataType: {
      value: [],
      options: []
    },
    maxValue: {
      value: [],
      options: []
    },
    minValue: {
      value: [],
      options: []
    },
    signalId: {
      value: [],
      options: []
    },
    defaultValue: {
      value: [],
      options: []
    },
    description: {
      value: [],
      options: []
    },
    enable: {
      value: [],
      options: []
    },
    visible: {
      value: [],
      options: []
    },
    moduleNo: {
      value: [],
      options: []
    },
    baseTypeName: {
      value: [],
      options: []
    },
    controlMeaningsList: {
      value: [],
      options: []
    }
  };
};

// 更新动态选项
const updateDynamicOptions = () => {
  if (tableData.value.length === 0) return;

  // 更新控制名称选项
  const controlNames = [
    ...new Set(tableData.value.map(item => item.controlName).filter(Boolean))
  ];
  filterState.value.controlName.options = controlNames.map(name => ({
    label: name,
    value: name
  }));

  // 更新控制ID选项
  const controlIds = [
    ...new Set(
      tableData.value
        .map(item => item.controlId)
        .filter(id => id !== null && id !== undefined)
    )
  ];
  filterState.value.controlId.options = controlIds.map(id => ({
    label: id.toString(),
    value: id
  }));

  // 更新显示顺序选项
  const displayIndexes = [
    ...new Set(
      tableData.value
        .map(item => item.displayIndex)
        .filter(index => index !== null && index !== undefined)
    )
  ];
  filterState.value.displayIndex.options = displayIndexes.map(index => ({
    label: index.toString(),
    value: index
  }));

  // 更新命令字符串选项
  const cmdTokens = [
    ...new Set(tableData.value.map(item => item.cmdToken).filter(Boolean))
  ];
  filterState.value.cmdToken.options = cmdTokens.map(token => ({
    label: token,
    value: token
  }));

  // 更新说明选项
  const descriptions = [
    ...new Set(tableData.value.map(item => item.description).filter(Boolean))
  ];
  filterState.value.description.options = descriptions.map(desc => ({
    label: desc,
    value: desc
  }));

  // 更新基类名称选项
  const baseTypeNames = [
    ...new Set(tableData.value.map(item => item.baseTypeName).filter(Boolean))
  ];
  filterState.value.baseTypeName.options = baseTypeNames.map(name => ({
    label: name,
    value: name
  }));

  // 更新参数含义选项（包含空值选项）
  const controlMeaningsValues = [
    ...new Set(
      tableData.value
        .map(item => {
          if (
            item.controlMeaningsList &&
            Array.isArray(item.controlMeaningsList) &&
            item.controlMeaningsList.length > 0
          ) {
            const values = item.controlMeaningsList
              .map((meaning: any) => {
                const val =
                  meaning.parameterValue !== null &&
                  meaning.parameterValue !== undefined
                    ? meaning.parameterValue
                    : meaning.stateValue;
                return val !== null && val !== undefined ? val : "";
              })
              .filter(val => val !== "");
            return values.length > 0 ? values.join("/") : "(空)";
          }
          return "(空)"; // 空值显示为 "(空)"
        })
        .filter(value => value !== null && value !== undefined)
    )
  ];
  filterState.value.controlMeaningsList.options = controlMeaningsValues.map(
    value => ({
      label: value === "(空)" ? "未配置" : value,
      value: value === "(空)" ? "" : value
    })
  );

  // 从字典更新选项
  if (controlCategoryList.value.length > 0) {
    filterState.value.controlCategory.options = controlCategoryList.value.map(
      item => ({
        label: item.itemValue,
        value: item.itemId
      })
    );
  }

  if (controlSeverityList.value.length > 0) {
    filterState.value.controlSeverity.options = controlSeverityList.value.map(
      item => ({
        label: item.itemValue,
        value: item.itemId
      })
    );
  }

  if (commandTypeList.value.length > 0) {
    filterState.value.commandType.options = commandTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (controlTypeList.value.length > 0) {
    filterState.value.controlType.options = controlTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (dataTypeList.value.length > 0) {
    filterState.value.dataType.options = dataTypeList.value.map(item => ({
      label: item.itemValue,
      value: item.itemId
    }));
  }

  if (signalList.value.length > 0) {
    filterState.value.signalId.options = signalList.value.map(item => ({
      label: item.name,
      value: item.id
    }));
  }

  // 设置布尔值字段的过滤选项
  filterState.value.enable.options = [
    { label: "是", value: true },
    { label: "否", value: false }
  ];

  filterState.value.visible.options = [
    { label: "是", value: true },
    { label: "否", value: false }
  ];
};

// 创建过滤器头部组件
const createFilterHeader = (column: any) => {
  const filterKey = column.dataKey;

  return (props: any) => {
    const popoverRef = ref();

    const onFilter = () => {
      popoverRef.value?.hide();
    };

    const onReset = () => {
      if (filterState.value[filterKey]) {
        filterState.value[filterKey].value = [];
      }
    };

    return h("div", { class: "flex items-center justify-center" }, [
      h("span", { class: "mr-2 text-xs" }, props.column.title),
      h(
        ElPopover,
        {
          ref: popoverRef,
          trigger: "click",
          width: 250
        },
        {
          default: () =>
            h("div", { class: "filter-wrapper" }, [
              h("div", { class: "filter-group" }, [
                h(
                  ElSelect,
                  {
                    modelValue: filterState.value[filterKey]?.value || [],
                    "onUpdate:modelValue": (value: any[]) => {
                      if (filterState.value[filterKey]) {
                        filterState.value[filterKey].value = value;
                      }
                    },
                    placeholder: "选择过滤条件",
                    size: "small",
                    multiple: true,
                    collapseTags: true,
                    filterable: true,
                    clearable: true,
                    style: { width: "100%" }
                  },
                  {
                    default: () =>
                      (filterState.value[filterKey]?.options || []).map(
                        (option: any) =>
                          h(ElOption, {
                            key: option.value,
                            label: option.label,
                            value: option.value
                          })
                      )
                  }
                )
              ]),
              h("div", { class: "el-table-v2__demo-filter" }, [
                h(ElButton, { text: true, onClick: onFilter }, () => "确认"),
                h(ElButton, { text: true, onClick: onReset }, () => "重置")
              ])
            ]),
          reference: () =>
            h(ElIcon, { class: "cursor-pointer" }, () => [h(Filter)])
        }
      )
    ]);
  };
};

// 过滤后的数据
const filteredData = computed(() => {
  let data = tableData.value;

  // 应用搜索文本过滤
  const currentSearchText = searchText.value || props.tableSearchText || '';
  if (currentSearchText.trim() !== "") {
    const searchValue = currentSearchText.toLowerCase();
    data = data.filter(item => {
      // 基本字段搜索
      const basicMatch =
        item.controlName?.toLowerCase().includes(searchValue) ||
        item.controlId?.toString().includes(searchValue) ||
        item.description?.toLowerCase().includes(searchValue);

      // 参数含义搜索
      let meaningMatch = false;
      if (item.controlMeaningsList && Array.isArray(item.controlMeaningsList)) {
        meaningMatch = item.controlMeaningsList.some((meaning: any) => {
          const meaningText = meaning.meanings
            ?.toLowerCase()
            .includes(searchText);
          const parameterValue =
            meaning.parameterValue !== null &&
            meaning.parameterValue !== undefined
              ? meaning.parameterValue.toString().includes(searchText)
              : false;
          const stateValue =
            meaning.stateValue !== null && meaning.stateValue !== undefined
              ? meaning.stateValue.toString().includes(searchText)
              : false;
          return meaningText || parameterValue || stateValue;
        });
      }

      return basicMatch || meaningMatch;
    });
  }

  return data;
});

// 虚拟表格配置
const tableWidth = ref(1800);
const tableHeight = ref(500);

// 获取容器尺寸并更新表格尺寸
const updateTableSize = () => {
  // 查找 el-tabs__content 容器
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent) {
    const rect = tabsContent.getBoundingClientRect();
    tableWidth.value = rect.width - 32; // 减去内边距
    tableHeight.value = rect.height - 32; // 减去内边距
  }
};

// 监听窗口尺寸变化
let resizeObserver: ResizeObserver | null = null;

// 表格列配置
const tableColumns: any[] = [
  {
    key: "controlName",
    title: "名称",
    dataKey: "controlName",
    width: 155,
    cellRenderer: createReadOnlyCell({ dataKey: "controlName" }),
    headerCellRenderer: createFilterHeader({ dataKey: "controlName" })
  },
  {
    key: "controlId",
    title: "控制ID",
    dataKey: "controlId",
    width: 85,
    cellRenderer: createReadOnlyCell({ dataKey: "controlId" }),
    headerCellRenderer: createFilterHeader({ dataKey: "controlId" })
  },
  {
    key: "description",
    title: "标准化ID",
    dataKey: "description",
    width: 250,
    cellRenderer: createReadOnlyCell({ dataKey: "description" }),
    headerCellRenderer: createFilterHeader({ dataKey: "description" })
  },
  {
    key: "displayIndex",
    title: "显示顺序",
    dataKey: "displayIndex",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "displayIndex" }),
    headerCellRenderer: createFilterHeader({ dataKey: "displayIndex" })
  },
  {
    key: "controlCategory",
    title: "命令种类",
    dataKey: "controlCategory",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "controlCategory" }),
    headerCellRenderer: createFilterHeader({ dataKey: "controlCategory" })
  },
  {
    key: "controlSeverity",
    title: "重要度",
    dataKey: "controlSeverity",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "controlSeverity" }),
    headerCellRenderer: createFilterHeader({ dataKey: "controlSeverity" })
  },
  {
    key: "cmdToken",
    title: "命令字符串",
    dataKey: "cmdToken",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "cmdToken" }),
    headerCellRenderer: createFilterHeader({ dataKey: "cmdToken" })
  },
  {
    key: "timeOut",
    title: "超时(秒)",
    dataKey: "timeOut",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "timeOut" }),
    headerCellRenderer: createFilterHeader({ dataKey: "timeOut" })
  },
  {
    key: "retry",
    title: "重试",
    dataKey: "retry",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "retry" }),
    headerCellRenderer: createFilterHeader({ dataKey: "retry" })
  },
  {
    key: "commandType",
    title: "控制命令类型",
    dataKey: "commandType",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "commandType" }),
    headerCellRenderer: createFilterHeader({ dataKey: "commandType" })
  },
  {
    key: "controlType",
    title: "控件分类",
    dataKey: "controlType",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "controlType" }),
    headerCellRenderer: createFilterHeader({ dataKey: "controlType" })
  },
  {
    key: "dataType",
    title: "数据类型",
    dataKey: "dataType",
    width: 120,
    cellRenderer: createReadOnlyCell({ dataKey: "dataType" }),
    headerCellRenderer: createFilterHeader({ dataKey: "dataType" })
  },
  {
    key: "maxValue",
    title: "参数上限",
    dataKey: "maxValue",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "maxValue" }),
    headerCellRenderer: createFilterHeader({ dataKey: "maxValue" })
  },
  {
    key: "minValue",
    title: "参数下限",
    dataKey: "minValue",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "minValue" }),
    headerCellRenderer: createFilterHeader({ dataKey: "minValue" })
  },
  {
    key: "signalId",
    title: "关联信号",
    dataKey: "signalId",
    width: 200,
    cellRenderer: createReadOnlyCell({ dataKey: "signalId" }),
    headerCellRenderer: createFilterHeader({ dataKey: "signalId" })
  },
  {
    key: "controlMeaningsList",
    title: "参数含义",
    dataKey: "controlMeaningsList",
    width: 150,
    cellRenderer: createReadOnlyCell({ dataKey: "controlMeaningsList" }),
    headerCellRenderer: createFilterHeader({ dataKey: "controlMeaningsList" })
  },
  {
    key: "defaultValue",
    title: "默认值",
    dataKey: "defaultValue",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "defaultValue" }),
    headerCellRenderer: createFilterHeader({ dataKey: "defaultValue" })
  },
  {
    key: "enable",
    title: "有效",
    dataKey: "enable",
    width: 100,
    align: "center",
    cellRenderer: createReadOnlyCell({ dataKey: "enable" }),
    headerCellRenderer: createFilterHeader({ dataKey: "enable" })
  },
  {
    key: "visible",
    title: "可见",
    dataKey: "visible",
    width: 100,
    align: "center",
    cellRenderer: createReadOnlyCell({ dataKey: "visible" }),
    headerCellRenderer: createFilterHeader({ dataKey: "visible" })
  },
  {
    key: "moduleNo",
    title: "所属模块",
    dataKey: "moduleNo",
    width: 100,
    cellRenderer: createReadOnlyCell({ dataKey: "moduleNo" }),
    headerCellRenderer: createFilterHeader({ dataKey: "moduleNo" })
  },
  {
    key: "baseTypeName",
    title: "基类控制",
    dataKey: "baseTypeName",
    width: 200,
    cellRenderer: createReadOnlyCell({ dataKey: "baseTypeName" }),
    headerCellRenderer: createFilterHeader({ dataKey: "baseTypeName" })
  }
];

// 组件初始化
onMounted(() => {
  // 初始化过滤器状态
  initFilterState();

  initializeDictionaryData();

  // 初始化表格尺寸
  updateTableSize();

  // 设置 ResizeObserver 监听容器尺寸变化
  const tabsContent = document.querySelector(
    ".el-tabs__content"
  ) as HTMLElement;
  if (tabsContent && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      updateTableSize();
    });
    resizeObserver.observe(tabsContent);
  }

  // 监听窗口尺寸变化作为备选方案
  window.addEventListener("resize", updateTableSize);
});

// 加载信号列表
const loadSignalList = async () => {
  if (!props.templateData?.id) return;

  try {
    const res = await getSignalListByTempId(props.templateData.id);
    if (res.code === 0 && res.data) {
      signalList.value = res.data.map((signal: any) => ({
        id: parseInt(signal.signalId), // 确保ID是数字类型
        name: signal.signalName || "" // 确保名称是字符串类型
      }));
    }
  } catch (error) {
    console.error("获取信号列表失败:", error);
  }
};

// 处理标准化ID的Alt+点击事件
const handleStandardIdClick = (standardId: string) => {
  if (!standardId || typeof standardId !== 'string') {
    console.warn('标准化ID无效:', standardId);
    return;
  }

  // 提取标准化ID的前6位（根据生成逻辑，应该提取最后6位去掉后3位的部分）
  let prefix = '';
  if (standardId.length >= 9) {
    // 例如：001312001 -> 提取 001312（最后9位的前6位）
    const lastNineChars = standardId.slice(-9);
    prefix = lastNineChars.slice(0, 6);
  } else if (standardId.length >= 6) {
    // 如果长度介于6-8位，去掉最后3位
    prefix = standardId.slice(0, -3);
  } else {
    prefix = standardId; // 如果不足6位，使用全部
  }

  console.log(`🔍 Alt+点击控制标准化ID: ${standardId}，提取前缀: ${prefix}`);
  
  // 发送事件到父组件，用于过滤左侧标准化库
  emit('filter-standard-id', prefix);
  
  // 显示提示信息
  ElMessage({
    message: `已使用前缀 "${prefix}" 过滤左侧标准化库`,
    type: 'success',
    duration: 2000
  });
};

// 加载控制列表
const loadControlList = async () => {
  if (!props.templateData?.id) return;

  try {
    loading.value = true;

    // 确保信号列表已加载（避免时序问题）
    await loadSignalList();

    const res = await getEquipmentTemplateControl(props.templateData.id);
    if (res.code === 0 && res.data) {
      processControlData(res.data);
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取控制列表失败:", error);
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听模板数据变化
watch(
  () => props.templateData,
  newData => {
    if (newData && newData.template && props.tabIndex === 3) {
      loadControlList();
    }
  },
  { immediate: true }
);

// 初始化字典数据
const initializeDictionaryData = async () => {
  try {
    await Promise.all([
      loadControlSeverityList(),
      loadControlCategoryList(),
      loadCommandTypeList(),
      loadControlTypeList(),
      loadDataTypeList(),
      loadSignalList()
    ]);
  } catch (error) {
    console.error("初始化字典数据失败:", error);
  }
};

// 加载控制重要度列表
const loadControlSeverityList = async () => {
  try {
    const res = await getDataitems(28); // 重要度
    if (res.code === 0) {
      controlSeverityList.value = res.data;
    }
  } catch (error) {
    console.error("获取控制重要度列表失败:", error);
  }
};

// 加载控制命令种类列表
const loadControlCategoryList = async () => {
  try {
    const res = await getDataitems(31); // 命令种类
    if (res.code === 0) {
      controlCategoryList.value = res.data;
    }
  } catch (error) {
    console.error("获取控制命令种类列表失败:", error);
  }
};

// 加载控制命令类型列表
const loadCommandTypeList = async () => {
  try {
    const res = await getDataitems(32); // 控制命令类型
    if (res.code === 0) {
      commandTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取控制命令类型列表失败:", error);
  }
};

// 加载控件分类列表
const loadControlTypeList = async () => {
  try {
    const res = await getDataitems(68); // 控件分类
    if (res.code === 0) {
      controlTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取控件分类列表失败:", error);
  }
};

// 加载数据类型列表
const loadDataTypeList = async () => {
  try {
    const res = await getDataitems(70); // 数据类型
    if (res.code === 0) {
      dataTypeList.value = res.data;
    }
  } catch (error) {
    console.error("获取数据类型列表失败:", error);
  }
};

// 处理控制数据
const processControlData = (data: ControlInfo[]) => {
  data.forEach(item => {
    // 初始化 controlMeaningsList（如果不存在）
    if (!item.controlMeaningsList) {
      item.controlMeaningsList = [];
    }
  });

  tableData.value = data;

  // 数据加载后更新过滤器选项
  updateDynamicOptions();
};

// 行事件处理器
const rowEventHandlers = {
  onClick: ({ rowData, rowIndex, event }: any) => {
    console.log("Row clicked:", rowIndex, "isDragging:", props.isDragging, "dragData:", props.dragData);

    // 如果正在拖拽，不处理选中逻辑
    if (props.isDragging && props.dragData) {
      console.log("Dragging in progress, ignoring click");
      return;
    }

    // 单击时选中行
    const clickedRowIndex = filteredData.value.findIndex(
      item => item.controlId === rowData.controlId
    );
    if (clickedRowIndex !== -1) {
      // 如果按住Ctrl键，支持多选
      if (event.ctrlKey || event.metaKey) {
        const isSelected = selectedRowIndexes.value.includes(clickedRowIndex);
        if (isSelected) {
          // 取消选中
          selectedRowIndexes.value = selectedRowIndexes.value.filter(
            index => index !== clickedRowIndex
          );
        } else {
          // 添加选中
          selectedRowIndexes.value.push(clickedRowIndex);
        }
      } else {
        // 单选
        selectedRowIndexes.value = [clickedRowIndex];
      }

      // 更新选中的行数据
      selectedRows.value = selectedRowIndexes.value.map(
        index => filteredData.value[index]
      );
    }
  },
  onMouseup: ({ rowIndex }: any) => {
    console.log("Mouse up on row:", rowIndex, "isDragging:", props.isDragging, "dragData:", props.dragData);

    // 鼠标松开时处理拖放
    if (props.isDragging && props.dragData) {
      console.log("Triggering drop for row:", rowIndex);
      handleDrop(rowIndex);
    }
  },
  onMouseenter: ({ rowIndex }: any) => {
    handleRowMouseEnter(rowIndex);
  },
  onMouseleave: () => {
    handleRowMouseLeave();
  }
};

// 获取行样式类
const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  const isSelected = selectedRowIndexes.value.includes(rowIndex);
  const isDropTargetRow = dropTargetRowIndex.value === rowIndex && isDropTarget.value;

  let classes = "";
  if (isSelected) classes += "selected-row ";
  if (isDropTargetRow) classes += "drop-target-row ";

  return classes.trim();
};

// 拖拽目标相关方法
const handleRowMouseEnter = (rowIndex: number) => {
  console.log("Mouse enter row:", rowIndex, "isDragging:", props.isDragging, "dragData:", props.dragData);
  if (props.isDragging && props.dragData) {
    dropTargetRowIndex.value = rowIndex;
    isDropTarget.value = true;
    console.log("Set drop target row:", rowIndex);
  }
};

const handleRowMouseLeave = () => {
  console.log("Mouse leave, isDragging:", props.isDragging);
  if (props.isDragging) {
    dropTargetRowIndex.value = -1;
    isDropTarget.value = false;
    console.log("Clear drop target");
  }
};

// 处理拖放事件
const handleDrop = (rowIndex: number) => {
  console.log("handleDrop called:", { 
    rowIndex, 
    isDragging: props.isDragging, 
    dragData: props.dragData,
    dropTargetRowIndex: dropTargetRowIndex.value
  });

  if (props.isDragging && props.dragData && dropTargetRowIndex.value === rowIndex) {
    const targetRow = filteredData.value[rowIndex];
    if (targetRow) {
      console.log("Emitting drop-standard event:", {
        targetRow,
        standardData: props.dragData.data,
        type: "control"
      });
      
      emit("drop-standard", {
        targetRow,
        standardData: props.dragData.data,
        type: "control"
      });
    }
  }

  // 重置拖拽状态
  dropTargetRowIndex.value = -1;
  isDropTarget.value = false;
};

// 删除标准化ID
const handleRemoveStandardId = async (rowData: any) => {
  try {
    const updateData = { ...rowData };
    updateData.description = ""; // 清空标准化ID

    console.log("删除控制标准化ID:", updateData);

    // 调用更新API，将标准化ID设置为空
    const apiResponse = await updateTableRow("control", updateData);

    // 检查API调用结果
    if (apiResponse && apiResponse.code === 0) {
      // 更新成功，更新本地数据
      const index = tableData.value.findIndex(item => item.controlId === rowData.controlId);
      if (index !== -1) {
        tableData.value[index].description = "";
      }
      
      // 显示成功消息
      const { ElMessage } = await import("element-plus");
      ElMessage.success("标准化ID删除成功！");
    } else {
      // 更新失败
      const errorMsg = apiResponse?.msg || "删除标准化ID失败";
      const { ElMessage } = await import("element-plus");
      ElMessage.error(errorMsg);
      console.error("删除控制标准化ID失败:", apiResponse);
    }

  } catch (error) {
    console.error("删除控制标准化ID失败:", error);
    const { ElMessage } = await import("element-plus");
    ElMessage.error("删除标准化ID失败");
  }
};

// 刷新数据方法
const refreshData = (updatedRow: any) => {
  const index = tableData.value.findIndex(item => item.controlId === updatedRow.controlId);
  if (index !== -1) {
    tableData.value[index] = { ...tableData.value[index], ...updatedRow };
  }
};

// 组件卸载时清理监听器
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  // 清理选中状态
  selectedRowIndexes.value = [];
  selectedRows.value = [];
  window.removeEventListener("resize", updateTableSize);
});

// 暴露方法给父组件
defineExpose({
  loadControlList,
  refreshData
});
</script>

<style scoped>
.device-template-control {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.control-table-container {
  width: 100%;
  overflow: hidden;
}

/* 只读单元格样式 */
.read-only-cell {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 24px;
  color: var(--el-text-color-regular);
}

/* 过滤器样式 */
.el-table-v2__demo-filter {
  border-top: var(--el-border);
  margin: 12px -12px -12px;
  padding: 0 12px;
  display: flex;
  justify-content: space-between;
}

.filter-wrapper {
  padding: 8px 0;
}

.filter-group {
  margin-bottom: 8px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-xs {
  font-size: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.cursor-pointer {
  cursor: pointer;
}

/* 全局表格样式 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: var(--el-color-primary-light-9) !important;
}

:deep(.selected-row:hover) {
  background-color: var(--el-color-primary-light-8) !important;
}

/* 表格行样式优化 - 确保整行都能响应拖拽 */
:deep(.el-table-v2__row) {
  position: relative;
  cursor: pointer;
}

:deep(.el-table-v2__row-cell) {
  position: relative;
  z-index: 1;
  pointer-events: auto;
}

/* 确保行间空白区域也能响应鼠标事件 */
:deep(.el-table-v2__row) {
  background-clip: padding-box;
}

/* 当正在拖拽时，为目标行添加视觉反馈 */
:deep(.drop-target-row) {
  background-color: var(--el-color-success-light-9) !important;
  border: 2px dashed var(--el-color-success) !important;
  box-sizing: border-box;
}

/* 拖拽时的鼠标样式 */
:deep(.el-table-v2__row):hover {
  background-color: var(--el-fill-color-light);
}

/* 当有拖拽数据时，整个表格区域显示可放置状态 */
.control-table-container.dragging {
  position: relative;
}

.control-table-container.dragging :deep(.el-table-v2__row):hover {
  background-color: var(--el-color-success-light-9);
  cursor: copy;
}

/* 确保拖拽目标行的边框不被其他元素遮挡 */
:deep(.drop-target-row) {
  position: relative;
  z-index: 10;
}

/* 标准化ID单元格样式 */
.standard-id-cell {
  position: relative;
}

.standard-id-cell:hover .delete-btn {
  opacity: 1;
}
</style>