import type { RouteRecordRaw } from "vue-router";

import Home from "@/views/welcome/index.vue";
import Standard from "@/views/standard/index.vue";

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: "ic:baseline-extension",
      keepAlive: true,
      order: 1000,
      title: "南方移动插件"
    },
    name: "tcs-south-cmcc",
    path: "/tcs-south-cmcc",
    children: [
      {
        meta: {
          requiresMenuAuth: true,
          title: "首页",
          keepAlive: true
        },
        name: "home",
        path: "home",
        component: Home
      },
      {
        meta: {
          requiresMenuAuth: true,
          title: "移动标准化管理",
          keepAlive: true
        },
        name: "standard",
        path: "standard",
        component: Standard
      }
    ]
  }
];

export default routes;
