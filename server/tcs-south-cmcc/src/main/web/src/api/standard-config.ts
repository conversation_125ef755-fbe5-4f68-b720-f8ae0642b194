import { http } from "@/utils/http";

// 标准化数据接口
export interface StandardData {
  id: number;
  name: string;
  description: string;
  type: number;
  status: number;
  createTime: string;
  updateTime?: string;
}

// API响应格式
export type ApiResponse<T> = {
  code: number;
  timestamp: number;
  data: T;
  msg: string | null;
};

/**
 * 获取标准化数据列表
 */
export const getStandardDataList = (params?: any) => {
  return http.request<ApiResponse<StandardData[]>>(
    "get",
    "/api/standard/list",
    { params }
  );
};

/**
 * 根据模板ID获取标准化数据
 * @param templateId 模板ID
 */
export const getStandardDataByTemplateId = (templateId: string | number) => {
  return http.request<ApiResponse<StandardData[]>>(
    "get",
    `/api/standard/template/${templateId}`
  );
};

/**
 * 创建标准化数据
 * @param data 标准化数据
 */
export const createStandardData = (data: Partial<StandardData>) => {
  return http.request<ApiResponse<StandardData>>(
    "post",
    "/api/standard/create",
    { data }
  );
};

/**
 * 更新标准化数据
 * @param id 数据ID
 * @param data 更新数据
 */
export const updateStandardData = (id: number, data: Partial<StandardData>) => {
  return http.request<ApiResponse<StandardData>>(
    "put",
    `/api/standard/update/${id}`,
    { data }
  );
};

/**
 * 删除标准化数据
 * @param id 数据ID
 */
export const deleteStandardData = (id: number) => {
  return http.request<ApiResponse<boolean>>(
    "delete",
    `/api/standard/delete/${id}`
  );
};

/**
 * 应用标准化配置到子模板
 * @param templateId 模板ID
 * @param standardIds 标准化数据ID数组
 */
export const applyStandardToSubTemplates = (
  templateId: string | number,
  standardIds: number[]
) => {
  return http.request<ApiResponse<boolean>>(
    "post",
    "/api/standard/apply",
    {
      data: {
        templateId,
        standardIds
      }
    }
  );
};
