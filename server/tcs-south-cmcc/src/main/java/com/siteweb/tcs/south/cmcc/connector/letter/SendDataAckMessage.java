package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> (2025-05-12)
 **/

@Setter
@Getter
@JacksonXmlRootElement(localName = "Response")
public class SendDataAckMessage extends MobileBResponseMessage {
    public SendDataAckMessage() {
        super(PK_TypeName.SEND_DATA_ACK);
    }
}