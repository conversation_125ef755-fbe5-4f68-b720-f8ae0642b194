package com.siteweb.tcs.south.cmcc.util;

import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDeviceInit;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCDeviceInitDTO;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCFsuInitDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CMCC配置文件解析工具类
 * 用于解析cmb_init_list.ini配置文件
 */
@Slf4j
public class CmbConfigParser {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 解析cmb_init_list.ini配置文件内容
     * 
     * @param configContent 配置文件内容
     * @return CMCCFsuInitDTO 解析后的FSU配置信息
     * @throws Exception 解析异常
     */
    public static CMCCFsuInitDTO parseConfig(String configContent) throws Exception {
        if (!StringUtils.hasText(configContent)) {
            throw new IllegalArgumentException("配置文件内容不能为空");
        }

        try {
            Map<String, Map<String, String>> sections = parseIniContent(configContent);
            
            CMCCFsuInitDTO fsuInitDTO = new CMCCFsuInitDTO();
            
            // 解析FSU基本信息
            parseFsuInfo(sections.get("FSUINFO"), fsuInitDTO);
            
            // 解析设备数量
            int deviceNum = parseDeviceNum(sections.get("DEVICE_NUM"));
            fsuInitDTO.setDeviceNum(deviceNum);
            
            // 解析设备列表
            List<CMCCDeviceInitDTO> deviceList = parseDeviceList(sections, deviceNum);
            fsuInitDTO.setCmccDeviceInitDTOList(deviceList);
            
            log.info("成功解析配置文件，FSU ID: {}, 设备数量: {}", fsuInitDTO.getFsuId(), deviceNum);
            return fsuInitDTO;
            
        } catch (Exception e) {
            log.error("解析配置文件失败: {}", e.getMessage(), e);
            throw new Exception("配置文件解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析INI格式内容
     */
    private static Map<String, Map<String, String>> parseIniContent(String content) {
        Map<String, Map<String, String>> sections = new HashMap<>();
        Map<String, String> currentSection = null;
        String currentSectionName = null;

        String[] lines = content.split("\\r?\\n");
        
        for (String line : lines) {
            line = line.trim();
            
            // 跳过空行和注释
            if (line.isEmpty() || line.startsWith("#") || line.startsWith(";")) {
                continue;
            }
            
            // 处理节标题
            if (line.startsWith("[") && line.endsWith("]")) {
                currentSectionName = line.substring(1, line.length() - 1);
                currentSection = new HashMap<>();
                sections.put(currentSectionName, currentSection);
                continue;
            }
            
            // 处理键值对
            if (currentSection != null && line.contains("=")) {
                int equalIndex = line.indexOf("=");
                String key = line.substring(0, equalIndex).trim();
                String value = line.substring(equalIndex + 1).trim();
                currentSection.put(key, value);
            }
        }
        
        return sections;
    }

    /**
     * 解析FSU基本信息
     */
    private static void parseFsuInfo(Map<String, String> fsuInfo, CMCCFsuInitDTO fsuInitDTO) throws Exception {
        if (fsuInfo == null) {
            throw new Exception("未找到FSUINFO节");
        }

        fsuInitDTO.setFsuId(fsuInfo.get("FSUID"));
        fsuInitDTO.setType76DeviceId(fsuInfo.get("Type76DeviceID"));
        fsuInitDTO.setType76DeviceName(fsuInfo.get("Type76DeviceName"));
        fsuInitDTO.setRoomId(fsuInfo.get("RoomId"));
        fsuInitDTO.setRoomName(fsuInfo.get("RoomName"));
        fsuInitDTO.setSiteId(fsuInfo.get("SiteID"));
        fsuInitDTO.setSiteName(fsuInfo.get("SiteName"));
        fsuInitDTO.setFtpUser(fsuInfo.get("FTPUser"));
        fsuInitDTO.setFtpPwd(fsuInfo.get("FTPPwd"));
        fsuInitDTO.setLoginUser(fsuInfo.get("LoginUser"));
        fsuInitDTO.setLoginPwd(fsuInfo.get("LoginPwd"));
        fsuInitDTO.setScip(fsuInfo.get("SCIP"));
        fsuInitDTO.setScUrlSuffix(fsuInfo.get("SCURLSuffix"));
        fsuInitDTO.setPlatFormName(fsuInfo.get("PlatFormName"));

        // 解析数值类型字段
        try {
            if (fsuInfo.get("FSUPort") != null) {
                fsuInitDTO.setFsuPort(Integer.parseInt(fsuInfo.get("FSUPort")));
            }
            if (fsuInfo.get("FSUTYPE") != null) {
                fsuInitDTO.setFsuType(Integer.parseInt(fsuInfo.get("FSUTYPE")));
            }
            if (fsuInfo.get("SCPort") != null) {
                fsuInitDTO.setScPort(Integer.parseInt(fsuInfo.get("SCPort")));
            }
            if (fsuInfo.get("PlatFormNo") != null) {
                fsuInitDTO.setPlatFormNo(Integer.parseInt(fsuInfo.get("PlatFormNo")));
            }
            if (fsuInfo.get("SCSwitchMode") != null) {
                fsuInitDTO.setScSwitchMode(Integer.parseInt(fsuInfo.get("SCSwitchMode")));
            }
            if (fsuInfo.get("SCSwitchMode1") != null) {
                fsuInitDTO.setScSwitchMode1(Integer.parseInt(fsuInfo.get("SCSwitchMode1")));
            }
        } catch (NumberFormatException e) {
            throw new Exception("数值字段解析失败: " + e.getMessage());
        }

        // 解析布尔类型字段
        if (fsuInfo.get("EnableACL") != null) {
            fsuInitDTO.setEnableAcl(Boolean.parseBoolean(fsuInfo.get("EnableACL")));
        }
    }

    /**
     * 解析设备数量
     */
    private static int parseDeviceNum(Map<String, String> deviceNumSection) throws Exception {
        if (deviceNumSection == null) {
            throw new Exception("未找到DEVICE_NUM节");
        }
        
        String deviceNumStr = deviceNumSection.get("DeviceNum");
        if (deviceNumStr == null) {
            throw new Exception("未找到DeviceNum字段");
        }
        
        try {
            return Integer.parseInt(deviceNumStr);
        } catch (NumberFormatException e) {
            throw new Exception("设备数量解析失败: " + deviceNumStr);
        }
    }

    /**
     * 解析设备列表
     */
    private static List<CMCCDeviceInitDTO> parseDeviceList(Map<String, Map<String, String>> sections, int deviceNum) throws Exception {
        List<CMCCDeviceInitDTO> deviceList = new ArrayList<>();
        
        for (int i = 1; i <= deviceNum; i++) {
            String sectionName = "DEVICE" + i;
            Map<String, String> deviceSection = sections.get(sectionName);
            
            if (deviceSection == null) {
                log.warn("未找到设备节: {}", sectionName);
                continue;
            }
            
            try {
                CMCCDeviceInitDTO deviceDTO = parseDevice(deviceSection);
                deviceList.add(deviceDTO);
            } catch (Exception e) {
                log.error("解析设备{}失败: {}", sectionName, e.getMessage());
                throw new Exception("解析设备" + sectionName + "失败: " + e.getMessage());
            }
        }
        
        return deviceList;
    }

    /**
     * 解析单个设备信息
     */
    private static CMCCDeviceInitDTO parseDevice(Map<String, String> deviceSection) throws Exception {
        CMCCDeviceInitDTO deviceDTO = new CMCCDeviceInitDTO();
        
        // 基本字符串字段
        deviceDTO.setDeviceName(deviceSection.get("DeviceName"));
        deviceDTO.setRoomName(deviceSection.get("RoomName"));
        deviceDTO.setModel(deviceSection.get("Model"));
        deviceDTO.setBrand(deviceSection.get("Brand"));
        deviceDTO.setNamePrefix(deviceSection.get("NamePrefix"));
        deviceDTO.setSiteName(deviceSection.get("SiteName"));
        deviceDTO.setSiteId(deviceSection.get("SiteID"));
        
        // 数值字段解析
        try {
            if (deviceSection.get("DeviceId") != null) {
                deviceDTO.setDeviceId(Long.parseLong(deviceSection.get("DeviceId")));
            }
            if (deviceSection.get("RoomId") != null) {
                deviceDTO.setRoomId(Long.parseLong(deviceSection.get("RoomId")));
            }
            if (deviceSection.get("DeviceType") != null) {
                deviceDTO.setDeviceType(Integer.parseInt(deviceSection.get("DeviceType")));
            }
            if (deviceSection.get("DeviceSubType") != null) {
                deviceDTO.setDeviceSubType(Integer.parseInt(deviceSection.get("DeviceSubType")));
            }
            if (deviceSection.get("SiteWebEquipId") != null) {
                deviceDTO.setSiteWebEquipId(Integer.parseInt(deviceSection.get("SiteWebEquipId")));
            }
            if (deviceSection.get("RatedCapacity") != null) {
                deviceDTO.setRatedCapacity(Double.parseDouble(deviceSection.get("RatedCapacity")));
            }
        } catch (NumberFormatException e) {
            throw new Exception("设备数值字段解析失败: " + e.getMessage());
        }
        
        // 时间字段解析
        String beginRunTimeStr = deviceSection.get("BeginRunTime");
        if (StringUtils.hasText(beginRunTimeStr)) {
            try {
                deviceDTO.setBeginRunTime(LocalDateTime.parse(beginRunTimeStr, DATE_FORMATTER));
            } catch (DateTimeParseException e) {
                log.warn("设备投运时间解析失败: {}, 错误: {}", beginRunTimeStr, e.getMessage());
                // 不抛出异常，只记录警告
            }
        }
        
        return deviceDTO;
    }
}
