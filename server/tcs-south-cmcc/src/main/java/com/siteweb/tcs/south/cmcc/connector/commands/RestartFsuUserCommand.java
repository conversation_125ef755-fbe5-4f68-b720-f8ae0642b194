package com.siteweb.tcs.south.cmcc.connector.commands;

import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 重启FSU用户命令
 * 
 * 根据中国移动B接口技术规范5.6.14章节实现
 * 用于向FSU发送重启请求
 * 此报文用于FSU的升级等操作：SC侧先通过FTP将升级文件上传到FSU的/upgrade/下，再发此报文使FSU重启后自动升级
 * 
 * <AUTHOR> for CMCC Restart FSU Feature
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RestartFsuUserCommand extends CMCCSouthUserCommand {

    /**
     * 构造函数
     */
    public RestartFsuUserCommand() {
        super();
    }

    /**
     * 构造函数
     * 
     * @param gatewayId FSU ID
     * @param initiator 发起者
     */
    public RestartFsuUserCommand(String gatewayId, String initiator) {
        super(gatewayId, initiator);
    }

    @Override
    public PK_TypeName getRequestType() {
        return PK_TypeName.SET_FSUREBOOT;
    }

    /**
     * 创建重启FSU命令
     * 
     * @param gatewayId FSU ID
     * @param initiator 发起者
     * @return 重启FSU命令
     */
    public static RestartFsuUserCommand create(String gatewayId, String initiator) {
        return new RestartFsuUserCommand(gatewayId, initiator);
    }

    @Override
    public String toString() {
        return String.format("RestartFsuUserCommand{ gatewayId='%s', initiator='%s' }", 
                           getGatewayId(), getInitiator());
    }
}
