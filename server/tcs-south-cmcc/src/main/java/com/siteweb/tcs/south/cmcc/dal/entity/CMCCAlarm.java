package com.siteweb.tcs.south.cmcc.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumState;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * CMCC告警信息实体
 * <AUTHOR> (2025-05-22)
 **/
@Data
@NoArgsConstructor
@TableName("cmcc_alarms")
public class CMCCAlarm implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * FSU ID
     */
    @TableField("fsu_id")
    private String fsuId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 告警点ID带signalNumber
     */
    @TableField("sp_id")
    private String spId;

    /**
     * 原始SPID 不带signalNumber
     */
    @TableField("origin_sp_id")
    private String originSpId;


    /**
     *
     */
    @TableField("sp_hub_id")
    private Long spHubId;



    @TableField("sp_type")
    private EnumType spType;



    /**
     * 告警级别: 0=正常数据, 1=一级告警, 2=二级告警, 3=三级告警, 4=四级告警, 5=操作事件, 6=无效数据
     */
    @TableField("alarm_level")
    private EnumState alarmLevel;

    /**
     * 告警名称
     */
    @TableField("alarm_name")
    private String alarmName;




    /**
     * 类似于Siteweb的模块号
     */
    @TableField("signal_number")
    private String signalNumber;

    @TableField("nm_alarm_id")
    private String nmAlarmId;

    @TableField("meaning")
    private String Meaning;


    @TableField("threshold")
    private Double threshold;

    /**
     * 标识告警类别
     */
    private String unit;



}