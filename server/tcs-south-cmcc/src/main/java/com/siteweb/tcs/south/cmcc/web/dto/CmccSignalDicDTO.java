package com.siteweb.tcs.south.cmcc.web.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * CMCC信号字典DTO（包含字典名称）
 * 用于返回包含设备类型名称和信号类型名称的信号字典数据
 */
@ApiModel("CMCC信号字典DTO（包含字典名称）")
public class CmccSignalDicDTO {

    @ApiModelProperty("主键ID")
    private Integer id;

    @ApiModelProperty("标准版本")
    private Integer standardVersion;

    @ApiModelProperty("信号标准ID")
    private String signalStandardId;

    @ApiModelProperty("设备类型ID")
    private Integer deviceType;

    @ApiModelProperty("设备类型名称")
    private String deviceTypeName;

    @ApiModelProperty("设备子类型")
    private String deviceSubType;

    @ApiModelProperty("标准信号名称")
    private String standardSignalName;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("信号量类型ID")
    private Integer semaphoreType;

    @ApiModelProperty("信号量类型名称")
    private String semaphoreTypeName;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("含义")
    private String meaning;

    @ApiModelProperty("扩展字段")
    private String extendFiled;

    public CmccSignalDicDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStandardVersion() {
        return standardVersion;
    }

    public void setStandardVersion(Integer standardVersion) {
        this.standardVersion = standardVersion;
    }

    public String getSignalStandardId() {
        return signalStandardId;
    }

    public void setSignalStandardId(String signalStandardId) {
        this.signalStandardId = signalStandardId;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public String getDeviceSubType() {
        return deviceSubType;
    }

    public void setDeviceSubType(String deviceSubType) {
        this.deviceSubType = deviceSubType;
    }

    public String getStandardSignalName() {
        return standardSignalName;
    }

    public void setStandardSignalName(String standardSignalName) {
        this.standardSignalName = standardSignalName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getSemaphoreType() {
        return semaphoreType;
    }

    public void setSemaphoreType(Integer semaphoreType) {
        this.semaphoreType = semaphoreType;
    }

    public String getSemaphoreTypeName() {
        return semaphoreTypeName;
    }

    public void setSemaphoreTypeName(String semaphoreTypeName) {
        this.semaphoreTypeName = semaphoreTypeName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getMeaning() {
        return meaning;
    }

    public void setMeaning(String meaning) {
        this.meaning = meaning;
    }

    public String getExtendFiled() {
        return extendFiled;
    }

    public void setExtendFiled(String extendFiled) {
        this.extendFiled = extendFiled;
    }
} 