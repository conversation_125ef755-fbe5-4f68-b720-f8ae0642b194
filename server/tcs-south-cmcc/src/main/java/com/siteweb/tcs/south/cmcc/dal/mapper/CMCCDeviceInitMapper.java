package com.siteweb.tcs.south.cmcc.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDeviceInit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * CMCC设备初始化 Mapper
 * CMCC Device Init Mapper Interface
 */
@Mapper
@Repository
public interface CMCCDeviceInitMapper extends BaseMapper<CMCCDeviceInit> {

    /**
     * 根据设备ID和SiteWeb设备ID查询设备信息
     * @param deviceId CMCC设备ID
     * @param siteWebEquipId SiteWeb设备ID
     * @return 设备信息
     */
    CMCCDeviceInit selectByDeviceIdAndSiteWebEquipId(@Param("deviceId") Long deviceId, @Param("siteWebEquipId") Integer siteWebEquipId);

    /**
     * 根据设备ID查询设备信息（设备ID全局唯一）
     */
    CMCCDeviceInit selectByDeviceId(@Param("deviceId") Long deviceId);

    /**
     * 根据机房ID查询设备列表
     * @param roomId 机房ID
     * @return 设备列表
     */
    List<CMCCDeviceInit> selectByRoomId(@Param("roomId") Long roomId);

    /**
     * 根据设备类型查询设备列表
     * @param deviceType 设备类型
     * @return 设备列表
     */
    List<CMCCDeviceInit> selectByDeviceType(@Param("deviceType") Integer deviceType);

    /**
     * 根据设备名称模糊查询设备列表
     * @param deviceName 设备名称
     * @return 设备列表
     */
    List<CMCCDeviceInit> selectByDeviceNameLike(@Param("deviceName") String deviceName);

    /**
     * 根据SiteWeb设备ID查询设备信息
     * @param siteWebEquipId SiteWeb设备ID
     * @return 设备信息
     */
    CMCCDeviceInit selectBySiteWebEquipId(@Param("siteWebEquipId") Integer siteWebEquipId);

    /**
     * 根据SiteWeb监控单元ID查询设备列表
     * @param siteWebMuId SiteWeb监控单元ID
     * @return 设备列表
     */
    List<CMCCDeviceInit> selectBySiteWebMuId(@Param("siteWebMuId") Integer siteWebMuId);

    /**
     * 批量插入设备信息
     * @param deviceList 设备列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<CMCCDeviceInit> deviceList);

    /**
     * 根据设备ID和SiteWeb设备ID删除设备信息
     * @param deviceId CMCC设备ID
     * @param siteWebEquipId SiteWeb设备ID
     * @return 删除数量
     */
    int deleteByDeviceIdAndSiteWebEquipId(@Param("deviceId") Long deviceId, @Param("siteWebEquipId") Integer siteWebEquipId);
}
