package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import com.siteweb.tcs.south.cmcc.connector.protocol.TFSUResponseResult;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> (2025-07-28)
 **/
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@JacksonXmlRootElement(localName = "Response")
public class SetPointAckMessage extends MobileBResponseMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private TFSUResponseResult info = new TFSUResponseResult();

    public SetPointAckMessage() {
        super(PK_TypeName.SET_POINT_ACK);
    }
}
