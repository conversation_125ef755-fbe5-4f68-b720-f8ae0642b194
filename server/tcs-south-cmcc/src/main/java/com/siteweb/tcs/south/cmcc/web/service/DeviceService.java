package com.siteweb.tcs.south.cmcc.web.service;

import com.siteweb.tcs.south.cmcc.web.vo.DeviceVO;

import java.util.List;

/**
 * 设备服务接口
 * <p>
 * 提供设备相关的业务功能
 * </p>
 */
public interface DeviceService {

    /**
     * 获取设备列表
     * @return 设备列表
     */
    List<DeviceVO> listDevices();

    /**
     * 获取设备详情
     * @param deviceId 设备ID
     * @return 设备详情
     */
    DeviceVO getDevice(String deviceId);

    /**
     * 添加设备
     * @param deviceVO 设备信息
     * @return 添加结果
     */
    boolean addDevice(DeviceVO deviceVO);

    /**
     * 更新设备
     * @param deviceVO 设备信息
     * @return 更新结果
     */
    boolean updateDevice(DeviceVO deviceVO);

    /**
     * 删除设备
     * @param deviceId 设备ID
     * @return 删除结果
     */
    boolean deleteDevice(String deviceId);
} 