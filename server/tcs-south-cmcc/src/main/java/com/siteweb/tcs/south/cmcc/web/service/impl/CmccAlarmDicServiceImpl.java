package com.siteweb.tcs.south.cmcc.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.south.cmcc.dal.entity.CmccAlarmDic;
import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryItem;
import com.siteweb.tcs.south.cmcc.dal.mapper.CmccAlarmDicMapper;
import com.siteweb.tcs.south.cmcc.web.dto.CmccAlarmDicDTO;
import com.siteweb.tcs.south.cmcc.web.service.ICmccAlarmDicService;
import com.siteweb.tcs.south.cmcc.web.service.IDictionaryItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * CMCC告警字典Service实现类
 */
@Service
public class CmccAlarmDicServiceImpl extends ServiceImpl<CmccAlarmDicMapper, CmccAlarmDic> implements ICmccAlarmDicService {

    private static final String standardAlarmIdTemplate = "0500-002-%s-10-%s";

    @Resource
    private CmccAlarmDicMapper cmccAlarmDicMapper;

    @Resource
    private IDictionaryItemService dictionaryItemService;

    @Override
    public CmccAlarmDic getByAlarmStandardId(String alarmStandardId) {
        return cmccAlarmDicMapper.selectByAlarmStandardId(alarmStandardId);
    }

    @Override
    public List<CmccAlarmDic> listByDeviceType(Integer deviceType) {
        return cmccAlarmDicMapper.selectByDeviceType(deviceType);
    }

    @Override
    public List<CmccAlarmDic> listByStandardVersion(Integer standardVersion) {
        return cmccAlarmDicMapper.selectByStandardVersion(standardVersion);
    }

    @Override
    public List<CmccAlarmDic> listByAlarmLogicClass(Integer alarmLogicClass) {
        return cmccAlarmDicMapper.selectByAlarmLogicClass(alarmLogicClass);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "DefaultTransactionManager")
    public boolean saveAlarmDic(CmccAlarmDic cmccAlarmDic) {
        //处理标准化告警id
        String deviceTypeStr = String.format("%03d", cmccAlarmDic.getDeviceType());
        String standardAlarmId = String.format(standardAlarmIdTemplate, deviceTypeStr, cmccAlarmDic.getAlarmStandardId());
        cmccAlarmDic.setAlarmStandardId(standardAlarmId);
        return save(cmccAlarmDic);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "DefaultTransactionManager")
    public boolean updateAlarmDic(CmccAlarmDic cmccAlarmDic) {
        return updateById(cmccAlarmDic);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "DefaultTransactionManager")
    public boolean deleteAlarmDic(Integer id) {
        return removeById(id);
    }

    @Override
    public List<CmccAlarmDicDTO> listAllWithNames() {
        // 获取所有告警字典数据
        List<CmccAlarmDic> alarmDicList = list();
        
        // 获取设备类型字典（CategoryId=2）
        List<DictionaryItem> deviceTypes = dictionaryItemService.listByCategoryId(2);
        Map<Integer, String> deviceTypeMap = deviceTypes.stream()
                .collect(Collectors.toMap(DictionaryItem::getItemId, DictionaryItem::getItemValue));
        
        // 获取告警逻辑分类字典（CategoryId=4）
        List<DictionaryItem> alarmLogicClasses = dictionaryItemService.listByCategoryId(4);
        Map<Integer, String> alarmLogicClassMap = alarmLogicClasses.stream()
                .collect(Collectors.toMap(DictionaryItem::getItemId, DictionaryItem::getItemValue));
        
        // 获取告警级别字典（CategoryId=7）
        List<DictionaryItem> alarmLevels = dictionaryItemService.listByCategoryId(7);
        Map<Integer, String> alarmLevelMap = alarmLevels.stream()
                .collect(Collectors.toMap(DictionaryItem::getItemId, DictionaryItem::getItemValue));
        
        // 转换为DTO
        List<CmccAlarmDicDTO> result = new ArrayList<>();
        for (CmccAlarmDic alarmDic : alarmDicList) {
            CmccAlarmDicDTO dto = new CmccAlarmDicDTO();
            dto.setId(alarmDic.getId());
            dto.setStandardVersion(alarmDic.getStandardVersion());
            dto.setAlarmStandardId(alarmDic.getAlarmStandardId());
            dto.setDeviceType(alarmDic.getDeviceType());
            dto.setAlarmLogicClass(alarmDic.getAlarmLogicClass());
            dto.setAlarmLogicSubclass(alarmDic.getAlarmLogicSubclass());
            dto.setAlarmStandardName(alarmDic.getAlarmStandardName());
            dto.setMeaning(alarmDic.getMeaning());
            dto.setCommunicationBuildingAlarmLevel(alarmDic.getCommunicationBuildingAlarmLevel());
            dto.setTransmissionNodeAlarmLevel(alarmDic.getTransmissionNodeAlarmLevel());
            dto.setCommunicationBaseStationAlarmLevel(alarmDic.getCommunicationBaseStationAlarmLevel());
            dto.setIdcAlarmLevel(alarmDic.getIdcAlarmLevel());
            dto.setDescription(alarmDic.getDescription());
            dto.setExtendFiled(alarmDic.getExtendFiled());
            
            // 设置字典名称
            dto.setDeviceTypeName(deviceTypeMap.get(alarmDic.getDeviceType()));
            dto.setAlarmLogicClassName(alarmLogicClassMap.get(alarmDic.getAlarmLogicClass()));
            dto.setCommunicationBuildingAlarmLevelName(alarmLevelMap.get(alarmDic.getCommunicationBuildingAlarmLevel()));
            dto.setTransmissionNodeAlarmLevelName(alarmLevelMap.get(alarmDic.getTransmissionNodeAlarmLevel()));
            dto.setCommunicationBaseStationAlarmLevelName(alarmLevelMap.get(alarmDic.getCommunicationBaseStationAlarmLevel()));
            dto.setIdcAlarmLevelName(alarmLevelMap.get(alarmDic.getIdcAlarmLevel()));
            
            result.add(dto);
        }
        
        return result;
    }
} 