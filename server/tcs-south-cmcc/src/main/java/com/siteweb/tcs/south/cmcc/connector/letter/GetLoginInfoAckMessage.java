package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumResult;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取FSU注册信息响应报文
 * 
 * 根据中国移动B接口技术规范5.6.7章节实现
 * FSU向SC返回FSU的注册信息，包括用户名、密码、IP、MAC地址等
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.7
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@JacksonXmlRootElement(localName = "Response")
public class GetLoginInfoAckMessage extends MobileBResponseMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public GetLoginInfoAckMessage() {
        super(PK_TypeName.GET_LOGININFO_ACK);
    }


    @Setter
    @Getter
    public static class Info extends StandardResponseInfo {
        /**
         * 用户名
         * 长度：USER_LENGTH (20字节)
         */
        @JsonProperty("UserName")
        @JacksonXmlProperty(localName = "UserName")
        private String userName;

        /**
         * 口令
         * 长度：PASSWORD_LEN (20字节)
         */
        @JsonProperty("PassWord")
        @JacksonXmlProperty(localName = "PassWord")
        private String passWord;

        /**
         * FSU ID号
         * 长度：FSUID_LEN (20字节)
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;

        /**
         * FSU的内网IP
         * 长度：IP_LENGTH (15字节)
         */
        @JsonProperty("FSUIP")
        @JacksonXmlProperty(localName = "FSUIP")
        private String fsuIp;

        /**
         * FSU的MAC地址
         * 长度：MAC_LENGTH (推测17字节，格式如：XX:XX:XX:XX:XX:XX)
         */
        @JsonProperty("FSUMAC")
        @JacksonXmlProperty(localName = "FSUMAC")
        private String fsuMac;

        /**
         * FSU版本号
         * 长度：VER_LENGTH (20字节)
         */
        @JsonProperty("FSUVER")
        @JacksonXmlProperty(localName = "FSUVER")
        private String fsuVer;

        /**
         * 所属站点编码
         * 长度：ID_LENGTH (20字节)
         */
        @JsonProperty("SiteID")
        @JacksonXmlProperty(localName = "SiteID")
        private String siteId;

        /**
         * FSU物理机房编码
         * 长度：n*ID_LENGTH (n个20字节的ID)
         */
        @JsonProperty("RoomID")
        @JacksonXmlProperty(localName = "RoomID")
        private String roomId;

        /**
         * 执行结果
         */
        @JsonProperty("Result")
        @JacksonXmlProperty(localName = "Result")
        private EnumResult result;

        /**
         * 失败原因（当Result为FAILURE时）
         * 长度：FAILURE_CAUSE_LEN (40字节)
         */
        @JsonProperty("FailureCause")
        @JacksonXmlProperty(localName = "FailureCause")
        private String failureCause;
    }
}
