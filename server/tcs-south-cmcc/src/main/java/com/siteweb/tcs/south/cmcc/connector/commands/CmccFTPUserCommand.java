package com.siteweb.tcs.south.cmcc.connector.commands;

import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CMCC FTP用户命令基类
 * 
 * 为基于FTP协议的用户命令提供统一的基础结构
 * 包含FTP连接所需的用户名和密码等通用字段
 * 
 * <AUTHOR> for CMCC FTP Command Framework
 */
@Data
@EqualsAndHashCode(callSuper = true)
public abstract class CmccFTPUserCommand extends CMCCSouthUserCommand {


    /**
     * 构造函数
     */
    public CmccFTPUserCommand() {
        super();
    }

    /**
     * 构造函数
     *
     * @param gatewayId FSU ID
     * @param initiator 发起者
     */
    public CmccFTPUserCommand(String gatewayId, String initiator) {
        super(gatewayId, initiator);
    }

    @Override
    public CommandType getCommandType() {
        return CommandType.FTP;
    }

    @Override
    public PK_TypeName getRequestType() {
        return null; // FTP命令不使用PK_TypeName
    }

    /**
     * 获取FTP操作类型
     * 
     * @return FTP操作类型
     */
    public abstract FTPOperationType getFTPOperationType();

    /**
     * FTP操作类型枚举
     */
    public enum FTPOperationType {
        GET_CONFIG_DATA,    // 获取配置数据
        GET_IMAGE_FILES,    // 获取图像文件
        GET_ALARM_FILES,    // 获取告警文件
        UPLOAD_FILE,        // 上传文件
        GET_PERFORMANCE_DATA, // 获取性能数据
        GET_LOG_FILES       // 获取日志文件
    }
}
