package com.siteweb.tcs.south.cmcc.connector;

import com.siteweb.tcs.common.util.HttpRequestUtil;
import com.siteweb.tcs.plugin.common.MessageTracer;
import com.siteweb.tcs.south.cmcc.connector.letter.MobileBRawMessage;
import com.siteweb.tcs.south.cmcc.connector.letter.MobileBRequestMessage;
import com.siteweb.tcs.south.cmcc.connector.letter.MobileBResponseMessage;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumResult;
import com.siteweb.tcs.south.cmcc.exception.CMCCBusinessErrorCode;
import com.siteweb.tcs.south.cmcc.exception.CMCCTechnicalErrorCode;
import com.siteweb.tcs.south.cmcc.util.CMCCSerializer;
import com.siteweb.tcs.south.cmcc.util.SoapHelper;
import org.apache.commons.lang.StringEscapeUtils;

import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import java.util.function.Supplier;

/**
 * CMCC Xml scop协议 SC -> SU 调用类
 *
 * <AUTHOR> (2025-07-29)
 **/
public class CmccXmlHttpRequester {
    public final int DEFAULT_TIMEOUT_SEC=15;

    private final MessageTracer messageTracer;
    private final Supplier<String> uriGetter;

    public CmccXmlHttpRequester(MessageTracer messageTracer, Supplier<String> uriGetter) {
        this.messageTracer = messageTracer;
        this.uriGetter = uriGetter;
    }



    /**
     * 默认超时时间 DEFAULT_TIMEOUT_SEC(15s)
     * @param requestMessage
     * @return
     */
    public CompletionStage<MobileBRawMessage> send(MobileBRequestMessage requestMessage) {
        return send(requestMessage, DEFAULT_TIMEOUT_SEC);
    }

    /**
     * 默认超时时间 DEFAULT_TIMEOUT_SEC(15s)
     * @param requestMessage
     * @param responseClass
     * @return
     * @param <T>
     */
    public <T extends MobileBResponseMessage> CompletionStage<T> send(MobileBRequestMessage requestMessage, Class<T> responseClass) {
        return send(requestMessage, responseClass, DEFAULT_TIMEOUT_SEC);
    }



    /**
     * 请求CMCC远程FSU接口服务，成功返回MobileBResponseMessage
     * 调用阶段异常或接口返回失败则会进入CompletionStage的异常处理
     *
     * @param requestMessage
     * @param timeoutSeconds
     * @return
     */
    public CompletionStage<MobileBRawMessage> send(MobileBRequestMessage requestMessage, int timeoutSeconds) {
        var xmlContent = requestMessage.toSURequestXml();
        var msgId = requestMessage.getMsgId();
        var reqPkType = requestMessage.getPkType().getName().name();
        var traceFrame = messageTracer.traceRequestFrame(msgId, reqPkType, xmlContent);
        return HttpRequestUtil.xmlPOSTAsync(uriGetter.get(), xmlContent, timeoutSeconds)
                .handle((body, throwable) -> {
                    if (traceFrame != null) traceFrame.writeResponse(body);
                    if (throwable != null) {
                        throw new CompletionException(CMCCTechnicalErrorCode.CMCC_DESC_HTTP_HOST_ERROR.toException(throwable));
                    }
                    var unescapeXml = StringEscapeUtils.unescapeXml(body);
                    // 尝试解析最简单的报文结构
                    MobileBResponseMessage ack = CMCCSerializer.parseAckMessage(unescapeXml, MobileBResponseMessage.class);
                    if (!EnumResult.SUCCESS.equals(ack.getInfo().getResult())) {
                        // 接口返回失败，提示错误原因
                        throw new CompletionException(CMCCBusinessErrorCode.PROTOCOL_RESULT_FAILURE.toException(new Exception(ack.getInfo().getFailureCause())));
                    }
                    var fsuId = SoapHelper.parseXmlFSUId(unescapeXml);
                    var pkType = ack.getPkType().getName();
                    return new MobileBRawMessage(fsuId, pkType, body, null);
                })
                .whenComplete((body, throwable) -> {
                    if (throwable != null && traceFrame != null) {
                        traceFrame.writeException(throwable);
                    }
                });
    }


    /**
     * 请求CMCC远程FSU接口服务，并立即解包为指定格式的报文。
     *
     * @param requestMessage
     * @param responseClass
     * @param timeoutSeconds
     * @param <T>
     * @return
     */
    public <T extends MobileBResponseMessage> CompletionStage<T> send(MobileBRequestMessage requestMessage, Class<T> responseClass, int timeoutSeconds) {
        var xmlContent = requestMessage.toSURequestXml();
        var msgId = requestMessage.getMsgId();
        var reqPkType = requestMessage.getPkType().getName().name();
        var traceFrame = messageTracer.traceRequestFrame(msgId, reqPkType, xmlContent);
        return HttpRequestUtil.xmlPOSTAsync(uriGetter.get(), xmlContent, timeoutSeconds)
                .handle((body, throwable) -> {
                    if (traceFrame != null) traceFrame.writeResponse(body);
                    if (throwable != null) {
                        throw new CompletionException(CMCCTechnicalErrorCode.CMCC_DESC_HTTP_HOST_ERROR.toException(throwable));
                    }
                    var unescapeXml = StringEscapeUtils.unescapeXml(body);
                    // 尝试解析最简单的报文结构
                    T ack = CMCCSerializer.parseAckMessage(unescapeXml, responseClass);
                    if (!EnumResult.SUCCESS.equals(ack.getInfo().getResult())) {
                        // 接口返回失败，提示错误原因
                        throw new CompletionException(CMCCBusinessErrorCode.PROTOCOL_RESULT_FAILURE.toException(new Exception(ack.getInfo().getFailureCause())));
                    }
                    return ack;
                })
                .whenComplete((body, throwable) -> {
                    if (throwable != null && traceFrame != null) {
                        traceFrame.writeException(throwable);
                    }
                });
    }
}
