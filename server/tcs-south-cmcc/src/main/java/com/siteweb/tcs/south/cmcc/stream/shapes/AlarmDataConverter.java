package com.siteweb.tcs.south.cmcc.stream.shapes;

import com.siteweb.tcs.hub.domain.v2.letter.DeviceAlarmChange;
import com.siteweb.tcs.hub.domain.v2.letter.AlarmChange;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCFsu;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCDevice;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCAlarm;
import com.siteweb.tcs.south.cmcc.connector.protocol.TAlarm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 告警数据转换器
 * 负责将TAlarm转换为DeviceAlarmChange，使用空间换时间策略实现O(1)复杂度查找
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.2
 */
public class AlarmDataConverter {
    private static final Logger log = LoggerFactory.getLogger(AlarmDataConverter.class);
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final String fsuId;
    private final CMCCFsu gatewayInfo;
    
    // ========== 堆内内存缓存映射关系 ==========
    
    /**
     * FSU ID到Gateway Hub ID的映射缓存
     * Key: FSU ID (String)
     * Value: Gateway Hub ID (Long)
     */
    private final Map<String, Long> fsuToGatewayHubIdCache = new ConcurrentHashMap<>();
    
    /**
     * 设备ID到设备信息的映射缓存
     * Key: 南向设备ID (String)
     * Value: CMCCDevice实体
     */
    private final Map<String, CMCCDevice> deviceCache = new ConcurrentHashMap<>();
    
    /**
     * 设备ID到Hub设备ID的映射缓存
     * Key: 南向设备ID (String)
     * Value: Hub设备ID (Long)
     */
    private final Map<String, Long> deviceIdMappingCache = new ConcurrentHashMap<>();
    
    /**
     * 设备ID+告警ID到告警信息的映射缓存
     * Key: "deviceId:alarmId" (String)
     * Value: CMCCAlarm实体
     */
    private final Map<String, CMCCAlarm> alarmCache = new ConcurrentHashMap<>();
    
    /**
     * 设备ID+告警ID到Hub告警ID的映射缓存
     * Key: "deviceId:alarmId" (String)
     * Value: Hub告警ID (Long)
     */
    private final Map<String, Long> alarmIdMappingCache = new ConcurrentHashMap<>();

    /**
     * 构造函数
     * @param fsuId FSU ID
     * @param gatewayInfo 网关信息
     */
    public AlarmDataConverter(String fsuId, CMCCFsu gatewayInfo) {
        this.fsuId = fsuId;
        this.gatewayInfo = gatewayInfo;
        initializeCaches();
    }

    /**
     * 初始化缓存映射关系
     * 将gatewayInfo中的设备、告警信息预加载到内存中，实现O(1)查找
     */
    private void initializeCaches() {
        if (gatewayInfo == null) {
            log.warn("[{}] GatewayInfo为空，无法初始化缓存", fsuId);
            return;
        }

        try {
            // 预加载FSU到Gateway Hub ID的映射
            fsuToGatewayHubIdCache.put(fsuId, gatewayInfo.getGatewayHubId());
            
            // 预加载设备缓存
            if (gatewayInfo.getDeviceList() != null) {
                for (CMCCDevice device : gatewayInfo.getDeviceList()) {
                    String deviceId = device.getDeviceId();
                    if (deviceId != null) {
                        deviceCache.put(deviceId, device);
                        deviceIdMappingCache.put(deviceId, device.getDeviceHubId());
                        
                        // 预加载告警缓存
                        if (device.getAlarmList() != null) {
                            for (CMCCAlarm alarm : device.getAlarmList()) {
                                String alarmId = alarm.getSpId();
                                if (alarmId != null) {
                                    // 使用 "deviceId:alarmId" 作为唯一键
                                    String alarmKey = buildAlarmKey(deviceId, alarmId);
                                    alarmCache.put(alarmKey, alarm);
                                    alarmIdMappingCache.put(alarmKey, alarm.getSpHubId());
                                }
                            }
                        }
                    }
                }
            }
            
            log.info("[{}] 告警缓存初始化完成: FSU映射={}, 设备数量={}, 告警数量={}", 
                fsuId, fsuToGatewayHubIdCache.size(), deviceCache.size(), alarmCache.size());
                
        } catch (Exception e) {
            log.error("[{}] 初始化告警缓存失败", fsuId, e);
        }
    }

    /**
     * 构建告警缓存键
     * 格式: "deviceId:alarmId"
     */
    private String buildAlarmKey(String deviceId, String alarmId) {
        return deviceId + ":" + alarmId;
    }

    /**
     * 从缓存中获取Gateway Hub ID
     * 时间复杂度: O(1)
     */
    private Long getGatewayHubId(String fsuId) {
        return fsuToGatewayHubIdCache.get(fsuId);
    }

    /**
     * 从缓存中获取Hub设备ID
     * 时间复杂度: O(1)
     */
    private Long getHubDeviceId(String deviceId) {
        return deviceIdMappingCache.get(deviceId);
    }

    /**
     * 从缓存中获取Hub告警ID
     * 时间复杂度: O(1)
     */
    private Long getHubAlarmId(String deviceId, String alarmId) {
        String alarmKey = buildAlarmKey(deviceId, alarmId);
        return alarmIdMappingCache.get(alarmKey);
    }

    /**
     * 从缓存中获取告警信息
     * 时间复杂度: O(1)
     */
    private CMCCAlarm getAlarmFromCache(String deviceId, String alarmId) {
        String alarmKey = buildAlarmKey(deviceId, alarmId);
        return alarmCache.get(alarmKey);
    }

    /**
     * 批量转换告警数据
     * 使用缓存映射，实现O(1)复杂度的查找
     * @param alarms 告警列表
     * @return 转换后的DeviceAlarmChange列表
     */
    public List<DeviceAlarmChange> convertAlarms(List<TAlarm> alarms) {
        List<DeviceAlarmChange> result = new ArrayList<>();
        
        if (alarms == null || alarms.isEmpty()) {
            return result;
        }
        
        // 按设备分组处理告警
        Map<String, List<TAlarm>> deviceAlarmMap = alarms.stream()
            .collect(Collectors.groupingBy(TAlarm::getDeviceId));
        
        for (Map.Entry<String, List<TAlarm>> entry : deviceAlarmMap.entrySet()) {
            String deviceId = entry.getKey();
            List<TAlarm> deviceAlarms = entry.getValue();
            
            try {
                DeviceAlarmChange deviceAlarmChange = convertDeviceAlarmsToDeviceAlarmChange(
                    fsuId, deviceId, deviceAlarms);
                if (deviceAlarmChange != null) {
                    result.add(deviceAlarmChange);
                }
            } catch (Exception e) {
                log.error("[{}] 处理设备 {} 的告警数据失败", fsuId, deviceId, e);
            }
        }
        
        return result;
    }

    /**
     * 将设备告警数据转换为DeviceAlarmChange
     * 使用缓存映射，实现高效的ID转换
     */
    private DeviceAlarmChange convertDeviceAlarmsToDeviceAlarmChange(String fsuId, String deviceId, List<TAlarm> alarms) {
        if (deviceId == null) {
            log.warn("[{}] 设备ID为空，跳过处理", fsuId);
            return null;
        }

        // 从缓存获取Gateway Hub ID
        Long gatewayHubId = getGatewayHubId(fsuId);
        if (gatewayHubId == null) {
            log.warn("[{}] 未找到FSU {} 的Gateway映射，跳过处理", fsuId, fsuId);
            return null;
        }

        // 从缓存获取Hub设备ID
        Long hubDeviceId = getHubDeviceId(deviceId);
        if (hubDeviceId == null) {
            log.warn("[{}] 未找到设备 {} 的Hub映射，跳过处理", fsuId, deviceId);
            return null;
        }

        // 创建DeviceAlarmChange对象
        DeviceAlarmChange deviceAlarmChange = new DeviceAlarmChange();
        deviceAlarmChange.setGatewayId(gatewayHubId); // 使用Gateway Hub ID
        deviceAlarmChange.setDeviceId(hubDeviceId); // 使用Hub设备ID

        // 转换告警数据
        List<AlarmChange> alarmChanges = convertTAlarmsToAlarmChanges(fsuId, deviceId, alarms);
        deviceAlarmChange.setAlarmChangeList(alarmChanges);

        return deviceAlarmChange;
    }

    /**
     * 转换告警数据为AlarmChange列表
     * 使用缓存映射，实现高效的告警ID转换
     */
    private List<AlarmChange> convertTAlarmsToAlarmChanges(String fsuId, String deviceId, List<TAlarm> alarms) {
        List<AlarmChange> result = new ArrayList<>();
        
        if (alarms == null || alarms.isEmpty()) {
            return result;
        }
        
        for (TAlarm alarm : alarms) {
            try {
                AlarmChange alarmChange = convertTAlarmToAlarmChange(fsuId, deviceId, alarm);
                if (alarmChange != null) {
                    result.add(alarmChange);
                }
            } catch (Exception e) {
                log.error("[{}] 转换告警数据失败: deviceId={}, alarmId={}", 
                    fsuId, deviceId, alarm.getNmAlarmId(), e);
            }
        }
        
        return result;
    }

    /**
     * 将TAlarm转换为AlarmChange
     * 使用缓存映射，实现高效的告警ID转换
     */
    private AlarmChange convertTAlarmToAlarmChange(String fsuId, String deviceId, TAlarm alarm) {
        String alarmId = alarm.getId()+alarm.getSignalNumber();
        if (alarmId == null) {
            log.warn("[{}] 告警ID为空，跳过处理", fsuId);
            return null;
        }

        // 从缓存获取Hub告警ID
        Long hubAlarmId = getHubAlarmId(deviceId, alarmId);
        if (hubAlarmId == null) {
            log.warn("[{}] 未找到告警 {}/{} 的Hub映射，跳过处理", fsuId, deviceId, alarmId);
            return null;
        }

        // 从缓存获取告警详细信息
        CMCCAlarm alarmInfo = getAlarmFromCache(deviceId, alarmId);
        
        // 创建AlarmChange对象
        AlarmChange alarmChange = new AlarmChange();
        alarmChange.setGatewayId(getGatewayHubId(fsuId)); // 使用Gateway Hub ID
        alarmChange.setDeviceId(getHubDeviceId(deviceId)); // 使用Hub设备ID
        alarmChange.setAlarmId(hubAlarmId); // 使用Hub告警ID
        alarmChange.setSerialNo(alarm.getSerialNo());
        alarmChange.setAlarmLevel(alarm.getAlarmLevel() != null ? alarm.getAlarmLevel().getCode() : 0);
        alarmChange.setAlarmFlag(alarm.getAlarmFlag() != null ? alarm.getAlarmFlag().toString() : "");
        alarmChange.setAlarmDescription(alarm.getAlarmDesc());
        alarmChange.setTriggerValue(String.valueOf(alarm.getEventValue()));
        
        // 设置告警时间
        if (alarm.getAlarmTime() != null) {
            try {
                LocalDateTime alarmTime = LocalDateTime.parse(alarm.getAlarmTime(), TIME_FORMATTER);
                alarmChange.setStartTime(alarmTime);
                // 根据告警标志设置结束时间
                if (alarm.getAlarmFlag() != null && "END".equals(alarm.getAlarmFlag().toString())) {
                    alarmChange.setEndTime(alarmTime);
                }
            } catch (Exception e) {
                log.warn("[{}] 解析告警时间失败: {}", fsuId, alarm.getAlarmTime());
                alarmChange.setStartTime(LocalDateTime.now());
            }
        } else {
            alarmChange.setStartTime(LocalDateTime.now());
        }
        
        // 设置告警含义（如果有）
        if (alarmInfo != null && alarmInfo.getMeaning() != null) {
            alarmChange.setMeanings(alarmInfo.getMeaning());
        }

        return alarmChange;
    }

    /**
     * 刷新缓存映射关系
     * 当gatewayInfo发生变化时调用
     */
    public void refreshCaches() {
        log.info("[{}] 开始刷新告警缓存映射关系", fsuId);
        
        // 清空现有缓存
        fsuToGatewayHubIdCache.clear();
        deviceCache.clear();
        deviceIdMappingCache.clear();
        alarmCache.clear();
        alarmIdMappingCache.clear();
        
        // 重新初始化缓存
        initializeCaches();
        
        log.info("[{}] 告警缓存映射关系刷新完成", fsuId);
    }

    /**
     * 获取缓存统计信息
     * 用于监控和调试
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("fsuToGatewayHubIdCacheSize", fsuToGatewayHubIdCache.size());
        stats.put("deviceCacheSize", deviceCache.size());
        stats.put("deviceIdMappingCacheSize", deviceIdMappingCache.size());
        stats.put("alarmCacheSize", alarmCache.size());
        stats.put("alarmIdMappingCacheSize", alarmIdMappingCache.size());
        stats.put("fsuId", fsuId);
        stats.put("gatewayHubId", gatewayInfo != null ? gatewayInfo.getGatewayHubId() : null);
        return stats;
    }
}
