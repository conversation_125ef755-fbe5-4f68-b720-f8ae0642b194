package com.siteweb.tcs.south.cmcc.connector.commands;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 上传文件用户命令
 * 
 * 根据移动B接口技术规范5.7.4节实现
 * 通过FTP向FSU的\ upgrade\目录上传升级文件等
 * 
 * <AUTHOR> for CMCC FTP Upload Feature
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UploadFileUserCommand extends CmccFTPUserCommand {

    /**
     * 要上传的文件名
     */
    private String fileName;

    /**
     * 文件数据
     */
    private byte[] fileData;

    /**
     * 文件描述
     */
    private String fileDescription;

    /**
     * 构造函数
     */
    public UploadFileUserCommand() {
        super();
    }

    /**
     * 构造函数
     *
     * @param gatewayId FSU ID
     * @param initiator 发起者
     */
    public UploadFileUserCommand(String gatewayId, String initiator) {
        super(gatewayId, initiator);
    }

    @Override
    public FTPOperationType getFTPOperationType() {
        return FTPOperationType.UPLOAD_FILE;
    }

    /**
     * 创建上传文件命令
     *
     * @param gatewayId FSU ID
     * @param initiator 发起者
     * @param fileName 文件名
     * @param fileData 文件数据
     * @return 上传文件命令
     */
    public static UploadFileUserCommand create(String gatewayId, String initiator,
                                               String fileName, byte[] fileData) {
        UploadFileUserCommand command = new UploadFileUserCommand(gatewayId, initiator);
        command.setFileName(fileName);
        command.setFileData(fileData);
        return command;
    }

    /**
     * 创建上传文件命令（带描述）
     *
     * @param gatewayId FSU ID
     * @param initiator 发起者
     * @param fileName 文件名
     * @param fileData 文件数据
     * @param fileDescription 文件描述
     * @return 上传文件命令
     */
    public static UploadFileUserCommand create(String gatewayId, String initiator,
                                               String fileName, byte[] fileData, String fileDescription) {
        UploadFileUserCommand command = create(gatewayId, initiator, fileName, fileData);
        command.setFileDescription(fileDescription);
        return command;
    }

    @Override
    public String toString() {
        return String.format("UploadFileUserCommand{ gatewayId='%s', initiator='%s', fileName='%s', fileSize=%d }", 
                           getGatewayId(), getInitiator(), fileName, 
                           fileData != null ? fileData.length : 0);
    }
}
