package com.siteweb.tcs.south.cmcc.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCSignal;
import com.siteweb.tcs.south.cmcc.web.dto.CMCCSignalWithStandardization;
import com.siteweb.tcs.south.cmcc.web.service.ICMCCSignalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CMCC信号Controller
 * <AUTHOR> (2025-07-30)
 */
@Slf4j
@Api(tags = "CMCC信号管理")
@RestController
@RequestMapping("/cmcc-signal")
public class CMCCSignalController {

    @Autowired
    private ICMCCSignalService cmccSignalService;

    /**
     * 根据FSUid和DeviceId查询信号列表
     */
    @ApiOperation("根据FSUid和DeviceId查询信号列表")
    @GetMapping("/list")
    public ResponseEntity<ResponseResult> list(
            @ApiParam("FSU ID") @RequestParam(required = false) String fsuId,
            @ApiParam("设备ID") @RequestParam(required = false) String deviceId) {
        
        log.info("查询信号列表，FSUid: {}, DeviceId: {}", fsuId, deviceId);
        
        List<CMCCSignal> result;
        if (StringUtils.hasText(fsuId) && StringUtils.hasText(deviceId)) {
            result = cmccSignalService.listByFsuIdAndDeviceId(fsuId, deviceId);
        } else if (StringUtils.hasText(fsuId)) {
            result = cmccSignalService.listByFsuId(fsuId);
        } else if (StringUtils.hasText(deviceId)) {
            result = cmccSignalService.listByDeviceId(deviceId);
        } else {
            result = cmccSignalService.list();
        }
        
        return ResponseHelper.successful(result);
    }

    /**
     * 根据FSUid和DeviceId分页查询信号
     */
    @ApiOperation("根据FSUid和DeviceId分页查询信号")
    @GetMapping("/page")
    public ResponseEntity<ResponseResult> page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("FSU ID") @RequestParam(required = false) String fsuId,
            @ApiParam("设备ID") @RequestParam(required = false) String deviceId) {
        
        log.info("分页查询信号，FSUid: {}, DeviceId: {}, current: {}, size: {}", fsuId, deviceId, current, size);
        
        IPage<CMCCSignal> result;
        if (StringUtils.hasText(fsuId) && StringUtils.hasText(deviceId)) {
            result = cmccSignalService.pageByFsuIdAndDeviceId(current, size, fsuId, deviceId);
        } else if (StringUtils.hasText(fsuId)) {
            result = cmccSignalService.pageByFsuId(current, size, fsuId);
        } else if (StringUtils.hasText(deviceId)) {
            result = cmccSignalService.pageByDeviceId(current, size, deviceId);
        } else {
            result = cmccSignalService.page(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(current, size));
        }
        
        return ResponseHelper.successful(result);
    }

    /**
     * 根据ID查询信号
     */
    @ApiOperation("根据ID查询信号")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseResult> getById(@ApiParam("信号ID") @PathVariable Long id) {
        log.info("根据ID查询信号: {}", id);
        CMCCSignal result = cmccSignalService.getSignalById(id);
        return ResponseHelper.successful(result);
    }

    /**
     * 根据FSUid和DeviceId查询信号列表（包含标准化信息）
     */
    @ApiOperation("根据FSUid和DeviceId查询信号列表（包含标准化信息）")
    @GetMapping("/standardization/list")
    public ResponseEntity<ResponseResult> listWithStandardization(
            @ApiParam("FSU ID") @RequestParam(required = false) String fsuId,
            @ApiParam("设备ID") @RequestParam(required = false) String deviceId) {
        List<CMCCSignalWithStandardization> result = cmccSignalService.listWithStandardizationByFsuIdAndDeviceId(fsuId, deviceId);
        return ResponseHelper.successful(result);
    }
}
