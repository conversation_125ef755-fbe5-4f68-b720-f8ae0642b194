package com.siteweb.tcs.south.cmcc.dal.entity;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.hub.domain.letter.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * CMCC设备信息实体
 * <AUTHOR> (2025-05-12)
 **/
@Data
@NoArgsConstructor
@Slf4j
@TableName("cmcc_devices")
public class CMCCDevice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * FSU ID
     */
    @TableField("fsu_id")
    private String fsuId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    @TableField("device_hub_id")
    private Long deviceHubId;


    /**
     * 设备名称
     */
    @TableField("device_name")
    private String deviceName;

    /**
     * 站点名称
     */
    @TableField("site_name")
    private String siteName;

    /**
     * 机房名称
     */
    @TableField("room_name")
    private String roomName;


    /**
     * 站点名称
     */
    @TableField("site_id")
    private String siteId;

    /**
     * 机房名称
     */
    @TableField("room_id")
    private String roomId;





    /**
     * 设备类型
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 设备子类型
     */
    @TableField("device_sub_type")
    private String deviceSubType;

    /**
     * 型号
     */
    private String model;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 额定容量
     */
    @TableField("rated_capacity")
    private Float ratedCapacity;

    /**
     * 版本
     */
    private String version;

    /**
     * 开始运行时间
     */
    @TableField("begin_run_time")
    private String beginRunTime;

    /**
     * 设备安装位置描述
     */
    @TableField("dev_describe")
    private String devDescribe;

    /**
     * 设备描述
     */
    private String description;

    /**
     * 信号点列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<CMCCSignal> signalList = new ArrayList<>();

    /**
     * 告警列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<CMCCAlarm> alarmList = new ArrayList<>();


    /**
     * 控制点列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<CMCCControl> controlList = new ArrayList<>();

    /**
     * 设备大类名称
     */
    @TableField(exist = false)
    private String deviceTypeName;

    /**
     * 设备子类名称
     */
    @TableField(exist = false)
    private String deviceSubTypeName;

}
