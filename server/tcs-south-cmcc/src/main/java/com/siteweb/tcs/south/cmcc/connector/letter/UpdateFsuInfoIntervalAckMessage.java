package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.EnumResult;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 更新FSU状态信息获取周期响应报文
 * 
 * 根据中国移动B接口技术规范5.6.13章节实现
 * FSU向SC返回更新FSU状态信息获取周期的执行结果
 * 当Result取值为1时，FailureCause取值为"NULL"
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Response")
public class UpdateFsuInfoIntervalAckMessage extends MobileBResponseMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public UpdateFsuInfoIntervalAckMessage() {
        super(PK_TypeName.UPDATE_FSUINFO_INTERVAL_ACK);
    }

    @Setter
    @Getter
    public static class Info extends StandardResponseInfo {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;

        /**
         * 执行结果
         */
        @JsonProperty("Result")
        @JacksonXmlProperty(localName = "Result")
        private EnumResult result;

        /**
         * 失败原因（当Result为FAILURE时）
         * 更新FSU状态信息获取周期失败的原因（厂家自定义）
         * 当Result取值为1时，FailureCause取值为"NULL"
         */
        @JsonProperty("FailureCause")
        @JacksonXmlProperty(localName = "FailureCause")
        private String failureCause;
    }
}
