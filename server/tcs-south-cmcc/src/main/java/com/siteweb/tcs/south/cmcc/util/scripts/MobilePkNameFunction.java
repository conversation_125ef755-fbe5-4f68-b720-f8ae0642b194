package com.siteweb.tcs.south.cmcc.util.scripts;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.siteweb.tcs.south.cmcc.connector.letter.MobileBMessage;

import java.util.Map;

/**
 * <AUTHOR> (2025-07-02)
 **/
public class MobilePkNameFunction extends AbstractFunction {
    @Override
    public String getName() {
        return "$GET_MOBILE_PK_NAME";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        var param = arg1.getValue(env);
        if (param == null) return new AviatorString(null);
        if (param instanceof MobileBMessage mobileBMessage) {
            return new AviatorString(mobileBMessage.pkName());
        }
        return new AviatorString(null);
    }
}
