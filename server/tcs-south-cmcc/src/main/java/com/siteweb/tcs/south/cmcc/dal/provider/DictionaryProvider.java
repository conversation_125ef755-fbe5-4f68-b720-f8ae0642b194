package com.siteweb.tcs.south.cmcc.dal.provider;

import com.siteweb.tcs.south.cmcc.dal.entity.DeviceTypeInfo;
import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryItem;
import com.siteweb.tcs.south.cmcc.web.service.IDictionaryItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @program: tcs2
 * @description: 字典业务提供类
 * @author: xsx
 * @create: 2025-08-20 14:02
 **/
@Slf4j
@Component
public class DictionaryProvider {

    @Autowired
    private IDictionaryItemService iDictionaryItemService;

    /**
     * 设备大类缓存 - 键为itemId，值为DictionaryItem
     */
    private final Map<Integer, DictionaryItem> deviceCategoryCache = new ConcurrentHashMap<>();

    /**
     * 设备子类缓存 - 键为itemId，值为DictionaryItem
     */
    private final Map<Integer, DictionaryItem> deviceSubCategoryCache = new ConcurrentHashMap<>();

    /**
     * 设备大类ID常量
     */
    private static final Integer DEVICE_CATEGORY_ID = 2;

    /**
     * 设备子类ID常量
     */
    private static final Integer DEVICE_SUB_CATEGORY_ID = 3;

//    /**
//     * Bean初始化后从数据库加载设备大类和设备子类数据到缓存
//     */
//    @PostConstruct
//    public void initCache() {
//        log.info("开始初始化设备类型字典缓存...");
//
//        try {
//            // 加载设备大类数据
//            loadDeviceCategories();
//
//            // 加载设备子类数据
//            loadDeviceSubCategories();
//
//            log.info("设备类型字典缓存初始化完成 - 设备大类: {} 条, 设备子类: {} 条",
//                    deviceCategoryCache.size(), deviceSubCategoryCache.size());
//        } catch (Exception e) {
//            log.error("初始化设备类型字典缓存失败", e);
//        }
//    }

    /**
     * 加载设备大类数据
     */
    private void loadDeviceCategories() {
        List<DictionaryItem> categories = iDictionaryItemService.listByCategoryId(DEVICE_CATEGORY_ID);
        
        // 过滤启用的设备大类
        List<DictionaryItem> enabledCategories = categories.stream()
                .filter(item -> item.getEnable() != null && item.getEnable() == 1)
                .collect(Collectors.toList());
        
        // 清空缓存并重新加载
        deviceCategoryCache.clear();
        for (DictionaryItem item : enabledCategories) {
            deviceCategoryCache.put(item.getItemId(), item);
        }
        
        log.info("加载设备大类数据完成，共 {} 条", enabledCategories.size());
    }

    /**
     * 加载设备子类数据
     */
    private void loadDeviceSubCategories() {
        List<DictionaryItem> subCategories = iDictionaryItemService.listByCategoryId(DEVICE_SUB_CATEGORY_ID);
        
        // 过滤启用的设备子类
        List<DictionaryItem> enabledSubCategories = subCategories.stream()
                .filter(item -> item.getEnable() != null && item.getEnable() == 1)
                .collect(Collectors.toList());
        
        // 清空缓存并重新加载
        deviceSubCategoryCache.clear();
        for (DictionaryItem item : enabledSubCategories) {
            deviceSubCategoryCache.put(item.getItemId(), item);
        }
        
        log.info("加载设备子类数据完成，共 {} 条", enabledSubCategories.size());
    }


    /**
     * 根据设备大类ID获取设备大类信息
     * @param categoryItemId 设备大类ID
     * @return 设备大类信息，如果不存在返回null
     */
    public DictionaryItem getDeviceCategory(Integer categoryItemId) {
        if (categoryItemId == null) {
            return null;
        }else if(deviceCategoryCache.isEmpty()){
            loadDeviceCategories();
        }
        return deviceCategoryCache.get(categoryItemId);
    }

    /**
     * 根据设备子类ID获取设备子类信息
     * @param subCategoryItemId 设备子类ID
     * @return 设备子类信息，如果不存在返回null
     */
    public DictionaryItem getDeviceSubCategory(Integer subCategoryItemId) {
        if (subCategoryItemId == null) {
            return null;
        }else if(deviceSubCategoryCache.isEmpty()){
            loadDeviceSubCategories();
        }
        return deviceSubCategoryCache.get(subCategoryItemId);
    }

    /**
     * 根据设备大类ID和设备子类ID获取设备类型信息
     * @param categoryItemId 设备大类ID
     * @param subCategoryItemId 设备子类ID
     * @return 设备类型信息对象，包含大类和子类信息
     */
    public DeviceTypeInfo getDeviceTypeInfo(Integer categoryItemId, Integer subCategoryItemId) {
        DictionaryItem category = getDeviceCategory(categoryItemId);
        DictionaryItem subCategory = getDeviceSubCategory(subCategoryItemId);
        return new DeviceTypeInfo(category, subCategory);
    }
}
