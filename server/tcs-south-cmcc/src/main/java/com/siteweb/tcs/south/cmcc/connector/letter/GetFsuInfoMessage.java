package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取FSU信息请求报文
 * 
 * 根据中国移动B接口技术规范5.6.12章节实现
 * SC向FSU请求FSU的基本信息，包括状态、配置等
 * 
 * <AUTHOR> from CMCC B Interface Specification 5.6.12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Request")
public class GetFsuInfoMessage extends MobileBRequestMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public GetFsuInfoMessage() {
        super(PK_TypeName.GET_FSUINFO);
    }

    /**
     * 创建获取FSU信息请求消息
     * @param fsuId FSU ID号
     * @return 获取FSU信息请求消息
     */
    public static GetFsuInfoMessage create(String fsuId) {
        GetFsuInfoMessage message = new GetFsuInfoMessage();
        message.getInfo().setFsuId(fsuId);
        return message;
    }

    /**
     * 创建获取指定类型FSU信息请求消息
     * @param fsuId FSU ID号
     * @param infoType 信息类型
     * @return 获取FSU信息请求消息
     */
    public static GetFsuInfoMessage create(String fsuId, InfoType infoType) {
        GetFsuInfoMessage message = new GetFsuInfoMessage();
        message.getInfo().setFsuId(fsuId);
        message.getInfo().setInfoType(infoType);
        return message;
    }

    /**
     * 转换为SOAP XML格式
     * @return SOAP XML字符串
     */
    public String toSoapXml() {
        StringBuilder xmlBuilder = new StringBuilder();
        xmlBuilder.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n")
                  .append("<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n")
                  .append("  <soap:Body>\n")
                  .append("    <Request>\n")
                  .append("      <PkType>").append(getPkType()).append("</PkType>\n")
                  .append("      <Info>\n")
                  .append("        <FSUID>").append(info.getFsuId()).append("</FSUID>\n");
        
        if (info.getInfoType() != null) {
            xmlBuilder.append("        <InfoType>").append(info.getInfoType().getCode()).append("</InfoType>\n");
        }
        
        xmlBuilder.append("      </Info>\n")
                  .append("    </Request>\n")
                  .append("  </soap:Body>\n")
                  .append("</soap:Envelope>");
        
        return xmlBuilder.toString();
    }

    @Setter
    @Getter
    public static class Info {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;

        /**
         * 信息类型（可选）
         */
        @JsonProperty("InfoType")
        @JacksonXmlProperty(localName = "InfoType")
        private InfoType infoType;
    }

    /**
     * FSU信息类型枚举
     */
    public enum InfoType {
        /**
         * 基本信息
         */
        BASIC("basic", "基本信息"),

        /**
         * 状态信息
         */
        STATUS("status", "状态信息"),

        /**
         * 配置信息
         */
        CONFIG("config", "配置信息"),

        /**
         * 设备信息
         */
        DEVICE("device", "设备信息"),

        /**
         * 网络信息
         */
        NETWORK("network", "网络信息"),

        /**
         * 全部信息
         */
        ALL("all", "全部信息");

        private final String code;
        private final String description;

        InfoType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 根据代码获取信息类型
         * 
         * @param code 代码
         * @return 信息类型
         */
        public static InfoType fromCode(String code) {
            for (InfoType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return ALL; // 默认返回全部信息
        }
    }
}
