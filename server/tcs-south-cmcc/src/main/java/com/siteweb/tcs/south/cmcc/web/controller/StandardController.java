package com.siteweb.tcs.south.cmcc.web.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.south.cmcc.dal.entity.DictionaryItem;
import com.siteweb.tcs.south.cmcc.web.dto.DeviceTypeStandardInfoDTO;
import com.siteweb.tcs.south.cmcc.web.dto.StationTypeStandardInfoDTO;
import com.siteweb.tcs.south.cmcc.web.service.IDictionaryItemService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: tcs2
 * @description: 标准化信息控制器
 * @author: xsx
 * @create: 2025-07-22 14:33
 **/
@Slf4j
@Api(tags = "标准化信息获取")
@RestController
@RequestMapping("/standard")
public class StandardController {
    @Resource
    private IDictionaryItemService dictionaryItemService;

    @GetMapping(value = "/standard-device-type")
    public ResponseEntity<ResponseResult> getStandardDeviceType(){
        List<DeviceTypeStandardInfoDTO> standardInfoDTOList = new ArrayList<>();
        Map<Integer,DictionaryItem> deviceTypeIdMap = new HashMap<>();
        List<DictionaryItem> deviceTypeList = dictionaryItemService.listByCategoryId(2);
        deviceTypeList.forEach(e -> deviceTypeIdMap.put(e.getItemId(),e));
        List<DictionaryItem> deviceSubTypeList = dictionaryItemService.listByCategoryId(3);
        if(CollectionUtil.isNotEmpty(deviceSubTypeList)){
            deviceSubTypeList.forEach(item->{
                DeviceTypeStandardInfoDTO standardInfoDTO = new DeviceTypeStandardInfoDTO();
                standardInfoDTO.setDeviceTypeId(item.getParentItemId());
                standardInfoDTO.setDeviceSubTypeId(item.getItemId());
                standardInfoDTO.setDeviceTypeName(deviceTypeIdMap.get(item.getParentItemId()).getItemValue());
                standardInfoDTO.setDeviceSubTypeName(item.getItemValue());
                standardInfoDTOList.add(standardInfoDTO);
            });
        }
        return ResponseHelper.successful(standardInfoDTOList);
    }


    @GetMapping(value = "/standard-station-type")
    public ResponseEntity<ResponseResult> getStandardStationType(){
        List<StationTypeStandardInfoDTO> standardInfoDTOList = new ArrayList<>();
        List<DictionaryItem> dictionaryItemList = dictionaryItemService.listByCategoryId(1);
        if(CollectionUtil.isNotEmpty(dictionaryItemList)){
            dictionaryItemList.forEach(item->{
                StationTypeStandardInfoDTO standardInfoDTO = new StationTypeStandardInfoDTO();
                standardInfoDTO.setStationTypeId(item.getItemId());
                standardInfoDTO.setStationTypeName(item.getItemValue());
                standardInfoDTOList.add(standardInfoDTO);
            });
        }
        return ResponseHelper.successful(standardInfoDTOList);
    }
}
