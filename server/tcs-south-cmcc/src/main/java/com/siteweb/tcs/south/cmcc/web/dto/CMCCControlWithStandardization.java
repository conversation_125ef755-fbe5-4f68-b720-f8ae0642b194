package com.siteweb.tcs.south.cmcc.web.dto;

import com.siteweb.tcs.south.cmcc.dal.entity.CMCCControl;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CMCC控制信息（包含标准化信息）
 * <AUTHOR> (2025-08-06)
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CMCCControlWithStandardization extends CMCCControl {

    /**
     * 是否已标准化
     */
    private Boolean standardized;

    /**
     * 标准化名称
     */
    private String standardName;

    /**
     * 标准化ID
     */
    private String standardId;

    /**
     * 构造函数
     */
    public CMCCControlWithStandardization() {
        super();
    }

    /**
     * 从CMCCControl构造
     */
    public CMCCControlWithStandardization(CMCCControl control) {
        super();
        if (control != null) {
            this.setId(control.getId());
            this.setFsuId(control.getFsuId());
            this.setDeviceId(control.getDeviceId());
            this.setSpType(control.getSpType());
            this.setSpId(control.getSpId());
            this.setOriginSpId(control.getOriginSpId());
            this.setSpHubId(control.getSpHubId());
            this.setControlName(control.getControlName());
            this.setMeanings(control.getMeanings());
            this.setSignalNumber(control.getSignalNumber());
            this.setMaxValue(control.getMaxValue());
            this.setMinValue(control.getMinValue());
        }
    }
}
