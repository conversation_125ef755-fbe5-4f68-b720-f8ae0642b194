package com.siteweb.tcs.south.cmcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.siteweb.tcs.south.cmcc.connector.protocol.PK_TypeName;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> (2025-07-01)
 **/

@Getter
@Setter
public class GetFTPAckMessage extends MobileBResponseMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public GetFTPAckMessage() {
        super(PK_TypeName.GET_FTP_ACK);
    }


    @Setter
    @Getter
    public static class Info extends StandardResponseInfo {
        @JacksonXmlProperty(localName = "FSUID")
        @JsonProperty("FSUID")
        private String fsuId;


        /**
         * FTP 账号
         */
        @JacksonXmlProperty(localName = "UserName")
        @JsonProperty("UserName")
        private String username;

        /**
         * FTP 密码
         */
        @JacksonXmlProperty(localName = "PassWord")
        @JsonProperty("PassWord")
        private String password;

    }


}
