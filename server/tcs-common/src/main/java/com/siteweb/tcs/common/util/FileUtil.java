package com.siteweb.tcs.common.util;

import com.siteweb.tcs.common.service.IFileOperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

/**
 * 文件操作工具类
 * 通过接口使用文件系统服务，避免循环依赖
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Slf4j
@Component
public class FileUtil {
    
    @Autowired(required = false)
    private IFileOperationService fileOperationService;
    
    /**
     * 写入文件
     * @param path 文件路径
     * @param fileName 文件名
     * @param content 文件内容
     * @return 是否成功
     */
    public boolean writeFile(String path, String fileName, byte[] content) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }
        return fileOperationService.writeFile(path, fileName, content);
    }
    
    /**
     * 读取文件
     * @param path 文件路径
     * @param fileName 文件名
     * @return 文件内容，不存在时返回null
     */
    public byte[] readFile(String path, String fileName) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }
        return fileOperationService.readFile(path, fileName);
    }
    
    /**
     * 删除文件
     * @param path 文件路径
     * @param fileName 文件名
     * @return 是否成功
     */
    public boolean deleteFile(String path, String fileName) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }
        return fileOperationService.deleteFile(path, fileName);
    }
    
    /**
     * 检查文件是否存在
     * @param path 文件路径
     * @param fileName 文件名
     * @return 是否存在
     */
    public boolean fileExists(String path, String fileName) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }
        return fileOperationService.fileExists(path, fileName);
    }
    
    /**
     * 创建目录
     * @param path 目录路径
     * @return 是否成功
     */
    public boolean createDirectory(String path) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }
        return fileOperationService.createDirectory(path);
    }
    
    /**
     * 列出目录文件
     * @param path 目录路径
     * @return 文件名列表
     */
    public List<String> listFiles(String path) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }
        return fileOperationService.listFiles(path);
    }
    
    /**
     * 复制文件
     * @param sourcePath 源文件路径
     * @param sourceFileName 源文件名
     * @param targetPath 目标文件路径
     * @param targetFileName 目标文件名
     * @return 是否成功
     */
    public boolean copyFile(String sourcePath, String sourceFileName, String targetPath, String targetFileName) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }
        return fileOperationService.copyFile(sourcePath, sourceFileName, targetPath, targetFileName);
    }
    
    /**
     * 移动文件
     * @param sourcePath 源文件路径
     * @param sourceFileName 源文件名
     * @param targetPath 目标文件路径
     * @param targetFileName 目标文件名
     * @return 是否成功
     */
    public boolean moveFile(String sourcePath, String sourceFileName, String targetPath, String targetFileName) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }
        return fileOperationService.moveFile(sourcePath, sourceFileName, targetPath, targetFileName);
    }
    
    /**
     * 删除目录
     * @param path 目录路径
     * @param recursive 是否递归删除
     * @return 是否成功
     */
    public boolean deleteDirectory(String path, boolean recursive) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }
        return fileOperationService.deleteDirectory(path, recursive);
    }

    // ==================== UTF-8字符串便利方法 ====================

    /**
     * 写入UTF-8字符串到文件
     * @param path 文件路径
     * @param fileName 文件名
     * @param content 文件内容
     * @return 是否成功
     */
    public boolean writeUtf8String(String path, String fileName, String content) {
        if (content == null) {
            content = "";
        }
        return writeFile(path, fileName, content.getBytes(java.nio.charset.StandardCharsets.UTF_8));
    }

    /**
     * 读取文件为UTF-8字符串
     * @param path 文件路径
     * @param fileName 文件名
     * @return 文件内容，不存在时返回null
     */
    public String readUtf8String(String path, String fileName) {
        byte[] content = readFile(path, fileName);
        return content != null ? new String(content, java.nio.charset.StandardCharsets.UTF_8) : null;
    }

    /**
     * 追加UTF-8字符串到文件
     * @param path 文件路径
     * @param fileName 文件名
     * @param content 要追加的内容
     * @return 是否成功
     */
    public boolean appendUtf8String(String path, String fileName, String content) {
        if (content == null) {
            content = "";
        }

        // 读取现有内容
        String existingContent = readUtf8String(path, fileName);
        if (existingContent == null) {
            existingContent = "";
        }

        // 合并内容
        String newContent = existingContent + content;
        return writeUtf8String(path, fileName, newContent);
    }

    /**
     * 清空目录（删除目录内容但保留目录）
     * @param path 目录路径
     * @return 是否成功
     */
    public boolean cleanDirectory(String path) {
        // 删除目录
        boolean deleted = deleteDirectory(path, true);
        if (!deleted) {
            log.warn("Failed to delete directory for cleaning: {}", path);
            return false;
        }

        // 重新创建目录
        boolean created = createDirectory(path);
        if (!created) {
            log.warn("Failed to recreate directory after cleaning: {}", path);
            return false;
        }

        return true;
    }

    /**
     * 确保目录存在（如果不存在则创建）
     * @param path 目录路径
     * @return 是否成功
     */
    public boolean ensureDirectoryExists(String path) {
        return createDirectory(path);
    }

    /**
     * 检查目录是否存在
     * @param path 目录路径
     * @return 是否存在
     */
    public boolean directoryExists(String path) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }

        try {
            // 通过尝试列出目录内容来检查目录是否存在
            List<String> files = fileOperationService.listFiles(path);
            return files != null; // 如果能成功列出（即使是空列表），说明目录存在
        } catch (Exception e) {
            // 如果列出失败，说明目录不存在或无权限访问
            return false;
        }
    }

    /**
     * 递归遍历目录下的所有文件
     * @param dirPath 目录路径
     * @return 文件列表（包含完整路径）
     */
    public List<String> loopFiles(String dirPath) {
        if (fileOperationService == null) {
            throw new RuntimeException("FileOperationService not available");
        }

        List<String> allFiles = new java.util.ArrayList<>();
        loopFilesRecursive(dirPath, allFiles);
        return allFiles;
    }

    /**
     * 递归遍历目录的内部实现
     * 使用简单的策略：假设包含扩展名的是文件，不包含的可能是目录
     * @param currentPath 当前路径
     * @param allFiles 收集所有文件的列表
     */
    private void loopFilesRecursive(String currentPath, List<String> allFiles) {
        try {
            // 获取当前目录下的所有文件名
            List<String> fileNames = fileOperationService.listFiles(currentPath);

            for (String fileName : fileNames) {
                String fullPath = currentPath.endsWith("/") || currentPath.endsWith("\\")
                    ? currentPath + fileName
                    : currentPath + "/" + fileName;

                // 简单的文件/目录判断策略
                if (isLikelyFile(fileName)) {
                    // 看起来像文件，直接添加
                    allFiles.add(fullPath);
                } else {
                    // 看起来像目录，尝试递归
                    try {
                        loopFilesRecursive(fullPath, allFiles);
                    } catch (Exception e) {
                        // 如果作为目录失败，可能是一个没有扩展名的文件
                        log.debug("Failed to recurse into {}, treating as file", fullPath);
                        allFiles.add(fullPath);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to list files in directory: {}", currentPath, e);
        }
    }

    /**
     * 简单判断是否可能是文件
     * @param fileName 文件名
     * @return 是否可能是文件
     */
    private boolean isLikelyFile(String fileName) {
        // 包含扩展名的很可能是文件
        if (fileName.contains(".") && !fileName.endsWith(".")) {
            return true;
        }

        // 一些常见的无扩展名文件
        String lowerName = fileName.toLowerCase();
        return lowerName.equals("readme") ||
               lowerName.equals("makefile") ||
               lowerName.equals("dockerfile") ||
               lowerName.equals("license");
    }





    public void deleteFile(File file) {
        deleteFile(file.getPath(),file.getName());
    }

    public void writeUtf8String(String muContext, File file) {
        writeUtf8String(file.getPath(),file.getName(),muContext);
    }
}
