package com.siteweb.tcs.common.o11y.message;

import com.siteweb.tcs.common.ISerializableMessage;
import com.siteweb.tcs.common.o11y.ActorParameter;
import com.siteweb.tcs.common.o11y.WindowLogQueue;
import lombok.Data;

import java.util.HashMap;

/**
 * <AUTHOR> (2025-08-26)
 **/
@Data
public class ProbeDetails implements ISerializableMessage {
    private boolean enableLog = false;
    private boolean enableBypass = false;
    private HashMap<String, Double> gauges;
    private HashMap<String, Long> counters;
    private HashMap<String, WindowLogQueue> windowLogs = new HashMap<String, WindowLogQueue>();
    private HashMap<String, ActorParameter> parameters = new HashMap<>();
}
