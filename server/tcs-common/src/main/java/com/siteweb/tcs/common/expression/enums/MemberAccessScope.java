package com.siteweb.tcs.common.expression.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.siteweb.tcs.common.annotations.EnumScan;
import com.siteweb.tcs.common.annotations.IEnumerable;
import com.siteweb.tcs.common.expression.ExpLBuilder;
import lombok.Getter;

/**
 * 成员访问表达式用，用于赋值
 * <AUTHOR> (2025-02-28)
 **/
@EnumScan(name = "expression_member_access")
public enum MemberAccessScope implements IEnumerable {
    GLOBAL("global", (property) -> "global." + property, "全局属性"),
    GRAPH("graph", (property) -> "graph." + property, "Graph属性"),
    FLOW("flow", (property) -> "flow." + property, "Flow属性"),
    SHAPE("shape", (property) -> "shape." + property, "Shape属性"),
    MESSAGE("msg", (property) -> property.isEmpty() ? "nsg" : "msg." + property, "消息属性"),
    EXPRESSION("exp", (property) -> property, "表达式"),

    ;


    public final ExpLBuilder buildMethod;

    @Getter
    @JsonValue
    private final String value;

    @Getter
    private final String description;


    MemberAccessScope(String code, ExpLBuilder buildMethod, String description) {
        this.value = code;
        this.description = description;
        this.buildMethod = buildMethod;
    }



    @JsonCreator
    public static MemberAccessScope fromInt(String i) {
        for (MemberAccessScope status : MemberAccessScope.values()) {
            if (status.value.equals(i)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with ordinal " + i);
    }


}
