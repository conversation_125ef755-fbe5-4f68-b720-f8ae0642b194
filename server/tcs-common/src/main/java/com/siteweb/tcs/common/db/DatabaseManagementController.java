package com.siteweb.tcs.common.db;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库管理控制器
 * 提供数据库管理相关的REST API接口
 */
@RestController
@RequestMapping("/api/db")
public class DatabaseManagementController {

    @Autowired
    private DatabaseMigrationService migrationService;

    @Autowired
    private DatabaseRepairService repairService;

    @Autowired
    private DatabaseModeManager modeManager;

    /**
     * 获取数据库当前状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getDatabaseStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("version", migrationService.getCurrentVersion());
        status.put("mode", modeManager.getCurrentMode());
        status.put("needMigration", migrationService.needMigration());
        status.put("writeAllowed", modeManager.isWriteAllowed());
        return ResponseEntity.ok(status);
    }

    /**
     * 获取迁移历史
     */
    @GetMapping("/migrations")
    public ResponseEntity<List<Map<String, Object>>> getMigrationHistory() {
        return ResponseEntity.ok(migrationService.getMigrationHistory());
    }

    /**
     * 执行数据库修复
     */
    @PostMapping("/repair")
    public ResponseEntity<Map<String, Object>> repairDatabase() {
        boolean success = repairService.repairDatabase();
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("message", success ? "数据库修复成功" : "数据库修复失败");
        return ResponseEntity.ok(result);
    }

    /**
     * 切换数据库运行模式
     */
    @PostMapping("/mode")
    public ResponseEntity<Map<String, Object>> switchMode(@RequestParam String mode) {
        DatabaseModeManager.DatabaseMode targetMode;
        try {
            targetMode = DatabaseModeManager.DatabaseMode.valueOf(mode.toUpperCase());
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "无效的数据库运行模式"
            ));
        }

        boolean success = modeManager.switchMode(targetMode);
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("message", success ? "模式切换成功" : "模式切换失败");
        result.put("currentMode", modeManager.getCurrentMode());
        return ResponseEntity.ok(result);
    }

    /**
     * 执行数据库重置
     */
    @PostMapping("/reset")
    public ResponseEntity<Map<String, Object>> resetDatabase() {
        boolean success = repairService.resetDatabase();
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("message", success ? "数据库重置成功" : "数据库重置失败");
        return ResponseEntity.ok(result);
    }

    /**
     * 执行手动迁移脚本
     */
    @PostMapping("/execute-script")
    public ResponseEntity<Map<String, Object>> executeScript(
            @RequestParam String scriptPath,
            @RequestParam(required = false) String description) {
        boolean success = migrationService.executeScript(scriptPath, description);
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("message", success ? "脚本执行成功" : "脚本执行失败");
        return ResponseEntity.ok(result);
    }

    /**
     * 上传并执行增量SQL脚本
     */
    @PostMapping("/upload-script")
    public ResponseEntity<Map<String, Object>> uploadAndExecuteScript(
            @RequestParam String scriptContent,
            @RequestParam String version,
            @RequestParam String description) {
        boolean success = migrationService.uploadAndExecuteIncrementalScript(
                scriptContent, version, description);
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        result.put("message", success ? "增量脚本执行成功" : "增量脚本执行失败");
        return ResponseEntity.ok(result);
    }
}