//package com.siteweb.tcs.common.config;
//
//import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
//import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
//import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 禁用Redis自动配置
// * 用于在没有Redis服务的环境中运行
// */
//@Configuration
//@EnableAutoConfiguration(exclude = {
//        RedisAutoConfiguration.class,
//        RedisRepositoriesAutoConfiguration.class
//})
//public class DisableRedisConfig {
//    // 空实现，仅用于禁用Redis自动配置
//}
