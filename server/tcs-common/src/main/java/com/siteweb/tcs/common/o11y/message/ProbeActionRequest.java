package com.siteweb.tcs.common.o11y.message;

import com.siteweb.tcs.common.ISerializableMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.pekko.actor.ActorRef;

/**
 * 探针操作请求消息
 * 用于向Actor发送探针相关的操作请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProbeActionRequest implements ISerializableMessage {
    /**
     * 发送者是谁
     */
    private ActorRef sender;

    /**
     * 操作类型
     */
    private ActionType actionType;

    /**
     * 操作参数名称
     */
    private String paramName;

    /**
     * 操作参数值
     */
    private String paramValue;

    /**
     * 操作类型枚举
     */
    public enum ActionType {
        /**
         * 获取日志
         */
        GET_DETAILS,

        /**
         * 启用/禁用Actor日志
         */
        ENABLE_LOG,

        /**
         * 启用/禁用旁路
         */
        ENABLE_BYPASS,

        /**
         * 设置参数
         */
        SET_PARAMETER
    }

}