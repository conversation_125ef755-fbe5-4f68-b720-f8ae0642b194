package com.siteweb.tcs.common.expression;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 赋值表达式
 * <AUTHOR> (2025-02-28)
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssignExpression {

    /**
     * 成员访问表达式
     */
    private MemberAccessExpression property;


    /**
     * 值表达式
     */
    private ValueExpression value;


    public String toExpression(){
        return  property.toExpression() + " = " + value.toExpression();
    }
}
