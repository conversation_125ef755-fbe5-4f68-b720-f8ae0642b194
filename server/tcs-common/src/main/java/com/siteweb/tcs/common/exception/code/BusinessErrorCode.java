package com.siteweb.tcs.common.exception.code;

import com.siteweb.tcs.common.exception.core.BusinessException;

/**
 * Interface for business error codes.
 * All business error code enums should implement this interface.
 */
public interface BusinessErrorCode extends ErrorCode {
    /**
     * Get the error category
     * @return Always returns BUSINESS
     */
    @Override
    default ErrorCategory getCategory() {
        return ErrorCategory.BUSINESS;
    }

    /**
     * Convert to a BusinessException
     * @return A new BusinessException with this error code
     */
    default BusinessException toException() {
        return new BusinessException(this);
    }

    /**
     * Convert to a BusinessException with details
     * @param details Additional error details
     * @return A new BusinessException with this error code and details
     */
    default BusinessException toException(Object details) {
        return new BusinessException(this, details);
    }

    /**
     * Convert to a BusinessException with a cause
     * @param cause The cause of the exception
     * @return A new BusinessException with this error code and cause
     */
    default BusinessException toException(Throwable cause) {
        return new BusinessException(this, cause);
    }

    /**
     * Convert to a BusinessException with details and a cause
     * @param details Additional error details
     * @param cause The cause of the exception
     * @return A new BusinessException with this error code, details, and cause
     */
    default BusinessException toException(Object details, Throwable cause) {
        return new BusinessException(this, details, cause);
    }

    /**
     * Convert to a BusinessException with parameters for message formatting
     * @param params Parameters for message formatting
     * @return A new BusinessException with this error code and parameters
     */
    default BusinessException toException(String[] params) {
        return new BusinessException(this, params);
    }
}
