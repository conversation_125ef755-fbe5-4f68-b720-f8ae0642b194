package com.siteweb.tcs.common.exception.util;

import com.siteweb.tcs.common.exception.code.StandardBusinessErrorCode;
import com.siteweb.tcs.common.exception.code.StandardTechnicalErrorCode;
import com.siteweb.tcs.common.exception.core.BusinessException;
import com.siteweb.tcs.common.exception.core.TCSException;
import com.siteweb.tcs.common.exception.core.TechnicalException;
import com.siteweb.tcs.common.exception.handler.ExceptionTranslator;

import java.util.function.Supplier;

/**
 * Utility methods for working with exceptions.
 */
public class ExceptionUtils {

    /**
     * Execute a function and translate any exceptions
     * @param supplier The function to execute
     * @param <T> The return type
     * @return The result of the function
     * @throws TCSException If an exception occurs
     */
    public static <T> T executeAndTranslate(Supplier<T> supplier) throws TCSException {
        try {
            return supplier.get();
        } catch (Throwable throwable) {
            throw ExceptionTranslator.translate(throwable);
        }
    }

    /**
     * Execute a function and translate any exceptions to business exceptions
     * @param supplier The function to execute
     * @param <T> The return type
     * @return The result of the function
     * @throws BusinessException If an exception occurs
     */
    public static <T> T executeAndTranslateToBusiness(Supplier<T> supplier) throws BusinessException {
        try {
            return supplier.get();
        } catch (Throwable throwable) {
            TCSException translated = ExceptionTranslator.translate(throwable);
            if (translated instanceof BusinessException) {
                throw (BusinessException) translated;
            } else {
                throw new BusinessException(StandardBusinessErrorCode.BUSINESS_ERROR, throwable);
            }
        }
    }

    /**
     * Execute a function and translate any exceptions to technical exceptions
     * @param supplier The function to execute
     * @param <T> The return type
     * @return The result of the function
     * @throws TechnicalException If an exception occurs
     */
    public static <T> T executeAndTranslateToTechnical(Supplier<T> supplier) throws TechnicalException {
        try {
            return supplier.get();
        } catch (Throwable throwable) {
            TCSException translated = ExceptionTranslator.translate(throwable);
            if (translated instanceof TechnicalException) {
                throw (TechnicalException) translated;
            } else {
                throw new TechnicalException(StandardTechnicalErrorCode.SYSTEM_ERROR, throwable);
            }
        }
    }

    /**
     * Get the root cause of an exception
     * @param throwable The exception
     * @return The root cause
     */
    public static Throwable getRootCause(Throwable throwable) {
        Throwable cause = throwable.getCause();
        if (cause == null || cause == throwable) {
            return throwable;
        }
        return getRootCause(cause);
    }

    /**
     * Get the exception message including the root cause
     * @param throwable The exception
     * @return The exception message including the root cause
     */
    public static String getMessageWithCause(Throwable throwable) {
        Throwable rootCause = getRootCause(throwable);
        if (rootCause == throwable) {
            return throwable.getMessage();
        }
        return throwable.getMessage() + " (Caused by: " + rootCause.getMessage() + ")";
    }

    /**
     * Check if an exception is of a specific type or has a cause of that type
     * @param throwable The exception
     * @param exceptionClass The exception class to check for
     * @return True if the exception is of the specified type or has a cause of that type
     */
    public static boolean isExceptionOfType(Throwable throwable, Class<? extends Throwable> exceptionClass) {
        if (exceptionClass.isInstance(throwable)) {
            return true;
        }
        Throwable cause = throwable.getCause();
        if (cause == null || cause == throwable) {
            return false;
        }
        return isExceptionOfType(cause, exceptionClass);
    }
}
