package com.siteweb.tcs.common.expression.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.siteweb.tcs.common.annotations.EnumScan;
import com.siteweb.tcs.common.annotations.IEnumerable;
import com.siteweb.tcs.common.expression.ExpLBuilder;
import lombok.Getter;

/**
 * 值表达式Scope，用于获取指定结果值
 *
 * <AUTHOR> (2025-02-18)
 **/
@EnumScan(name = "expression_values")
public enum ValueScope implements IEnumerable {
    ENVIRON("env", (property) -> "env." + property, "环境变量"),
    GLOBAL("global", (property) -> "global." + property, "全局属性"),
    GRAPH("graph", (property) -> "graph." + property, "Graph属性"),
    FLOW("flow", (property) -> "flow." + property, "Flow属性"),
    SHAPE("shape", (property) -> "shape." + property, "Shape属性"),
    MESSAGE("msg", (property) -> property.isEmpty() ? "nsg" : "msg." + property, "消息属性"),

    // ====================
    // 静态数据
    NULL("null", (property) -> "nil", "空对象"),
    TEXT("text", (property) -> "'" + property + "'", "文本"),
    INTEGER("int", (property) -> property, "整数"),
    FLOAT("float", (property) -> property, "浮点数"),
    BOOLEAN("bool", (property) -> property, "布尔值"),
    JSON("json", (property) -> "JSON$('" + property.replace("\n", "\\n") + "')", "Json对象"),
    OBJECT("object", (property) -> "seq.map()", "空对象(Map)"),
    // ====================
    // 表达式
    EXPRESSION("exp", (property) -> property, "表达式"),
    ;

    public final ExpLBuilder buildMethod;

    @Getter
    @JsonValue
    private final String value;

    @Getter
    private final String description;


    ValueScope(String code, ExpLBuilder buildMethod, String description) {
        this.value = code;
        this.description = description;
        this.buildMethod = buildMethod;
    }


    @JsonCreator
    public static ValueScope fromInt(String i) {
        for (ValueScope status : ValueScope.values()) {
            if (status.value.equals(i)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with ordinal " + i);
    }


}
