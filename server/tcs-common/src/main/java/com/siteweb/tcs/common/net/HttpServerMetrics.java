package com.siteweb.tcs.common.net;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * HTTP服务器监控指标管理器
 * 用于跟踪请求数量、异常、延迟和速率等关键监控指标
 *
 * <AUTHOR> (2025-01-15)
 */
public class HttpServerMetrics {

    private static final Logger logger = LoggerFactory.getLogger(HttpServerMetrics.class);

    // 总包数量计数器
    private final LongAdder totalPackageCount = new LongAdder();
    
    // 异常数量计数器
    private final LongAdder exceptionCount = new LongAdder();
    
    // 成功数量计数器
    private final LongAdder successCount = new LongAdder();
    
    // 总响应时间累计（毫秒）
    private final LongAdder totalResponseTime = new LongAdder();
    
    // 最大响应时间
    private final AtomicLong maxResponseTime = new AtomicLong(0);
    
    // 最小响应时间
    private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
    
    // 按路径统计的请求数
    private final ConcurrentHashMap<String, LongAdder> pathRequestCounts = new ConcurrentHashMap<>();
    
    // 按路径统计的异常数
    private final ConcurrentHashMap<String, LongAdder> pathExceptionCounts = new ConcurrentHashMap<>();
    
    // 速率计算相关
    private final AtomicLong lastResetTime = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong packagesAtLastReset = new AtomicLong(0);
    
    // 读写锁用于保护复合操作
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    
    // 服务器启动时间
    private final LocalDateTime startTime = LocalDateTime.now();


    public void incrementTotalPackage(){
        totalPackageCount.increment();
    }

    public void incrementExceptionPackage(){
        exceptionCount.increment();
    }


    /**
     * 记录请求开始
     */
    public void recordRequestStart(String path) {

        pathRequestCounts.computeIfAbsent(path, k -> new LongAdder()).increment();
        logger.debug("Request started for path: {}, total count: {}", path, totalPackageCount.sum());
    }

    /**
     * 记录请求成功完成
     */
    public void recordRequestSuccess(String path, long responseTimeMs) {
        successCount.increment();
        recordResponseTime(responseTimeMs);
        logger.debug("Request completed successfully for path: {}, response time: {}ms", path, responseTimeMs);
    }

    /**
     * 记录请求异常
     */
    public void recordRequestException(String path, Throwable exception) {
        exceptionCount.increment();
        pathExceptionCounts.computeIfAbsent(path, k -> new LongAdder()).increment();
        logger.warn("Request failed for path: {}, exception: {}", path, exception.getMessage());
    }

    /**
     * 记录响应时间
     */
    private void recordResponseTime(long responseTimeMs) {
        totalResponseTime.add(responseTimeMs);
        
        // 更新最大响应时间
        long currentMax = maxResponseTime.get();
        while (responseTimeMs > currentMax && !maxResponseTime.compareAndSet(currentMax, responseTimeMs)) {
            currentMax = maxResponseTime.get();
        }
        
        // 更新最小响应时间
        long currentMin = minResponseTime.get();
        while (responseTimeMs < currentMin && !minResponseTime.compareAndSet(currentMin, responseTimeMs)) {
            currentMin = minResponseTime.get();
        }
    }

    /**
     * 获取当前监控指标快照
     */
    public MetricsSnapshot getMetricsSnapshot() {
        lock.readLock().lock();
        try {
            long totalCount = totalPackageCount.sum();
            long exceptions = exceptionCount.sum();
            long successes = successCount.sum();
            long totalTime = totalResponseTime.sum();
            
            return new MetricsSnapshot(
                totalCount,
                exceptions,
                successes,
                calculatePackageRate(),
                calculateAverageResponseTime(successes, totalTime),
                maxResponseTime.get(),
                minResponseTime.get() == Long.MAX_VALUE ? 0 : minResponseTime.get(),
                startTime,
                LocalDateTime.now(),
                new ConcurrentHashMap<>(pathRequestCounts),
                new ConcurrentHashMap<>(pathExceptionCounts)
            );
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 计算包速率 (包/秒)
     */
    private double calculatePackageRate() {
        long currentTime = System.currentTimeMillis();
        long lastReset = lastResetTime.get();
        long currentCount = totalPackageCount.sum();
        long lastCount = packagesAtLastReset.get();
        
        long timeDiffMs = currentTime - lastReset;
        if (timeDiffMs <= 0) {
            return 0.0;
        }
        
        long countDiff = currentCount - lastCount;
        return (double) countDiff / (timeDiffMs / 1000.0);
    }

    /**
     * 计算平均响应时间
     */
    private double calculateAverageResponseTime(long successCount, long totalTime) {
        return successCount > 0 ? (double) totalTime / successCount : 0.0;
    }

    /**
     * 重置速率计算基准点
     */
    public void resetRateCalculation() {
        lock.writeLock().lock();
        try {
            lastResetTime.set(System.currentTimeMillis());
            packagesAtLastReset.set(totalPackageCount.sum());
            logger.info("Rate calculation reset");
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 重置所有监控指标
     */
    public void resetAllMetrics() {
        lock.writeLock().lock();
        try {
            totalPackageCount.reset();
            exceptionCount.reset();
            successCount.reset();
            totalResponseTime.reset();
            maxResponseTime.set(0);
            minResponseTime.set(Long.MAX_VALUE);
            pathRequestCounts.clear();
            pathExceptionCounts.clear();
            lastResetTime.set(System.currentTimeMillis());
            packagesAtLastReset.set(0);
            logger.info("All metrics reset");
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 监控指标快照数据类
     */
    public static class MetricsSnapshot {
        @JsonProperty("totalPackageCount")
        private final long totalPackageCount;
        
        @JsonProperty("exceptionCount")
        private final long exceptionCount;
        
        @JsonProperty("successCount")
        private final long successCount;
        
        @JsonProperty("packageRatePerSecond")
        private final double packageRatePerSecond;
        
        @JsonProperty("averageResponseTimeMs")
        private final double averageResponseTimeMs;
        
        @JsonProperty("maxResponseTimeMs")
        private final long maxResponseTimeMs;
        
        @JsonProperty("minResponseTimeMs")
        private final long minResponseTimeMs;
        
        @JsonProperty("startTime")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private final LocalDateTime startTime;
        
        @JsonProperty("snapshotTime")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private final LocalDateTime snapshotTime;
        
        @JsonProperty("pathRequestCounts")
        private final ConcurrentHashMap<String, LongAdder> pathRequestCounts;
        
        @JsonProperty("pathExceptionCounts")
        private final ConcurrentHashMap<String, LongAdder> pathExceptionCounts;

        public MetricsSnapshot(long totalPackageCount, long exceptionCount, long successCount,
                             double packageRatePerSecond, double averageResponseTimeMs,
                             long maxResponseTimeMs, long minResponseTimeMs,
                             LocalDateTime startTime, LocalDateTime snapshotTime,
                             ConcurrentHashMap<String, LongAdder> pathRequestCounts,
                             ConcurrentHashMap<String, LongAdder> pathExceptionCounts) {
            this.totalPackageCount = totalPackageCount;
            this.exceptionCount = exceptionCount;
            this.successCount = successCount;
            this.packageRatePerSecond = packageRatePerSecond;
            this.averageResponseTimeMs = averageResponseTimeMs;
            this.maxResponseTimeMs = maxResponseTimeMs;
            this.minResponseTimeMs = minResponseTimeMs;
            this.startTime = startTime;
            this.snapshotTime = snapshotTime;
            this.pathRequestCounts = pathRequestCounts;
            this.pathExceptionCounts = pathExceptionCounts;
        }

        // Getters
        public long getTotalPackageCount() { return totalPackageCount; }
        public long getExceptionCount() { return exceptionCount; }
        public long getSuccessCount() { return successCount; }
        public double getPackageRatePerSecond() { return packageRatePerSecond; }
        public double getAverageResponseTimeMs() { return averageResponseTimeMs; }
        public long getMaxResponseTimeMs() { return maxResponseTimeMs; }
        public long getMinResponseTimeMs() { return minResponseTimeMs; }
        public LocalDateTime getStartTime() { return startTime; }
        public LocalDateTime getSnapshotTime() { return snapshotTime; }
        public ConcurrentHashMap<String, LongAdder> getPathRequestCounts() { return pathRequestCounts; }
        public ConcurrentHashMap<String, LongAdder> getPathExceptionCounts() { return pathExceptionCounts; }

        @Override
        public String toString() {
            return String.format(
                "MetricsSnapshot{total=%d, success=%d, exceptions=%d, rate=%.2f/s, avgTime=%.2fms, maxTime=%dms, minTime=%dms}",
                totalPackageCount, successCount, exceptionCount, packageRatePerSecond, 
                averageResponseTimeMs, maxResponseTimeMs, minResponseTimeMs
            );
        }
    }
} 