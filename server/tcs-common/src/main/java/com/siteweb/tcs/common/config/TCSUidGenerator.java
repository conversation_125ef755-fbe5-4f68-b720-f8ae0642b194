package com.siteweb.tcs.common.config;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.github.wujun234.uid.UidGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * @ClassName: TCSUidGenerator
 * @descriptions: 百度雪花算法生成器
 * @author: xsx
 * @date: 12/9/2024 5:33 PM
 **/
@Component
public class TCSUidGenerator implements IdentifierGenerator {

    @Autowired
    @Lazy
    private UidGenerator cachedUidGenerator;

    @Override
    public Number nextId(Object entity) {
        long uid = cachedUidGenerator.getUID();
        return uid;
    }
}
