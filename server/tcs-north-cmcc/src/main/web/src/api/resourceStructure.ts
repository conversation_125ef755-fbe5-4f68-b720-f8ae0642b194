import { http } from "@/utils/http";

export interface StructureTreeNode {
  rId: number;
  typeId: number;
  rName: string;
  pId?: number;
  disp?: boolean;
  sort?: number;
  eqs?: any[];
  children?: StructureTreeNode[];
  sId?: number;
  hId?: number;
  ssId?: number;
}

export interface ApiResponse<T> {
  code: number;
  timestamp: number;
  data: T;
  msg: string | null;
}

/**
 * 获取资源结构树
 */
export const getResourceStructureTree = (params?: {
  resourceStructureId?: number;
  eqs?: boolean;
  maxDepth?: number;
}) => {
  return http.request<ApiResponse<StructureTreeNode>>(
    "get",
    "/api/thing/tcs-north-cmcc/siteweb-resource-structure/tree",
    { params }
  );
};

/**
 * 调整资源结构层级关系
 */
export const changeResourceStructureHierarchy = (params: {
  resourceStructureId: number;
  newParentId: number;
}) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-cmcc/siteweb-resource-structure/change-hierarchy",
    { params }
  );
};
