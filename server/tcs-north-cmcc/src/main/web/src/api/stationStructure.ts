import { http } from "@/utils/http";

export interface StationStructureNode {
  structureId: number;
  parentStructureId?: number;
  structureName: string;
  structureType?: number;
  levelPath?: string;
  children?: StationStructureNode[];
  stations?: StationNode[];
}

export interface StationNode {
  stationId: number;
  stationName: string;
}

export interface ApiResponse<T> {
  code: number;
  timestamp: number;
  data: T;
  msg: string | null;
}

export const getStationStructureTree = () => {
  return http.request<ApiResponse<StationStructureNode>>(
    "get",
    "/api/thing/tcs-north-cmcc/siteweb-station-structure/tree"
  );
};

export const changeDivisionHierarchy = (params: {
  divisionStructureId: number;
  newParentStructureId: number;
}) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-cmcc/siteweb-station-structure/change-division-hierarchy",
    { params }
  );
};

export const changeStationHierarchy = (params: {
  stationId: number;
  newDivisionStructureId: number;
}) => {
  return http.request<ApiResponse<string>>(
    "post",
    "/api/thing/tcs-north-cmcc/siteweb-station-structure/change-station-hierarchy",
    { params }
  );
};


