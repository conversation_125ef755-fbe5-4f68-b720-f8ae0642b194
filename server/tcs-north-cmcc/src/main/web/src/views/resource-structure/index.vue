<template>
  <div class="page-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header flex-bc">
          <span>资源结构管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshTree">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="info" @click="toggleDragMode">
              <el-icon><Operation /></el-icon>
              {{ dragMode ? '退出拖拽' : '拖拽调整' }}
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="tree-container">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :loading="loading"
          node-key="rId"
          :expand-on-click-node="false"
          :default-expand-all="false"
          :default-expanded-keys="expandedKeys"
          :draggable="dragMode"
          :allow-drop="allowDrop"
          :allow-drag="allowDrag"
          @node-click="handleNodeClick"
          @node-drop="handleNodeDrop"
          @node-drag-start="handleDragStart"
          @node-drag-over="handleDragOver"
          @node-drag-leave="handleDragLeave"
          @node-drag-end="handleDragEnd"
          class="resource-tree"
          :class="{ 'drag-mode': dragMode }"
        >
          <template #default="{ node, data }">
            <div class="tree-node" :class="{ 'drag-over': data.dragOver }">
              <el-icon class="node-icon" :class="getNodeIconClass(data.typeId)">
                <component :is="getNodeIcon(data.typeId)" />
              </el-icon>
              <span class="node-label">{{ data.rName }}</span>
              <el-tag v-if="data.typeId" size="small" :type="getNodeTagType(data.typeId)">
                {{ getNodeTypeName(data.typeId) }}
              </el-tag>
              <div v-if="dragMode" class="drag-handle">
                <el-icon><Rank /></el-icon>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Refresh, Operation, Rank } from "@element-plus/icons-vue";
import { getResourceStructureTree, changeResourceStructureHierarchy, type StructureTreeNode } from "@/api/resourceStructure";

// 响应式数据
const loading = ref(false);
const treeData = ref<StructureTreeNode[]>([]);
const expandedKeys = ref<number[]>([]);
const treeRef = ref();
const dragMode = ref(false);
const dragNode = ref<StructureTreeNode | null>(null);

// 树配置
const treeProps = {
  children: "children",
  label: "rName"
};

// 资源类型映射 - 使用更贴切的图标
const structureTypeMap = {
  1: { name: "ECC", icon: "Monitor", color: "primary" },
  2: { name: "园区", icon: "OfficeBuilding", color: "success" },
  3: { name: "大楼", icon: "Building", color: "info" },
  4: { name: "楼层", icon: "Grid", color: "warning" },
  5: { name: "房间", icon: "House", color: "danger" },
  6: { name: "MDC", icon: "DataBoard", color: "primary" },
  7: { name: "设备", icon: "Setting", color: "success" },
  8: { name: "通用对象", icon: "Box", color: "info" },
  9: { name: "机架", icon: "Grid", color: "warning" },
  10: { name: "IT设备", icon: "Monitor", color: "danger" },
  101: { name: "省中心", icon: "OfficeBuilding", color: "primary" },
  102: { name: "地市中心", icon: "Location", color: "success" },
  103: { name: "片区", icon: "MapLocation", color: "info" },
  104: { name: "基站", icon: "Tower", color: "warning" },
  105: { name: "局房", icon: "House", color: "danger" }
};

// 获取节点图标
const getNodeIcon = (typeId: number) => {
  return structureTypeMap[typeId]?.icon || "Box";
};

// 获取节点图标样式类
const getNodeIconClass = (typeId: number) => {
  return `icon-${structureTypeMap[typeId]?.color || "info"}`;
};

// 获取节点类型名称
const getNodeTypeName = (typeId: number) => {
  return structureTypeMap[typeId]?.name || "未知类型";
};

// 获取节点标签类型
const getNodeTagType = (typeId: number) => {
  return structureTypeMap[typeId]?.color || "info";
};

// 切换拖拽模式
const toggleDragMode = () => {
  dragMode.value = !dragMode.value;
  if (!dragMode.value) {
    // 退出拖拽模式时清理状态
    dragNode.value = null;
    clearDragOverState();
  }
};

// 清理拖拽悬停状态
const clearDragOverState = () => {
  const clearDragOver = (nodes: StructureTreeNode[]) => {
    nodes.forEach(node => {
      node.dragOver = false;
      if (node.children) {
        clearDragOver(node.children);
      }
    });
  };
  clearDragOver(treeData.value);
};

// 拖拽开始
const handleDragStart = (node: any, ev: DragEvent) => {
  dragNode.value = node.data;
  console.log("开始拖拽:", node.data);
};

// 拖拽悬停
const handleDragOver = (draggingNode: any, dropNode: any, ev: DragEvent) => {
  // 设置悬停状态
  dropNode.data.dragOver = true;
};

// 拖拽离开
const handleDragLeave = (draggingNode: any, dropNode: any, ev: DragEvent) => {
  // 清除悬停状态
  dropNode.data.dragOver = false;
};

// 拖拽结束
const handleDragEnd = (draggingNode: any, dropNode: any, dropType: string, ev: DragEvent) => {
  // 清理所有拖拽状态
  clearDragOverState();
  dragNode.value = null;
};

// 判断是否允许拖拽
const allowDrag = (draggingNode: any) => {
  // 根节点不允许拖拽
  return draggingNode.level !== 1;
};

// 判断是否允许放置
const allowDrop = (draggingNode: any, dropNode: any, type: string) => {
  // 不能拖拽到自己或自己的子节点
  if (draggingNode.data.rId === dropNode.data.rId) {
    return false;
  }
  
  // 检查是否是子节点
  const isChildNode = (parent: StructureTreeNode, child: StructureTreeNode): boolean => {
    if (!parent.children) return false;
    for (const node of parent.children) {
      if (node.rId === child.rId) return true;
      if (isChildNode(node, child)) return true;
    }
    return false;
  };
  
  if (isChildNode(draggingNode.data, dropNode.data)) {
    return false;
  }
  
  // 根据业务规则判断是否允许放置
  const dragType = draggingNode.data.typeId;
  const dropType = dropNode.data.typeId;
  
  // 基站不能拖拽到局房下
  if (dragType === 104 && dropType === 105) {
    return false;
  }
  
  return true;
};

// 处理节点放置
const handleNodeDrop = async (draggingNode: any, dropNode: any, dropType: string, ev: DragEvent) => {
  try {
    const resourceStructureId = draggingNode.data.rId;
    const newParentId = dropNode.data.rId;
    
    // 确认操作
    await ElMessageBox.confirm(
      `确定要将 "${draggingNode.data.rName}" 移动到 "${dropNode.data.rName}" 下吗？`,
      "确认调整",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    loading.value = true;
    const res = await changeResourceStructureHierarchy({
      resourceStructureId,
      newParentId
    });
    
    if (res.code === 0) {
      ElMessage.success("调整层级关系成功");
      // 刷新树数据
      await loadTreeData();
    } else {
      ElMessage.error(res.msg || "调整层级关系失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("调整层级关系失败:", error);
      ElMessage.error("调整层级关系失败");
    }
  } finally {
    loading.value = false;
  }
};

// 加载资源结构树
const loadTreeData = async () => {
  loading.value = true;
  try {
    const res = await getResourceStructureTree({ eqs: false, maxDepth: 10 });
    if (res.code === 0) {
      treeData.value = res.data ? [res.data] : [];
      // 默认展开前两级
      expandedKeys.value = getExpandedKeys(treeData.value, 2);
    } else {
      ElMessage.error(res.msg || "获取资源结构树失败");
    }
  } catch (error) {
    console.error("获取资源结构树失败:", error);
    ElMessage.error("获取资源结构树失败");
  } finally {
    loading.value = false;
  }
};

// 获取展开的节点键
const getExpandedKeys = (nodes: StructureTreeNode[], maxDepth: number, currentDepth = 0): number[] => {
  if (currentDepth >= maxDepth) return [];
  
  const keys: number[] = [];
  nodes.forEach(node => {
    keys.push(node.rId);
    if (node.children && node.children.length > 0) {
      keys.push(...getExpandedKeys(node.children, maxDepth, currentDepth + 1));
    }
  });
  return keys;
};

// 刷新树
const refreshTree = () => {
  loadTreeData();
};

// 节点点击事件
const handleNodeClick = (data: StructureTreeNode) => {
  if (!dragMode.value) {
    console.log("点击节点:", data);
  }
};

// 组件挂载
onMounted(() => {
  loadTreeData();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.tree-container {
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 10px;
}

.resource-tree {
  width: 100%;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.tree-node:hover {
  background-color: var(--el-fill-color-light);
}

.tree-node.drag-over {
  background-color: var(--el-color-primary-light-9);
  border: 2px dashed var(--el-color-primary);
}

.drag-mode .tree-node {
  cursor: move;
}

.drag-mode .tree-node:hover {
  background-color: var(--el-color-primary-light-9);
}

.node-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.icon-primary {
  color: var(--el-color-primary);
}

.icon-success {
  color: var(--el-color-success);
}

.icon-info {
  color: var(--el-color-info);
}

.icon-warning {
  color: var(--el-color-warning);
}

.icon-danger {
  color: var(--el-color-danger);
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.drag-handle {
  display: flex;
  align-items: center;
  color: var(--el-text-color-secondary);
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

/* 拖拽模式下的样式 */
.drag-mode .el-tree-node__content {
  cursor: move;
}

.drag-mode .el-tree-node__content:hover {
  background-color: var(--el-color-primary-light-9);
}

/* 自定义滚动条 */
.tree-container::-webkit-scrollbar {
  width: 6px;
}

.tree-container::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

.tree-container::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.tree-container::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 拖拽时的视觉反馈 */
:deep(.el-tree-node.is-drop-inner) {
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-tree-node.is-drop-inner .el-tree-node__content) {
  background-color: var(--el-color-primary-light-9);
  border: 2px dashed var(--el-color-primary);
}
</style>