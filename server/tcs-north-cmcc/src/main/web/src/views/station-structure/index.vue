<template>
  <div class="page-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header flex-bc">
          <span>局站结构管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshTree">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="info" @click="toggleDragMode">
              <el-icon><Operation /></el-icon>
              {{ dragMode ? '退出拖拽' : '拖拽调整' }}
            </el-button>
          </div>
        </div>
      </template>

      <div class="tree-container">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          :loading="loading"
          node-key="structureId"
          :expand-on-click-node="false"
          :default-expand-all="false"
          :default-expanded-keys="expandedKeys"
          :draggable="dragMode"
          :allow-drop="allowDrop"
          :allow-drag="allowDrag"
          @node-drop="handleNodeDrop"
          class="resource-tree"
          :class="{ 'drag-mode': dragMode }"
        >
          <template #default="{ node, data }">
            <div class="flex-bc w-full">
              <div class="flex items-center gap-2">
                <span>{{ data.structureName }}</span>
                <el-tag v-if="data.structureType === 1" size="small" type="info">区划</el-tag>
                <el-tag v-else-if="data.structureType === 2" size="small" type="success">中心</el-tag>
                <el-tag v-else-if="data.structureType === 3" size="small" type="warning">三级</el-tag>
                <el-tag v-else-if="data.structureType === 4" size="small" type="danger">四级</el-tag>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </el-card>
  </div>
  
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Refresh, Operation } from "@element-plus/icons-vue";
import { getStationStructureTree, changeDivisionHierarchy } from "@/api/stationStructure";

type StationStructureNode = {
  structureId: number;
  parentStructureId?: number;
  structureName: string;
  structureType?: number;
  children?: StationStructureNode[];
};

const loading = ref(false);
const treeData = ref<StationStructureNode[]>([]);
const expandedKeys = ref<number[]>([]);
const dragMode = ref(false);
const treeRef = ref();

const treeProps = {
  children: "children",
  label: "structureName"
};

const allowDrag = () => dragMode.value;
const allowDrop = (dragNode: any, dropNode: any, type: string) => {
  if (!dragMode.value) return false;
  // 只允许区划之间调整层级，或区划移动到中心/区划下
  const dragType = dragNode.data.structureType;
  const dropType = dropNode.data.structureType;
  if (dragType === 1 && (dropType === 1 || dropType === 2)) {
    return type === "inner"; // 只能作为子节点
  }
  return false;
};

const handleNodeDrop = async (draggingNode: any, dropNode: any, dropType: string) => {
  try {
    const divisionStructureId = draggingNode.data.structureId;
    const newParentStructureId = dropNode.data.structureId;
    await ElMessageBox.confirm(
      `确定要将 "${draggingNode.data.structureName}" 移动到 "${dropNode.data.structureName}" 下吗？`,
      "确认调整",
      { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }
    );
    loading.value = true;
    const res = await changeDivisionHierarchy({ divisionStructureId, newParentStructureId });
    if (res.code === 0) {
      ElMessage.success("调整层级关系成功");
      await loadTreeData();
    } else {
      ElMessage.error(res.msg || "调整层级关系失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("调整层级关系失败:", error);
      ElMessage.error("调整层级关系失败");
    }
  } finally {
    loading.value = false;
  }
};

const loadTreeData = async () => {
  loading.value = true;
  try {
    const res = await getStationStructureTree();
    if (res.code === 0) {
      treeData.value = res.data ? [res.data] : [];
      expandedKeys.value = getExpandedKeys(treeData.value, 2);
    } else {
      ElMessage.error(res.msg || "获取局站结构树失败");
    }
  } catch (error) {
    console.error("获取局站结构树失败:", error);
    ElMessage.error("获取局站结构树失败");
  } finally {
    loading.value = false;
  }
};

const toggleDragMode = () => {
  dragMode.value = !dragMode.value;
};

const refreshTree = () => loadTreeData();

const getExpandedKeys = (nodes: StationStructureNode[], maxDepth: number, depth = 0): number[] => {
  let keys: number[] = [];
  for (const n of nodes) {
    if (depth < maxDepth) keys.push(n.structureId);
    if (n.children && n.children.length) {
      keys = keys.concat(getExpandedKeys(n.children, maxDepth, depth + 1));
    }
  }
  return keys;
};

onMounted(loadTreeData);
</script>

<style scoped>
.tree-container {
  padding: 8px;
}
.resource-tree.drag-mode :deep(.el-tree-node__content) {
  cursor: move;
}
</style>


