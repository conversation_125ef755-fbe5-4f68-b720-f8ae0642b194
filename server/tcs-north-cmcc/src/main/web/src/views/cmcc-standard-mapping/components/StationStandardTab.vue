<template>
  <div class="station-standard-tab">
    <!-- 操作按钮区域 -->
    <div class="operation-bar mb-4">
      <el-button 
        type="primary" 
        @click="fetchCmccStationTypesHandler"
        :loading="fetchLoading"
      >
        获取移动局站类型
      </el-button>
      <el-button 
        type="success" 
        @click="saveMapping"
        :loading="saveLoading"
      >
        保存映射
      </el-button>
    </div>

    <!-- 主内容区域 -->
    <div class="content-area">
      <el-row :gutter="20">
        <!-- 左侧：移动局站类型 -->
        <el-col :span="12">
          <el-card class="station-card">
            <template #header>
              <span class="font-bold">移动局站类型</span>
            </template>
            
            <el-table 
              :data="stationMappings" 
              style="width: 100%"
              :loading="tableLoading"
              @row-click="handleStationRowClick"
              highlight-current-row
            >
              <el-table-column prop="stationTypeId" label="局站类型ID" width="120" />
              <el-table-column prop="stationTypeName" label="局站类型名称" />
            </el-table>
          </el-card>
        </el-col>

        <!-- 右侧：SiteWeb局站类型和映射 -->
        <el-col :span="12">
          <el-card class="mapping-card">
            <template #header>
              <span class="font-bold">SiteWeb局站类型映射</span>
            </template>
            
            <div v-if="selectedStation" class="mapping-content">
              <div class="selected-station mb-4">
                <el-tag type="info" size="large">
                  {{ selectedStation.stationTypeName }}
                </el-tag>
              </div>
              
              <div class="mapping-selector">
                <el-form-item label="映射到SiteWeb局站类型:">
                  <el-select
                    v-model="selectedStation.stationCategoryId"
                    placeholder="请选择SiteWeb局站类型"
                    class="w-full"
                    @change="handleMappingChange"
                  >
                    <el-option
                      v-for="category in siteWebCategories"
                      :key="category.itemId"
                      :label="category.itemValue"
                      :value="parseInt(category.itemId)"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>
            
            <div v-else class="no-selection">
              <el-empty description="请选择左侧的局站类型进行映射配置" />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 映射列表 -->
      <el-card class="mapping-list-card mt-4">
        <template #header>
          <span class="font-bold">当前映射关系</span>
        </template>
        
        <el-table 
          :data="mappingTableData" 
          style="width: 100%"
          :loading="tableLoading"
        >
          <el-table-column prop="stationTypeId" label="局站类型ID" width="120" />
          <el-table-column prop="stationTypeName" label="局站类型名称" width="200" />
          <el-table-column prop="stationCategoryName" label="SiteWeb局站类型" width="200">
            <template #default="scope">
              <el-tag v-if="scope.row.stationCategoryName" type="success">
                {{ scope.row.stationCategoryName }}
              </el-tag>
              <el-tag v-else type="warning">未映射</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button 
                size="small" 
                @click="editMapping(scope.row)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { 
  getStationStandardData, 
  fetchCmccStationTypes, 
  saveStationMapping,
  getSiteWebStationCategories,
  insertCmccStationTypes,
  type StationTypeMapping,
  type SiteWebStationCategory,
  type StationStandardData
} from "@/api/cmcc-standard-mapping";

const emit = defineEmits(["loading-change"]);

// 响应式数据
const fetchLoading = ref(false);
const saveLoading = ref(false);
const tableLoading = ref(false);

const stationMappings = ref<StationTypeMapping[]>([]);
const siteWebCategories = ref<SiteWebStationCategory[]>([]);
const selectedStation = ref<StationTypeMapping | null>(null);

// 映射表格数据
const mappingTableData = computed(() => {
  if (!stationMappings.value || !Array.isArray(stationMappings.value)) {
    return [];
  }
  return stationMappings.value.map(station => ({
    ...station,
    stationCategoryName: siteWebCategories.value?.find(
      cat => parseInt(cat.itemId) === station.stationCategoryId
    )?.itemValue || ''
  }));
});

// 方法
const loadData = async () => {
  tableLoading.value = true;
  emit("loading-change", true);
  
  try {
    const response = await getStationStandardData();
    if (response.state) {
      stationMappings.value = response.data;
    } else {
      ElMessage.error(response.err_msg || "获取数据失败");
    }
    
    // 单独获取 SiteWeb 局站类型
    await loadSiteWebCategories();
  } catch (error) {
    ElMessage.error("获取数据失败");
    console.error(error);
  } finally {
    tableLoading.value = false;
    emit("loading-change", false);
  }
};

const loadSiteWebCategories = async () => {
  try {
    const response = await getSiteWebStationCategories();
    if (response.state) {
      siteWebCategories.value = response.data;
    } else {
      console.warn("获取SiteWeb局站类型失败:", response.err_msg);
    }
  } catch (error) {
    console.warn("获取SiteWeb局站类型失败:", error);
  }
};

const fetchCmccStationTypesHandler = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将清空现有移动局站类型数据并重新获取，是否继续？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    fetchLoading.value = true;
    emit("loading-change", true);
    
    // 1. 先调用StandardController获取移动局站类型
    const response = await fetchCmccStationTypes();
    if (response.state) {
      // 2. 调用成功后持久化数据
      const insertResponse = await insertCmccStationTypes(response.data);
      if (insertResponse.state) {
        ElMessage.success("获取并保存移动局站类型成功");
        await loadData(); // 重新加载数据
      } else {
        ElMessage.error(insertResponse.err_msg || "保存移动局站类型失败");
      }
    } else {
      ElMessage.error(response.err_msg || "获取移动局站类型失败");
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error("获取移动局站类型失败");
      console.error(error);
    }
  } finally {
    fetchLoading.value = false;
    emit("loading-change", false);
  }
};

const handleStationRowClick = (row: StationTypeMapping) => {
  selectedStation.value = { ...row };
};

const handleMappingChange = () => {
  if (selectedStation.value) {
    // 更新本地数据
    const index = stationMappings.value.findIndex(
      station => station.stationTypeId === selectedStation.value!.stationTypeId
    );
    
    if (index !== -1) {
      stationMappings.value[index].stationCategoryId = selectedStation.value.stationCategoryId;
    }
  }
};

const editMapping = (mapping: StationTypeMapping) => {
  selectedStation.value = { ...mapping };
};

const saveMapping = async () => {
  // 过滤出有映射关系的数据
  const mappingsToSave = stationMappings.value.filter(
    station => station.stationCategoryId
  );
  
  if (mappingsToSave.length === 0) {
    ElMessage.warning("没有需要保存的映射关系");
    return;
  }
  
  try {
    saveLoading.value = true;
    emit("loading-change", true);
    
    const response = await saveStationMapping(mappingsToSave);
    if (response.state) {
      ElMessage.success("保存映射关系成功");
      await loadData(); // 重新加载数据
    } else {
      ElMessage.error(response.err_msg || "保存映射关系失败");
    }
  } catch (error) {
    ElMessage.error("保存映射关系失败");
    console.error(error);
  } finally {
    saveLoading.value = false;
    emit("loading-change", false);
  }
};

// 暴露方法给父组件
defineExpose({
  loadData
});

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.station-standard-tab {
  padding: 20px;
}

.operation-bar {
  display: flex;
  gap: 12px;
}

.station-card,
.mapping-card,
.mapping-list-card {
  height: auto;
  min-height: 400px;
}

.mapping-content {
  padding: 10px 0;
}

.selected-station {
  padding: 10px;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
}

.no-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.mapping-list-card {
  margin-top: 20px;
}

.w-full {
  width: 100%;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.font-bold {
  font-weight: bold;
}
</style>
