-- Table: tbl_standardback
CREATE TABLE tbl_standardback (
                                  EntryCategory INT NOT NULL,
                                  EquipmentTemplateId INT NOT NULL,
                                  EntryId INT NOT NULL,
                                  EventConditionId INT NOT NULL,
                                  SignalName VARCHAR(128) DEFAULT NULL,
                                  StoreInterval DOUBLE PRECISION DEFAULT NULL,  -- Adjusted to DOUBLE PRECISION
                                  AbsValueThreshold DOUBLE PRECISION DEFAULT NULL,
                                  PercentThreshold DOUBLE PRECISION DEFAULT NULL,
                                  EventName VARCHAR(128) DEFAULT NULL,
                                  EventSeverity INT DEFAULT NULL,
                                  StartCompareValue DOUBLE PRECISION DEFAULT NULL,
                                  StartDelay INT DEFAULT NULL,
                                  StandardName INT DEFAULT NULL,
                                  Meanings VARCHAR(255) DEFAULT NULL,
                                  ControlName VARCHAR(128) DEFAULT NULL,
                                  PRIMARY KEY (EntryCategory, EntryId, EquipmentTemplateId, EventConditionId)
);

-- Table: tbl_standarddiccontrol
CREATE TABLE tbl_standarddiccontrol (
                                        StandardDicId INT NOT NULL,
                                        StandardType INT NOT NULL,
                                        EquipmentLogicClassId INT NOT NULL,
                                        EquipmentLogicClass VARCHAR(128) NOT NULL,
                                        ControlLogicClassId INT DEFAULT NULL,
                                        ControlLogicClass VARCHAR(128) DEFAULT NULL,
                                        ControlStandardName VARCHAR(255) DEFAULT NULL,
                                        NetManageId VARCHAR(255) DEFAULT NULL,
                                        StationCategory INT NOT NULL,
                                        ModifyType INT DEFAULT NULL,
                                        Description VARCHAR(255) DEFAULT NULL,
                                        ExtendFiled1 TEXT,  -- Replaced CLOB with TEXT
                                        ExtendFiled2 TEXT,  -- Replaced CLOB with TEXT
                                        PRIMARY KEY (StandardDicId, StandardType, StationCategory)
);

-- Table: tbl_standarddicevent
CREATE TABLE tbl_standarddicevent (
                                      StandardDicId INT NOT NULL,
                                      StandardType INT NOT NULL,
                                      EquipmentLogicClassId INT NOT NULL,
                                      EquipmentLogicClass VARCHAR(128) NOT NULL,
                                      EventLogicClassId INT DEFAULT NULL,
                                      EventLogicClass VARCHAR(128) DEFAULT NULL,
                                      EventClass VARCHAR(255) DEFAULT NULL,
                                      EventStandardName VARCHAR(255) DEFAULT NULL,
                                      NetManageId VARCHAR(255) DEFAULT NULL,
                                      EventSeverity INT DEFAULT NULL,
                                      CompareValue VARCHAR(128) DEFAULT NULL,
                                      StartDelay VARCHAR(64) DEFAULT NULL,
                                      Meanings VARCHAR(255) DEFAULT NULL,
                                      EquipmentAffect VARCHAR(255) DEFAULT NULL,
                                      BusinessAffect VARCHAR(255) DEFAULT NULL,
                                      StationCategory INT NOT NULL,
                                      ModifyType INT DEFAULT NULL,
                                      Description VARCHAR(255) DEFAULT NULL,
                                      ExtendFiled1 TEXT,  -- Replaced CLOB with TEXT
                                      ExtendFiled2 TEXT,  -- Replaced CLOB with TEXT
                                      ExtendFiled3 TEXT,  -- Replaced CLOB with TEXT
                                      PRIMARY KEY (StandardDicId, StandardType, StationCategory)
);

-- Table: tbl_standarddicsig
CREATE TABLE tbl_standarddicsig (
                                    StandardDicId INT NOT NULL,
                                    StandardType INT NOT NULL,
                                    EquipmentLogicClassId INT NOT NULL,
                                    EquipmentLogicClass VARCHAR(128) NOT NULL,
                                    SignalLogicClassId INT DEFAULT NULL,
                                    SignalLogicClass VARCHAR(128) DEFAULT NULL,
                                    SignalStandardName VARCHAR(255) NOT NULL,
                                    NetManageId VARCHAR(255) DEFAULT NULL,
                                    StoreInterval INT DEFAULT NULL,
                                    AbsValueThreshold DOUBLE PRECISION DEFAULT NULL,  -- Adjusted to DOUBLE PRECISION
                                    StatisticsPeriod INT DEFAULT NULL,
                                    PercentThreshold DOUBLE PRECISION DEFAULT NULL,  -- Adjusted to DOUBLE PRECISION
                                    StationCategory INT NOT NULL,
                                    ModifyType INT DEFAULT NULL,
                                    Description VARCHAR(255) DEFAULT NULL,
                                    ExtendFiled1 TEXT,  -- Replaced CLOB with TEXT
                                    ExtendFiled2 TEXT,  -- Replaced CLOB with TEXT
                                    PRIMARY KEY (StandardDicId, StandardType, StationCategory)
);

-- Table: tbl_standardtype
CREATE TABLE tbl_standardtype (
                                  StandardId INT NOT NULL,
                                  StandardName VARCHAR(255) NOT NULL,
                                  StandardAlias VARCHAR(255) NOT NULL,
                                  Remark VARCHAR(255) DEFAULT NULL,
                                  PRIMARY KEY (StandardId)
);
