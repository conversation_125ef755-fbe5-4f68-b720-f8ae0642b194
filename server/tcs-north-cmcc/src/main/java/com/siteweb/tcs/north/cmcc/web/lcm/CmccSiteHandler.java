package com.siteweb.tcs.north.cmcc.web.lcm;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;
import com.siteweb.tcs.north.cmcc.dal.dto.CmccSiteInfoDTO;
import com.siteweb.tcs.north.cmcc.dal.entity.CmccSiteMap;
import com.siteweb.tcs.north.cmcc.dal.entity.CmccStationTypeMap;
import com.siteweb.tcs.north.cmcc.web.service.ICmccSiteMapService;
import com.siteweb.tcs.north.cmcc.web.service.ICmccStandardMappingService;
import com.siteweb.tcs.north.cmcc.web.service.ICmccStationTypeMapService;
import com.siteweb.tcs.north.cmcc.web.service.ICmccStationTypeService;
import com.siteweb.tcs.siteweb.dto.StationDTO;
import com.siteweb.tcs.siteweb.entity.StationStructure;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * CMCC站点处理器
 * 负责处理站点相关的创建、更新、删除逻辑
 */
@Component
public class CmccSiteHandler extends CmccBaseHandler {

    @Autowired
    private ICmccSiteMapService cmccSiteMapService;

    @Autowired
    private ICmccStationTypeMapService cmccStationTypeMapService;

    @Autowired
    private ICmccStationTypeService cmccStationTypeService;

    @Autowired
    private ICmccStandardMappingService cmccStandardMappingService;

    /**
     * 处理站点信息
     */
    public void handleSiteInfo(GatewayConfigChangeDto gatewayConfigChangeDto,
                               Map<String, CmccSiteMap> cmccSiteMapMap) {
        CmccSiteInfoDTO siteInfoDTO = cmccConfigParser.parseSiteInfo(gatewayConfigChangeDto);
        handleSiteInfo(siteInfoDTO, cmccSiteMapMap);
    }

    /**
     * 处理站点信息
     */
    public void handleSiteInfo(CmccSiteInfoDTO siteInfoDTO,
                               Map<String, CmccSiteMap> cmccSiteMapMap) {
        // 已存在，直接返回
        if (cmccSiteMapMap.containsKey(siteInfoDTO.getSiteId())) {
            return;
        }

        // 从数据库查询
        CmccSiteMap cmccSiteMap = cmccSiteMapService.getBySiteId(siteInfoDTO.getSiteId());
        
        // 判断是更新还是新建
        boolean needUpdate = ObjectUtil.isNotEmpty(cmccSiteMap) &&
                (!StringUtils.equals(cmccSiteMap.getCmccSiteName(), siteInfoDTO.getSiteName())
                        || !NumberUtil.equals(cmccSiteMap.getCmccSiteType(), siteInfoDTO.getSiteType()));

        StationDTO stationDTO = buildStationDTO(cmccSiteMap, siteInfoDTO, needUpdate);

        if (needUpdate) {
            updateSite(stationDTO);
        } else if(ObjectUtil.isEmpty(cmccSiteMap)) {
            cmccSiteMap = new CmccSiteMap();
            createSite(siteInfoDTO, stationDTO,cmccSiteMap);
        }
        cmccSiteMapMap.put(cmccSiteMap.getCmccSiteId(), cmccSiteMap);
    }

    /**
     * 创建新站点
     */
    private void createSite(CmccSiteInfoDTO siteInfoDTO, StationDTO stationDTO,CmccSiteMap cmccSiteMap) {
        StationStructure defaultStationStructure = siteWebDefaultProvider.getDefaultStationStructure();
        stationDTO.setStationStructureId(defaultStationStructure.getStructureId());
        sitewebPersistentService.executeTransaction(()->{
            sitewebPersistentService.getConfigAPI().createStation(stationDTO);
        });
        if(ObjectUtil.isEmpty(stationDTO.getStationId())){
            return;
        }
        cmccSiteMap.setCmccSiteId(siteInfoDTO.getSiteId());
        cmccSiteMap.setCmccSiteName(siteInfoDTO.getSiteName());
        cmccSiteMap.setCmccSiteType(siteInfoDTO.getSiteType());
        cmccSiteMap.setStationId(stationDTO.getStationId());
        cmccSiteMap.setStationCategoryId(stationDTO.getStationCategory());
        
        cmccSiteMapService.save(cmccSiteMap);
    }

    /**
     * 更新站点
     */
    private void updateSite(StationDTO stationDTO) {
        sitewebPersistentService.getConfigAPI().updateStationDTO(stationDTO);
    }

    /**
     * 构建 StationDTO（新建或更新）
     */
    private StationDTO buildStationDTO(CmccSiteMap cmccSiteMap, CmccSiteInfoDTO siteInfoDTO, boolean update) {
        StationDTO stationDTO = new StationDTO();
        if (update) {
            stationDTO.setStationId(cmccSiteMap.getStationId());
        }
        stationDTO.setStationName(siteInfoDTO.getSiteName());
        stationDTO.setStationCategory(resolveStationCategory(siteInfoDTO));
        return stationDTO;
    }

    /**
     * 处理局站类型映射逻辑
     */
    private Integer resolveStationCategory(CmccSiteInfoDTO siteInfoDTO) {
        CmccStationTypeMap stationTypeMap = cmccStationTypeMapService.getByStationTypeId(siteInfoDTO.getSiteType());
        if (ObjectUtil.isNotEmpty(stationTypeMap)) {
            return stationTypeMap.getStationCategoryId();
        }

        // 删除并创建 cmcc 局站类型
        cmccStationTypeService.clearAndAdd(siteInfoDTO);
        
        // 创建 siteweb 局站类型
        Integer siteWebCategoryId = cmccStandardMappingService.createSitewebStationCategory(siteInfoDTO.getSiteTypeName());
        
        // 落库 cmcc 与 siteweb 的局站类型映射
        cmccStationTypeMapService.saveStationTypeMap(siteInfoDTO.getSiteType(), siteWebCategoryId);
        
        return siteWebCategoryId;
    }
}
