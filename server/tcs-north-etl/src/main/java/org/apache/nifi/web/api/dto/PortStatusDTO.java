package org.apache.nifi.web.api.dto;

import java.util.Date;

/**
 * Stub implementation of PortStatusDTO for compilation purposes.
 * This is a simplified version of the original class from Apache NiFi.
 */
public class PortStatusDTO {
    private String id;
    private String groupId;
    private String name;
    private String runStatus;
    private Date statsLastRefreshed;
    private String inputCount;
    private String inputBytes;
    private String outputCount;
    private String outputBytes;
    private String transmitting;
    private String runningCount;
    private String stoppedCount;
    private String invalidCount;
    private String disabledCount;
    private String activeThreadCount;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRunStatus() {
        return runStatus;
    }

    public void setRunStatus(String runStatus) {
        this.runStatus = runStatus;
    }

    public Date getStatsLastRefreshed() {
        return statsLastRefreshed;
    }

    public void setStatsLastRefreshed(Date statsLastRefreshed) {
        this.statsLastRefreshed = statsLastRefreshed;
    }

    public String getInputCount() {
        return inputCount;
    }

    public void setInputCount(String inputCount) {
        this.inputCount = inputCount;
    }

    public String getInputBytes() {
        return inputBytes;
    }

    public void setInputBytes(String inputBytes) {
        this.inputBytes = inputBytes;
    }

    public String getOutputCount() {
        return outputCount;
    }

    public void setOutputCount(String outputCount) {
        this.outputCount = outputCount;
    }

    public String getOutputBytes() {
        return outputBytes;
    }

    public void setOutputBytes(String outputBytes) {
        this.outputBytes = outputBytes;
    }

    public String getTransmitting() {
        return transmitting;
    }

    public void setTransmitting(String transmitting) {
        this.transmitting = transmitting;
    }

    public String getRunningCount() {
        return runningCount;
    }

    public void setRunningCount(String runningCount) {
        this.runningCount = runningCount;
    }

    public String getStoppedCount() {
        return stoppedCount;
    }

    public void setStoppedCount(String stoppedCount) {
        this.stoppedCount = stoppedCount;
    }

    public String getInvalidCount() {
        return invalidCount;
    }

    public void setInvalidCount(String invalidCount) {
        this.invalidCount = invalidCount;
    }

    public String getDisabledCount() {
        return disabledCount;
    }

    public void setDisabledCount(String disabledCount) {
        this.disabledCount = disabledCount;
    }

    public String getActiveThreadCount() {
        return activeThreadCount;
    }

    public void setActiveThreadCount(String activeThreadCount) {
        this.activeThreadCount = activeThreadCount;
    }
}
