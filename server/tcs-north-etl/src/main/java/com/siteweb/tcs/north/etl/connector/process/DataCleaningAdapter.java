package com.siteweb.tcs.north.etl.connector.process;

import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.north.etl.domain.letter.DataCleaningMessage;
import lombok.extern.slf4j.Slf4j;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * 数据处理适配器
 * 负责接收和预处理数据
 */
@Slf4j
public class DataCleaningAdapter extends AbstractActor {

    private final ActorProbe probe;
    private final ActorRef dataCleaningStore;
    
    /**
     * 构造函数
     */
    public DataCleaningAdapter(ActorRef dataCleaningStore) {
        this.dataCleaningStore = dataCleaningStore;
        this.probe = createProbe(this);
        probe.addCounter("ProcessedDataCounter");
    }
    
    /**
     * Actor接收消息的处理方法
     */
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DataCleaningMessage.class, this::handleDataCleaning)
                .matchAny(this::unhandled)
                .build();
    }
    
    /**
     * 处理数据处理消息
     */
    private void handleDataCleaning(DataCleaningMessage message) {
        probe.info("Processing data: " + message.getWindowLogString());
        
        try {
            // 预处理数据
            preProcessData(message);
            
            // 转发到数据存储
            dataCleaningStore.tell(message, getSelf());
            
            // 更新计数器
            probe.incrementCounterAmount("ProcessedDataCounter", 1);
        } catch (Exception e) {
            probe.error("Error processing data: " + e.getMessage());
        }
    }
    
    /**
     * 预处理数据
     */
    private void preProcessData(DataCleaningMessage message) {
        // 实现数据预处理逻辑
    }
    
    /**
     * Actor停止时的清理工作
     */
    @Override
    public void postStop() {
        removeProbe(probe);
        super.postStop();
    }
    
    /**
     * 创建Props
     */
    public static Props props(ActorRef dataCleaningStore) {
        return Props.create(DataCleaningAdapter.class, dataCleaningStore);
    }
}
