package com.siteweb.tcs.north.etl.service;

import com.siteweb.tcs.north.etl.model.Task;
import com.siteweb.tcs.north.etl.model.TaskDependency;
import com.siteweb.tcs.north.etl.model.TaskExecution;
import com.siteweb.tcs.north.etl.repository.TaskDependencyRepository;
import com.siteweb.tcs.north.etl.repository.TaskExecutionRepository;
import com.siteweb.tcs.north.etl.repository.TaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务依赖关系服务
 */
@Slf4j
@Service
public class TaskDependencyService {
    
    @Autowired
    private TaskDependencyRepository taskDependencyRepository;
    
    @Autowired
    private TaskRepository taskRepository;
    
    @Autowired
    private TaskExecutionRepository taskExecutionRepository;
    
    /**
     * 添加任务依赖关系
     */
    @Transactional
    public TaskDependency addDependency(Integer taskId, Integer dependsOnTaskId, String dependencyType) {
        log.info("Adding dependency: task {} depends on task {}, type: {}", taskId, dependsOnTaskId, dependencyType);
        
        // 验证任务存在
        Task task = taskRepository.selectById(taskId);
        if (task == null) {
            throw new IllegalArgumentException("Task not found: " + taskId);
        }
        
        Task dependsOnTask = taskRepository.selectById(dependsOnTaskId);
        if (dependsOnTask == null) {
            throw new IllegalArgumentException("Depends on task not found: " + dependsOnTaskId);
        }
        
        // 验证不会形成循环依赖
        if (wouldCreateCycle(taskId, dependsOnTaskId)) {
            throw new IllegalArgumentException("Adding this dependency would create a cycle");
        }
        
        // 创建依赖关系
        TaskDependency dependency = new TaskDependency();
        dependency.setTaskId(taskId);
        dependency.setDependsOnTaskId(dependsOnTaskId);
        dependency.setDependencyType(dependencyType);
        dependency.setCreatedAt(LocalDateTime.now());
        dependency.setUpdatedAt(LocalDateTime.now());
        
        taskDependencyRepository.insert(dependency);
        
        log.info("Added dependency with ID: {}", dependency.getId());
        
        return dependency;
    }
    
    /**
     * 删除任务依赖关系
     */
    @Transactional
    public void removeDependency(Integer taskId, Integer dependsOnTaskId) {
        log.info("Removing dependency: task {} depends on task {}", taskId, dependsOnTaskId);
        
        // 查找依赖关系
        List<TaskDependency> dependencies = taskDependencyRepository.findByTaskId(taskId);
        for (TaskDependency dependency : dependencies) {
            if (dependency.getDependsOnTaskId().equals(dependsOnTaskId)) {
                taskDependencyRepository.deleteById(dependency.getId());
                log.info("Removed dependency: {}", dependency.getId());
                return;
            }
        }
        
        log.warn("Dependency not found: task {} depends on task {}", taskId, dependsOnTaskId);
    }
    
    /**
     * 获取任务的所有依赖任务
     */
    public List<Task> getDependencies(Integer taskId) {
        List<Integer> dependsOnTaskIds = taskDependencyRepository.findDependsOnTaskIds(taskId);
        if (dependsOnTaskIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        return taskRepository.selectBatchIds(dependsOnTaskIds);
    }
    
    /**
     * 获取依赖于指定任务的所有任务
     */
    public List<Task> getDependents(Integer taskId) {
        List<Integer> dependentTaskIds = taskDependencyRepository.findDependentTaskIds(taskId);
        if (dependentTaskIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        return taskRepository.selectBatchIds(dependentTaskIds);
    }
    
    /**
     * 检查任务依赖是否满足
     */
    public boolean areDependenciesSatisfied(Integer taskId) {
        log.info("Checking dependencies for task: {}", taskId);
        
        // 获取任务
        Task task = taskRepository.selectById(taskId);
        if (task == null) {
            log.warn("Task not found: {}", taskId);
            return false;
        }
        
        // 如果任务配置为不等待依赖，则直接返回true
        if (task.getWaitForDependencies() != null && !task.getWaitForDependencies()) {
            log.info("Task {} is configured to not wait for dependencies", taskId);
            return true;
        }
        
        // 获取任务的所有依赖
        List<TaskDependency> dependencies = taskDependencyRepository.findByTaskId(taskId);
        if (dependencies.isEmpty()) {
            log.info("Task {} has no dependencies", taskId);
            return true;
        }
        
        // 检查每个依赖
        for (TaskDependency dependency : dependencies) {
            Integer dependsOnTaskId = dependency.getDependsOnTaskId();
            String dependencyType = dependency.getDependencyType();
            
            // 获取依赖任务的最新执行记录
            TaskExecution latestExecution = taskExecutionRepository.findLatestByTaskId(dependsOnTaskId);
            
            // 如果没有执行记录，或者执行记录未完成，则依赖不满足
            if (latestExecution == null || 
                latestExecution.getEndTime() == null || 
                TaskExecution.ExecutionStatus.RUNNING.name().equals(latestExecution.getStatus())) {
                
                log.info("Dependency not satisfied: task {} depends on task {}, which is not completed", 
                        taskId, dependsOnTaskId);
                return false;
            }
            
            // 如果是强依赖，且依赖任务执行失败，则依赖不满足
            if (TaskDependency.DependencyType.STRONG.name().equals(dependencyType) && 
                TaskExecution.ExecutionStatus.FAILURE.name().equals(latestExecution.getStatus())) {
                
                log.info("Strong dependency not satisfied: task {} depends on task {}, which failed", 
                        taskId, dependsOnTaskId);
                return false;
            }
            
            // 如果是条件依赖，需要根据依赖任务的结果决定
            // 这里可以实现更复杂的条件逻辑
            if (TaskDependency.DependencyType.CONDITIONAL.name().equals(dependencyType)) {
                // 简单实现：如果依赖任务成功，则满足条件
                if (!TaskExecution.ExecutionStatus.SUCCESS.name().equals(latestExecution.getStatus())) {
                    log.info("Conditional dependency not satisfied: task {} depends on task {}", 
                            taskId, dependsOnTaskId);
                    return false;
                }
            }
        }
        
        log.info("All dependencies satisfied for task: {}", taskId);
        return true;
    }
    
    /**
     * 检查添加依赖是否会形成循环
     */
    private boolean wouldCreateCycle(Integer taskId, Integer dependsOnTaskId) {
        // 如果任务依赖自己，肯定会形成循环
        if (taskId.equals(dependsOnTaskId)) {
            return true;
        }
        
        // 检查是否存在反向依赖路径
        return isReachable(dependsOnTaskId, taskId, new ArrayList<>());
    }
    
    /**
     * 检查是否存在从startTaskId到endTaskId的依赖路径
     */
    private boolean isReachable(Integer startTaskId, Integer endTaskId, List<Integer> visited) {
        // 如果已经访问过该任务，跳过以避免无限循环
        if (visited.contains(startTaskId)) {
            return false;
        }
        
        // 标记为已访问
        visited.add(startTaskId);
        
        // 获取startTaskId依赖的所有任务
        List<Integer> dependsOnTaskIds = taskDependencyRepository.findDependsOnTaskIds(startTaskId);
        
        // 如果直接依赖endTaskId，则存在路径
        if (dependsOnTaskIds.contains(endTaskId)) {
            return true;
        }
        
        // 递归检查每个依赖任务
        for (Integer dependsOnTaskId : dependsOnTaskIds) {
            if (isReachable(dependsOnTaskId, endTaskId, visited)) {
                return true;
            }
        }
        
        return false;
    }
}
