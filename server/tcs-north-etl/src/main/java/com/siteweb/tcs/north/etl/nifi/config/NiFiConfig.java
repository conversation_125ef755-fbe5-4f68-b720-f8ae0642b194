package com.siteweb.tcs.north.etl.nifi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * NiFi配置类
 * 用于配置NiFi连接参数
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "plugin.etl.nifi")
public class NiFiConfig {
    
    /**
     * NiFi API基础URL
     */
    private String baseUrl = "http://localhost:8080/nifi-api";
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 访问令牌
     */
    private String token;
    
    /**
     * 是否使用SSL
     */
    private boolean useSSL = false;
    
    /**
     * 信任所有证书
     */
    private boolean trustAllCerts = false;
    
    /**
     * 连接超时（毫秒）
     */
    private int connectTimeout = 30000;
    
    /**
     * 读取超时（毫秒）
     */
    private int readTimeout = 30000;
    
    /**
     * 根进程组ID
     */
    private String rootProcessGroupId = "root";
    
    /**
     * 轮询间隔（毫秒）
     */
    private int pollingInterval = 5000;
}
