package com.siteweb.tcs.north.etl.nifi.service;

import com.siteweb.tcs.north.etl.nifi.client.NiFiClient;
import com.siteweb.tcs.north.etl.nifi.config.NiFiConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.nifi.web.api.dto.BulletinDTO;
import org.apache.nifi.web.api.dto.NodeDTO;
import org.apache.nifi.web.api.dto.ProcessGroupStatusDTO;
import org.apache.nifi.web.api.dto.ProcessorStatusDTO;
import org.apache.nifi.web.api.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * NiFi服务
 * 提供NiFi操作的高级封装
 */
@Slf4j
@Service
public class NiFiService {

    private final NiFiClient nifiClient;
    private final NiFiConfig nifiConfig;

    @Autowired
    public NiFiService(NiFiClient nifiClient, NiFiConfig nifiConfig) {
        this.nifiClient = nifiClient;
        this.nifiConfig = nifiConfig;
    }

    /**
     * 创建ETL流程组
     * 
     * @param name 流程组名称
     * @return 流程组ID
     */
    public String createEtlProcessGroup(String name) {
        try {
            log.info("Creating ETL process group: {}", name);
            ProcessGroupEntity processGroup = nifiClient.createProcessGroup(nifiConfig.getRootProcessGroupId(), name, "0,0");
            log.info("Created ETL process group: {} with ID: {}", name, processGroup.getId());
            return processGroup.getId();
        } catch (Exception e) {
            log.error("Error creating ETL process group: {}", name, e);
            throw new RuntimeException("Failed to create ETL process group", e);
        }
    }

    /**
     * 创建数据库提取处理器
     * 
     * @param processGroupId 流程组ID
     * @param name 处理器名称
     * @param databaseType 数据库类型
     * @param connectionUrl 连接URL
     * @param username 用户名
     * @param password 密码
     * @param query SQL查询
     * @return 处理器ID
     */
    public String createDatabaseExtractProcessor(String processGroupId, String name, 
                                               String databaseType, String connectionUrl, 
                                               String username, String password, String query) {
        try {
            log.info("Creating database extract processor: {}", name);
            
            Map<String, String> properties = new HashMap<>();
            properties.put("Database Connection Pooling Service", "dbcp-service");
            properties.put("Database Type", databaseType);
            properties.put("Connection URL", connectionUrl);
            properties.put("Database Driver Class Name", getDatabaseDriverClassName(databaseType));
            properties.put("Database User", username);
            properties.put("Password", password);
            properties.put("SQL select query", query);
            
            ProcessorEntity processor = nifiClient.createProcessor(
                    processGroupId, 
                    "org.apache.nifi.processors.standard.QueryDatabaseTable", 
                    name, 
                    properties);
            
            log.info("Created database extract processor: {} with ID: {}", name, processor.getId());
            return processor.getId();
        } catch (Exception e) {
            log.error("Error creating database extract processor: {}", name, e);
            throw new RuntimeException("Failed to create database extract processor", e);
        }
    }

    /**
     * 创建文件写入处理器
     * 
     * @param processGroupId 流程组ID
     * @param name 处理器名称
     * @param directory 目录
     * @param filename 文件名
     * @return 处理器ID
     */
    public String createFileWriteProcessor(String processGroupId, String name, String directory, String filename) {
        try {
            log.info("Creating file write processor: {}", name);
            
            Map<String, String> properties = new HashMap<>();
            properties.put("Directory", directory);
            properties.put("Filename", filename);
            
            ProcessorEntity processor = nifiClient.createProcessor(
                    processGroupId, 
                    "org.apache.nifi.processors.standard.PutFile", 
                    name, 
                    properties);
            
            log.info("Created file write processor: {} with ID: {}", name, processor.getId());
            return processor.getId();
        } catch (Exception e) {
            log.error("Error creating file write processor: {}", name, e);
            throw new RuntimeException("Failed to create file write processor", e);
        }
    }

    /**
     * 创建数据库写入处理器
     * 
     * @param processGroupId 流程组ID
     * @param name 处理器名称
     * @param databaseType 数据库类型
     * @param connectionUrl 连接URL
     * @param username 用户名
     * @param password 密码
     * @param tableName 表名
     * @return 处理器ID
     */
    public String createDatabaseWriteProcessor(String processGroupId, String name, 
                                             String databaseType, String connectionUrl, 
                                             String username, String password, String tableName) {
        try {
            log.info("Creating database write processor: {}", name);
            
            Map<String, String> properties = new HashMap<>();
            properties.put("Database Connection Pooling Service", "dbcp-service");
            properties.put("Database Type", databaseType);
            properties.put("Connection URL", connectionUrl);
            properties.put("Database Driver Class Name", getDatabaseDriverClassName(databaseType));
            properties.put("Database User", username);
            properties.put("Password", password);
            properties.put("Table Name", tableName);
            properties.put("Statement Type", "INSERT");
            
            ProcessorEntity processor = nifiClient.createProcessor(
                    processGroupId, 
                    "org.apache.nifi.processors.standard.PutDatabaseRecord", 
                    name, 
                    properties);
            
            log.info("Created database write processor: {} with ID: {}", name, processor.getId());
            return processor.getId();
        } catch (Exception e) {
            log.error("Error creating database write processor: {}", name, e);
            throw new RuntimeException("Failed to create database write processor", e);
        }
    }

    /**
     * 创建转换处理器
     * 
     * @param processGroupId 流程组ID
     * @param name 处理器名称
     * @param scriptType 脚本类型
     * @param scriptBody 脚本内容
     * @return 处理器ID
     */
    public String createTransformProcessor(String processGroupId, String name, String scriptType, String scriptBody) {
        try {
            log.info("Creating transform processor: {}", name);
            
            Map<String, String> properties = new HashMap<>();
            properties.put("Script Engine", scriptType);
            properties.put("Script Body", scriptBody);
            
            ProcessorEntity processor = nifiClient.createProcessor(
                    processGroupId, 
                    "org.apache.nifi.processors.script.ExecuteScript", 
                    name, 
                    properties);
            
            log.info("Created transform processor: {} with ID: {}", name, processor.getId());
            return processor.getId();
        } catch (Exception e) {
            log.error("Error creating transform processor: {}", name, e);
            throw new RuntimeException("Failed to create transform processor", e);
        }
    }

    /**
     * 创建连接
     * 
     * @param processGroupId 流程组ID
     * @param sourceId 源处理器ID
     * @param destinationId 目标处理器ID
     * @return 连接ID
     */
    public String createConnection(String processGroupId, String sourceId, String destinationId) {
        try {
            log.info("Creating connection between {} and {}", sourceId, destinationId);
            
            ConnectionEntity connection = nifiClient.createConnection(
                    processGroupId, 
                    sourceId, 
                    destinationId, 
                    "PROCESSOR", 
                    "PROCESSOR", 
                    null, 
                    null);
            
            log.info("Created connection with ID: {}", connection.getId());
            return connection.getId();
        } catch (Exception e) {
            log.error("Error creating connection between {} and {}", sourceId, destinationId, e);
            throw new RuntimeException("Failed to create connection", e);
        }
    }

    /**
     * 启动流程组
     * 
     * @param processGroupId 流程组ID
     */
    public void startProcessGroup(String processGroupId) {
        try {
            log.info("Starting process group: {}", processGroupId);
            nifiClient.startProcessGroup(processGroupId);
            log.info("Process group started: {}", processGroupId);
        } catch (Exception e) {
            log.error("Error starting process group: {}", processGroupId, e);
            throw new RuntimeException("Failed to start process group", e);
        }
    }

    /**
     * 停止流程组
     * 
     * @param processGroupId 流程组ID
     */
    public void stopProcessGroup(String processGroupId) {
        try {
            log.info("Stopping process group: {}", processGroupId);
            nifiClient.stopProcessGroup(processGroupId);
            log.info("Process group stopped: {}", processGroupId);
        } catch (Exception e) {
            log.error("Error stopping process group: {}", processGroupId, e);
            throw new RuntimeException("Failed to stop process group", e);
        }
    }

    /**
     * 获取流程组状态
     * 
     * @param processGroupId 流程组ID
     * @return 状态信息
     */
    public Map<String, Object> getProcessGroupStatus(String processGroupId) {
        try {
            ProcessGroupStatusEntity statusEntity = nifiClient.getProcessGroupStatus(processGroupId);
            ProcessGroupStatusDTO statusDTO = statusEntity.getProcessGroupStatus().getAggregateSnapshot();
            
            Map<String, Object> status = new HashMap<>();
            status.put("id", statusDTO.getId());
            status.put("name", statusDTO.getName());
            status.put("activeThreadCount", statusDTO.getActiveThreadCount());
            status.put("flowFilesIn", statusDTO.getFlowFilesIn());
            status.put("bytesIn", statusDTO.getBytesIn());
            status.put("flowFilesOut", statusDTO.getFlowFilesOut());
            status.put("bytesOut", statusDTO.getBytesOut());
            status.put("queuedCount", statusDTO.getQueuedCount());
            status.put("queuedBytes", statusDTO.getQueuedBytes());
            
            List<Map<String, Object>> processorStatuses = new ArrayList<>();
            for (ProcessorStatusDTO processorStatus : statusDTO.getProcessorStatus()) {
                Map<String, Object> processor = new HashMap<>();
                processor.put("id", processorStatus.getId());
                processor.put("name", processorStatus.getName());
                processor.put("type", processorStatus.getType());
                processor.put("runStatus", processorStatus.getRunStatus());
                processor.put("activeThreadCount", processorStatus.getActiveThreadCount());
                processor.put("flowFilesIn", processorStatus.getFlowFilesIn());
                processor.put("bytesIn", processorStatus.getBytesIn());
                processor.put("flowFilesOut", processorStatus.getFlowFilesOut());
                processor.put("bytesOut", processorStatus.getBytesOut());
                processorStatuses.add(processor);
            }
            status.put("processors", processorStatuses);
            
            return status;
        } catch (Exception e) {
            log.error("Error getting process group status: {}", processGroupId, e);
            throw new RuntimeException("Failed to get process group status", e);
        }
    }

    /**
     * 获取集群状态
     * 
     * @return 集群状态信息
     */
    public Map<String, Object> getClusterStatus() {
        try {
            ClusterEntity clusterEntity = nifiClient.getClusterStatus();
            
            Map<String, Object> status = new HashMap<>();
            status.put("connectedNodeCount", clusterEntity.getCluster().getConnectedNodeCount());
            status.put("totalNodeCount", clusterEntity.getCluster().getTotalNodeCount());
            
            List<Map<String, Object>> nodes = new ArrayList<>();
            for (NodeDTO node : clusterEntity.getCluster().getNodes()) {
                Map<String, Object> nodeInfo = new HashMap<>();
                nodeInfo.put("nodeId", node.getNodeId());
                nodeInfo.put("address", node.getAddress());
                nodeInfo.put("status", node.getStatus());
                nodeInfo.put("activeThreadCount", node.getActiveThreadCount());
                nodeInfo.put("queued", node.getQueued());
                nodes.add(nodeInfo);
            }
            status.put("nodes", nodes);
            
            return status;
        } catch (Exception e) {
            log.error("Error getting cluster status", e);
            throw new RuntimeException("Failed to get cluster status", e);
        }
    }

    /**
     * 获取公告
     * 
     * @param componentId 组件ID
     * @return 公告列表
     */
    public List<Map<String, Object>> getBulletins(String componentId) {
        try {
            BulletinBoardEntity bulletinBoard = nifiClient.getBulletins();
            
            List<Map<String, Object>> bulletins = new ArrayList<>();
            for (BulletinEntity bulletin : bulletinBoard.getBulletins()) {
                BulletinDTO dto = bulletin.getBulletin();
                
                if (componentId == null || componentId.equals(dto.getSourceId())) {
                    Map<String, Object> bulletinInfo = new HashMap<>();
                    bulletinInfo.put("id", dto.getId());
                    bulletinInfo.put("sourceId", dto.getSourceId());
                    bulletinInfo.put("sourceName", dto.getSourceName());
                    bulletinInfo.put("category", dto.getCategory());
                    bulletinInfo.put("level", dto.getLevel());
                    bulletinInfo.put("message", dto.getMessage());
                    bulletinInfo.put("timestamp", dto.getTimestamp());
                    bulletins.add(bulletinInfo);
                }
            }
            
            return bulletins;
        } catch (Exception e) {
            log.error("Error getting bulletins for component: {}", componentId, e);
            throw new RuntimeException("Failed to get bulletins", e);
        }
    }

    /**
     * 获取数据库驱动类名
     * 
     * @param databaseType 数据库类型
     * @return 驱动类名
     */
    private String getDatabaseDriverClassName(String databaseType) {
        return switch (databaseType.toLowerCase()) {
            case "mysql" -> "com.mysql.cj.jdbc.Driver";
            case "postgresql" -> "org.postgresql.Driver";
            case "oracle" -> "oracle.jdbc.driver.OracleDriver";
            case "mssql", "sqlserver" -> "com.microsoft.sqlserver.jdbc.SQLServerDriver";
            default -> throw new IllegalArgumentException("Unsupported database type: " + databaseType);
        };
    }
}
