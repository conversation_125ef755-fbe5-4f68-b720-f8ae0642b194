package com.siteweb.tcs.north.etl.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.north.etl.model.Task;
import com.siteweb.tcs.north.etl.model.TaskExecution;
import com.siteweb.tcs.north.etl.service.TaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 任务控制器
 */
@Slf4j
@RestController
@RequestMapping("/etl/tasks")
@Api(tags = "任务管理")
public class TaskController {
    
    @Autowired
    private TaskService taskService;
    
    /**
     * 创建任务
     */
    @ApiOperation("创建任务")
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createTask(@RequestBody Task task) {
        Task createdTask = taskService.createTask(task);
        return ResponseEntity.ok(ResponseResult.success(createdTask));
    }
    
    /**
     * 更新任务
     */
    @ApiOperation("更新任务")
    @PutMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateTask(@PathVariable Integer id, @RequestBody Task task) {
        task.setId(id);
        Task updatedTask = taskService.updateTask(task);
        return ResponseEntity.ok(ResponseResult.success(updatedTask));
    }
    
    /**
     * 删除任务
     */
    @ApiOperation("删除任务")
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteTask(@PathVariable Integer id) {
        taskService.deleteTask(id);
        return ResponseEntity.ok(ResponseResult.success());
    }
    
    /**
     * 启动任务
     */
    @ApiOperation("启动任务")
    @PostMapping(value = "/{id}/start", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> startTask(@PathVariable Integer id) {
        taskService.startTask(id);
        return ResponseEntity.ok(ResponseResult.success());
    }
    
    /**
     * 停止任务
     */
    @ApiOperation("停止任务")
    @PostMapping(value = "/{id}/stop", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> stopTask(@PathVariable Integer id) {
        taskService.stopTask(id);
        return ResponseEntity.ok(ResponseResult.success());
    }
    
    /**
     * 获取任务状态
     */
    @ApiOperation("获取任务状态")
    @GetMapping(value = "/{id}/status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTaskStatus(@PathVariable Integer id) {
        Map<String, Object> status = taskService.getTaskStatus(id);
        return ResponseEntity.ok(ResponseResult.success(status));
    }
    
    /**
     * 获取任务日志
     */
    @ApiOperation("获取任务日志")
    @GetMapping(value = "/{id}/logs", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTaskLogs(@PathVariable Integer id) {
        List<Map<String, Object>> logs = taskService.getTaskLogs(id);
        return ResponseEntity.ok(ResponseResult.success(logs));
    }
    
    /**
     * 获取任务
     */
    @ApiOperation("获取任务")
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTask(@PathVariable Integer id) {
        Task task = taskService.getTaskById(id);
        return ResponseEntity.ok(ResponseResult.success(task));
    }
    
    /**
     * 获取所有任务
     */
    @ApiOperation("获取所有任务")
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllTasks() {
        List<Task> tasks = taskService.getAllTasks();
        return ResponseEntity.ok(ResponseResult.success(tasks));
    }
    
    /**
     * 分页获取任务
     */
    @ApiOperation("分页获取任务")
    @GetMapping(value = "/page", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTasksByPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        Page<Task> page = taskService.getTasksByPage(pageNum, pageSize);
        return ResponseEntity.ok(ResponseResult.success(page));
    }
    
    /**
     * 根据策略ID获取任务
     */
    @ApiOperation("根据策略ID获取任务")
    @GetMapping(value = "/strategy/{strategyId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTasksByStrategyId(@PathVariable Integer strategyId) {
        List<Task> tasks = taskService.getTasksByStrategyId(strategyId);
        return ResponseEntity.ok(ResponseResult.success(tasks));
    }
    
    /**
     * 根据状态获取任务
     */
    @ApiOperation("根据状态获取任务")
    @GetMapping(value = "/status/{status}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTasksByStatus(@PathVariable String status) {
        List<Task> tasks = taskService.getTasksByStatus(status);
        return ResponseEntity.ok(ResponseResult.success(tasks));
    }
    
    /**
     * 获取任务执行记录
     */
    @ApiOperation("获取任务执行记录")
    @GetMapping(value = "/{id}/executions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTaskExecutions(@PathVariable Integer id) {
        List<TaskExecution> executions = taskService.getTaskExecutions(id);
        return ResponseEntity.ok(ResponseResult.success(executions));
    }
}
