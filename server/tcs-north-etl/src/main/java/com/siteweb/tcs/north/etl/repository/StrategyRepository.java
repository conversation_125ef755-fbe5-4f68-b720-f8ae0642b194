package com.siteweb.tcs.north.etl.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.etl.model.Strategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 策略数据访问接口
 */
@Mapper
public interface StrategyRepository extends BaseMapper<Strategy> {
    
    /**
     * 根据读取器ID查询策略列表
     */
    @Select("SELECT * FROM etl_strategy WHERE reader_id = #{readerId}")
    List<Strategy> findByReaderId(@Param("readerId") Integer readerId);
    
    /**
     * 根据存储器ID查询策略列表
     */
    @Select("SELECT * FROM etl_strategy WHERE writer_id = #{writerId}")
    List<Strategy> findByWriterId(@Param("writerId") Integer writerId);
    
    /**
     * 根据读取器ID和存储器ID查询策略列表
     */
    @Select("SELECT * FROM etl_strategy WHERE reader_id = #{readerId} AND writer_id = #{writerId}")
    List<Strategy> findByReaderIdAndWriterId(@Param("readerId") Integer readerId, @Param("writerId") Integer writerId);
}
