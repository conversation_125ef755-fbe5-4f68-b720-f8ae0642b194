package com.siteweb.tcs.north.etl.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.north.etl.model.Reader;
import com.siteweb.tcs.north.etl.service.ReaderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 读取器控制器
 */
@Slf4j
@RestController
@RequestMapping("/etl/readers")
@Api(tags = "读取器管理")
public class ReaderController {
    
    @Autowired
    private ReaderService readerService;
    
    /**
     * 创建读取器
     */
    @ApiOperation("创建读取器")
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createReader(@RequestBody Reader reader) {
        Reader createdReader = readerService.createReader(reader);
        return ResponseEntity.ok(ResponseResult.success(createdReader));
    }
    
    /**
     * 更新读取器
     */
    @ApiOperation("更新读取器")
    @PutMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateReader(@PathVariable Integer id, @RequestBody Reader reader) {
        reader.setId(id);
        Reader updatedReader = readerService.updateReader(reader);
        return ResponseEntity.ok(ResponseResult.success(updatedReader));
    }
    
    /**
     * 删除读取器
     */
    @ApiOperation("删除读取器")
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteReader(@PathVariable Integer id) {
        readerService.deleteReader(id);
        return ResponseEntity.ok(ResponseResult.success());
    }
    
    /**
     * 获取读取器
     */
    @ApiOperation("获取读取器")
    @GetMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getReader(@PathVariable Integer id) {
        Reader reader = readerService.getReaderById(id);
        return ResponseEntity.ok(ResponseResult.success(reader));
    }
    
    /**
     * 获取所有读取器
     */
    @ApiOperation("获取所有读取器")
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllReaders() {
        List<Reader> readers = readerService.getAllReaders();
        return ResponseEntity.ok(ResponseResult.success(readers));
    }
    
    /**
     * 分页获取读取器
     */
    @ApiOperation("分页获取读取器")
    @GetMapping(value = "/page", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getReadersByPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        Page<Reader> page = readerService.getReadersByPage(pageNum, pageSize);
        return ResponseEntity.ok(ResponseResult.success(page));
    }
    
    /**
     * 根据类型获取读取器
     */
    @ApiOperation("根据类型获取读取器")
    @GetMapping(value = "/type/{type}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getReadersByType(@PathVariable String type) {
        List<Reader> readers = readerService.getReadersByType(type);
        return ResponseEntity.ok(ResponseResult.success(readers));
    }
    
    /**
     * 测试读取器连接
     */
    @ApiOperation("测试读取器连接")
    @PostMapping(value = "/test-connection", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> testConnection(@RequestBody Reader reader) {
        boolean success = readerService.testConnection(reader);
        return ResponseEntity.ok(ResponseResult.success(success));
    }
}
