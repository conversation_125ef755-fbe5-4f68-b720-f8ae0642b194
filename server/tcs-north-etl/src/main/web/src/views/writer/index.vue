<template>
  <div class="writer-container">
    <div class="page-header">
      <h2>{{ $t('etl.writer.list') }}</h2>
      <el-button type="primary" @click="handleCreateWriter">
        <el-icon><plus /></el-icon>
        {{ $t('etl.writer.create') }}
      </el-button>
    </div>

    <el-card class="writer-table-card">
      <div class="table-operations">
        <el-input
          v-model="searchQuery"
          :placeholder="$t('etl.common.search')"
          style="width: 200px"
          clearable
          @clear="fetchWriters"
        >
          <template #prefix>
            <el-icon><search /></el-icon>
          </template>
        </el-input>
        <el-button @click="fetchWriters">
          <el-icon><refresh /></el-icon>
          {{ $t('etl.common.refresh') }}
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="filteredWriters"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" :label="$t('etl.writer.name')" min-width="150" />
        <el-table-column :label="$t('etl.writer.type')" min-width="120">
          <template #default="scope">
            <el-tag>{{ $t(`etl.writer.types.${scope.row.type}`) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('etl.writer.description')" min-width="200" show-overflow-tooltip />
        <el-table-column :label="$t('etl.common.operations')" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="handleViewWriter(scope.row)"
              :title="$t('etl.writer.actions.view')"
            >
              <el-icon><view /></el-icon>
            </el-button>
            <el-button 
              size="small" 
              type="success" 
              @click="handleEditWriter(scope.row)"
              :title="$t('etl.writer.actions.edit')"
            >
              <el-icon><edit /></el-icon>
            </el-button>
            <el-button 
              size="small" 
              type="warning" 
              @click="handleTestConnection(scope.row)"
              :title="$t('etl.writer.actions.test')"
            >
              <el-icon><connection /></el-icon>
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDeleteWriter(scope.row)"
              :title="$t('etl.writer.actions.delete')"
            >
              <el-icon><delete /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalWriters"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 存储器表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'create' ? $t('etl.writer.create') : $t('etl.writer.edit')"
      width="60%"
      destroy-on-close
    >
      <el-form
        ref="writerFormRef"
        :model="writerForm"
        :rules="writerRules"
        label-width="120px"
      >
        <el-form-item :label="$t('etl.writer.name')" prop="name">
          <el-input v-model="writerForm.name" />
        </el-form-item>
        <el-form-item :label="$t('etl.writer.type')" prop="type">
          <el-select v-model="writerForm.type" style="width: 100%">
            <el-option
              v-for="type in Object.keys(writerTypes)"
              :key="type"
              :label="$t(`etl.writer.types.${type}`)"
              :value="type"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('etl.writer.description')" prop="description">
          <el-input v-model="writerForm.description" type="textarea" :rows="3" />
        </el-form-item>

        <!-- 根据不同类型显示不同的配置表单 -->
        <template v-if="writerForm.type === 'DATABASE'">
          <el-form-item label="数据库类型" prop="configJson.dbType">
            <el-select v-model="writerForm.configJson.dbType" style="width: 100%">
              <el-option label="MySQL" value="mysql" />
              <el-option label="PostgreSQL" value="postgresql" />
              <el-option label="Oracle" value="oracle" />
              <el-option label="SQL Server" value="sqlserver" />
            </el-select>
          </el-form-item>
          <el-form-item label="主机地址" prop="configJson.host">
            <el-input v-model="writerForm.configJson.host" />
          </el-form-item>
          <el-form-item label="端口" prop="configJson.port">
            <el-input v-model="writerForm.configJson.port" />
          </el-form-item>
          <el-form-item label="数据库名" prop="configJson.database">
            <el-input v-model="writerForm.configJson.database" />
          </el-form-item>
          <el-form-item label="用户名" prop="configJson.username">
            <el-input v-model="writerForm.configJson.username" />
          </el-form-item>
          <el-form-item label="密码" prop="configJson.password">
            <el-input v-model="writerForm.configJson.password" type="password" show-password />
          </el-form-item>
          <el-form-item label="表名" prop="configJson.table">
            <el-input v-model="writerForm.configJson.table" />
          </el-form-item>
        </template>

        <template v-else-if="writerForm.type === 'FILE'">
          <el-form-item label="文件路径" prop="configJson.filePath">
            <el-input v-model="writerForm.configJson.filePath" />
          </el-form-item>
          <el-form-item label="文件格式" prop="configJson.fileFormat">
            <el-select v-model="writerForm.configJson.fileFormat" style="width: 100%">
              <el-option label="CSV" value="csv" />
              <el-option label="JSON" value="json" />
              <el-option label="XML" value="xml" />
              <el-option label="Excel" value="excel" />
            </el-select>
          </el-form-item>
          <el-form-item label="分隔符" prop="configJson.delimiter" v-if="writerForm.configJson.fileFormat === 'csv'">
            <el-input v-model="writerForm.configJson.delimiter" />
          </el-form-item>
        </template>

        <template v-else-if="writerForm.type === 'HDFS'">
          <el-form-item label="HDFS地址" prop="configJson.hdfsUrl">
            <el-input v-model="writerForm.configJson.hdfsUrl" />
          </el-form-item>
          <el-form-item label="文件路径" prop="configJson.filePath">
            <el-input v-model="writerForm.configJson.filePath" />
          </el-form-item>
          <el-form-item label="文件格式" prop="configJson.fileFormat">
            <el-select v-model="writerForm.configJson.fileFormat" style="width: 100%">
              <el-option label="CSV" value="csv" />
              <el-option label="JSON" value="json" />
              <el-option label="Parquet" value="parquet" />
              <el-option label="ORC" value="orc" />
            </el-select>
          </el-form-item>
        </template>

        <template v-else-if="writerForm.type === 'KAFKA'">
          <el-form-item label="Bootstrap服务器" prop="configJson.bootstrapServers">
            <el-input v-model="writerForm.configJson.bootstrapServers" />
          </el-form-item>
          <el-form-item label="主题" prop="configJson.topic">
            <el-input v-model="writerForm.configJson.topic" />
          </el-form-item>
          <el-form-item label="键序列化器" prop="configJson.keySerializer">
            <el-select v-model="writerForm.configJson.keySerializer" style="width: 100%">
              <el-option label="String" value="org.apache.kafka.common.serialization.StringSerializer" />
              <el-option label="Integer" value="org.apache.kafka.common.serialization.IntegerSerializer" />
              <el-option label="Long" value="org.apache.kafka.common.serialization.LongSerializer" />
            </el-select>
          </el-form-item>
          <el-form-item label="值序列化器" prop="configJson.valueSerializer">
            <el-select v-model="writerForm.configJson.valueSerializer" style="width: 100%">
              <el-option label="String" value="org.apache.kafka.common.serialization.StringSerializer" />
              <el-option label="JSON" value="org.apache.kafka.connect.json.JsonSerializer" />
              <el-option label="Avro" value="io.confluent.kafka.serializers.KafkaAvroSerializer" />
            </el-select>
          </el-form-item>
        </template>

        <template v-else-if="writerForm.type === 'API'">
          <el-form-item label="API地址" prop="configJson.url">
            <el-input v-model="writerForm.configJson.url" />
          </el-form-item>
          <el-form-item label="请求方法" prop="configJson.method">
            <el-select v-model="writerForm.configJson.method" style="width: 100%">
              <el-option label="GET" value="GET" />
              <el-option label="POST" value="POST" />
              <el-option label="PUT" value="PUT" />
              <el-option label="DELETE" value="DELETE" />
            </el-select>
          </el-form-item>
          <el-form-item label="请求头" prop="configJson.headers">
            <el-input v-model="writerForm.configJson.headers" type="textarea" :rows="3" placeholder="格式：key1:value1,key2:value2" />
          </el-form-item>
          <el-form-item label="认证令牌" prop="configJson.authToken">
            <el-input v-model="writerForm.configJson.authToken" show-password />
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ $t('etl.common.cancel') }}</el-button>
          <el-button type="primary" @click="submitWriterForm">{{ $t('etl.common.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      :title="$t('etl.common.deleteConfirmation')"
      width="30%"
    >
      <span>{{ $t('etl.writer.deleteConfirmation') }}</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">{{ $t('etl.common.cancel') }}</el-button>
          <el-button type="danger" @click="confirmDelete">{{ $t('etl.common.delete') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search, Refresh, View, Edit, Delete, Connection } from '@element-plus/icons-vue';
import api from '@/utils/api';

// 存储器类型
const writerTypes = {
  DATABASE: 'DATABASE',
  FILE: 'FILE',
  HDFS: 'HDFS',
  KAFKA: 'KAFKA',
  API: 'API'
};

// 数据
const writers = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const dialogType = ref('create'); // 'create' or 'edit'
const currentWriter = ref(null);
const writerFormRef = ref(null);
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const totalWriters = ref(0);

// 表单数据
const writerForm = reactive({
  id: null,
  name: '',
  type: 'DATABASE',
  description: '',
  configJson: {
    // 数据库配置
    dbType: 'mysql',
    host: '',
    port: '',
    database: '',
    username: '',
    password: '',
    table: '',
    
    // 文件配置
    filePath: '',
    fileFormat: 'csv',
    delimiter: ',',
    
    // HDFS配置
    hdfsUrl: '',
    
    // Kafka配置
    bootstrapServers: '',
    topic: '',
    keySerializer: 'org.apache.kafka.common.serialization.StringSerializer',
    valueSerializer: 'org.apache.kafka.common.serialization.StringSerializer',
    
    // API配置
    url: '',
    method: 'POST',
    headers: '',
    authToken: ''
  }
});

// 表单验证规则
const writerRules = {
  name: [
    { required: true, message: '请输入存储器名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择存储器类型', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ]
};

// 过滤后的存储器列表
const filteredWriters = computed(() => {
  if (!searchQuery.value) {
    return writers.value;
  }
  const query = searchQuery.value.toLowerCase();
  return writers.value.filter(writer => 
    writer.name.toLowerCase().includes(query) || 
    writer.description.toLowerCase().includes(query)
  );
});

// 生命周期钩子
onMounted(() => {
  fetchWriters();
});

// 获取所有存储器
const fetchWriters = async () => {
  loading.value = true;
  try {
    const response = await api.writer.getAll();
    writers.value = response || [];
    totalWriters.value = writers.value.length;
  } catch (error) {
    console.error('Failed to fetch writers:', error);
    ElMessage.error('获取存储器列表失败');
  } finally {
    loading.value = false;
  }
};

// 创建存储器
const handleCreateWriter = () => {
  dialogType.value = 'create';
  resetForm();
  dialogVisible.value = true;
};

// 查看存储器
const handleViewWriter = (row) => {
  // 跳转到详情页或显示详情对话框
  ElMessageBox.alert(JSON.stringify(row, null, 2), '存储器详情', {
    confirmButtonText: '确定'
  });
};

// 编辑存储器
const handleEditWriter = (row) => {
  dialogType.value = 'edit';
  currentWriter.value = row;
  
  // 复制数据到表单
  writerForm.id = row.id;
  writerForm.name = row.name;
  writerForm.type = row.type;
  writerForm.description = row.description;
  writerForm.configJson = JSON.parse(JSON.stringify(row.configJson || {}));
  
  dialogVisible.value = true;
};

// 测试连接
const handleTestConnection = async (row) => {
  try {
    const response = await api.writer.testConnection(row.id);
    if (response) {
      ElMessage.success('连接测试成功');
    } else {
      ElMessage.error('连接测试失败');
    }
  } catch (error) {
    console.error('Connection test failed:', error);
    ElMessage.error('连接测试失败: ' + (error.message || '未知错误'));
  }
};

// 删除存储器
const handleDeleteWriter = (row) => {
  currentWriter.value = row;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (!currentWriter.value) return;
  
  try {
    await api.writer.delete(currentWriter.value.id);
    ElMessage.success('删除成功');
    deleteDialogVisible.value = false;
    fetchWriters();
  } catch (error) {
    console.error('Delete failed:', error);
    ElMessage.error('删除失败: ' + (error.message || '未知错误'));
  }
};

// 提交表单
const submitWriterForm = async () => {
  if (!writerFormRef.value) return;
  
  await writerFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'create') {
          await api.writer.create(writerForm);
          ElMessage.success('创建成功');
        } else {
          await api.writer.update(writerForm.id, writerForm);
          ElMessage.success('更新成功');
        }
        dialogVisible.value = false;
        fetchWriters();
      } catch (error) {
        console.error('Form submission failed:', error);
        ElMessage.error('操作失败: ' + (error.message || '未知错误'));
      }
    } else {
      return false;
    }
  });
};

// 重置表单
const resetForm = () => {
  if (writerFormRef.value) {
    writerFormRef.value.resetFields();
  }
  
  writerForm.id = null;
  writerForm.name = '';
  writerForm.type = 'DATABASE';
  writerForm.description = '';
  writerForm.configJson = {
    dbType: 'mysql',
    host: '',
    port: '',
    database: '',
    username: '',
    password: '',
    table: '',
    filePath: '',
    fileFormat: 'csv',
    delimiter: ',',
    hdfsUrl: '',
    bootstrapServers: '',
    topic: '',
    keySerializer: 'org.apache.kafka.common.serialization.StringSerializer',
    valueSerializer: 'org.apache.kafka.common.serialization.StringSerializer',
    url: '',
    method: 'POST',
    headers: '',
    authToken: ''
  };
};

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchWriters();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchWriters();
};
</script>

<style scoped>
.writer-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-operations {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
