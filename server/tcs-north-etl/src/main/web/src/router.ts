import { RouteRecordRaw } from 'vue-router'
import Layout from './views/layout.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    redirect: '/reader',
    children: [
      {
        path: 'reader',
        component: () => import('./views/reader/index.vue'),
        name: 'reader',
        meta: {
          title: 'etl.reader.title',
          icon: 'database'
        }
      },
      {
        path: 'writer',
        component: () => import('./views/writer/index.vue'),
        name: 'writer',
        meta: {
          title: 'etl.writer.title',
          icon: 'save'
        }
      },
      {
        path: 'strategy',
        component: () => import('./views/strategy/index.vue'),
        name: 'strategy',
        meta: {
          title: 'etl.strategy.title',
          icon: 'setting'
        }
      },
      {
        path: 'task',
        component: () => import('./views/task/index.vue'),
        name: 'task',
        meta: {
          title: 'etl.task.title',
          icon: 'schedule'
        }
      },
      {
        path: 'cluster',
        component: () => import('./views/cluster/index.vue'),
        name: 'cluster',
        meta: {
          title: 'etl.cluster.title',
          icon: 'cloud'
        }
      }
    ]
  }
]

export default routes
