package com.siteweb.stream.core.runtime;

public class StreamGraphProbe {
    private long streamGraphId;
    private int runStatus;
    private long graphInstanceId;
    private long timestamp;

    //。。。。

//    public StreamGraphProbe(StreamGraphInstance streamGraphInstance) {
//        this.streamGraphId = streamGraphInstance.getStreamGraphId();
//        this.runStatus = streamGraphInstance.isRunning() ? 1 : 0;
//    }


}
