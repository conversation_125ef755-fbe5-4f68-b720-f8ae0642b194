package com.siteweb.stream.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.stream.common.stream.StreamGraphOption;
import com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: StreamGraph
 * @descriptions: actor模式下流模型-图配置类
 * @author: xsx
 * @date: 2/14/2025 10:49 AM
 **/
@Data
@TableName(value = "tcs_streams",autoResultMap = true)
public class StreamGraph {

    // 实例化图id
    @TableId(value = "stream_graph_id", type = IdType.ASSIGN_ID)
    private Long streamGraphId;

    // 实例化图名称
    @TableField("stream_graph_name")
    private String streamGraphName;

    @TableField(value = "graph_option", typeHandler = EnhancedJacksonTypeHandler.class )
    private StreamGraphOption graphOption;

    // 实例化图下的实例化流列表
    @TableField(value = "flows", typeHandler = EnhancedJacksonTypeHandler.class)
    private List<StreamFlow> flows;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(value = "created_by")
    private String createdBy;

    @TableField(value = "updated_by")
    private String updatedBy;
}
