package com.siteweb.tcs.plugin.common.tracer;

import com.siteweb.tcs.common.ISerializableMessage;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <AUTHOR> (2025-07-24)
 **/
@Data

public class TraceRequestFrame implements ISerializableMessage {


    @Data
    public static class TraceResponse implements ISerializableMessage{

        public TraceResponse() {
            this.timestamp = LocalDateTime.now();
        }
        /**
         * 时间戳
         */
        private final LocalDateTime timestamp;
        /**
         * 报文内容
         */
        private  String message;

        private  String exception;
    }


    public TraceRequestFrame(UUID msgId, String message) {
        this.msgId = msgId;
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }


    /**
     * 消息ID
     */
    private final UUID msgId;

    /**
     * 时间戳
     */
    private final LocalDateTime timestamp;

    /**
     * 报文内容
     */
    private final String message;

    /**
     * 返回报文
     */
    private TraceResponse response;


    /**
     * 已完成并返回
     *
     * @return
     */
    public boolean isCompleted() {
        return response != null;
    }


    public void writeResponse(String msg) {
        if (response == null) response = new TraceResponse();
        response.setMessage(msg);
    }

    public void writeException(Throwable exception) {
        if (response == null) response = new TraceResponse();
        response.setException(exception.toString());
    }

}
