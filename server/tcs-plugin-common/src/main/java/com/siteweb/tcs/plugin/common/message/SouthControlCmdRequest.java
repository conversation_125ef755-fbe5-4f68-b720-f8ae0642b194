package com.siteweb.tcs.plugin.common.message;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.tcs.common.sharding.IGatewayShardingMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.pekko.actor.ActorRef;

import java.util.List;

/**
 * <AUTHOR> (2025-07-31)
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class SouthControlCmdRequest extends StreamMessage implements IGatewayShardingMessage {

    /**
     * 南向GatewayId
     */
    private String gatewayId;

    /**
     * 控制的设备列表
     */
    private List<SouthControlCmdPoint> points;

    /**
     * 谁发过来的
     */
    private ActorRef sender;
}
