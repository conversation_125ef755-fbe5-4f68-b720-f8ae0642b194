<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (C) Red Gate Software Ltd 2010-2023

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

            http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-parent</artifactId>
        <version>10.1.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>flyway-database-oracle</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <version>${project.parent.version}</version>

    <dependencies>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>flyway-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <optional>true</optional>
        </dependency>





















































    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-license</id>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>..</directory>
                                    <includes>
                                        <include>LICENSE.txt</include>
                                        <include>README.txt</include>
                                    </includes>
                                </resource>
                            </resources>
                            <outputDirectory>${project.build.outputDirectory}/META-INF</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>