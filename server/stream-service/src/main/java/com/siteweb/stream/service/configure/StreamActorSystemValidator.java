package com.siteweb.stream.service.configure;

import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.ActorSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * Stream Actor System 验证器
 * 在应用启动时验证 Stream 模块是否正确使用 TCS 的 Actor System
 */
@Component
@Slf4j
public class StreamActorSystemValidator implements ApplicationListener<ApplicationStartedEvent> {
    
//    @Autowired
//    @Qualifier("actorSystem")
//    private ActorSystem tcsActorSystem;
//
//    @Autowired
//    @Qualifier("streamActorSystem")
//    private ActorSystem streamActorSystem;
//
//    @Autowired
//    @Qualifier("stream-root-actor")
//    private ActorRef streamRootActor;

    /**
     * TODO 可以移除，全局统一ActorSystem
     * @param event the event to respond to
     */
    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
//        log.info("验证 Stream 模块的 Actor System 配置...");
//
//        // 验证 Stream 模块使用的是 TCS 的 Actor System
//        boolean usingTcsActorSystem = tcsActorSystem == streamActorSystem;
//        log.info("Stream 模块使用 TCS 的 Actor System: {}", usingTcsActorSystem);
//
//        // 验证 AkkaSystemEnvironment 中的 Actor System 是否正确设置
////        boolean akkaEnvironmentCorrect = tcsActorSystem == PekkoSys AkkaSystemEnvironment.getActorSystem();
////        log.info("AkkaSystemEnvironment 使用 TCS 的 Actor System: {}", akkaEnvironmentCorrect);
//
//        // 验证 StreamRootActor 是否正确创建
//        log.info("StreamRootActor 路径: {}", streamRootActor.path());
//
//        // 验证 Pekko 版本
//        String pekkoVersion = tcsActorSystem.settings().config().getString("pekko.version");
//        log.info("使用的 Pekko 版本: {}", pekkoVersion);

        // 总结
//        if (usingTcsActorSystem && akkaEnvironmentCorrect) {
//            log.info("Stream 模块成功集成到 TCS 的 Actor System 中！");
//        } else {
//            log.error("Stream 模块未能正确集成到 TCS 的 Actor System 中，请检查配置！");
//        }
    }
}
