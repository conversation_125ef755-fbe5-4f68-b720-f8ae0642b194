<?xml version="1.0" encoding="UTF-8"?>
<EquipmentTemplates Name="设备模板列表">
  <EquipmentTemplate EquipmentTemplateId="876003590" ParentTemplateId="0" EquipmentTemplateName="MDU" ProtocolCode="MDUIO 6-00" EquipmentCategory="53" EquipmentType="1" Memo="2015/3/12 13:41:40:首次导入模板" Property="1/3" Decription="" EquipmentStyle="" Unit="" Vendor="" EquipmentBaseType="" StationCategory="">
    <Signals Name="模板信号">
      <Signal SignalId="-3" SignalName="设备通讯状态" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="" AbsValueThreshold="" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" SignalProperty="27" SignalMeanings="0:通讯异常;1:通讯正常" />
      <Signal SignalId="530000010" SignalName="市电有无" SignalCategory="2" SignalType="1" ChannelNo="0" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="2" SignalProperty="27" SignalMeanings="0:有电;1:停电" />
      <Signal SignalId="530000020" SignalName="水浸" SignalCategory="2" SignalType="1" ChannelNo="1" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="3" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="530000030" SignalName="通用DI1" SignalCategory="2" SignalType="1" ChannelNo="2" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="4" SignalProperty="27" SignalMeanings="0:断开;1:闭合" />
      <Signal SignalId="530000040" SignalName="通用DI2" SignalCategory="2" SignalType="1" ChannelNo="3" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="5" SignalProperty="27" SignalMeanings="0:断开;1:闭合" />
      <Signal SignalId="530000050" SignalName="烟感" SignalCategory="2" SignalType="1" ChannelNo="4" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="6" SignalProperty="27" SignalMeanings="0:正常;1:告警" />
      <Signal SignalId="530000060" SignalName="门碰" SignalCategory="2" SignalType="1" ChannelNo="5" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="1" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="7" SignalProperty="27" SignalMeanings="0:门开;1:门关" />
      <Signal SignalId="530000171" SignalName="温度" SignalCategory="1" SignalType="1" ChannelNo="16" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="℃" StoreInterval="10800" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="8" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="530000181" SignalName="信号强度" SignalCategory="1" SignalType="1" ChannelNo="17" ChannelType="1" Expression="" DataType="0" ShowPrecision="0.00" Unit="dBm" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="9" SignalProperty="27" SignalMeanings="" />
    </Signals>
    <Events Name="模板事件">
      <Event EventId="-3" EventName="设备通讯状态" EventCategory="63" StartType="1" EndType="3" StartExpression="[-1,-3]" SuppressExpression="" SignalId="-3" Enable="True" Visible="True" Description="" DisplayIndex="1">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="0" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="通讯异常" EquipmentState="" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="530000010" EventName="市电有无" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,530000010]" SuppressExpression="" SignalId="530000010" Enable="True" Visible="True" Description="" DisplayIndex="2">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="停电" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="530000020" EventName="水浸" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,530000020]" SuppressExpression="" SignalId="530000020" Enable="True" Visible="True" Description="" DisplayIndex="3">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="530000050" EventName="烟感" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,530000050]" SuppressExpression="" SignalId="530000050" Enable="True" Visible="True" Description="" DisplayIndex="6">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="530000060" EventName="门碰" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,530000060]" SuppressExpression="" SignalId="530000060" Enable="True" Visible="True" Description="" DisplayIndex="7">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="门开" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="530000171" EventName="温度" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,530000171]" SuppressExpression="" SignalId="530000171" Enable="True" Visible="True" Description="" DisplayIndex="8">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="2" StartOperation="&gt;" StartCompareValue="50" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="高温" EquipmentState="2" BaseTypeId="" StandardName="" />
          <EventCondition EventConditionId="1" EventSeverity="2" StartOperation="&lt;" StartCompareValue="10" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="低温" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="模板控制" />
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="876000119" SamplerName="MDU" SamplerType="18" ProtocolCode="MDUIO 6-00" DllCode="" DLLVersion="" ProtocolFilePath="" DLLFilePath="" DllPath="MDUAGENT.dll" Setting="9600,N,8,1" Description="" />
  </Samplers>
</EquipmentTemplates>