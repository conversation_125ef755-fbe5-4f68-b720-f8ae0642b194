<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="Equipment Template List">
  <EquipmentTemplate EquipmentTemplateId="1" ParentTemplateId="0" EquipmentTemplateName="Derivative Alarm Equipment" ProtocolCode="Derivative Alarm Equipment 6-00" EquipmentCategory="99" EquipmentType="2" Memo="" Property="" Decription=" " EquipmentStyle=" " Unit=" " Vender=" ">
    <Signals Name="Template Signal">
    </Signals>
    <Events Name="Template Event">
	  <Event EventId="100" EventName="Large Scale Stations Broken Alarm" EventCategory="6" StartType="1" EndType="3" StartExpression="" SuppressExpression=" " SignalId="" Enable="True" Visible="True" Description=" " DisplayIndex="100">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Large Scale Stations Broken Alarm" EquipmentState="" BaseTypeId="1302302001" StandardName="0" />
        </Conditions>
      </Event>
	   <Event EventId="101" EventName="Large Scale Power Cut Alarm" EventCategory="6" StartType="1" EndType="3" StartExpression="" SuppressExpression=" " SignalId="" Enable="True" Visible="True" Description=" " DisplayIndex="101">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="3" StartOperation="=" StartCompareValue="2" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Large Scale Power Cut Alarm" EquipmentState="" BaseTypeId="1302323001" StandardName="0" />
        </Conditions>
      </Event>
    </Events>
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="1" SamplerName="Derivative Alarm Sampler" SamplerType="30" ProtocolCode="Derivative Alarm Equipment 6-00" DllCode="" DLLVersion=" " ProtocolFilePath=" " DLLFilePath=" " DllPath="KoloBusinessServer.exe" Setting="" Description=" " />
  </Samplers>
</EquipmentTemplates>