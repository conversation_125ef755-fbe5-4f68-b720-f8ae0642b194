package com.siteweb.tcs.graph.service;

import java.util.Map;

/**
 * 图数据导出服务接口
 * 提供将图数据导出为各种格式的功能
 */
public interface GraphExportService {
    
    /**
     * 导出整个图数据为JSON格式
     * 
     * @return 包含图数据的JSON字符串
     */
    String exportGraphToJson();
    
    /**
     * 导出指定区域的图数据为JSON格式
     * 
     * @param regionId 区域ID
     * @param depth 最大遍历深度
     * @return 包含区域图数据的JSON字符串
     */
    String exportRegionToJson(Long regionId, int depth);
    
    /**
     * 导出指定网关的图数据为JSON格式
     * 
     * @param gatewayId 网关ID
     * @return 包含网关图数据的JSON字符串
     */
    String exportGatewayToJson(String gatewayId);
    
    /**
     * 导出图统计信息为JSON格式
     * 
     * @return 包含图统计信息的JSON字符串
     */
    String exportStatisticsToJson();
    
    /**
     * 导出图数据为GraphML格式
     * 
     * @return 包含图数据的GraphML字符串
     */
    String exportGraphToGraphML();
    
    /**
     * 导出图数据为CSV格式
     * 
     * @return 包含节点和关系CSV数据的Map
     */
    Map<String, String> exportGraphToCSV();
}
