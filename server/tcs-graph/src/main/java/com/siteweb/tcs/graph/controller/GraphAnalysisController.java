package com.siteweb.tcs.graph.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.graph.service.GraphAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 图分析控制器
 * 提供高级图分析功能的REST API
 */
@RestController
@RequestMapping("/analysis")
@Slf4j
public class GraphAnalysisController {
    
    @Autowired
    private GraphAnalysisService graphAnalysisService;
    
    @Value("${tcs.graph.query.max-traversal-depth:5}")
    private int maxTraversalDepth;
    
    /**
     * 执行影响分析
     * 
     * @param nodeId 节点ID
     * @param nodeType 节点类型 (Region, Gateway, Device)
     * @param depth 最大遍历深度 (可选，默认为1)
     * @return 影响分析结果
     */
    @GetMapping(value = "/impact", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> performImpactAnalysis(
            @RequestParam String nodeId,
            @RequestParam(required = false) String nodeType,
            @RequestParam(required = false, defaultValue = "1") int depth) {
        log.info("收到影响分析请求: nodeId={}, nodeType={}, depth={}", nodeId, nodeType, depth);
        
        try {
            int actualDepth = Math.min(depth, maxTraversalDepth);
            Map<String, Object> result = graphAnalysisService.performImpactAnalysis(nodeId, nodeType, actualDepth);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("执行影响分析时出错", e);
            return ResponseHelper.failed("执行影响分析时出错: " + e.getMessage());
        }
    }
    
    /**
     * 执行依赖分析
     * 
     * @param nodeId 节点ID
     * @param nodeType 节点类型 (Region, Gateway, Device)
     * @param depth 最大遍历深度 (可选，默认为1)
     * @return 依赖分析结果
     */
    @GetMapping(value = "/dependency", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> performDependencyAnalysis(
            @RequestParam String nodeId,
            @RequestParam(required = false) String nodeType,
            @RequestParam(required = false, defaultValue = "1") int depth) {
        log.info("收到依赖分析请求: nodeId={}, nodeType={}, depth={}", nodeId, nodeType, depth);
        
        try {
            int actualDepth = Math.min(depth, maxTraversalDepth);
            Map<String, Object> result = graphAnalysisService.performDependencyAnalysis(nodeId, nodeType, actualDepth);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("执行依赖分析时出错", e);
            return ResponseHelper.failed("执行依赖分析时出错: " + e.getMessage());
        }
    }
    
    /**
     * 查找共同依赖
     * 
     * @param nodeIds 节点ID列表
     * @param nodeType 节点类型 (Region, Gateway, Device)
     * @return 共同依赖节点列表
     */
    @GetMapping(value = "/common-dependencies", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findCommonDependencies(
            @RequestParam List<String> nodeIds,
            @RequestParam(required = false) String nodeType) {
        log.info("收到查找共同依赖请求: nodeIds={}, nodeType={}", nodeIds, nodeType);
        
        try {
            List<Map<String, Object>> result = graphAnalysisService.findCommonDependencies(nodeIds, nodeType);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("查找共同依赖时出错", e);
            return ResponseHelper.failed("查找共同依赖时出错: " + e.getMessage());
        }
    }
    
    /**
     * 查找关键路径
     * 
     * @param sourceId 源节点ID
     * @param targetId 目标节点ID
     * @param depth 最大遍历深度 (可选，默认为5)
     * @return 关键路径列表
     */
    @GetMapping(value = "/critical-paths", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findCriticalPaths(
            @RequestParam String sourceId,
            @RequestParam String targetId,
            @RequestParam(required = false, defaultValue = "5") int depth) {
        log.info("收到查找关键路径请求: sourceId={}, targetId={}, depth={}", sourceId, targetId, depth);
        
        try {
            int actualDepth = Math.min(depth, maxTraversalDepth);
            List<Map<String, Object>> result = graphAnalysisService.findCriticalPaths(sourceId, targetId, actualDepth);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("查找关键路径时出错", e);
            return ResponseHelper.failed("查找关键路径时出错: " + e.getMessage());
        }
    }
    
    /**
     * 查找孤立节点
     * 
     * @param nodeType 节点类型 (Region, Gateway, Device)，可选
     * @return 孤立节点列表
     */
    @GetMapping(value = "/isolated-nodes", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findIsolatedNodes(
            @RequestParam(required = false) String nodeType) {
        log.info("收到查找孤立节点请求: nodeType={}", nodeType);
        
        try {
            List<Map<String, Object>> result = graphAnalysisService.findIsolatedNodes(nodeType);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("查找孤立节点时出错", e);
            return ResponseHelper.failed("查找孤立节点时出错: " + e.getMessage());
        }
    }
    
    /**
     * 查找相似节点
     * 
     * @param nodeId 节点ID
     * @param nodeType 节点类型 (Region, Gateway, Device)
     * @param threshold 相似度阈值 (0-1)，可选，默认为0.5
     * @return 相似节点列表
     */
    @GetMapping(value = "/similar-nodes", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findSimilarNodes(
            @RequestParam String nodeId,
            @RequestParam(required = false) String nodeType,
            @RequestParam(required = false, defaultValue = "0.5") double threshold) {
        log.info("收到查找相似节点请求: nodeId={}, nodeType={}, threshold={}", nodeId, nodeType, threshold);
        
        try {
            List<Map<String, Object>> result = graphAnalysisService.findSimilarNodes(nodeId, nodeType, threshold);
            return ResponseHelper.successful(result);
        } catch (Exception e) {
            log.error("查找相似节点时出错", e);
            return ResponseHelper.failed("查找相似节点时出错: " + e.getMessage());
        }
    }
}
