package com.siteweb.stream.common.stream;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Shape 内存描述文件,内存创建实例使用
 * <AUTHOR> (2025-02-21)
 **/
@Data
public class StreamShapeDescriptor {

    private String i18nPrefix;

    private String type;

    private String icon;

    private  String bkColor;

    /**
     * 前端默认图标颜色
     */
    private String iconColor;

    private String author;

    private Class<?> clazz;

    private StreamShapeVersion version ;

    /**
     * 内部的，不向编辑器展现的，仅供代码内部使用的
     */
    private boolean internal;

    private StreamShapeOption defaultOptions;
    /**
     * Shape的Class Name
     */
    private String className;

    /**
     * Shape所在Package
     */
    private String packageName;

    /**
     * Shape所在Module
     */
    private String moduleId;


    private boolean deprecated;

    private JsonNode optionSchema;
    /**
     * 入口元配置
     */
    private List<StreamShapeLetInfo> inlets = new ArrayList<>();

    /**
     * 出口元配置
     */
    private List<StreamShapeLetInfo> outlets = new ArrayList<>();


    public StreamShapeInfo toShapeInfo(){
        var info = new  StreamShapeInfo();
        info.setShapeType(type);
        info.setDefaultIcon(icon);
        info.setVersion(version.clone());
        info.setAuthor(author);
        info.setDefaultOptions(defaultOptions);
        info.setDeprecated(deprecated);
        if(!inlets.isEmpty()){
            info.setInlets(inlets.stream().map(StreamShapeLetInfo::clone).toList());
        }
        if(!outlets.isEmpty()) {
            info.setOutlets(outlets.stream().map(StreamShapeLetInfo::clone).toList());
        }
        info.setOptionSchema(optionSchema);
        info.setDefaultIconColor(iconColor);
        info.setDefaultBkColor(bkColor);
        return info;
    }


}
