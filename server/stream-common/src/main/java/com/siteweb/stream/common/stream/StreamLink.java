package com.siteweb.stream.common.stream;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StreamLink {
    private long streamLinkId;

    /**
     * 出线Node
     */
    private long inNodeId;
    /**
     * 出线Node的出线口
     */
    private int outletId;


    /**
     * 入线Node
     */
    private long outNodeId;
    /**
     * 入线Node的入线口（暂未使用）
     */
    private int inletId;







}
