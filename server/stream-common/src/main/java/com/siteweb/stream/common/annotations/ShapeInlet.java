package com.siteweb.stream.common.annotations;

import com.siteweb.stream.common.enums.Inlet;
import com.siteweb.stream.common.enums.RunMode;

import java.lang.annotation.*;

/**
 * 定义Shape 的输入参数<br/>
 * 目前Shape仅支持单入口参数
 *
 * <AUTHOR> (2025-02-12)
 **/

@Repeatable(ShapeInlets.class)
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ShapeInlet {

    /**
     * 输入端身份标识符，单个Shape内应保持唯一
     */
    Inlet id();

    /**
     * 输入参数数据类型
     */
    Class<?> type();

    /**
     * 单个Inlet的最大扇入数量
     */
    int maxFan() default 255;



    /**
     * 代码内注释，没其他用
     */
    String  desc () default "";
}