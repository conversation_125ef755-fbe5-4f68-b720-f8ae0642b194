package com.siteweb.stream.common.stream;

import com.siteweb.stream.common.messages.ShapeRouteMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.pekko.actor.ActorRef;

/**
 * @ClassName: FlowRuntimeContext
 * @descriptions: 流运行上下文
 * @author: xsx
 * @date: 2/14/2025 6:34 PM
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowRuntimeContext extends RuntimeContext {
    private Long streamFlowId;
    private GraphRuntimeContext graphRuntimeContext;

    private ActorRef  graphActor;
}
