package com.siteweb.stream.common.util;

/**
 * <AUTHOR> (2025-02-26)
 **/
public class DynamicOutletUtils {


    /**
     * 将 outletId和dynamicIndex 组合为outletInstanceId
     * @param outletId
     * @param dynamicIndex
     * @return
     */
    public static int combo(short outletId, short dynamicIndex){
        return ((outletId & 0xFFFF) << 16) | (dynamicIndex & 0xFFFF);
    }

    /**
     * 将 outletId和dynamicIndex 组合为outletInstanceId
     * @param outletId
     * @param dynamicIndex 静态outlet时  dynamicIndex 始终为0
     * @return
     */
    public static int combo(int outletId, int dynamicIndex){
        return ((outletId & 0xFFFF) << 16) | (dynamicIndex & 0xFFFF);
    }



    /**
     * 将outletInstanceId 拆分为outletId和dynamicIndex
     * @param outletInstanceId outletInstanceId
     * @return [outletId,dynamicIndex]
     */
    public static short[] split(int outletInstanceId) {
        short outletId = (short) ((outletInstanceId >> 16) & 0xFFFF);
        short dynamicIndex = (short) (outletInstanceId & 0xFFFF);
        return new short[]{outletId, dynamicIndex};
    }
}
