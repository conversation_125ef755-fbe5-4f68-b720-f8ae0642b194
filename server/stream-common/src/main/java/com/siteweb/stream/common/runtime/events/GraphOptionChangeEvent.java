package com.siteweb.stream.common.runtime.events;

import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.stream.StreamFlowOption;
import com.siteweb.stream.common.stream.StreamGraphOption;
import com.siteweb.stream.common.stream.StreamShapeOption;
import lombok.Data;

import java.util.Map;

/**
 * @ClassName: GraphOptionChangeEvent
 * @descriptions: 图配置修改消息
 * @author: xsx
 * @date: 3/19/2025 5:50 PM
 **/
@Data
public class GraphOptionChangeEvent extends StreamMessage {
    /**
     * 图实例id
     */
    private Long streamGraphId;
    /**
     * 图配置参数
     */
    private StreamGraphOption streamGraphOption;
    /**
     * 流配置实例列表
     */
    private Map<Long,StreamFlowOption> streamFlowOptionMap;
    /**
     * key->流实例id
     * value->流下游节点配置参数列表
     */
    private Map<Long, Map<Long,StreamShapeOption>> streamShapeOptionMap;
}
