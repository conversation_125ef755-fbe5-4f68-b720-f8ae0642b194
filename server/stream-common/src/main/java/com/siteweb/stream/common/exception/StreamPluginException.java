package com.siteweb.stream.common.exception;

import com.siteweb.tcs.common.exception.code.PluginErrorCode;
import lombok.Getter;

/**
 * <AUTHOR> (2025-04-24)
 **/
public enum StreamPluginException  implements PluginErrorCode {

    /**
     * 例子
     */
    INITIALIZATION_DEVICE_FAILED("INITIALIZATION_DEVICE_FAILED", "设备初始化失败"), //




    ;

    StreamPluginException(String code, String message) {
        this.code = code;
        this.message = message;
    }


    @Getter
    private String code;


    @Getter
    private String message;

}
