package com.siteweb.tcs.north.cmcc.dal.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
@NoArgsConstructor
public class ServiceHostState {

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private Integer hostId;
    private Integer hostType;
    private Integer state;
    private String heartBeatTime;
    private Float cpuRate;
    private Float memRate;
    private Float diskRate;
    private Integer threadCount;
    private String interrupTime;
    private String sequenceId;

    public void uptHeartBeatTime(LocalDateTime heartBeatTime) {
        this.heartBeatTime = heartBeatTime.format(dateTimeFormatter);
    }

    public void uptInterrupTime(LocalDateTime interrupTime) {
        this.interrupTime = interrupTime.format(dateTimeFormatter);
    }
}
