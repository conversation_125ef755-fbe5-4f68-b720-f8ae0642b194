package com.siteweb.tcs.north.cmcc.connector.process;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.hub.domain.letter.EquipmentAlarmChange;
import com.siteweb.tcs.hub.domain.letter.EquipmentChange;
import com.siteweb.tcs.hub.domain.process.EnumDeviceConnectState;
import com.siteweb.tcs.north.cmcc.connector.ConnectorDataHolder;
import com.siteweb.tcs.north.cmcc.connector.letter.SetRedisItemAction;
import com.siteweb.tcs.north.cmcc.dal.entity.EventResponseItem;
import com.siteweb.tcs.north.cmcc.dal.provider.EventProvider;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * @ClassName: EquipmentSelfDiagnosisWatcher
 * @descriptions: 设备自诊断
 * @author: xsx
 * @date: 2024/9/12 10:35
 **/
public class EquipmentSelfDiagnosisWatcher extends AbstractActor {

    private final ActorProbe probe = createProbe(this);

    private EventProvider eventProvider = ConnectorDataHolder.getContext().getBean(EventProvider.class);

    private final ActorRef redisSink;

    private EquipmentSelfDiagnosisWatcher(ActorRef redisSink){
        this.redisSink = redisSink;
    }

    public static Props props(ActorRef redisSink) {
        return Props.create(EquipmentSelfDiagnosisWatcher.class,()-> new EquipmentSelfDiagnosisWatcher(redisSink));
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(List.class, this::onAlarmChanges)
                .build();
    }

    private void onAlarmChanges(List<EquipmentAlarmChange> alarmChanges) {
        //todo: 当配置变更时，Siteweb6侦听进行更新内存
        probe.info("[EquipmentSelfDiagnosis] 收到告警变更: " + alarmChanges.toString());
        if(CollectionUtil.isEmpty(alarmChanges)) return;
        List<EventResponseItem> eventResponseItems = Optional.ofNullable(alarmChanges)
                .orElseGet(ArrayList::new).stream()
                .map(e -> EventResponseItem.fromEquipmentAlarmChange(e))
                .toList();
        Pair<List<Integer>, List<Integer>> pair = eventProvider.deviceSelfDiagnosis(eventResponseItems);
        List<Integer> offlineList = pair.getKey();
        List<Integer> onlineList = pair.getValue();
        SetRedisItemAction setOffLineRedisItemAction = new SetRedisItemAction();
        SetRedisItemAction setOnLineRedisItemAction = new SetRedisItemAction();
        if(CollectionUtil.isNotEmpty(offlineList)){
            offlineList.forEach(e -> setOffLineRedisItemAction.addItem(EquipmentChange.getRedisKey(e),EquipmentChange.getRedisValue(e,EnumDeviceConnectState.OFFLINE.getCode())));
            redisSink.tell(setOffLineRedisItemAction,getSelf());
            probe.info("[EquipmentSelfDiagnosis] 需要置成离线的设备id列表是: " + offlineList);
        }
        if(CollectionUtil.isNotEmpty(onlineList)){
            onlineList.forEach(e -> setOnLineRedisItemAction.addItem(EquipmentChange.getRedisKey(e),EquipmentChange.getRedisValue(e, EnumDeviceConnectState.ONLINE.getCode())));
            redisSink.tell(setOnLineRedisItemAction,getSelf());
            probe.info("[EquipmentSelfDiagnosis] 需要置成在线的设备id列表是: " + onlineList);
        }
    }

    @Override
    public void postStop() {
        removeProbe(this.probe);
    }

}
