package com.siteweb.tcs.north.cmcc.dal.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.north.cmcc.dal.mapper.EquipmentMapper;
import com.siteweb.tcs.north.cmcc.dal.provider.EquipmentProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName: EquipmentProviderImpl
 * @descriptions: SiteWeb设备相关业务实现类
 * @author: xsx
 * @date: 2024/8/15 13:00
 **/
@Service
public class EquipmentProviderImpl implements EquipmentProvider {

    @Autowired
    private EquipmentMapper equipmentMapper;

    @Override
    public void updateConnectState(Integer equipmentId, Integer connectState) {
        if(ObjectUtil.isEmpty(equipmentId) || ObjectUtil.isEmpty(connectState)) return;
        Map<String, Object> params = new HashMap<>();
        params.put("equipmentId", equipmentId);
        params.put("connectState", connectState);
        equipmentMapper.updateConnectState(params);
    }

    @Override
    public void updateConnectState(List<Integer> equipmentIdList, Integer connectState) {
        if(CollectionUtil.isEmpty(equipmentIdList) || ObjectUtil.isEmpty(connectState)) return;;
        Map<String, Object> params = new HashMap<>();
        String idStr = equipmentIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
        params.put("equipmentId", idStr);
        params.put("connectState", connectState);
        equipmentMapper.updateConnectStateList(params);
    }

    @Override
    public void updateConnectStateByMonitorUnitId(Collection<Integer> monitorUnitIdList, Integer fromConnectState, Integer toConnectState) {
        if(CollectionUtil.isEmpty(monitorUnitIdList) || ObjectUtil.isEmpty(fromConnectState)|| ObjectUtil.isEmpty(toConnectState)) return;
        Map<String, Object> params = new HashMap<>();
        String idStr = monitorUnitIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
        params.put("monitorUnitId", idStr);
        params.put("fromConnectState", fromConnectState);
        params.put("toConnectState", toConnectState);
        equipmentMapper.updateConnectStateByMonitorUnitId(params);
    }
}
