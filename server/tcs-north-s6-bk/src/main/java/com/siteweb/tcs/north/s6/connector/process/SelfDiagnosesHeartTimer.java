package com.siteweb.tcs.north.cmcc.connector.process;

import cn.hutool.json.JSONUtil;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.north.cmcc.connector.ConnectorDataHolder;
import com.siteweb.tcs.north.cmcc.dal.entity.ServiceHostState;
import lombok.extern.slf4j.Slf4j;
import nonapi.io.github.classgraph.json.JSONSerializer;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Cancellable;
import org.apache.pekko.actor.Props;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.Duration;
import java.time.LocalDateTime;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * tcs向s6的redis定时刷心跳时间
 */
@Slf4j
public class SelfDiagnosesHeartTimer extends AbstractActor {

    private final ActorProbe probe = createProbe(this);

    /** 心跳定时器循环周期(秒) */
    public final int CYCLE_PERIOD_SECONDS = 30;
    /** WorkStation的redis心跳数据保存前缀 */
    public final String REDIS_PREFIX_WS_HEART = "WorkStationState:";

    private String tcsWsId;

    private final RedisTemplate redisTemplate;
    private final ConnectorDataHolder dataHolder = ConnectorDataHolder.getContext().getBean(ConnectorDataHolder.class);

    // 定时器
    private Cancellable heartbeatScheduler;

    public static Props props(RedisTemplate redisTemplate){
        return Props.create(SelfDiagnosesHeartTimer.class, redisTemplate);
    }

    public SelfDiagnosesHeartTimer(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void preStart() {
        try {
            tcsWsId = dataHolder.getTcsWsId();
            if(StringUtils.isBlank(tcsWsId)) {
                probe.warn("wsId is null, do not start the heartbeat timer");
            } else {
                probe.info("Heartbeat timer starts to start, with a cycle of " + CYCLE_PERIOD_SECONDS + " seconds");
                // 延迟 30 秒后首次执行
                heartbeatScheduler = getContext().getSystem().scheduler()
                        .scheduleWithFixedDelay(Duration.ofSeconds(30), Duration.ofSeconds(CYCLE_PERIOD_SECONDS),
                        this::updateHeartbeat, getContext().getDispatcher()
                );
            }
        } catch (Exception ex) {
            probe.error("start heartbeat timer throw exception : " + ex.getMessage());
        }
    }

    /** 更新心跳逻辑 */
    private void updateHeartbeat() {
        try {
            String redisKey = REDIS_PREFIX_WS_HEART + tcsWsId;

            ServiceHostState thisWorkStationState = mGetServiceHostState(redisKey);
            if (thisWorkStationState == null) {
                thisWorkStationState = new ServiceHostState();
                thisWorkStationState.setHostId(Integer.valueOf(tcsWsId));
                thisWorkStationState.setHostType(102);//TODO: 待确定
                thisWorkStationState.setState(1);
                thisWorkStationState.uptHeartBeatTime(LocalDateTime.now());
                thisWorkStationState.setCpuRate(0f);
                thisWorkStationState.setMemRate(0f);
                thisWorkStationState.setDiskRate(0f);
                thisWorkStationState.setThreadCount(0);
                thisWorkStationState.setSequenceId("");
                thisWorkStationState.uptInterrupTime(LocalDateTime.of(1, 1, 1, 0, 0));
            } else {
                thisWorkStationState.uptHeartBeatTime(LocalDateTime.now());
            }
            mSetServiceHostState(redisKey, thisWorkStationState);

            probe.info("Successfully refreshed heartbeat time to Redis，Key: " + redisKey);
        } catch (Exception e) {
            probe.error("Error refreshing heartbeat time to Redis: " + e.getMessage());
        }
    }

    public ServiceHostState mGetServiceHostState(String key){

        Object obj = redisTemplate.opsForValue().get(key);
        if(obj == null){
            return null;
        } else {
            return JSONUtil.toBean(obj.toString(), ServiceHostState.class);
        }
    }

    public void mSetServiceHostState(String key, ServiceHostState value) {
        String strVal = JSONSerializer.serializeObject(value);
        redisTemplate.opsForValue().set(key,strVal);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder().build();
    }

    @Override
    public void postStop() {
        // 取消定时器
        if (heartbeatScheduler != null && !heartbeatScheduler.isCancelled()) {
            heartbeatScheduler.cancel();
            heartbeatScheduler = null;
            probe.info("The heartbeat timer has stopped");
        }
        removeProbe(this.probe);
    }

}

