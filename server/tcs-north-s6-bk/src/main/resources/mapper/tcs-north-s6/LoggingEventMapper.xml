<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.tcs.north.cmcc.dal.mapper.LoggingEventMapper">
    <!--
        IN  StationId   	INT       	,
        IN  HostId      	INT   	   	,
        IN  EquipmentId 	INT		    ,
        IN  SignalTotal	 	INT		    ,
        IN  BSequenceId		VARCHAR(4000),
        IN  BSampleTime		VARCHAR(4000),
        IN  BDoorId			VARCHAR(4000),
        IN  BCardId			VARCHAR(4000),
        IN  BEnter			VARCHAR(4000),
        IN  BSwapFlag		VARCHAR(4000),
    -->
    <select id="batchSaveSwapCardEvent" statementType="CALLABLE"  resultType="int">
        CALL PBL_BatchSaveSwapCardRecord(
        #{StationId}, #{HostId}, #{EquipmentId}, #{SignalTotal},
        #{BSequenceId}, #{BSampleTime}, #{BDoorId}, #{BCardId},
        #{BEnter}, #{BSwapFlag},
        #{ret, mode=OUT,jdbcType=INTEGER})
    </select>
</mapper>

