-- SQLite特定函数和存储过程
-- 版本: 1.0.0
-- 描述: SQLite数据库特定的函数实现

-- SQLite不支持存储过程，但我们可以创建一些辅助函数

-- 创建获取当前时间戳的函数（SQLite内置CURRENT_TIMESTAMP已提供此功能）

-- 创建更新时间戳触发器
CREATE TRIGGER IF NOT EXISTS update_timestamp
AFTER UPDATE ON tcs_user
FOR EACH ROW
BEGIN
    UPDATE tcs_user SET updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_timestamp_role
AFTER UPDATE ON tcs_role
FOR EACH ROW
BEGIN
    UPDATE tcs_role SET updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.id;
END;