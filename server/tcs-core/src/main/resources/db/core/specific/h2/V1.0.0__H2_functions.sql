-- H2特定函数和存储过程
-- 版本: 1.0.0
-- 描述: H2数据库特定的函数实现

-- 示例：创建获取当前时间戳的函数
CREATE ALIAS IF NOT EXISTS get_current_timestamp AS $$
  java.sql.Timestamp getCurrentTimestamp() {
    return new java.sql.Timestamp(System.currentTimeMillis());
  }
$$;

-- 示例：创建更新时间戳触发器
CREATE TRIGGER IF NOT EXISTS update_timestamp
BEFORE UPDATE ON tcs_user
FOR EACH ROW
CALL "com.siteweb.tcs.common.trigger.UpdateTimestampTrigger";

CREATE TRIGGER IF NOT EXISTS update_timestamp_role
BEFORE UPDATE ON tcs_role
FOR EACH ROW
CALL "com.siteweb.tcs.common.trigger.UpdateTimestampTrigger";