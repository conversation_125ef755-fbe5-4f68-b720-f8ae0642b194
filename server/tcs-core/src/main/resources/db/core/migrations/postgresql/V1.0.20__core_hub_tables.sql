-- Table: tcs_region_items
CREATE TABLE tcs_region_items (
  regionItemId SERIAL PRIMARY KEY,
  regionId INT DEFAULT NULL,
  pluginId VARCHAR(45) DEFAULT NULL,
  itemId VARCHAR(45) DEFAULT NULL,
  itemName VARCHAR(45) DEFAULT NULL,
  itemType VARCHAR(45) DEFAULT NULL
);

-- Table: tcs_regions
CREATE TABLE tcs_regions (
  regionId SERIAL PRIMARY KEY,
  regionName VARCHAR(45) DEFAULT NULL,
  parentId INT DEFAULT NULL,
  displayIndex INT DEFAULT NULL,
  description VARCHAR(64) DEFAULT NULL,
  resourceStructureId INT NOT NULL
);

-- Table: tcs_foreign_alarm
-- Comment: 南向插件中设备告警与动环告警映射关系
CREATE TABLE tcs_foreign_alarm (
  ForeignAlarmId VARCHAR(128) NOT NULL,
  EventId INT DEFAULT NULL,
  EquipmentId INT NOT NULL, -- 外键，设备信号id
  EventConditionId INT DEFAULT NULL, -- 动环中告警条件id
  PRIMARY KEY (ForeignAlarmId, EquipmentId)
);

-- Table: tcs_foreign_control
-- Comment: 南向插件设备的控制与动环设备控制映射关系
CREATE TABLE tcs_foreign_control (
  ForeignControlId VARCHAR(128) NOT NULL,
  ControlId INT DEFAULT NULL,
  EquipmentId INT NOT NULL, -- 外键，动环中设备id
  ControlMeaningsId INT DEFAULT NULL,
  PRIMARY KEY (ForeignControlId, EquipmentId)
);

-- Table: tcs_foreign_device
-- Comment: 南向设备与动环设备映射表
CREATE TABLE tcs_foreign_device (
  MonitorUnitId INT DEFAULT NULL, -- 外键，监控单元id
  ForeignDeviceId VARCHAR(128) NOT NULL,
  EquipmentId INT NOT NULL,
  EquipmentTemplateId INT DEFAULT NULL,
  PRIMARY KEY (ForeignDeviceId, EquipmentId)
);

-- Table: tcs_foreign_gateway
CREATE TABLE tcs_foreign_gateway (
  ForeignGatewayId VARCHAR(64) NOT NULL,
  PluginId VARCHAR(32) NOT NULL,
  MonitorUnitId INT DEFAULT NULL,
  StationId INT DEFAULT NULL,
  PRIMARY KEY (ForeignGatewayId, PluginId)
);

-- Table: tcs_foreign_signal
-- Comment: 南向插件设备信号与动环信号映射关系
CREATE TABLE tcs_foreign_signal (
  ForeignSignalId VARCHAR(128) NOT NULL,
  SignalId INT DEFAULT NULL,
  EquipmentId VARCHAR(45) NOT NULL, -- 外键，动环的设备id
  PRIMARY KEY (ForeignSignalId, EquipmentId)
);

-- Table: tcs_plugins
CREATE TABLE tcs_plugins (
  pluginId VARCHAR(64) NOT NULL PRIMARY KEY,
  pluginName VARCHAR(32) NULL,
  version VARCHAR(12) NULL,
  provider VARCHAR(32) NULL,
  description VARCHAR(64) NULL,
  className VARCHAR(64) NULL,
  buildTime VARCHAR(45) NULL,
  fileName VARCHAR(64) NULL,
  enabled BOOLEAN NULL,
  uploadJARDate TIMESTAMP NULL,
  updateJARDate TIMESTAMP NULL,
  changeDate TIMESTAMP NULL,
  operateDate TIMESTAMP NULL,
  applicationName VARCHAR(32) NULL
);

-- Table: tcs_plugin_dependencies
CREATE TABLE tcs_plugin_dependencies (
  applicationName VARCHAR(64) NOT NULL,
  pluginId VARCHAR(64) NOT NULL,
  dependencies TEXT NOT NULL,
  CONSTRAINT APP_PLUGIN_INDEX UNIQUE (applicationName, pluginId)
);

-- Table: tcs_plugins_backup
-- Comment: 插件备份信息表
CREATE TABLE tcs_plugins_backup (
  backupId BIGSERIAL PRIMARY KEY,
  pluginId VARCHAR(64) NOT NULL,
  backupName VARCHAR(64) NOT NULL,
  version VARCHAR(12) NOT NULL,
  backupDate TIMESTAMP NOT NULL,
  backupSize VARCHAR(128) DEFAULT NULL,
  fileName VARCHAR(128) DEFAULT NULL
);
CREATE INDEX idx_pluginId ON tcs_plugins_backup (pluginId);

-- Table: tcs_account
-- Comment: 用户表
CREATE TABLE tcs_account (
  UserId BIGSERIAL PRIMARY KEY, -- 账户id，id建议自增，如若考虑到后续的分布式自增问题，可以使用雪花算法，类型为bigint
  UserName VARCHAR(256) NOT NULL, -- 用户名称
  LoginId VARCHAR(256) NOT NULL, -- 登录的用户名（填在用户名输入框）
  Password VARCHAR(256) NOT NULL, -- 密码，一般为加密串，用使用杂凑算法计算杂凑值
  Enable BOOLEAN DEFAULT FALSE, -- 是否启用
  MaxError INT DEFAULT 10, -- 登录最大错误次数，默认十次
  Locked BOOLEAN DEFAULT FALSE, -- 账户是否锁定，默认为false
  ValidTime TIMESTAMP DEFAULT NULL, -- 账户有效期
  PasswordValidTime TIMESTAMP DEFAULT NULL, -- 密码有效期
  Description VARCHAR(256) DEFAULT NULL, -- 描述字段
  DepartmentId BIGINT DEFAULT NULL -- 所属部门ID，外键关联tcs_department表
);

-- Table: tcs_role
-- Comment: 角色表
CREATE TABLE tcs_role (
  RoleId BIGSERIAL PRIMARY KEY, -- 角色id
  RoleName VARCHAR(128) NOT NULL, -- 角色名称
  Description VARCHAR(256) DEFAULT NULL, -- 描述
  status INT DEFAULT 1, -- 启用状态：1=启用，0=停用
  sort_order INT DEFAULT 0, -- 排序值，数值越小越靠前
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  role_code VARCHAR(64) DEFAULT NULL -- 角色标识代码
);

-- 添加角色表索引
CREATE INDEX idx_tcs_role_status ON tcs_role(status);
CREATE INDEX idx_tcs_role_sort ON tcs_role(sort_order);
CREATE INDEX idx_tcs_role_code ON tcs_role(role_code);

-- Table: tcs_account_role_map
-- Comment: 用户角色映射表
CREATE TABLE tcs_account_role_map (
  UserId BIGINT NOT NULL, -- 用户id
  RoleId BIGINT NOT NULL, -- 角色id
  PRIMARY KEY (UserId, RoleId)
);

-- Table: tcs_menu_item
-- Comment: 菜单项表
CREATE TABLE tcs_menu_item (
  MenuItemId BIGINT NOT NULL, -- 菜单项id
  PluginId VARCHAR(128) NOT NULL, -- 插件id
  MenuItemName VARCHAR(128) NOT NULL, -- 菜单项名称
  Name VARCHAR(128) DEFAULT NULL, -- 路由名称（用于路由跳转）
  ParentMenuItemId BIGINT DEFAULT NULL, -- 父菜单项id
  Path VARCHAR(512) NOT NULL, -- 菜单项路径
  Icon VARCHAR(128) DEFAULT NULL, -- 图标
  Component VARCHAR(512) DEFAULT NULL, -- 组件路径（字符串形式）
  ShowLink BOOLEAN DEFAULT TRUE, -- 是否在导航菜单中显示
  ShowParent BOOLEAN DEFAULT FALSE, -- 是否显示父级菜单
  ActivePath VARCHAR(512) DEFAULT NULL, -- 激活路径（用于详情页等）
  Redirect VARCHAR(512) DEFAULT NULL, -- 重定向路径
  Rank INT DEFAULT 0, -- 排序权重
  Auths VARCHAR(1024) DEFAULT NULL, -- 权限标识（JSON数组格式）
  PRIMARY KEY (MenuItemId, PluginId)
);

-- Table: tcs_permission
-- Comment: 权限表
CREATE TABLE tcs_permission (
  PermissionId BIGSERIAL PRIMARY KEY,
  PluginId VARCHAR(128) NOT NULL, -- 插件id
  PermissionName VARCHAR(128) DEFAULT NULL, -- 权限名称
  PermissionType INT DEFAULT NULL, -- 权限类型，为一个枚举，其中1-菜单权限，2-区域权限，3-操作权限（预留）
  Description VARCHAR(256) DEFAULT NULL, -- 描述
  CreateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UpdateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: tcs_role_permission_map
-- Comment: 角色权限映射表
CREATE TABLE tcs_role_permission_map (
  PluginId VARCHAR(128) NOT NULL, -- 插件id
  RoleId BIGINT NOT NULL, -- 角色id
  PermissionId BIGINT NOT NULL, -- 权限点id
  permissionType INT NOT NULL, -- 当permissiontype 为1时 permissionId 为menuId ，为2时 permissionId 为 regionId
  PRIMARY KEY (PluginId, RoleId, PermissionId, permissionType)
);

-- Table: tcs_operation_log
-- Comment: 操作日志表
CREATE TABLE tcs_operation_log (
  LogId BIGSERIAL PRIMARY KEY,
  UserId BIGINT NOT NULL, -- 用户id，外键
  ObjectId BIGINT NOT NULL, -- 操作对象id
  ObjectName VARCHAR(256) DEFAULT NULL, -- 操作对象名称
  ObjectType INT DEFAULT NULL, -- 操作对象类型，枚举值，其中1-用户，2-角色，3-菜单，4-操作，5-区域，6-权限
  OperationTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 操作时间
  OperationType VARCHAR(128) DEFAULT NULL, -- 操作类型,1-创建,2-修改,3-删除
  OldValue TEXT DEFAULT NULL, -- 修改前值
  NewValue TEXT DEFAULT NULL -- 修改后值
);

-- Table: tcs_auth_code
-- Comment: 权限代码表
CREATE TABLE tcs_auth_code (
  auth_id BIGSERIAL PRIMARY KEY,  -- 权限ID（自增）
  auth_code VARCHAR(128) NOT NULL,            -- 权限代码，如 "system:user:add"
  auth_name VARCHAR(128) NOT NULL,            -- 权限名称，如 "新增用户"
  menu_item_id BIGINT,                        -- 所属菜单ID（可选）
  plugin_id VARCHAR(128) NOT NULL,            -- 所属插件ID
  UNIQUE (auth_code, plugin_id)
);

CREATE TABLE tcs_alarm (
  id BIGINT NOT NULL,
  DeviceId BIGINT DEFAULT NULL,
  SouthAlarmId VARCHAR(256) DEFAULT NULL,
  SouthAlarmName VARCHAR(256) DEFAULT NULL,
  SouthAlarmMeaning VARCHAR(256) DEFAULT NULL,
  RelatedSignalId BIGINT DEFAULT NULL,
  Metadata JSON DEFAULT NULL,
  Deleted BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (id)
);

COMMENT ON TABLE tcs_alarm IS '告警表';
COMMENT ON COLUMN tcs_alarm.id IS '告警全局id';
COMMENT ON COLUMN tcs_alarm.DeviceId IS '设备全局id';
COMMENT ON COLUMN tcs_alarm.SouthAlarmId IS '南向告警id';
COMMENT ON COLUMN tcs_alarm.SouthAlarmName IS '南向告警名称，当只有但条件时，SouthAlarmMeaning为空，SouthAlarmName本身就代表告警含义';
COMMENT ON COLUMN tcs_alarm.SouthAlarmMeaning IS '单条件';
COMMENT ON COLUMN tcs_alarm.RelatedSignalId IS '关联信号的id';
COMMENT ON COLUMN tcs_alarm.Metadata IS '南向元数据';
COMMENT ON COLUMN tcs_alarm.Deleted IS '逻辑删除标志';

CREATE TABLE tcs_control (
  id BIGINT NOT NULL,
  DeviceId BIGINT DEFAULT NULL,
  SouthControlId VARCHAR(256) DEFAULT NULL,
  SouthControlName VARCHAR(256) DEFAULT NULL,
  MaxValue DOUBLE PRECISION DEFAULT NULL,
  MinValue DOUBLE PRECISION DEFAULT NULL,
  ControlType INTEGER DEFAULT NULL,
  ControlMeanings JSON DEFAULT NULL,
  RelatedSignalId BIGINT DEFAULT NULL,
  Metadata JSON DEFAULT NULL,
  Deleted BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (id)
);

COMMENT ON TABLE tcs_control IS '控制表';
COMMENT ON COLUMN tcs_control.id IS '告警控制id';
COMMENT ON COLUMN tcs_control.DeviceId IS '设备全局id';
COMMENT ON COLUMN tcs_control.SouthControlId IS '南向控制id';
COMMENT ON COLUMN tcs_control.SouthControlName IS '南向控制名称';
COMMENT ON COLUMN tcs_control.MaxValue IS '控制允许的最大值';
COMMENT ON COLUMN tcs_control.MinValue IS '控制允许的最小值';
COMMENT ON COLUMN tcs_control.ControlMeanings IS '控制含义';
COMMENT ON COLUMN tcs_control.RelatedSignalId IS '关联信号的id';
COMMENT ON COLUMN tcs_control.Metadata IS '南向元数据';
COMMENT ON COLUMN tcs_control.Deleted IS '逻辑删除标志';

CREATE TABLE tcs_device (
  id BIGINT NOT NULL,
  GatewayId BIGINT DEFAULT NULL,
  SouthDeviceId VARCHAR(256) DEFAULT NULL,
  SouthDeviceName VARCHAR(256) DEFAULT NULL,
  SouthDeviceType INTEGER DEFAULT NULL,
  Metadata JSON DEFAULT NULL,
  Deleted BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (id)
);

COMMENT ON TABLE tcs_device IS '设备表';
COMMENT ON COLUMN tcs_device.id IS '设备全局id';
COMMENT ON COLUMN tcs_device.GatewayId IS '网关全局id';
COMMENT ON COLUMN tcs_device.SouthDeviceId IS '南向设备id';
COMMENT ON COLUMN tcs_device.SouthDeviceName IS '南向设备名称';
COMMENT ON COLUMN tcs_device.SouthDeviceType IS '南向设备类型';
COMMENT ON COLUMN tcs_device.Metadata IS '南向元数据';
COMMENT ON COLUMN tcs_device.Deleted IS '逻辑删除标志';

CREATE TABLE tcs_gateway (
  id BIGINT NOT NULL,
  PluginId VARCHAR(128) NOT NULL,
  SouthGatewayId VARCHAR(256) DEFAULT NULL,
  SouthGatewayName VARCHAR(256) DEFAULT NULL,
  SouthAddress VARCHAR(128) DEFAULT NULL,
  Metadata JSON DEFAULT NULL,
  Deleted BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (id)
);

COMMENT ON TABLE tcs_gateway IS '网关表';
COMMENT ON COLUMN tcs_gateway.id IS '全局id';
COMMENT ON COLUMN tcs_gateway.PluginId IS '插件id';
COMMENT ON COLUMN tcs_gateway.SouthGatewayId IS '南向网关id';
COMMENT ON COLUMN tcs_gateway.SouthGatewayName IS '南向网关名称';
COMMENT ON COLUMN tcs_gateway.SouthAddress IS '南向网关地址';
COMMENT ON COLUMN tcs_gateway.Metadata IS '南向元数据';
COMMENT ON COLUMN tcs_gateway.Deleted IS '逻辑删除标志';

CREATE TABLE tcs_signal (
  id BIGINT NOT NULL,
  DeviceId BIGINT DEFAULT NULL,
  SouthSignalId VARCHAR(256) DEFAULT NULL,
  SouthSignalName VARCHAR(256) DEFAULT NULL,
  SouthSignalMeanings JSON DEFAULT NULL,
  SouthSignalUnit VARCHAR(16) DEFAULT NULL,
  SignalType INTEGER DEFAULT NULL,
  Metadata JSON DEFAULT NULL,
  Deleted BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (id)
);

COMMENT ON TABLE tcs_signal IS '信号表';
COMMENT ON COLUMN tcs_signal.id IS '信号全局id';
COMMENT ON COLUMN tcs_signal.DeviceId IS '设备全局id';
COMMENT ON COLUMN tcs_signal.SouthSignalId IS '南向信号id';
COMMENT ON COLUMN tcs_signal.SouthSignalName IS '南向信号名称';
COMMENT ON COLUMN tcs_signal.SouthSignalMeanings IS '南向信号含义';
COMMENT ON COLUMN tcs_signal.SouthSignalUnit IS '南向信号单位';
COMMENT ON COLUMN tcs_signal.SignalType IS '信号类型，其中1-模拟量、2-开关量、3-字符串、4-时间戳';
COMMENT ON COLUMN tcs_signal.Metadata IS '南向元数据';
COMMENT ON COLUMN tcs_signal.Deleted IS '逻辑删除标志';

CREATE TABLE WORKER_NODE (
  ID BIGSERIAL NOT NULL,
  HOST_NAME VARCHAR(64) NOT NULL,
  PORT VARCHAR(64) NOT NULL,
  TYPE INTEGER NOT NULL,
  LAUNCH_DATE DATE NOT NULL,
  MODIFIED TIMESTAMP NOT NULL,
  CREATED TIMESTAMP NOT NULL,
  PRIMARY KEY (ID)
);

COMMENT ON TABLE WORKER_NODE IS 'DB WorkerID Assigner for UID Generator';
COMMENT ON COLUMN WORKER_NODE.ID IS 'auto increment id';
COMMENT ON COLUMN WORKER_NODE.HOST_NAME IS 'host name';
COMMENT ON COLUMN WORKER_NODE.PORT IS 'port';
COMMENT ON COLUMN WORKER_NODE.TYPE IS 'node type: CONTAINER(1), ACTUAL(2), FAKE(3)';
COMMENT ON COLUMN WORKER_NODE.LAUNCH_DATE IS 'launch date';
COMMENT ON COLUMN WORKER_NODE.MODIFIED IS 'modified time';
COMMENT ON COLUMN WORKER_NODE.CREATED IS 'created time';
