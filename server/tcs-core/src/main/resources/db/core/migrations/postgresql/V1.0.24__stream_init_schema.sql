-- =====================================================
-- 流模块表
-- =====================================================

CREATE TABLE IF NOT EXISTS tcs_stream_library (
    library_id VARCHAR(100) PRIMARY KEY, -- 流模块Id
    library_name VARCHAR(100) NOT NULL, -- 流模块名
    library_version VARCHAR(100) NOT NULL, -- 流模块版本
    library_package VARCHAR(100) NOT NULL, -- 流模块包名
    library_provider VARCHAR(100) NOT NULL, -- 流模块技术支持
    build_time VARCHAR(100) NOT NULL, -- 流模块编译日期
    jar_file VARCHAR(100) NOT NULL, -- jar文件路径
    jar_code VARCHAR(100) NOT NULL, -- jar文件哈希
    enable VARCHAR(50) NOT NULL, -- 是否启用
    create_time timestamp DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time timestamp DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    created_by VARCHAR(50), -- 创建者
    updated_by VARCHAR(50), -- 更新者
    CONSTRAINT stream_library_updated_at CHECK (update_time >= create_time)
    );




COMMENT ON TABLE tcs_stream_library IS '流模块表';

-- 流图表
CREATE TABLE IF NOT EXISTS tcs_streams (
    stream_graph_id bigint PRIMARY KEY, -- 流图唯一ID
    stream_graph_name VARCHAR(100) NOT NULL, -- 流图名称
    graph_option JSON NOT NULL, -- 流图配置项
    flows JSON NOT NULL, -- 流配置
    create_time timestamp DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time timestamp DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    created_by VARCHAR(50), -- 创建者
    updated_by VARCHAR(50), -- 更新者
    CONSTRAINT stream_graph_updated_at CHECK (update_time >= create_time)
    );

COMMENT ON TABLE tcs_streams IS '流图表';

-- =====================================================
-- 索引创建
-- =====================================================

-- 创建流模块索引
CREATE INDEX IF NOT EXISTS idx_stream_library_id ON tcs_stream_library(library_id);

-- 创建流图索引
CREATE INDEX IF NOT EXISTS idx_stream_graph_id ON tcs_streams(stream_graph_id);

-- =====================================================
-- 评论添加
-- =====================================================

COMMENT ON COLUMN tcs_stream_library.library_id IS '流模块Id';
COMMENT ON COLUMN tcs_stream_library.library_name IS '流模块名';
COMMENT ON COLUMN tcs_stream_library.library_version IS '流模块版本';
COMMENT ON COLUMN tcs_stream_library.library_package IS '流模块包名';
COMMENT ON COLUMN tcs_stream_library.library_provider IS '流模块技术支持';
COMMENT ON COLUMN tcs_stream_library.build_time IS '流模块编译日期';
COMMENT ON COLUMN tcs_stream_library.jar_file IS 'jar文件路径';
COMMENT ON COLUMN tcs_stream_library.jar_code IS 'jar文件哈希';
COMMENT ON COLUMN tcs_stream_library.enable IS '是否启用';
COMMENT ON COLUMN tcs_stream_library.create_time IS '创建时间';
COMMENT ON COLUMN tcs_stream_library.update_time IS '更新时间';
COMMENT ON COLUMN tcs_stream_library.created_by IS '创建者';
COMMENT ON COLUMN tcs_stream_library.updated_by IS '更新者';

COMMENT ON COLUMN tcs_streams.stream_graph_id IS '流图唯一ID';
COMMENT ON COLUMN tcs_streams.stream_graph_name IS '流图名称';
COMMENT ON COLUMN tcs_streams.graph_option IS '流图配置项';
COMMENT ON COLUMN tcs_streams.flows IS '流配置';
COMMENT ON COLUMN tcs_streams.create_time IS '创建时间';
COMMENT ON COLUMN tcs_streams.update_time IS '更新时间';
COMMENT ON COLUMN tcs_streams.created_by IS '创建者';
COMMENT ON COLUMN tcs_streams.updated_by IS '更新者';
