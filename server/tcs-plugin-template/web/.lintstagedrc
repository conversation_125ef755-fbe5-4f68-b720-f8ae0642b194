{"*.{js,jsx,ts,tsx}": ["prettier --cache --ignore-unknown  --write", "eslint --cache --fix"], "{!(package)*.json,*.code-snippets,.!({browserslist,npm,nvm})*rc}": ["prettier --cache --write--parser json"], "package.json": ["prettier --cache --write"], "*.vue": ["prettier --write", "eslint --cache --fix", "stylelint --fix --allow-empty-input"], "*.{css,scss,html}": ["prettier --cache --ignore-unknown --write", "stylelint --fix --allow-empty-input"], "*.md": ["prettier --cache --ignore-unknown --write"]}