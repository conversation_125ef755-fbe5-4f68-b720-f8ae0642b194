{"streamGraphId": null, "streamGraphName": "CMCC Fsu - DataFlow Graph", "graphOption": {"Author": "xsx", "runMode": "RUN", "fsuId": "{FSU:ID}"}, "flows": [{"streamFlowId": 1000000, "streamFlowName": "Get Data Flow", "streamFlowOption": null, "nodes": [{"streamNodeId": 1000, "shapeType": "fixed-timer", "option": {"type": "com.siteweb.stream.defaults.options.TimerShapeOption", "version": null, "name": "", "bkColor": "", "iconColor": "", "x": 0, "y": 0, "icon": null, "comment": null, "enabled": true, "dynamicOutlets": [], "firstDelay": 3, "interval": 3, "defaultState": true}}, {"streamNodeId": 2000, "shapeType": "cmcc-get-data", "option": {"type": "com.siteweb.tcs.south.cmcc.stream.options.GetDataShapeOption", "version": null, "name": "", "bkColor": "", "iconColor": "", "x": 0, "y": 0, "icon": null, "comment": null, "enabled": true, "dynamicOutlets": []}}], "links": [{"streamLinkId": 4129218799920347136, "inNodeId": 1000, "outNodeId": 2000, "inletId": 65536, "outletId": 65536}]}, {"streamFlowId": 2000000, "streamFlowName": "自下往上流", "streamFlowOption": null, "nodes": [{"streamNodeId": 2400000, "shapeType": "cmcc-package-parser", "option": {"type": "com.siteweb.tcs.south.cmcc.stream.options.MobileParserShapeOption", "version": null, "name": "", "bkColor": "", "iconColor": "", "x": 0, "y": 0, "icon": null, "comment": null, "enabled": true, "dynamicOutlets": []}}, {"streamNodeId": 2500000, "shapeType": "data-switch", "option": {"type": "com.siteweb.stream.defaults.options.SwitchShapeOption", "version": null, "name": "", "bkColor": "", "iconColor": "", "x": 0, "y": 0, "icon": null, "comment": null, "enabled": true, "dynamicOutlets": [65537, 65538], "property": {"scope": "exp", "property": "$GET_CTCC_PK_NAME(msg)"}, "branches": [{"operator": "eq", "property": {"scope": "text", "text": "SEND_DATA"}, "output": 65537}, {"operator": "eq", "property": {"scope": "text", "text": "SEND_ALARM"}, "output": 65538}], "matchAll": true}}, {"streamNodeId": 2600000, "shapeType": "cmcc-send-data", "option": {"type": "com.siteweb.tcs.south.cmcc.stream.options.SendDataShapeOption", "version": null, "name": "", "bkColor": "", "iconColor": "", "x": 0, "y": 0, "icon": null, "comment": null, "enabled": true, "dynamicOutlets": []}}, {"streamNodeId": 2700000, "shapeType": "cmcc-send-alarm", "option": {"type": "com.siteweb.tcs.south.cmcc.stream.options.SendAlarmShapeOption", "version": null, "name": "", "bkColor": "", "iconColor": "", "x": 0, "y": 0, "icon": null, "comment": null, "logging": {"logLevel": "INFO", "enableDetailedLogging": "false"}, "enabled": true, "dynamicOutlets": []}}], "links": [{"streamLinkId": 560526772782505984, "inNodeId": 2400000, "outNodeId": 2500000, "inletId": 65536, "outletId": 65536}, {"streamLinkId": 2143772124606545920, "inNodeId": 2500000, "outNodeId": 2600000, "outletId": 65537, "inletId": 65536}, {"streamLinkId": 6021212459204377654, "inNodeId": 2500000, "outNodeId": 2700000, "outletId": 65538, "inletId": 65536}]}]}