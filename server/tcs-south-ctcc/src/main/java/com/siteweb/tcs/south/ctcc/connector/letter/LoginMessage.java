package com.siteweb.tcs.south.ctcc.connector.letter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.south.ctcc.protocol.PK_TypeName;
import lombok.Getter;
import lombok.Setter;

/**
 * LOGIN 消息 - FSU注册请求
 * 按照CTCC规范：FSU向SC传送SUID、用户名、口令等SU注册信息
 * 
 * <AUTHOR> (2025-05-09)
 */
@Getter
@JacksonXmlRootElement(localName = "Request")
public class LoginMessage extends CtccRequestMessage {

    public LoginMessage() {
        super(PK_TypeName.LOGIN);
    }

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private final Info info = new Info();

    @Setter
    @Getter
    public static class Info {
        /**
         * SC注册机认证用用户名
         */
        @JacksonXmlProperty(localName = "UserName")
        @JsonProperty("UserName")
        private String userName;
        
        /**
         * SC注册机认证用口令
         */
        @JacksonXmlProperty(localName = "Password")
        @JsonProperty("Password")
        private String password;
        
        /**
         * SU编码 (CTCC规范字段名)
         */
        @JacksonXmlProperty(localName = "SUID")
        @JsonProperty("SUID")
        private String suid;
        
        /**
         * SU的内网IP地址
         */
        @JacksonXmlProperty(localName = "SUIP")
        @JsonProperty("SUIP")
        private String suip;
        
        /**
         * SU的端口
         */
        @JacksonXmlProperty(localName = "SUPort")
        @JsonProperty("SUPort")
        private String suPort;
        
        /**
         * SU的制造商名称
         */
        @JacksonXmlProperty(localName = "SUVendor")
        @JsonProperty("SUVendor")
        private String suVendor;
        
        /**
         * SU的型号
         */
        @JacksonXmlProperty(localName = "SUModel")
        @JsonProperty("SUModel")
        private String suModel;
        
        /**
         * SU软件版本
         */
        @JacksonXmlProperty(localName = "SUSoftVer")
        @JsonProperty("SUSoftVer")
        private String suSoftVer;
        
        /**
         * SU硬件版本
         */
        @JacksonXmlProperty(localName = "SUHardVer")
        @JsonProperty("SUHardVer")
        private String suHardVer;
    }
}


