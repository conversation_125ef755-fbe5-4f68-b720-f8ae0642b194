package com.siteweb.tcs.south.ctcc.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.south.ctcc.dal.entity.CTCCPendingFsu;
import org.apache.ibatis.annotations.Mapper;

/**
 * CTCC 待处理FSU信息 Mapper接口
 * 基于MyBatis-Plus，提供基础CRUD操作
 * 
 * <AUTHOR> (2025-08-13)
 */
@Mapper
public interface CTCCPendingFSUMapper extends BaseMapper<CTCCPendingFsu> {

    // MyBatis-Plus提供的基础方法已足够使用
    // 主键为suid字段，对应CTCC协议的SUID
    
    // 示例自定义方法（如果需要）：
    // @Select("SELECT * FROM ctcc_pending_fsus WHERE ip_address = #{ipAddress}")
    // List<CTCCPendingFsu> selectByIpAddress(@Param("ipAddress") String ipAddress);
}