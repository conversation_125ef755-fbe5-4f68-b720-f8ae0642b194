package com.siteweb.tcs.south.ctcc.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.south.ctcc.dal.entity.CTCCOperationLog;
import org.apache.ibatis.annotations.Mapper;

/**
 * CTCC 操作日志 Mapper接口
 * 基于MyBatis-Plus，提供基础CRUD操作
 * 
 * <AUTHOR> (2025-08-13)
 */
@Mapper
public interface CTCCOperationLogMapper extends BaseMapper<CTCCOperationLog> {

    // MyBatis-Plus提供的基础方法已足够使用
    // 用于记录FSU相关的操作日志
    
    // 示例自定义方法（如果需要）：
    // @Select("SELECT * FROM ctcc_operation_logs WHERE object_id = #{objectId} ORDER BY op_time DESC LIMIT #{limit}")
    // List<CTCCOperationLog> selectRecentLogsByObjectId(@Param("objectId") String objectId, @Param("limit") int limit);
}