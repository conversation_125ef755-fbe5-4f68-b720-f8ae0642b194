package com.siteweb.tcs.south.ctcc.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.siteweb.tcs.south.ctcc.connector.letter.CtccRawMessage;
import lombok.extern.slf4j.Slf4j;

/**
 * CTCC消息序列化工具
 * 负责XML与Java对象之间的转换
 * 
 * <AUTHOR> (2025-05-09)
 */
@Slf4j
public class CtccSerializer {
    
    private static final XmlMapper xmlMapper = new XmlMapper();
    
    /**
     * 解析请求消息
     * 
     * @param rawMessage 原始消息
     * @param clazz 目标类型
     * @return 解析后的对象
     */
    public static <T> T parseReqMessage(CtccRawMessage rawMessage, Class<T> clazz) {
        try {
            // 从SOAP消息中提取业务载荷
            String payload = SoapHelper.getRequestPayload(rawMessage.getContent());
            if (payload.isEmpty()) {
                payload = rawMessage.getContent();
            }
            
            log.debug("Parsing CTCC request message to {}: {}", clazz.getSimpleName(), payload);
            return xmlMapper.readValue(payload, clazz);
            
        } catch (JsonProcessingException e) {
            log.error("Failed to parse CTCC request message to " + clazz.getSimpleName(), e);
            throw new RuntimeException("Message parsing failed", e);
        }
    }
    
    /**
     * 序列化响应消息
     * 
     * @param responseObject 响应对象
     * @return XML字符串
     */
    public static String serializeResponse(Object responseObject) {
        try {
            String xml = xmlMapper.writeValueAsString(responseObject);
            log.debug("Serialized CTCC response message: {}", xml);
            return xml;
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize CTCC response message", e);
            throw new RuntimeException("Message serialization failed", e);
        }
    }
    
    /**
     * 解析XML字符串为指定类型
     * 
     * @param xmlContent XML内容
     * @param clazz 目标类型
     * @return 解析后的对象
     */
    public static <T> T parseXml(String xmlContent, Class<T> clazz) {
        try {
            return xmlMapper.readValue(xmlContent, clazz);
        } catch (JsonProcessingException e) {
            log.error("Failed to parse XML to " + clazz.getSimpleName() + ": " + xmlContent, e);
            throw new RuntimeException("XML parsing failed", e);
        }
    }
}