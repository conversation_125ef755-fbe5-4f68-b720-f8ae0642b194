package com.siteweb.tcs.south.ctcc.dal.provider;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.tcs.common.runtime.PluginI18nMessageSource;
import com.siteweb.tcs.hub.util.CurrentUserUtil;
import com.siteweb.tcs.south.ctcc.dal.entity.CTCCOperationLog;
import com.siteweb.tcs.south.ctcc.dal.enums.OperationObject;
import com.siteweb.tcs.south.ctcc.dal.enums.OperationType;
import com.siteweb.tcs.south.ctcc.dal.mapper.CTCCOperationLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * CTCC 操作日志提供者
 * 提供操作日志的记录和查询服务
 * 
 * <AUTHOR> (2025-08-13)
 */
@Slf4j
@Service
public class CtccOperationLogProvider {

    @Autowired
    private PluginI18nMessageSource messageSource;

    @Autowired
    private CTCCOperationLogMapper ctccOperationLogMapper;

    /**
     * 记录操作日志
     *
     * @param objectType      操作对象类型
     * @param objectId        对象ID (SUID等)
     * @param opType          操作类型
     * @param i18nDescription I18N说明
     * @param params          i18n format 参数
     */
    public void record(OperationObject objectType, String objectId, OperationType opType, 
                      String i18nDescription, Object... params) {
        try {
            CTCCOperationLog log = new CTCCOperationLog();
            log.setId(null);
            log.setTime(LocalDateTime.now());
            log.setObjectId(objectId);
            log.setOperationType(opType);
            log.setObjectType(objectType);
            
            // 获取当前用户信息
            var userName = CurrentUserUtil.getCurrentUserName();
            if (userName == null || userName.isEmpty()) {
                userName = "System";
            }
            log.setUserId(1L);
            log.setUserName(userName);
            
            // 处理国际化描述
            var description = messageSource.getMessage(i18nDescription, params);
            log.setDescription(description);
            
            ctccOperationLogMapper.insert(log);
        } catch (Exception ex) {
            log.error("CTCC operation log record failure :", ex);
        }
    }

    /**
     * 根据对象ID查询操作日志
     *
     * @param objectId 对象ID
     * @return 操作日志列表
     */
    public List<CTCCOperationLog> findByObjectId(String objectId) {
        QueryWrapper<CTCCOperationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("object_id", objectId);
        queryWrapper.orderByDesc("op_time");
        return ctccOperationLogMapper.selectList(queryWrapper);
    }

    /**
     * 根据对象类型查询操作日志
     *
     * @param objectType 对象类型
     * @return 操作日志列表
     */
    public List<CTCCOperationLog> findByObjectType(OperationObject objectType) {
        QueryWrapper<CTCCOperationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("object_type", objectType.name());
        queryWrapper.orderByDesc("op_time");
        return ctccOperationLogMapper.selectList(queryWrapper);
    }

    /**
     * 根据操作类型查询操作日志
     *
     * @param operationType 操作类型
     * @return 操作日志列表
     */
    public List<CTCCOperationLog> findByOperationType(OperationType operationType) {
        QueryWrapper<CTCCOperationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("op_type", operationType.name());
        queryWrapper.orderByDesc("op_time");
        return ctccOperationLogMapper.selectList(queryWrapper);
    }

    /**
     * 组合查询操作日志
     *
     * @param objectType    操作对象类型
     * @param objectId      对象ID
     * @param operationType 操作类型
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return 操作日志列表
     */
    public List<CTCCOperationLog> find(OperationObject objectType, String objectId, 
                                      OperationType operationType, 
                                      LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<CTCCOperationLog> queryWrapper = new QueryWrapper<>();
        
        if (operationType != null) {
            queryWrapper.eq("op_type", operationType.name());
        }
        if (objectType != null && objectId != null) {
            queryWrapper.eq("object_type", objectType.name());
            queryWrapper.eq("object_id", objectId);
        }
        if (startTime != null) {
            queryWrapper.ge("op_time", startTime);
        }
        if (endTime != null) {
            queryWrapper.le("op_time", endTime);
        }
        
        queryWrapper.orderByDesc("op_time");
        return ctccOperationLogMapper.selectList(queryWrapper);
    }

    /**
     * 获取最近的操作日志
     *
     * @param objectId 对象ID
     * @param limit    限制条数
     * @return 操作日志列表
     */
    public List<CTCCOperationLog> getRecentLogs(String objectId, int limit) {
        QueryWrapper<CTCCOperationLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("object_id", objectId);
        queryWrapper.orderByDesc("op_time");
        queryWrapper.last("LIMIT " + limit);
        return ctccOperationLogMapper.selectList(queryWrapper);
    }
}