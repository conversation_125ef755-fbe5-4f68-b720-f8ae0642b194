package com.siteweb.tcs.south.ctcc.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.siteweb.tcs.plugin.common.sharding.IGateway;
import com.siteweb.tcs.south.ctcc.dal.enums.ConfigSyncEnum;
import com.siteweb.tcs.south.ctcc.dal.enums.FTPType;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

/**
 * FSU信息实体
 *
 * <AUTHOR> (2025-05-16)
 */
@Data
@TableName("ctcc_fsus")
public class CTCCFsu implements IGateway {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * FSU ID号
     */
    @TableField("fsu_id")
    private String gatewayId;

    /**
     * Hub 注册的GatewayID
     */
    @TableField("fsu_hub_id")
    private long gatewayHubId;

    /**
     * Gateway 是否启用
     */
    @TableField("enable")
    private boolean enable;


    /**
     * 配置同步
     * 从下往上B2T
     * 从上往下T2B
     */
    @TableField(value = "config_sync")
    private ConfigSyncEnum configSync;

    /**
     * FSU ID号
     */
    @TableField("fsu_name")
    private String gatewayName;


    @TableField("fsu_port")
    private String fsuPort;

    /**
     * 图形ID
     */
    @TableField("graph_id")
    private Long graphId;

    /**
     * FSU的内网IP
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * FSU的MAC地址
     */
    private String mac;

    /**
     * FSU版本号
     */
    private String version;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 厂商
     */
    private String vendor;

    /**
     * 型号
     */
    private String model;

    /**
     * 区域ID
     */
    @TableField("region_id")
    private long regionId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 固件版本
     */
    @TableField("firmware_version")
    private String firmwareVersion;

    /**
     * 软件版本
     */
    @TableField("software_version")
    private String softwareVersion;

    /**
     * 首次登录时间
     */
    @TableField("first_login_date")
    private LocalDateTime firstLoginDate;

    /**
     * 最后登录时间
     */
    @TableField("last_login_date")
    private LocalDateTime lastLoginDate;

    /**
     * 最后OTA时间
     */
    @TableField("last_ota_date")
    private LocalDateTime lastOtaDate;

    /**
     * 最后同步工厂设置时间
     */
    @TableField("last_sync_factory_date")
    private LocalDateTime lastSyncFactoryDate;

    /**
     * 最后同步方案时间
     */
    @TableField("last_sync_scheme_date")
    private LocalDateTime lastSyncSchemeDate;

    /**
     * 最后SU就绪时间
     */
    @TableField("last_su_ready_date")
    private LocalDateTime lastSuReadyDate;

    /**
     * FTP类型: 0=普通FTP, 1=SFTP
     */
    @TableField("ftp_type")
    private FTPType ftpType;

    /**
     * FTP端口
     */
    @TableField("ftp_port")
    private String ftpPort;

    /**
     * FTP用户名
     */
    @TableField("ftp_user_name")
    private String ftpUserName;

    /**
     * FTP密码
     */
    @TableField("ftp_password")
    private String ftpPassword;

    /**
     * CTCC特有字段 - SU制造商
     */
    @TableField("su_vendor")
    private String suVendor;

    /**
     * CTCC特有字段 - SU型号
     */
    @TableField("su_model")
    private String suModel;

    /**
     * CTCC特有字段 - SU硬件版本
     */
    @TableField("su_hard_ver")
    private String suHardVer;

    /**
     * CTCC特有字段 - SU软件版本
     */
    @TableField("su_soft_ver")
    private String suSoftVer;

//    public ForeignGatewayConfigChange toForeignGatewayConfigChange() {
//        ForeignGatewayConfigChange foreignGatewayConfigChange = new ForeignGatewayConfigChange();
//        foreignGatewayConfigChange.setForeignGatewayID(gatewayId);
//        foreignGatewayConfigChange.setIp(ipAddress);
//        foreignGatewayConfigChange.setGatewayName(gatewayName);
//        foreignGatewayConfigChange.setModal(model);
//        foreignGatewayConfigChange.setPort(fsuPort);
//        foreignGatewayConfigChange.setPluginID("south-ctcc-plugin");
//        return foreignGatewayConfigChange;
//    }

    /**
     * 从待处理FSU创建FSU对象
     *
     * @param pendingFsu 待处理FSU
     * @return FSU对象
     */
    public static CTCCFsu fromPending(CTCCPendingFsu pendingFsu) {
        CTCCFsu fsu = new CTCCFsu();
        fsu.setGatewayId(pendingFsu.getSuid()); // CTCC使用SUID作为gatewayId
        fsu.setIpAddress(pendingFsu.getIpAddress());
        fsu.setUsername(pendingFsu.getUsername());
        fsu.setPassword(pendingFsu.getPassword());
        fsu.setSuVendor(pendingFsu.getSuVendor());
        fsu.setSuModel(pendingFsu.getSuModel());
        fsu.setSuHardVer(pendingFsu.getSuHardVer());
        fsu.setSuSoftVer(pendingFsu.getSuSoftVer());
        fsu.setFsuPort(pendingFsu.getSuPort());
        fsu.setCreateTime(LocalDateTime.now());
        return fsu;
    }
}
