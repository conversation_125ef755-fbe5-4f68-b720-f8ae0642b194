package com.siteweb.tcs.south.ctcc.config;

import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.middleware.common.service.HttpServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * CTCC服务配置
 * 提供处理器和服务的Bean配置
 * 中间件相关resource和service的Bean准备
 * 
 * <AUTHOR> (2025-05-09)
 */
@Slf4j
@Configuration
public class CtccServiceConfig {

    @Value("${plugin.id}")
    private String pluginId;
    @Value("${plugin.middleware.http-server.primary}")
    private String httpServerServiceId;

    @Autowired
    private ServiceRegistry serviceRegistry;

    /**
     * HTTP服务Bean
     */
    @Bean("ctcc-http-server-service")
    public HttpServerService httpServerService() {
        log.info("[CTCC] init http server service: {} for plugin {}", httpServerServiceId, pluginId);
        return (HttpServerService) serviceRegistry.get(httpServerServiceId, pluginId);
    }
}


