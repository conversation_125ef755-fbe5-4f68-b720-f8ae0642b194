package com.siteweb.tcs.south.ctcc.util;

/**
 * CTCC SOAP 封装与解析
 * 支持电信动环监控系统B接口技术规范的SOAP格式
 * 兼容SCService和SUService两种命名空间
 */
public class SoapHelper {

    // SC响应SOAP模板（用于回复SU的请求）
    private static final String SC_SOAP_RESPONSE_START = "<SOAP-ENV:Envelope\n" +
            "    xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\"\n" +
            "    xmlns:SOAP-ENC=\"http://schemas.xmlsoap.org/soap/encoding/\"\n" +
            "    xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"\n" +
            "    xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
            "    <SOAP-ENV:Body SOAP-ENV:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\n" +
            "        <invokeResponse>\n" +
            "            <invokeReturn>";

    private static final String SC_SOAP_RESPONSE_END = "</invokeReturn>\n" +
            "        </invokeResponse>\n" +
            "    </SOAP-ENV:Body>\n" +
            "</SOAP-ENV:Envelope>";

    // SU请求SOAP模板（用于向SU发送请求）
    private static final String SU_SOAP_REQUEST_START = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
            "<SOAP-ENV:Envelope\n" +
            "    xmlns:SOAP-ENV=\"http://schemas.xmlsoap.org/soap/envelope/\"\n" +
            "    xmlns:SOAP-ENC=\"http://schemas.xmlsoap.org/soap/encoding/\"\n" +
            "    xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n" +
            "    xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n" +
            "    <SOAP-ENV:Body SOAP-ENV:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\n" +
            "        <invoke>\n" +
            "            <xmlData>";

    private static final String SU_SOAP_REQUEST_END = "</xmlData>\n" +
            "        </invoke>\n" +
            "    </SOAP-ENV:Body>\n" +
            "</SOAP-ENV:Envelope>";

    /**
     * 提取 SOAP 请求中的业务载荷（尽量兼容不同前缀）
     */
    public static String getRequestPayload(String soapMessage) {
        if (soapMessage == null) return "";
        int startIndex = indexOfIgnoreNs(soapMessage, "xmlData>");
        if (startIndex == -1) return soapMessage;
        int endIndex = indexOfIgnoreNs(soapMessage, "/xmlData>");
        if (endIndex == -1 || endIndex <= startIndex) return soapMessage;
        int lt = soapMessage.indexOf('<', startIndex + 1);
        if (lt == -1 || lt >= endIndex) return soapMessage;
        return soapMessage.substring(lt, endIndex);
    }

    /**
     * 提取 SOAP 响应中的业务载荷（以 invokeReturn 为准，兼容前缀）
     */
    public static String getResponsePayload(String soapMessage) {
        if (soapMessage == null) return "";
        int startIndex = indexOfIgnoreNs(soapMessage, "invokeReturn>");
        if (startIndex == -1) return soapMessage;
        int endIndex = indexOfIgnoreNs(soapMessage, "/invokeReturn>");
        if (endIndex == -1 || endIndex <= startIndex) return soapMessage;
        return soapMessage.substring(startIndex + "invokeReturn>".length() + 1, endIndex);
    }

    /**
     * 将业务载荷包裹为 SU 请求 SOAP（发往 SU）
     */
    public static String packetToSuRequestSoap(String payload) {
        return SU_SOAP_REQUEST_START + (payload == null ? "" : payload) + SU_SOAP_REQUEST_END;
    }

    /**
     * 将业务载荷包裹为 SC 响应 SOAP（返回给 SU/FSU）
     */
    public static String packetToSCResponseSoap(String payload) {
        return SC_SOAP_RESPONSE_START + (payload == null ? "" : payload) + SC_SOAP_RESPONSE_END;
    }

    /**
     * 解析XML中的FSU ID（CTCC使用SUID字段）
     */
    public static String parseXmlFSUId(String xmlPayload) {
        return extractXmlValue(xmlPayload, "SUID");
    }

    /**
     * 解析XML中的报文类型名称
     */
    public static String parseXmlPkType(String xmlPayload) {
        return extractXmlValue(xmlPayload, "Name");
    }

    /**
     * 验证SOAP消息格式（检查是否为有效的CTCC SOAP消息）
     */
    public static boolean isValidCtccSoap(String soapMessage) {
        if (soapMessage == null || soapMessage.trim().isEmpty()) {
            return false;
        }
        
        // 检查基本的SOAP结构
        return soapMessage.contains("SOAP-ENV:Envelope") && 
               soapMessage.contains("SOAP-ENV:Body") &&
               (soapMessage.contains("xmlData") || soapMessage.contains("invokeReturn"));
    }
    
    /**
     * 获取SOAP消息的服务类型（SCService或SUService）
     */
    public static String getServiceType(String soapMessage) {
        if (soapMessage == null) return "unknown";
        
        if (soapMessage.contains("SCService")) {
            return "SCService";
        } else if (soapMessage.contains("SUService")) {
            return "SUService";
        } else {
            return "unknown";
        }
    }
    
    /**
     * 提取XML中指定标签的值（增强版，支持属性和命名空间）
     */
    private static String extractXmlValue(String xml, String tagName) {
        if (xml == null || tagName == null) return null;
        
        // 尝试简单标签匹配
        String openTag = "<" + tagName + ">";
        String closeTag = "</" + tagName + ">";
        
        int startIndex = xml.indexOf(openTag);
        if (startIndex != -1) {
            int valueStart = startIndex + openTag.length();
            int endIndex = xml.indexOf(closeTag, valueStart);
            if (endIndex != -1) {
                return xml.substring(valueStart, endIndex).trim();
            }
        }
        
        // 尝试带属性的标签匹配
        String openTagWithAttrs = "<" + tagName + " ";
        startIndex = xml.indexOf(openTagWithAttrs);
        if (startIndex != -1) {
            int tagEnd = xml.indexOf(">", startIndex);
            if (tagEnd != -1) {
                int valueStart = tagEnd + 1;
                int endIndex = xml.indexOf(closeTag, valueStart);
                if (endIndex != -1) {
                    return xml.substring(valueStart, endIndex).trim();
                }
            }
        }
        
        return null;
    }

    private static int indexOfIgnoreNs(String text, String localWithGt) {
        // 尝试无前缀
        int idx = text.indexOf("<" + localWithGt);
        if (idx != -1) return idx;
        // 尝试带前缀
        for (int i = 0; i < text.length(); i++) {
            if (text.charAt(i) == '<') {
                int colon = text.indexOf(':', i + 1);
                if (colon != -1) {
                    int end = text.indexOf(localWithGt, colon + 1);
                    if (end != -1 && end - colon == 1) return i;
                }
            }
        }
        return -1;
    }
}


