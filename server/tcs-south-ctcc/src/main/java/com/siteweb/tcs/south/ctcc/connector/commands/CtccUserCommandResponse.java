package com.siteweb.tcs.south.ctcc.connector.commands;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * CTCC用户命令响应类
 * <p>
 * 封装用户命令的执行结果
 * 参考CMCC的命令响应实现
 * 
 * <AUTHOR> for CTCC User Command Response
 */
@Data
public class CtccUserCommandResponse {
    
    /**
     * 是否执行成功
     */
    private boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据（可为任意类型）
     */
    private Object data;
    
    /**
     * 执行时间
     */
    private LocalDateTime executionTime;
    
    /**
     * 错误代码（可选）
     */
    private String errorCode;
    
    /**
     * SU编码
     */
    private String suid;
    
    public CtccUserCommandResponse() {
        this.executionTime = LocalDateTime.now();
    }
    
    /**
     * 创建成功响应
     * 
     * @param suid SU编码
     * @param message 响应消息
     * @return 响应实例
     */
    public static CtccUserCommandResponse success(String suid, String message) {
        CtccUserCommandResponse response = new CtccUserCommandResponse();
        response.setSuid(suid);
        response.setSuccess(true);
        response.setMessage(message);
        return response;
    }
    
    /**
     * 创建成功响应（带数据）
     * 
     * @param suid SU编码
     * @param message 响应消息
     * @param data 响应数据
     * @return 响应实例
     */
    public static CtccUserCommandResponse success(String suid, String message, Object data) {
        CtccUserCommandResponse response = new CtccUserCommandResponse();
        response.setSuid(suid);
        response.setSuccess(true);
        response.setMessage(message);
        response.setData(data);
        return response;
    }
    
    /**
     * 创建失败响应
     * 
     * @param suid SU编码
     * @param message 错误消息
     * @return 响应实例
     */
    public static CtccUserCommandResponse failure(String suid, String message) {
        CtccUserCommandResponse response = new CtccUserCommandResponse();
        response.setSuid(suid);
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }
    
    /**
     * 创建失败响应（带错误代码）
     * 
     * @param suid SU编码
     * @param message 错误消息
     * @param errorCode 错误代码
     * @return 响应实例
     */
    public static CtccUserCommandResponse failure(String suid, String message, String errorCode) {
        CtccUserCommandResponse response = new CtccUserCommandResponse();
        response.setSuid(suid);
        response.setSuccess(false);
        response.setMessage(message);
        response.setErrorCode(errorCode);
        return response;
    }
}