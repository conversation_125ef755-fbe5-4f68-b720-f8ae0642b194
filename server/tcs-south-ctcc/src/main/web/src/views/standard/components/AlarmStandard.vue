<template>
  <div class="alarm-standard flex flex-col h-full">
    <!-- 工具栏 -->
    <div class="toolbar-container bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-4 flex-shrink-0">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <el-button type="primary" size="default" @click="openModal()">
            <el-icon size="16" class="mr-2"><Plus /></el-icon>
            添加告警标准化
          </el-button>
          <el-button 
            type="default" 
            size="default" 
            @click="refreshData"
            :loading="loading"
          >
            <el-icon size="16" class="mr-2"><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="flex items-center space-x-4">
          <!-- 搜索框 -->
          <el-input
            v-model="searchKeyword"
            placeholder="搜索告警标准ID、设备类型、告警名称..."
            style="width: 300px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <span class="text-sm text-gray-600 dark:text-gray-400">
            共 {{ filteredData.length }} 条记录
          </span>
        </div>
      </div>
    </div>
    
    <!-- 表格容器 -->
    <div class="table-container bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 flex-1 min-h-0 flex flex-col">
      <!-- 数据表格 -->
      <div class="flex-1 min-h-0">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              :data="filteredData"
              :columns="tableColumns"
              :width="width"
              :height="height"
              :row-height="36"
              :header-height="40"
              v-loading="loading"
              fixed
              :sort-by="sortBy"
              @column-sort="onSort"
              @resize="() => updateTableColumns(width)"
            />
          </template>
        </el-auto-resizer>
      </div>
    </div>
    
    <!-- 添加/编辑模态框 -->
    <AlarmStandardModal
      v-model:visible="modalVisible"
      :data="modalData"
      @confirm="handleModalConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, h } from 'vue';
import { ElMessage, ElMessageBox, ElButton, ElIcon } from 'element-plus';
import { Plus, Edit, Delete, Refresh, Search } from '@element-plus/icons-vue';
import type { AlarmStandardData } from '@/api/standard';
import { getAlarmStandardList, deleteAlarmStandard } from '@/api/standard';
import AlarmStandardModal from './AlarmStandardModal.vue';
import { debounce, PerformanceTimer, optimizeTableRendering } from '@/utils/performance';

// 表格数据
const tableData = ref<AlarmStandardData[]>([]);
const loading = ref(false);

// 搜索相关
const searchKeyword = ref('');

// 分页相关
const pagination = ref({
  current: 1,
  size: 20,
  total: 0
});

// 模态框相关
const modalVisible = ref(false);
const modalData = ref<AlarmStandardData | null>(null);

// 过滤后的数据
const filteredData = computed(() => {
  if (!searchKeyword.value) {
    return tableData.value;
  }
  
  const keyword = searchKeyword.value.toLowerCase();
  return tableData.value.filter(item => 
    item.alarmStandardId?.toLowerCase().includes(keyword) ||
    item.deviceTypeName?.toLowerCase().includes(keyword) ||
    item.alarmStandardName?.toLowerCase().includes(keyword) ||
    item.alarmLogicClassName?.toLowerCase().includes(keyword) ||
    item.meaning?.toLowerCase().includes(keyword)
  );
});

// 排序相关
const sortBy = ref({ key: 'alarmStandardId', order: 'asc' });

// 排序处理
const onSort = (sortData: any) => {
  sortBy.value = {
    key: sortData.key,
    order: sortData.order
  };
};

// 创建操作按钮渲染器
const createActionButtons = (row: AlarmStandardData) => {
  return h('div', { class: 'flex items-center justify-center space-x-2' }, [
    h(
      ElButton,
      {
        type: 'primary',
        size: 'default',
        text: true,
        onClick: () => openModal(row),
        title: '修改',
        style: { padding: '6px 8px' }
      },
      {
        default: () => h(ElIcon, { size: 16 }, { default: () => h(Edit) })
      }
    ),
    h(
      ElButton,
      {
        type: 'danger',
        size: 'default', 
        text: true,
        onClick: () => deleteItem(row),
        title: '删除',
        style: { padding: '6px 8px' }
      },
      {
        default: () => h(ElIcon, { size: 16 }, { default: () => h(Delete) })
      }
    )
  ]);
};

// 创建文本单元格渲染器
const createTextCell = (text: string) => {
  return h(
    'div',
    {
      class: 'text-cell',
      style: {
        padding: '8px 12px',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      },
      title: text
    },
    text || '-'
  );
};

// 基础列配置
const baseColumns = [
  {
    key: 'alarmStandardId',
    title: '告警标准ID',
    dataKey: 'alarmStandardId',
    width: 150,
    minWidth: 120,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.alarmStandardId)
  },
  {
    key: 'deviceTypeName',
    title: '设备类型',
    dataKey: 'deviceTypeName',
    width: 120,
    minWidth: 100,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.deviceTypeName)
  },
  {
    key: 'alarmLogicClass',
    title: '告警逻辑类别',
    dataKey: 'alarmLogicClass',
    width: 120,
    minWidth: 100,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.alarmLogicClass)
  },
  {
    key: 'alarmLogicClassName',
    title: '告警逻辑类别名称',
    dataKey: 'alarmLogicClassName',
    width: 150,
    minWidth: 120,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.alarmLogicClassName)
  },
  {
    key: 'alarmLogicSubclass',
    title: '告警逻辑子类别',
    dataKey: 'alarmLogicSubclass',
    width: 150,
    minWidth: 120,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.alarmLogicSubclass)
  },
  {
    key: 'alarmStandardName',
    title: '告警标准名称',
    dataKey: 'alarmStandardName',
    width: 180,
    minWidth: 150,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.alarmStandardName)
  },
  {
    key: 'meaning',
    title: '含义',
    dataKey: 'meaning',
    width: 150,
    minWidth: 120,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.meaning)
  },
  {
    key: 'communicationBuildingAlarmLevelName',
    title: '通信楼宇告警级别',
    dataKey: 'communicationBuildingAlarmLevelName',
    width: 180,
    minWidth: 150,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.communicationBuildingAlarmLevelName)
  },
  {
    key: 'transmissionNodeAlarmLevelName',
    title: '传输节点告警级别',
    dataKey: 'transmissionNodeAlarmLevelName',
    width: 180,
    minWidth: 150,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.transmissionNodeAlarmLevelName)
  },
  {
    key: 'communicationBaseStationAlarmLevelName',
    title: '通信基站告警级别',
    dataKey: 'communicationBaseStationAlarmLevelName',
    width: 180,
    minWidth: 150,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.communicationBaseStationAlarmLevelName)
  },
  {
    key: 'idcAlarmLevelName',
    title: 'IDC告警级别',
    dataKey: 'idcAlarmLevelName',
    width: 120,
    minWidth: 100,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.idcAlarmLevelName)
  },
  {
    key: 'description',
    title: '描述',
    dataKey: 'description',
    width: 200,
    minWidth: 150,
    sortable: true,
    cellRenderer: ({ rowData }: any) => createTextCell(rowData.description)
  },
  {
    key: 'actions',
    title: '操作',
    dataKey: 'actions',
    width: 120,
    minWidth: 120,
    fixed: 'right',
    align: 'center',
    cellRenderer: ({ rowData }: any) => createActionButtons(rowData)
  }
];

// 动态计算列宽
const calculateDynamicColumns = (containerWidth: number) => {
  const totalMinWidth = baseColumns.reduce((sum, col) => sum + (col.minWidth || 100), 0);
  
  // 如果容器宽度足够，使用原始宽度
  if (containerWidth >= totalMinWidth * 1.1) {
    return baseColumns;
  }
  
  // 计算缩放比例
  const scale = Math.max(0.7, (containerWidth - 50) / totalMinWidth);
  
  return baseColumns.map(col => ({
    ...col,
    width: Math.max(col.minWidth || 100, Math.floor((col.width || 100) * scale))
  }));
};

// 虚拟表格列配置 - 支持响应式
const tableColumns = ref(baseColumns);

// 监听容器尺寸变化，动态调整列宽
const updateTableColumns = (width: number) => {
  tableColumns.value = calculateDynamicColumns(width);
};


// 性能计时器
const timer = new PerformanceTimer();

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true;
  timer.start('告警标准化数据加载');
  
  try {
    const response = await getAlarmStandardList();
    if (response.state === true && response.data) {
      tableData.value = response.data;
      timer.end('告警标准化数据加载');
    } else {
      ElMessage.error('获取数据失败');
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const refreshData = async () => {
  await fetchTableData();
  ElMessage.success('数据已刷新');
};

// 防抖搜索
const handleSearch = debounce(() => {
  // 搜索时无需处理分页
}, 300);

// 打开模态框
const openModal = (item?: AlarmStandardData) => {
  modalData.value = item ? JSON.parse(JSON.stringify(item)) : null;
  modalVisible.value = true;
};

// 删除项目
const deleteItem = async (item: AlarmStandardData) => {
  try {
    await ElMessageBox.confirm(
      `请确认是否删除告警标准化：${item.alarmStandardName}?`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }
    );
    
    if (item.id) {
      const response = await deleteAlarmStandard(item.id);
      if (response.state === true) {
        ElMessage.success('删除成功');
        fetchTableData();
      } else {
        ElMessage.error('删除失败');
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 模态框确认处理
const handleModalConfirm = async () => {
  const text = modalData.value?.id ? '修改' : '新增';
  ElMessage.success(`${text}成功`);
  await fetchTableData();
};

// 组件挂载后获取数据
onMounted(() => {
  // 启用表格渲染优化
  optimizeTableRendering();
  fetchTableData();
});
</script>

<style scoped>
.alarm-standard {
  height: 100%;
  width: 100%;
}

.table-container {
  border-radius: 8px;
}

.toolbar-container {
  border-radius: 8px;
}

/* 虚拟表格样式优化 */
:deep(.el-table-v2) {
  border-radius: 8px;
  overflow: hidden;
  will-change: scroll-position;
  contain: layout style paint;
}

:deep(.el-table-v2__header) {
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-table-v2__header-cell) {
  background: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-right: 1px solid #e5e7eb;
}

:deep(.el-table-v2__row) {
  border-bottom: 1px solid #f3f4f6;
  will-change: transform;
  contain: layout;
}

:deep(.el-table-v2__row:hover) {
  background-color: #f8fafc !important;
}

:deep(.el-table-v2__row-cell) {
  border-right: 1px solid #f3f4f6;
}

/* 暗色主题适配 */
:deep(.dark .el-table-v2__header) {
  background: #374151;
  border-bottom-color: #4b5563;
}

:deep(.dark .el-table-v2__header-cell) {
  background: #374151 !important;
  color: #f3f4f6;
  border-right-color: #4b5563;
}

:deep(.dark .el-table-v2__row) {
  border-bottom-color: #374151;
}

:deep(.dark .el-table-v2__row:hover) {
  background-color: #374151 !important;
}

:deep(.dark .el-table-v2__row-cell) {
  border-right-color: #374151;
}

/* 文本单元格样式 */
.text-cell {
  font-size: 14px;
  line-height: 1.5;
  color: var(--el-text-color-primary);
}

/* 表格加载状态优化 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(1px);
}


/* 搜索框样式优化 */
:deep(.el-input__wrapper) {
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}
</style> 