import { http } from "@/utils/http";

// 通用API响应格式
export type ApiResponse<T> = {
  state: boolean;
  timestamp: number;
  data: T;
  code: number;
  err_msg: string | null;
  err_code: string | null;
};

// 分页响应类型
export interface PageResponse<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
}

// === CMCC设备相关类型定义 ===

// CMCC设备类型
export interface CMCCDevice {
  id: number;
  fsuId: string;
  deviceId: string;
  deviceHubId: number;
  deviceName: string;
  siteName: string;
  roomName: string;
  siteId: number | null;
  roomId: number | null;
  deviceType: string;
  deviceSubType: string;
  model: string;
  brand: string;
  ratedCapacity: string | null;
  version: string;
  beginRunTime: string;
  devDescribe: string;
  description: string | null;
  signalList?: CMCCSignal[];
  alarmList?: CMCCAlarm[];
  controlList?: CMCCControl[];
}

// CMCC信号类型
export interface CMCCSignal {
  id: number;
  fsuId: string;
  deviceId: string;
  spId: string;
  originSpId: string;
  spHubId: number;
  signalName: string;
  spType: number;
  signalNumber: string;
  meanings: Record<string, any>;
  visible: boolean | null;
  unit: string | null;
  // 标准化相关字段
  standardized?: boolean;
  standardName?: string;
  standardId?: string;
}

// CMCC告警类型
export interface CMCCAlarm {
  id: number;
  fsuId: string;
  deviceId: string;
  spId: string;
  originSpId: string;
  spHubId: number;
  spType: number;
  alarmLevel: number;
  alarmName: string;
  signalNumber: string;
  nmAlarmId: string;
  threshold: number;
  unit: string | null;
  meaning: string | null;
  // 标准化相关字段
  standardized?: boolean;
  standardName?: string;
  standardId?: string;
}

// CMCC控制类型
export interface CMCCControl {
  id: number;
  fsuId: string;
  deviceId: string;
  spType: number;
  spId: string;
  originSpId: string;
  spHubId: number;
  controlName: string;
  meanings: Record<string, any>;
  signalNumber: string;
  maxValue: number | null;
  minValue: number | null;
  // 标准化相关字段
  standardized?: boolean;
  standardName?: string;
  standardId?: string;
}

// API路径前缀
const prefix = "/api/thing/south-cmcc-plugin";

// === 设备查询接口 ===

/**
 * 查询设备列表
 * @param fsuId FSU ID（可选）
 */
export function getCMCCDeviceList(fsuId?: string): Promise<ApiResponse<CMCCDevice[]>> {
  const params = fsuId ? { fsuId } : {};
  return http.get(`${prefix}/cmcc-device/list`, { params }) as Promise<ApiResponse<CMCCDevice[]>>;
}

/**
 * 分页查询设备
 * @param current 页码（默认1）
 * @param size 页大小（默认10）
 * @param fsuId FSU ID（可选）
 */
export function getCMCCDevicePage(
  current = 1, 
  size = 10, 
  fsuId?: string
): Promise<ApiResponse<PageResponse<CMCCDevice>>> {
  const params: any = { current, size };
  if (fsuId) params.fsuId = fsuId;
  
  return http.get(`${prefix}/cmcc-device/page`, { params }) as Promise<ApiResponse<PageResponse<CMCCDevice>>>;
}

/**
 * 根据ID查询设备
 * @param id 设备ID
 */
export function getCMCCDeviceById(id: number): Promise<ApiResponse<CMCCDevice>> {
  return http.get(`${prefix}/cmcc-device/${id}`) as Promise<ApiResponse<CMCCDevice>>;
}

// === 信号查询接口 ===

/**
 * 查询信号列表
 * @param fsuId FSU ID（可选）
 * @param deviceId 设备ID（可选）
 */
export function getCMCCSignalList(fsuId?: string, deviceId?: string): Promise<ApiResponse<CMCCSignal[]>> {
  const params: any = {};
  if (fsuId) params.fsuId = fsuId;
  if (deviceId) params.deviceId = deviceId;
  
  return http.get(`${prefix}/cmcc-signal/standardization/list`, { params }) as Promise<ApiResponse<CMCCSignal[]>>;
}

/**
 * 分页查询信号
 * @param current 页码（默认1）
 * @param size 页大小（默认10）
 * @param fsuId FSU ID（可选）
 * @param deviceId 设备ID（可选）
 */
export function getCMCCSignalPage(
  current = 1, 
  size = 10, 
  fsuId?: string, 
  deviceId?: string
): Promise<ApiResponse<PageResponse<CMCCSignal>>> {
  const params: any = { current, size };
  if (fsuId) params.fsuId = fsuId;
  if (deviceId) params.deviceId = deviceId;
  
  return http.get(`${prefix}/cmcc-signal/page`, { params }) as Promise<ApiResponse<PageResponse<CMCCSignal>>>;
}

/**
 * 根据ID查询信号
 * @param id 信号ID
 */
export function getCMCCSignalById(id: number): Promise<ApiResponse<CMCCSignal>> {
  return http.get(`${prefix}/cmcc-signal/${id}`) as Promise<ApiResponse<CMCCSignal>>;
}

// === 告警查询接口 ===

/**
 * 查询告警列表
 * @param fsuId FSU ID（可选）
 * @param deviceId 设备ID（可选）
 */
export function getCMCCAlarmList(fsuId?: string, deviceId?: string): Promise<ApiResponse<CMCCAlarm[]>> {
  const params: any = {};
  if (fsuId) params.fsuId = fsuId;
  if (deviceId) params.deviceId = deviceId;
  
  return http.get(`${prefix}/cmcc-alarm/standardization/list`, { params }) as Promise<ApiResponse<CMCCAlarm[]>>;
}

/**
 * 分页查询告警
 * @param current 页码（默认1）
 * @param size 页大小（默认10）
 * @param fsuId FSU ID（可选）
 * @param deviceId 设备ID（可选）
 */
export function getCMCCAlarmPage(
  current = 1, 
  size = 10, 
  fsuId?: string, 
  deviceId?: string
): Promise<ApiResponse<PageResponse<CMCCAlarm>>> {
  const params: any = { current, size };
  if (fsuId) params.fsuId = fsuId;
  if (deviceId) params.deviceId = deviceId;
  
  return http.get(`${prefix}/cmcc-alarm/page`, { params }) as Promise<ApiResponse<PageResponse<CMCCAlarm>>>;
}

/**
 * 根据ID查询告警
 * @param id 告警ID
 */
export function getCMCCAlarmById(id: number): Promise<ApiResponse<CMCCAlarm>> {
  return http.get(`${prefix}/cmcc-alarm/${id}`) as Promise<ApiResponse<CMCCAlarm>>;
}

// === 标准化相关类型定义 ===

// 标准化使用率统计
export interface StandardizationUsageStats {
  totalStandardCount: number;
  implementedStandardCount: number;
  standardImplementationRate: number;
  totalActualCount: number;
  standardizedActualCount: number;
  actualStandardizationRate: number;
  implementedStandardItems: StandardizedItem[];
  unimplementedStandardItems: UnimplementedStandardItem[];
}

// 已实现的标准项目
export interface StandardizedItem {
  standardId: string;
  standardName: string;
  standardType: string;
  actualItemCount: number;
  actualItems: ActualItem[];
}

// 实际项目
export interface ActualItem {
  actualId: string;
  actualName: string;
  originSpId: string;
}

// 未实现的标准项目
export interface UnimplementedStandardItem {
  standardId: string;
  standardName: string;
  standardType: string;
}

// 设备标准化使用率详情
export interface DeviceStandardizationUsage {
  deviceId: string;
  deviceName: string;
  deviceType: string;
  deviceTypeName: string;
  alarmUsage: StandardizationUsageStats;
  signalUsage: StandardizationUsageStats;
  controlUsage: StandardizationUsageStats;
}

// === 控制查询接口 ===

/**
 * 查询控制列表
 * @param fsuId FSU ID（可选）
 * @param deviceId 设备ID（可选）
 */
export function getCMCCControlList(fsuId?: string, deviceId?: string): Promise<ApiResponse<CMCCControl[]>> {
  const params: any = {};
  if (fsuId) params.fsuId = fsuId;
  if (deviceId) params.deviceId = deviceId;
  
  return http.get(`${prefix}/cmcc-control/standardization/list`, { params }) as Promise<ApiResponse<CMCCControl[]>>;
}

/**
 * 分页查询控制
 * @param current 页码（默认1）
 * @param size 页大小（默认10）
 * @param fsuId FSU ID（可选）
 * @param deviceId 设备ID（可选）
 */
export function getCMCCControlPage(
  current = 1, 
  size = 10, 
  fsuId?: string, 
  deviceId?: string
): Promise<ApiResponse<PageResponse<CMCCControl>>> {
  const params: any = { current, size };
  if (fsuId) params.fsuId = fsuId;
  if (deviceId) params.deviceId = deviceId;
  
  return http.get(`${prefix}/cmcc-control/page`, { params }) as Promise<ApiResponse<PageResponse<CMCCControl>>>;
}

/**
 * 根据ID查询控制
 * @param id 控制ID
 */
export function getCMCCControlById(id: number): Promise<ApiResponse<CMCCControl>> {
  return http.get(`${prefix}/cmcc-control/${id}`) as Promise<ApiResponse<CMCCControl>>;
}

// === 标准化查询接口 ===

/**
 * 获取设备标准化使用率详情
 * @param deviceId 设备ID
 */
export function getDeviceStandardizationUsage(deviceId: string): Promise<ApiResponse<DeviceStandardizationUsage>> {
  return http.get(`${prefix}/cmcc-device/standardization/usage`, { 
    params: { deviceId } 
  }) as Promise<ApiResponse<DeviceStandardizationUsage>>;
}

// === 工具函数 ===

/**
 * 获取告警级别对应的颜色
 * @param level 告警级别
 */
export function getAlarmLevelColor(level: number): string {
  switch(level) {
    case 1: return 'text-red-600 bg-red-50';
    case 2: return 'text-orange-600 bg-orange-50';
    case 3: return 'text-yellow-600 bg-yellow-50';
    default: return 'text-gray-600 bg-gray-50';
  }
}

/**
 * 获取告警级别对应的文本
 * @param level 告警级别
 */
export function getAlarmLevelText(level: number): string {
  switch(level) {
    case 1: return '严重';
    case 2: return '重要';
    case 3: return '次要';
    default: return '一般';
  }
}

/**
 * 获取告警级别对应的Element Plus标签类型
 * @param level 告警级别
 */
export function getAlarmLevelTagType(level: number): string {
  switch(level) {
    case 1: return 'danger';
    case 2: return 'warning';
    case 3: return 'success';
    default: return 'info';
  }
}