import { http } from "@/utils/http";
import moment from "moment";

// 接口基础路径 (CTCC协议)
const BASE_URL = "/api/thing/south-ctcc-plugin/ctcc/2023/fsu/command";

// TypeScript 接口定义
export interface TracerStateResponse {
  state: boolean;
  timestamp: number;
  data: boolean;
  code: number;
  err_msg: string | null;
  err_code: string | null;
}

export interface ProtocolMessageItem {
  msgId: string;
  timestamp: string;
  message: string;
  response?: {
    timestamp: string;
    message: string;
    exception?: string;
    completed?: boolean;
  };
  completed?: boolean;
  exception?: string;
}

export interface ProtocolMessagesResponse {
  state: boolean;
  timestamp: number;
  data: {
    stream: number;
    [protocolType: string]: ProtocolMessageItem[] | number;
  };
  code: number;
  err_msg: string | null;
  err_code: string | null;
}

export interface ProtocolType {
  code: string;
  desc: string;
  count?: number;
}

export interface ProcessedMessageRecord {
  msgId: string;
  insertTime: string;
  cmdCode: string;
  cmdName: string;
  requestPacketInfo: string;
  responsePacketInfo?: string;
  completed?: boolean;
  exception?: string;
  timestamp: string;
  responseTimestamp?: string;
}

/**
 * 获取报文记录开关状态
 * @param suid SU编码
 */
export const getTracerState = (suid: string): Promise<TracerStateResponse> => {
  return http.request<TracerStateResponse>("get", `${BASE_URL}/tracer-state/${suid}`);
};

/**
 * 设置报文记录开关状态
 * @param suid SU编码
 * @param enable 是否启用
 */
export const setTracerState = (suid: string, enable: boolean): Promise<TracerStateResponse> => {
  return http.request<TracerStateResponse>(
    "put", 
    `${BASE_URL}/tracer-state/${suid}?enable=${enable}`
  );
};

/**
 * 获取协议报文信息
 * @param suid SU编码
 */
export const getTracerMessages = (suid: string): Promise<ProtocolMessagesResponse> => {
  return http.request<ProtocolMessagesResponse>("get", `${BASE_URL}/tracer-message/${suid}`);
};

/**
 * 将API返回的协议数据转换为协议类型列表
 * @param messagesData API返回的协议消息数据
 */
export const transformToProtocolTypes = (messagesData: ProtocolMessagesResponse['data']): ProtocolType[] => {
  return Object.keys(messagesData)
    .filter(key => key !== 'stream') // 过滤掉stream字段
    .map(protocolCode => {
      const protocolData = messagesData[protocolCode];
      return {
        code: protocolCode,
        desc: getCtccProtocolDescription(protocolCode), // 使用CTCC协议描述
        count: Array.isArray(protocolData) ? protocolData.length : 0
      };
    });
};

/**
 * 将协议消息转换为表格记录格式
 * @param protocolType 协议类型
 * @param messages 协议消息数组
 */
export const transformToTableRecords = (
  protocolType: string, 
  messages: ProtocolMessageItem[]
): ProcessedMessageRecord[] => {
  const records: ProcessedMessageRecord[] = [];
  
  messages.forEach(msg => {
    // 创建合并的请求-响应记录
    const record: ProcessedMessageRecord = {
      msgId: msg.msgId,
      insertTime: formatTimestamp(msg.timestamp),
      cmdCode: protocolType,
      cmdName: getCtccProtocolName(protocolType),
      requestPacketInfo: msg.message,
      responsePacketInfo: msg.response?.message,
      completed: msg.completed ?? (!!msg.response && (msg.response.completed ?? true)),
      exception: msg.exception || msg.response?.exception,
      timestamp: msg.timestamp,
      responseTimestamp: msg.response?.timestamp
    };
    records.push(record);
  });

  // 按时间倒序排列
  return records.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
};

/**
 * 获取CTCC协议名称映射
 * @param protocolCode 协议代码
 */
export const getCtccProtocolName = (protocolCode: string): string => {
  const protocolNames: { [key: string]: string } = {
    'LOGIN': '登录协议',
    'SU_READY': 'SU就绪',
    'TIME_SYNC': '时间同步',
    'GET_INFO': '获取信息',
    'REBOOT': '重启命令',
    'SET_FTP': '设置FTP',
    'GET_FTP': '获取FTP',
    'DATA_COLLECT': '数据采集',
    'ACTIVE_ALARM': '活动告警',
    'SET_NETWORK': '网络设置'
  };
  
  return protocolNames[protocolCode] || protocolCode;
};

/**
 * 获取CTCC协议描述
 * @param protocolCode 协议代码
 */
export const getCtccProtocolDescription = (protocolCode: string): string => {
  const protocolDescriptions: { [key: string]: string } = {
    'LOGIN': 'SU登录协议',
    'SU_READY': 'SU就绪协议',
    'TIME_SYNC': '时间同步协议',
    'GET_INFO': '获取SU信息协议',
    'REBOOT': 'SU重启协议',
    'SET_FTP': '设置FTP参数协议',
    'GET_FTP': '获取FTP参数协议',
    'DATA_COLLECT': '数据采集协议',
    'ACTIVE_ALARM': '活动告警协议',
    'SET_NETWORK': '网络参数设置协议'
  };
  
  return protocolDescriptions[protocolCode] || protocolCode;
};

/**
 * 格式化时间戳
 * @param timestamp 时间戳字符串
 */
export const formatTimestamp = (timestamp: string): string => {
  try {
    return moment(timestamp).format('YYYY-MM-DD HH:mm:ss');
  } catch {
    return timestamp;
  }
};

/**
 * 简单的XML格式化
 * @param xml XML字符串
 */
export const formatXmlString = (xml: string): string => {
  try {
    if (!xml || typeof xml !== 'string') {
      return xml || '';
    }

    // 清理XML字符串，移除多余的空白字符
    let cleanXml = xml.trim();
    
    // 检查是否包含XML声明，如果有多个声明则只保留第一个
    const xmlDeclarationRegex = /<\?xml[^?]*\?>/g;
    const declarations = cleanXml.match(xmlDeclarationRegex);
    if (declarations && declarations.length > 1) {
      // 移除所有XML声明
      cleanXml = cleanXml.replace(xmlDeclarationRegex, '');
      // 在开头添加第一个声明
      cleanXml = declarations[0] + '\n' + cleanXml.trim();
    }

    // 使用DOM解析器检查XML是否有效
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(cleanXml, 'text/xml');
    
    // 检查是否有解析错误
    const parserError = xmlDoc.querySelector('parsererror');
    if (parserError) {
      console.warn('XML解析出现错误，使用原始格式显示:', parserError.textContent);
      // 如果解析失败，使用简单的文本格式化
      return formatXmlAsText(cleanXml);
    }

    // 如果解析成功，使用序列化器格式化
    const serializer = new XMLSerializer();
    let formatted = serializer.serializeToString(xmlDoc);
    
    // 简单的缩进处理
    return formatXmlAsText(formatted);
    
  } catch (error) {
    console.warn('XML格式化失败，使用原始格式:', error);
    return formatXmlAsText(xml);
  }
};

/**
 * 纯文本方式格式化XML
 * @param xml XML字符串
 */
const formatXmlAsText = (xml: string): string => {
  if (!xml) return '';
  
  try {
    // 在标签之间添加换行
    let formatted = xml.replace(/></g, '>\n<');
    
    // 分行处理
    const lines = formatted.split('\n');
    let indentLevel = 0;
    
    const indentedLines = lines.map(line => {
      const trimmed = line.trim();
      if (!trimmed) return '';
      
      // 如果是结束标签，先减少缩进再处理
      if (trimmed.startsWith('</')) {
        indentLevel = Math.max(0, indentLevel - 1);
      }
      
      const indented = '  '.repeat(indentLevel) + trimmed;
      
      // 如果是开始标签且不是自闭合标签，增加缩进
      if (trimmed.startsWith('<') && 
          !trimmed.startsWith('</') && 
          !trimmed.startsWith('<?') && 
          !trimmed.endsWith('/>') &&
          !trimmed.includes('</')) {
        indentLevel++;
      }
      
      return indented;
    });
    
    return indentedLines.filter(line => line.trim()).join('\n');
  } catch (error) {
    return xml;
  }
};