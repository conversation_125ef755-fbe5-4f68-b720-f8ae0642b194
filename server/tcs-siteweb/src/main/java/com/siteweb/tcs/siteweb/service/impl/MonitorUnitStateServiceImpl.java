package com.siteweb.tcs.siteweb.service.impl;

import com.siteweb.tcs.siteweb.service.IMonitorUnitStateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 监控单元状态服务实现类
 */
@Slf4j
@Service
public class MonitorUnitStateServiceImpl implements IMonitorUnitStateService {

    @Override
    public void updateMonitorUnit(Integer monitorUnitId) {
        if (monitorUnitId == null) {
            log.warn("Cannot update monitor unit state: monitor unit ID is null");
            return;
        }

        log.info("更新监控单元状态: monitorUnitId={}", monitorUnitId);
        // 在实际实现中，这里应该调用相关服务更新监控单元状态
        // 由于我们没有直接访问 hub 模块的能力，这里只记录日志
    }

    @Override
    public void updateSampler(Integer samplerId) {
        if (samplerId == null) {
            log.warn("Cannot update sampler state: sampler ID is null");
            return;
        }

        log.info("更新采集器状态: samplerId={}", samplerId);
        // 在实际实现中，这里应该调用相关服务更新采集器状态
        // 由于我们没有直接访问 hub 模块的能力，这里只记录日志
    }
}
