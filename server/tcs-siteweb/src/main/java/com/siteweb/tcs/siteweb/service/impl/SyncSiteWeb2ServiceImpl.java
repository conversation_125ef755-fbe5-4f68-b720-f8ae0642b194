package com.siteweb.tcs.siteweb.service.impl;

import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.ChangeOperatorEnum;
import com.siteweb.tcs.siteweb.enums.StructureTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.SyncSiteWeb2Mapper;
import com.siteweb.tcs.siteweb.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SiteWeb2同步服务实现类
 * 迁移自原SyncSiteWeb2ServiceImpl
 */
@Service
@Slf4j
public class SyncSiteWeb2ServiceImpl implements ISyncSiteWeb2Service {

    @Autowired
    private SyncSiteWeb2Mapper syncSiteWeb2Mapper;

    @Autowired
    private IResourceStructureService resourceStructureService;

    @Autowired
    private IStationService stationService;

    @Autowired
    private IStationStructureService stationStructureService;

    @Autowired
    private IStationStructureMapService stationStructureMapService;

    @Autowired
    private IConfigChangeMacroLogService configChangeMacroLogService;

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private IEquipmentService equipmentService;

    /**
     * 同步站点资源结构
     *
     * <p>同步 SiteWeb2 的站点资源结构
     *
     * <p>1. 如果资源结构表为空，调用 {@link #syncResourceStructureFromStationData()} 方法同步资源结构
     * <p>2. 如果站点表为空，直接返回
     * <p>3. 过滤掉站点表中 bordNumber 不是 0 的记录
     * <p>4. 通过 {@link IStationStructureService#findCommonParentByStationIds(List, StationStructure)} 方法
     * 找到所有站点的公共父节点
     * <p>5. 通过 {@link #createResourceStructureList(StationStructure)} 方法根据公共父节点生成资源结构列表
     * <p>6. 通过 {@link IResourceStructureService#batchInsert(List)} 方法批量插入资源结构
     * <p>7. 通过 {@link IResourceStructureService#findResourceStructures()} 方法查询所有的资源结构
     * <p>8. 遍历资源结构，更新设备的resourceStructureId
     * <p>9. 通过 {@link IConfigChangeMacroLogService#configChangeLog(String, Integer, ChangeOperatorEnum)} 方法
     * 记录配置变更日志
     */
    @Override
    @Transactional
    public void syncSiteWeb2Config() {
        log.info("开始同步SiteWeb2配置");
        
        ResourceStructure rootStructure = resourceStructureService.getRootStructure();
        if (rootStructure == null) {
            log.info("资源结构表为空，开始从站点数据同步资源结构");
            syncResourceStructureFromStationData();
            return;
        }

        List<Station> allStation = stationService.list();
        if (allStation == null || allStation.isEmpty()) {
            log.info("站点表为空，直接返回");
            return;
        }
        
        // 查询未分组局站下的stationids
        // 查询未分组分组
        StationStructure ungroupedStructure = stationStructureService.findUngroupedStructure(1);
        List<Integer> ungroupedStationIds = stationStructureMapService.findStationIdsByStructureId(ungroupedStructure.getStructureId());

        List<Station> filteredStations = allStation.stream()
                .filter(station -> station.getBordNumber() != null && station.getBordNumber() == 0)
                .filter(station -> !ungroupedStationIds.contains(station.getStationId()))
                .collect(Collectors.toList());

        if (filteredStations.isEmpty()) {
            log.info("过滤后的站点列表为空，直接返回");
            return;
        }

        StationStructure stationStructureTree = stationStructureMapService.tree();
        List<Integer> stationIds = filteredStations.stream().map(Station::getStationId).collect(Collectors.toList());
        StationStructure commonParentStructure = stationStructureService.findCommonParentByStationIds(stationIds, stationStructureTree);

        List<ResourceStructure> resourceStructureList = createResourceStructureList(commonParentStructure);

        resourceStructureService.batchInsert(resourceStructureList);

        List<ResourceStructure> resourceStructures = resourceStructureService.findResourceStructures();
        resourceStructures = resourceStructures.stream()
                .filter(resourceStructure -> resourceStructure.getStructureTypeId() == StructureTypeEnum.STATION_HOUSE.getValue())
                .collect(Collectors.toList());

        for (ResourceStructure resourceStructure : resourceStructures) {
            equipmentService.updateResourceStructureIdIfNotMapped(resourceStructure.getOriginId(), resourceStructure.getResourceStructureId());
        }
        
        configChangeMacroLogService.configChangeLog("-1", 27, ChangeOperatorEnum.CREATE);
        configChangeMacroLogService.configChangeLog("-1", 3, ChangeOperatorEnum.CREATE);

        log.info("SiteWeb2配置同步完成");
    }

    /**
     * 从站点数据同步资源结构
     * 调用Mapper中的SQL方法进行数据同步
     */
    private void syncResourceStructureFromStationData() {
        log.info("开始从站点数据同步资源结构");
        
        Integer isSC = syncSiteWeb2Mapper.getIsScValue();

        if (isSC == 1) {
            syncSiteWeb2Mapper.updateResourceStructureForSc();
        }

        syncSiteWeb2Mapper.updateInitialStructureType();
        syncSiteWeb2Mapper.insertCenterStructure();
        syncSiteWeb2Mapper.updateCenterStructureName();
        syncSiteWeb2Mapper.deleteCenterStructure();
        syncSiteWeb2Mapper.updateCenterStructurePath();
        syncSiteWeb2Mapper.insertSubStructure();
        syncSiteWeb2Mapper.updateSubStructureName();
        syncSiteWeb2Mapper.deleteSubStructure();
        syncSiteWeb2Mapper.updateSubStructureParentPath();
        syncSiteWeb2Mapper.updateSubStructureSecondLevel();
        syncSiteWeb2Mapper.insertStationStructure();
        syncSiteWeb2Mapper.updateStationStructure();
        syncSiteWeb2Mapper.deleteStationStructure();
        syncSiteWeb2Mapper.updateStationStructurePath();
        syncSiteWeb2Mapper.insertHouseStructure();
        syncSiteWeb2Mapper.updateHouseStructure();
        syncSiteWeb2Mapper.deleteHouseStructure();
        syncSiteWeb2Mapper.updateHouseStructurePath();
        syncSiteWeb2Mapper.updateEquipmentResourceStructure();

        if (isSC == 1) {
            syncSiteWeb2Mapper.revertResourceStructureForSc();
        }

        syncSiteWeb2Mapper.insertConfigChangeLog();
        
        log.info("从站点数据同步资源结构完成");
    }

    /**
     * 创建资源结构列表
     * 根据公共父节点递归创建资源结构
     *
     * @param commonParentStructure 公共父节点
     * @return 资源结构列表
     */
    public List<ResourceStructure> createResourceStructureList(StationStructure commonParentStructure) {
        List<ResourceStructure> resourceStructureList = new ArrayList<>();
        Integer existingRootResourceStructureId = null;

        ResourceStructure rootStructure = resourceStructureService.getRootStructure();
        if (rootStructure != null) {
            existingRootResourceStructureId = rootStructure.getResourceStructureId();
        }

        if (existingRootResourceStructureId != null) {
            String levelPath = String.valueOf(existingRootResourceStructureId);
            if (commonParentStructure.getChildren() != null) {
                for (StationStructure child : commonParentStructure.getChildren()) {
                    createResourceStructureRecursive(child, existingRootResourceStructureId, levelPath, resourceStructureList);
                }
            }

            if (commonParentStructure.getStations() != null) {
                for (Station station : commonParentStructure.getStations()) {
                    createStationResourceStructure(station, existingRootResourceStructureId, levelPath, resourceStructureList);
                }
            }
        }

        return resourceStructureList;
    }

    /**
     * 递归创建资源结构
     *
     * @param currentStructure 当前结构
     * @param parentResourceStructureId 父资源结构ID
     * @param parentLevelPath 父级路径
     * @param resourceStructureList 资源结构列表
     */
    private void createResourceStructureRecursive(
            StationStructure currentStructure,
            Integer parentResourceStructureId,
            String parentLevelPath,
            List<ResourceStructure> resourceStructureList
    ) {
        Integer existingResourceStructureId = fetchExistingResourceStructureId(currentStructure.getStructureId(), 103);
        if (existingResourceStructureId != null) {
            String currentLevelPath = parentLevelPath + "." + existingResourceStructureId;
            if (currentStructure.getChildren() != null) {
                for (StationStructure child : currentStructure.getChildren()) {
                    createResourceStructureRecursive(child, existingResourceStructureId, currentLevelPath, resourceStructureList);
                }
            }
            if (currentStructure.getStations() != null) {
                for (Station station : currentStructure.getStations()) {
                    createStationResourceStructure(station, existingResourceStructureId, currentLevelPath, resourceStructureList);
                }
            }
            return;
        }

        int resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
        String currentLevelPath = parentLevelPath.isEmpty() ? String.valueOf(resourceStructureId) : parentLevelPath + "." + resourceStructureId;

        ResourceStructure resource = new ResourceStructure();
        resource.setResourceStructureId(resourceStructureId);
        resource.setDisplay(true);
        resource.setSortValue(1);
        resource.setParentResourceStructureId(parentResourceStructureId);
        resource.setLevelOfPath(currentLevelPath);
        resource.setResourceStructureName(currentStructure.getStructureName());
        resource.setOriginId(currentStructure.getStructureId());
        resource.setOriginParentId(currentStructure.getParentStructureId());
        resource.setStructureTypeId(103);

        resourceStructureList.add(resource);

        if (currentStructure.getChildren() != null) {
            for (StationStructure child : currentStructure.getChildren()) {
                createResourceStructureRecursive(child, resourceStructureId, currentLevelPath, resourceStructureList);
            }
        }

        if (currentStructure.getStations() != null) {
            for (Station station : currentStructure.getStations()) {
                createStationResourceStructure(station, resourceStructureId, currentLevelPath, resourceStructureList);
            }
        }
    }

    /**
     * 创建站点资源结构
     *
     * @param station 站点
     * @param parentResourceStructureId 父资源结构ID
     * @param parentLevelPath 父级路径
     * @param resourceStructureList 资源结构列表
     */
    private void createStationResourceStructure(
            Station station,
            Integer parentResourceStructureId,
            String parentLevelPath,
            List<ResourceStructure> resourceStructureList
    ) {
        Integer existingResourceStructureId = fetchExistingResourceStructureId(station.getStationId(), 104);

        // 如果 station 已经有对应的 ResourceStructure，获取其 ID 用于 house 的处理
        int resourceStructureId;
        String currentLevelPath;

        if (existingResourceStructureId != null) {
            resourceStructureId = existingResourceStructureId;
            currentLevelPath = parentLevelPath + "." + resourceStructureId;
        } else {
            // 如果不存在，则创建新的 ResourceStructure
            resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
            currentLevelPath = parentLevelPath + "." + resourceStructureId;

            ResourceStructure resource = new ResourceStructure();
            resource.setResourceStructureId(resourceStructureId);
            resource.setDisplay(true);
            resource.setSortValue(1);
            resource.setStructureTypeId(104);
            resource.setParentResourceStructureId(parentResourceStructureId);
            resource.setLevelOfPath(currentLevelPath);
            resource.setResourceStructureName(station.getStationName());
            resource.setOriginId(station.getStationId());
            StationStructureMap stationStructureMap = stationStructureMapService.findStationStructureMapByStationId(station.getStationId());
            resource.setOriginParentId(stationStructureMap.getStructureId());

            resourceStructureList.add(resource);
        }

        // 无论 station 是否已有对应的 ResourceStructure，处理 station 下的 house
        if (station.getHouses() != null) {
            for (House house : station.getHouses()) {
                createHouseResourceStructure(house, resourceStructureId, currentLevelPath, resourceStructureList);
            }
        }
    }

    /**
     * 创建房屋资源结构
     *
     * @param house 房屋
     * @param parentResourceStructureId 父资源结构ID
     * @param parentLevelPath 父级路径
     * @param resourceStructureList 资源结构列表
     */
    private void createHouseResourceStructure(
            House house,
            Integer parentResourceStructureId,
            String parentLevelPath,
            List<ResourceStructure> resourceStructureList
    ) {
        Integer existingResourceStructureId = fetchExistingResourceStructureId(house.getHouseId(), house.getStationId(), 105);
        if (existingResourceStructureId != null) {
            return;
        }

        int resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
        String currentLevelPath = parentLevelPath + "." + resourceStructureId;

        ResourceStructure resource = new ResourceStructure();
        resource.setResourceStructureId(resourceStructureId);
        resource.setStructureTypeId(105);
        resource.setDisplay(true);
        resource.setSortValue(1);
        resource.setParentResourceStructureId(parentResourceStructureId);
        resource.setLevelOfPath(currentLevelPath);
        resource.setResourceStructureName(house.getHouseName());
        resource.setOriginId(house.getHouseId());
        resource.setOriginParentId(house.getStationId());

        resourceStructureList.add(resource);
    }

    /**
     * 获取已存在的资源结构ID
     *
     * @param originId 源ID
     * @param structureTypeId 结构类型ID
     * @return 资源结构ID
     */
    private Integer fetchExistingResourceStructureId(Integer originId, Integer structureTypeId) {
        ResourceStructure existingStructure = resourceStructureService.findByOriginIdAndStructureType(originId, structureTypeId);
        return existingStructure != null ? existingStructure.getResourceStructureId() : null;
    }

    /**
     * 获取已存在的资源结构ID（带父ID）
     *
     * @param originId 源ID
     * @param parentOriginId 父源ID
     * @param structureTypeId 结构类型ID
     * @return 资源结构ID
     */
    private Integer fetchExistingResourceStructureId(Integer originId, Integer parentOriginId, Integer structureTypeId) {
        ResourceStructure existingStructure = resourceStructureService.findResourceStructureByOriginIdAndParentIdAndStructureTypeId(originId, parentOriginId, structureTypeId);
        return existingStructure != null ? existingStructure.getResourceStructureId() : null;
    }
}
