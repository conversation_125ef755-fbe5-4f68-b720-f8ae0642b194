package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Monitor unit project info entity
 */
@Data
@TableName("tbl_monitorunitprojectinfo")
public class MonitorUnitProjectInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("StationId")
    private Integer stationId;

    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("ProjectName")
    private String projectName;

    @TableField("ContractNo")
    private String contractNo;

    @TableField("InstallTime")
    private LocalDateTime installTime;
}
