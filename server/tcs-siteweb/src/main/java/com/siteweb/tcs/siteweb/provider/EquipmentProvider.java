package com.siteweb.tcs.siteweb.provider;

import com.siteweb.tcs.siteweb.dto.CreateEquipmentDto;
import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;
import com.siteweb.tcs.siteweb.entity.Equipment;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.entity.House;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.enums.EquipmentPropertyEnum;
import com.siteweb.tcs.siteweb.enums.StructureTypeEnum;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IEquipmentTemplateService;
import com.siteweb.tcs.siteweb.service.IHouseService;
import com.siteweb.tcs.siteweb.service.IMonitorUnitService;
import com.siteweb.tcs.siteweb.service.IResourceStructureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Equipment Controller
 */
@Service
@Slf4j
public class EquipmentProvider {

    @Autowired
    private IEquipmentService equipmentService;
    @Autowired
    private IEquipmentTemplateService equipmentTemplateService;
    @Autowired
    private IHouseService houseService;
    @Autowired
    private IResourceStructureService resourceStructureService;
    @Autowired
    private IMonitorUnitService monitorUnitService;

    /**
     * Get all equipment
     *
     * @return List of all equipment
     */
    public List<Equipment> list() {
        return equipmentService.allEquipment();
    }

    /**
     * Get equipment configuration by ID
     *
     * @param equipmentId Equipment ID
     * @return Equipment configuration
     */
    public Equipment getConfig(Integer equipmentId) {
        return equipmentService.findEquipmentById(equipmentId);
    }

    /**
     * Create a new equipment
     *
     * @param info Equipment creation information
     * @return Created equipment
     */
    public Equipment createEquipment(@RequestBody CreateEquipmentDto info) {
        // Check if equipment name already exists
        boolean repeatName = equipmentService.existsByMonitorUnitIdAndEquipmentName(
                null, info.getMonitorUnitId(), info.getEquipmentName());
        if (repeatName) {
            log.error("Equipment name already exists");
            return null;
        }

        // Convert DTO to entity
        Equipment eq = info.toEntrty();

        // Get template information
        EquipmentTemplate template = equipmentTemplateService.findById(info.getEquipmentTemplateId());
        if (template == null) {
            log.error("Equipment template not found");
            return null;
        }

        // Get resource structure and set station/house information
        ResourceStructure structure = resourceStructureService.getStructureByID(info.getResourceStructureId());
        if (structure == null) {
            log.error("Resource structure not found");
            return null;
        }

        if (structure.getStructureTypeId().equals(StructureTypeEnum.STATION_HOUSE.getValue())) {
            House house = houseService.findByHouseId(structure.getOriginId());
            if (house != null) {
                eq.setHouseId(house.getHouseId());
                eq.setStationId(house.getStationId());
            }
        } else {
            // For other structure types, try to find default house of the station
            House house = houseService.findStationDefaultHouse(structure.getOriginId());
            if (house != null) {
                eq.setHouseId(house.getHouseId());
                eq.setStationId(structure.getOriginId());
            }
        }

        // Check if it's a cross-site monitoring unit and set property accordingly
        String property = getProperty(eq.getMonitorUnitId(), template.getProperty());
        eq.setProperty(property);

        // Set template-related properties
        eq.setUpdateTime(LocalDateTime.now());
        eq.setEquipmentType(template.getEquipmentType());
        eq.setEquipmentStyle(template.getEquipmentStyle());
        eq.setEquipmentCategory(template.getEquipmentCategory());
        eq.setUnit(template.getUnit());
        eq.setVendor(template.getVendor());

        // Save equipment
        equipmentService.createEquipment(eq);

        // Instantiate template if needed
        if (Boolean.TRUE.equals(info.getInstantiated())) {
            Integer equipmentTemplateId = equipmentService.equipmentInstance(eq.getEquipmentId());
            eq.setEquipmentTemplateId(equipmentTemplateId);
        }

        return eq;
    }
    /**
     * Create a new equipment
     *
     * @param info Equipment creation information
     * @return Created equipment
     */
    public Equipment createEquipmentWithoutHouseStation( CreateEquipmentDto info) {
        // Check if equipment name already exists
        boolean repeatName = equipmentService.existsByMonitorUnitIdAndEquipmentName(
                null, info.getMonitorUnitId(), info.getEquipmentName());
        if (repeatName) {
            log.error("Equipment name already exists");
            return null;
        }

        // Convert DTO to entity
        Equipment eq = info.toEntrty();

        // Get template information
        EquipmentTemplate template = equipmentTemplateService.findById(info.getEquipmentTemplateId());
        if (template == null) {
            log.error("Equipment template not found");
            return null;
        }

        String property = getProperty(eq.getMonitorUnitId(), template.getProperty());
        eq.setProperty(property);

        // Set template-related properties
        eq.setUpdateTime(LocalDateTime.now());
        eq.setEquipmentType(template.getEquipmentType());
        eq.setEquipmentStyle(template.getEquipmentStyle());
        eq.setEquipmentCategory(template.getEquipmentCategory());
        eq.setUnit(template.getUnit());
        eq.setVendor(template.getVendor());

        // Save equipment
        equipmentService.createEquipment(eq);

        // Instantiate template if needed
        if (Boolean.TRUE.equals(info.getInstantiated())) {
            Integer equipmentTemplateId = equipmentService.equipmentInstance(eq.getEquipmentId());
            eq.setEquipmentTemplateId(equipmentTemplateId);
        }

        return eq;
    }
    /**
     * Get equipment property, if it's a cross-site monitoring unit, add cross-site virtual equipment property
     *
     * @param monitorUnitId Monitor unit ID
     * @param equipmentTemplateProperty Equipment template property
     * @return Processed equipment property
     */
    private String getProperty(Integer monitorUnitId, String equipmentTemplateProperty) {
        return Optional.ofNullable(monitorUnitId)
                .filter(id -> isCrossSiteMonitoringUnit(id))
                .map(id -> {
                    // Get cross-site virtual equipment property ID
                    String crossProperty = String.valueOf(EquipmentPropertyEnum.CROSS_VIRTUAL_EQUIPMENT.getId());
                    // If original property is empty, use cross property directly; otherwise, append cross property
                    return isBlank(equipmentTemplateProperty) ? crossProperty : equipmentTemplateProperty + "/" + crossProperty;
                })
                .orElse(equipmentTemplateProperty); // If not cross-site monitoring unit, return original property
    }

    /**
     * Check if monitor unit is cross-site
     *
     * @param monitorUnitId Monitor unit ID
     * @return True if cross-site, false otherwise
     */
    private boolean isCrossSiteMonitoringUnit(Integer monitorUnitId) {
        return monitorUnitService.isCrossSiteMonitoringUnit(monitorUnitId);
    }

    /**
     * Check if string is blank
     *
     * @param str String to check
     * @return True if blank, false otherwise
     */
    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * Update equipment
     *
     * @param equipmentDetailDTO Equipment detail DTO
     * @return Updated equipment
     */
    public Equipment updateEquipment(@RequestBody EquipmentDetailDTO equipmentDetailDTO) {
        // 检查设备名称是否重复
        boolean repeatName = equipmentService.existsByMonitorUnitIdAndEquipmentName(
                equipmentDetailDTO.getEquipmentId(),
                equipmentDetailDTO.getMonitorUnitId(),
                equipmentDetailDTO.getEquipmentName()
        );

        if (repeatName) {
            log.error("Equipment name already exists");
            return null;
        }

        return equipmentService.updateEquipment(equipmentDetailDTO);
    }

    /**
     * Delete equipment
     *
     * @param eqId Equipment ID
     * @return Operation result
     */
    public boolean deleteEquipment(@PathVariable("eqId") Integer eqId) {
        return equipmentService.deleteEquipment(eqId);
    }
}
