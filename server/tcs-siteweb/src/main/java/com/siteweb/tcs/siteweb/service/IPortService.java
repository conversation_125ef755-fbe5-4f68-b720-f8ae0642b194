package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.Port;

import java.util.List;

/**
 * Port Service Interface
 */
public interface IPortService extends IService<Port> {
    /**
     * 验证端口信息
     *
     * @param port 端口信息
     */
    void verification(Port port);

    /**
     * 创建端口
     *
     * @param port 端口信息
     * @return 创建的端口
     */
    Port createPort(Port port);

    /**
     * 根据监控单元ID删除端口
     *
     * @param monitorUnitId 监控单元ID
     * @return 是否删除成功
     */
    boolean deleteByMonitorUnitId(Integer monitorUnitId);

    Port findByPortId(Integer portId);

    /**
     * 根据端口id删除端口
     *
     * @param portId 监控单元ID
     * @return 是否删除成功
     */
    boolean deleteByPortId(Integer portId);

    /**
     * 获取监控单元下的最大端口号
     *
     * @param monitorUnitId 监控单元ID
     * @return 最大端口号
     */
    Integer getMaxPortByMonitorUnitId(Integer monitorUnitId);

    List<Port> findByMonitorUnitId(Integer monitorUnitId);

    boolean updatePort(Port port);
}
