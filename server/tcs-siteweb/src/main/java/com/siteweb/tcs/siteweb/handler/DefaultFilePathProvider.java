package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.siteweb.dto.DistributeFilePath;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 默认文件路径提供者实现
 * 使用FileServerProperty提供的配置
 */
@Slf4j
@Component
public class DefaultFilePathProvider implements FilePathProvider {
    
    @Autowired
    private FileServerProperty fileServerProperty;
    
    @Override
    public String getRootPath() {
        String rootPath = fileServerProperty.getRootPath();
        log.debug("Using default file path provider with rootPath: {}", rootPath);
        return rootPath;
    }

    @Override
    public String getWorkspacePath() {
        // 默认实现返回根路径，保持向后兼容
        return getRootPath();
    }

    @Override
    public String getProtocolPath() {
        // 默认实现返回根路径下的protocol目录
        return getFullPath("protocol");
    }

    @Override
    public String getPluginPath(String pluginId) {
        // 默认实现构建插件路径
        return getRootPath() + "/" + pluginId;
    }

    @Override
    public String getPluginWorkspacePath(String pluginId) {
        // 默认实现构建插件工作区域路径
        return getPluginPath(pluginId) + "/workspace";
    }

    @Override
    public List<DistributeFilePath> getDistributeFilePaths() {
        return List.of();
    }

    @Override
    public void addDistributeFilePath(DistributeFilePath distributeFilePath) {

    }

    @Override
    public void clearDistributeFilePath() {

    }

    @Override
    public void setDistributeFilePaths(List<DistributeFilePath> distributeFilePaths) {

    }

}
