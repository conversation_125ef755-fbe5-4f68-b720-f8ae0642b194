package com.siteweb.tcs.siteweb.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.siteweb.service.ISyncSiteWeb2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * SiteWeb2同步控制器
 * 用于测试和调用SiteWeb2同步功能
 */
@Slf4j
@RestController
@RequestMapping("/api/sync-siteweb2")
public class SyncSiteWeb2Controller {

    @Autowired
    private ISyncSiteWeb2Service syncSiteWeb2Service;

    /**
     * 同步SiteWeb2配置
     * 
     * @return 同步结果
     */
    @PostMapping("/sync-config")
    public ResponseEntity<ResponseResult<String>> syncSiteWeb2Config() {
        try {
            log.info("开始执行SiteWeb2配置同步");
            syncSiteWeb2Service.syncSiteWeb2Config();
            log.info("SiteWeb2配置同步完成");
            return ResponseHelper.successful("SiteWeb2配置同步成功");
        } catch (Exception e) {
            log.error("SiteWeb2配置同步失败", e);
            return ResponseHelper.failed("SiteWeb2配置同步失败: " + e.getMessage());
        }
    }
}
