package com.siteweb.tcs.siteweb.provider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.tcs.siteweb.util.StrSplitUtil;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.service.IEquipmentTemplateService;
import com.siteweb.tcs.siteweb.service.ISamplerService;
import com.siteweb.tcs.siteweb.service.ISamplerUnitService;
import com.siteweb.tcs.siteweb.util.I18n;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;

/**
 * Sampler Provider (业务逻辑类)
 */
@Slf4j
@Service
public class SamplerProvider {

    @Autowired
    private ISamplerService samplerService;

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private IEquipmentTemplateService equipmentTemplateService;

    @Autowired
    private I18n i18n;

    /**
     * 根据协议码获取采样器
     *
     * @param protocolCode 协议码
     * @return Sampler
     */
    public Sampler getSamplerByProtocolCode(String protocolCode) {
        if (!StringUtils.hasText(protocolCode)) {
            log.warn("Protocol code cannot be empty");
            return null;
        }
        try {
            Sampler sampler = samplerService.getSamplerByProtocolCode(protocolCode);
            if (sampler == null) {
                log.warn("Sampler not found for protocol code: {}", protocolCode);
            }
            return sampler;
        } catch (Exception e) {
            log.error("Failed to get sampler by protocol code: {}", protocolCode, e);
            return null;
        }
    }

    /**
     * 验证批量删除采集器的合法性
     *
     * @param samplerIds 采集器ID列表（逗号分隔）
     * @return 如果有引用关系，返回错误信息；否则返回null
     */
    public String validateBatchDeleteForSampler(String samplerIds) {
        if (CharSequenceUtil.isBlank(samplerIds)) {
            return null;
        }

        try {
            List<Integer> samplerIdList = StrSplitUtil.splitToIntList(samplerIds);
            List<Sampler> samplerList = samplerService.findByIds(samplerIdList);

            if (CollUtil.isEmpty(samplerList)) {
                return null;
            }

            // 获取引用了设备模板的采集器名称
            List<String> protocolCodes = samplerList.stream().map(Sampler::getProtocolCode).toList();
            Set<String> referenceEquipmentNames = samplerService.findReferenceEquipmentNameByProtocolCodes(protocolCodes);
            if (CollUtil.isNotEmpty(referenceEquipmentNames)) {
                String equipmentNames = String.join("、", referenceEquipmentNames);
                return i18n.T("monitor.equipmentTemplate.existsEquipmentReference", equipmentNames);
            }

            // 获取引用了采集单元的采集器名称
            Set<String> referenceSamplerNames = samplerService.findReferenceSamplerNameBySamplerIdList(samplerIdList);
            if (CollUtil.isNotEmpty(referenceSamplerNames)) {
                String samplerNames = String.join("、", referenceSamplerNames);
                return i18n.T("monitor.equipmentTemplate.existsSamplerUnitReference", samplerNames);
            }

            return null;
        } catch (Exception e) {
            log.error("Failed to validate batch delete for samplers: {}", samplerIds, e);
            return "验证删除操作时发生错误";
        }
    }

    /**
     * 批量删除采集器
     *
     * @param samplerIds 采集器ID列表（逗号分隔）
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteForSampler(String samplerIds) {
        if (CharSequenceUtil.isBlank(samplerIds)) {
            log.warn("Sampler IDs is empty");
            return true;
        }

        try {
            List<Integer> samplerIdList = StrSplitUtil.splitToIntList(samplerIds);
            return samplerService.deleteByIds(samplerIdList);
        } catch (Exception e) {
            log.error("Failed to batch delete samplers: {}", samplerIds, e);
            return false;
        }
    }

    /**
     * 验证单个删除采集器的合法性
     *
     * @param samplerId 采集器ID
     * @return 如果有引用关系，返回错误信息；否则返回null
     */
    public String validateDeleteByIdForSampler(Integer samplerId) {
        if (samplerId == null) {
            return null;
        }

        try {
            log.debug("SamplerProvider validateDeleteByIdForSampler: samplerId={}", samplerId);

            Sampler sampler = samplerService.findById(samplerId);
            log.debug("SamplerProvider findById result: {}", sampler != null ?
                     String.format("found sampler with id=%d, protocolCode=%s", sampler.getSamplerId(), sampler.getProtocolCode()) :
                     "null");

            if (sampler == null) {
                log.warn("SamplerProvider: Sampler with ID {} not found during validation", samplerId);
                return null;
            }

            // 检查设备模板引用
            Set<String> referenceEquipmentNames = samplerService.findReferenceEquipmentNameByProtocolCodes(List.of(sampler.getProtocolCode()));
            if (CollUtil.isNotEmpty(referenceEquipmentNames)) {
                return i18n.T("monitor.equipmentTemplate.existsEquipmentReference", sampler.getSamplerName());
            }

            // 检查采集单元引用
            Set<String> referenceSamplerNames = samplerService.findReferenceSamplerNameBySamplerIdList(List.of(samplerId));
            if (CollUtil.isNotEmpty(referenceSamplerNames)) {
                return i18n.T("monitor.equipmentTemplate.existsSamplerUnitReference", sampler.getSamplerName());
            }

            return null;
        } catch (Exception e) {
            log.error("Failed to validate delete for sampler: {}", samplerId, e);
            return "验证删除操作时发生错误";
        }
    }

    /**
     * 删除单个采集器
     *
     * @param samplerId 采集器ID
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIdForSampler(Integer samplerId) {
        if (samplerId == null) {
            log.warn("Sampler ID is null");
            return false;
        }

        try {
            return samplerService.deleteById(samplerId);
        } catch (Exception e) {
            log.error("Failed to delete sampler: {}", samplerId, e);
            return false;
        }
    }
}