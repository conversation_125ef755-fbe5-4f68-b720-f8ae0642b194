package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.ConfigChangeMap;

/**
 * 配置变更映射服务接口
 */
public interface IConfigChangeMapService extends IService<ConfigChangeMap> {
    
    /**
     * 根据配置ID和编辑类型查找配置变更映射
     *
     * @param configId 配置ID
     * @param editType 编辑类型
     * @return 配置变更映射
     */
    ConfigChangeMap findByConfigIdAndEditType(Integer configId, Integer editType);
}
