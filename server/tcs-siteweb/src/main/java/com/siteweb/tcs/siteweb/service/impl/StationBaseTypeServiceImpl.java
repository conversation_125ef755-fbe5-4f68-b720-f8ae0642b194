package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.entity.CommandBaseMap;
import com.siteweb.tcs.siteweb.entity.EventBaseMap;
import com.siteweb.tcs.siteweb.entity.SignalBaseMap;
import com.siteweb.tcs.siteweb.entity.StationBaseMap;
import com.siteweb.tcs.siteweb.entity.StationBaseType;
import com.siteweb.tcs.siteweb.entity.SysConfig;
import com.siteweb.tcs.siteweb.entity.StandardType;
import com.siteweb.tcs.siteweb.entity.StandardDicSig;
import com.siteweb.tcs.siteweb.entity.StandardDicEvent;
import com.siteweb.tcs.siteweb.entity.StandardDicControl;
import com.siteweb.tcs.siteweb.entity.SignalBaseDic;
import com.siteweb.tcs.siteweb.entity.EventBaseDic;
import com.siteweb.tcs.siteweb.entity.CommandBaseDic;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.mapper.CommandBaseMapMapper;
import com.siteweb.tcs.siteweb.mapper.EventBaseMapMapper;
import com.siteweb.tcs.siteweb.mapper.SignalBaseMapMapper;
import com.siteweb.tcs.siteweb.mapper.StationBaseMapMapper;
import com.siteweb.tcs.siteweb.mapper.StationBaseTypeMapper;
import com.siteweb.tcs.siteweb.mapper.StandardDicSigMapper;
import com.siteweb.tcs.siteweb.mapper.StandardDicEventMapper;
import com.siteweb.tcs.siteweb.mapper.StandardDicControlMapper;
import com.siteweb.tcs.siteweb.mapper.SignalBaseDicMapper;
import com.siteweb.tcs.siteweb.mapper.EventBaseDicMapper;
import com.siteweb.tcs.siteweb.mapper.CommandBaseDicMapper;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.dto.ApplyStandardDTO;
import com.siteweb.tcs.siteweb.dto.BaseMapCountWarnDTO;
import com.siteweb.tcs.siteweb.dto.BaseStationMapTypeDTO;
import com.siteweb.tcs.siteweb.dto.CommandBaseMapDTO;
import com.siteweb.tcs.siteweb.dto.ControlStationBaseTypeDTO;
import com.siteweb.tcs.siteweb.dto.EqStationBaseTypeDTO;
import com.siteweb.tcs.siteweb.dto.EventBaseMapDTO;
import com.siteweb.tcs.siteweb.dto.EventStationBaseTypeDTO;
import com.siteweb.tcs.siteweb.dto.IdValueDTO;
import com.siteweb.tcs.siteweb.dto.SignalBaseMapDTO;
import com.siteweb.tcs.siteweb.dto.SignalStationBaseTypeDTO;
import com.siteweb.tcs.siteweb.enums.StandardCategoryEnum;
import com.siteweb.tcs.siteweb.enums.SysConfigEnum;
import com.siteweb.tcs.siteweb.util.I18n;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Station Base Type Service Implementation
 */
@Service
public class StationBaseTypeServiceImpl extends IsolatedServiceImpl<StationBaseTypeMapper, StationBaseType> implements IStationBaseTypeService {

    private static final String LIMIT_1 = "limit 1";

    @Autowired
    private IStandardTypeService standardTypeService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private StationBaseMapMapper stationBaseMapMapper;

    @Autowired
    private SignalBaseMapMapper signalBaseMapMapper;

    @Autowired
    private EventBaseMapMapper eventBaseMapMapper;

    @Autowired
    private CommandBaseMapMapper commandBaseMapMapper;

    @Autowired
    private StandardDicSigMapper standardDicSigMapper;

    @Autowired
    private StandardDicEventMapper standardDicEventMapper;

    @Autowired
    private StandardDicControlMapper standardDicControlMapper;

    @Autowired
    private SignalBaseDicMapper signalBaseDicMapper;

    @Autowired
    private EventBaseDicMapper eventBaseDicMapper;

    @Autowired
    private CommandBaseDicMapper commandBaseDicMapper;

    @Autowired
    private ISignalService signalService;

    @Autowired
    private IEventService eventService;

    @Autowired
    private IControlService controlService;

    @Autowired
    private ISignalBaseMapService signalBaseMapService;

    @Autowired
    private IEventBaseMapService eventBaseMapService;

    @Autowired
    private ICommandBaseMapService commandBaseMapService;

    @Autowired
    private I18n i18n;

    @Autowired
    private IOperationDetailService operationDetailService;;

    @Override
    public int deleteByStandardId(int standardId) {
        return baseMapper.delete(new QueryWrapper<StationBaseType>().eq("StandardId", standardId));
    }

    @Override
    public List<IdValueDTO<Integer, String>> findCurrentStationBaseType() {
        SysConfig sysConfig = sysConfigService.findByKey(SysConfigEnum.STANDARD_VER);
        if (Objects.isNull(sysConfig) || CharSequenceUtil.isBlank(sysConfig.getConfigValue())) {
            //没有设置标准类型，返回空列表
            return list().stream()
                    .map(item -> new IdValueDTO<>(item.getId(), item.getType()))
                    .collect(Collectors.toList());
        }
        StandardType standardType = standardTypeService.findByStandardAlias(sysConfig.getConfigValue());
        if (Objects.isNull(standardType) || Objects.isNull(standardType.getStandardId())) {
            //没有设置标准类型，返回空列表
            return list().stream()
                    .map(item -> new IdValueDTO<>(item.getId(), item.getType()))
                    .collect(Collectors.toList());
        }

        // 根据标准ID查找对应的站点基类型
        return list(Wrappers.lambdaQuery(StationBaseType.class)
                .eq(StationBaseType::getStandardId, standardType.getStandardId()))
                .stream()
                .map(item -> new IdValueDTO<>(item.getId(), item.getType()))
                .collect(Collectors.toList());
    }

    @Override
    public List<BaseStationMapTypeDTO> getBaseStationMapType(Integer stationBaseTypeId) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        return baseMapper.getBaseStationMapType(stationBaseTypeId, currentStandardType.getValue());
    }

    @Override
    public void saveBaseStationMapType(BaseStationMapTypeDTO baseStationMapTypeDTO) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        if (Boolean.TRUE.equals(baseStationMapTypeDTO.getChecked())) {
            // 先删除之前的关联
            stationBaseMapMapper.delete(Wrappers.lambdaQuery(StationBaseMap.class)
                    .eq(StationBaseMap::getStationCategory, baseStationMapTypeDTO.getItemId())
                    .eq(StationBaseMap::getStandardType, currentStandardType.getValue()));
            // 新增关联
            StationBaseMap stationBaseMap = new StationBaseMap();
            stationBaseMap.setStationBaseType(baseStationMapTypeDTO.getId());
            stationBaseMap.setStationCategory(baseStationMapTypeDTO.getItemId());
            stationBaseMap.setStandardType(currentStandardType.getValue());
            stationBaseMapMapper.insert(stationBaseMap);
            operationDetailService.recordOperationLog(String.valueOf(stationBaseMap.getStationBaseType()),
                    OperationObjectTypeEnum.STANDARD, "局站类型映射", i18n.T("add"), "",
                    String.format("%s-%s", baseStationMapTypeDTO.getType(), baseStationMapTypeDTO.getItemValue()));
        } else {
            // 取消关联
            stationBaseMapMapper.delete(Wrappers.lambdaQuery(StationBaseMap.class).eq(StationBaseMap::getStationBaseType, baseStationMapTypeDTO.getId())
                    .eq(StationBaseMap::getStationCategory, baseStationMapTypeDTO.getItemId())
                    .eq(StationBaseMap::getStandardType, currentStandardType.getValue())
            );
            operationDetailService.recordOperationLog(String.valueOf(baseStationMapTypeDTO.getId()),
                    OperationObjectTypeEnum.STANDARD, "局站类型映射", i18n.T("delete"),
                    String.format("%s-%s", baseStationMapTypeDTO.getType(), baseStationMapTypeDTO.getItemValue()), "");
        }
    }

    @Override
    public List<EqStationBaseTypeDTO> getEquipmentTemplateStationBaseType() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        return baseMapper.getEquipmentTemplateStationBaseType(currentStandardType.getValue());
    }

    @Override
    public List<SignalStationBaseTypeDTO> getSignalStationBaseType() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        List<SignalStationBaseTypeDTO> signalStationBaseTypes = baseMapper.getSignalStationBaseType(currentStandardType.getValue());
        List<SignalBaseMapDTO> signalBaseMaps = signalBaseMapService.getSignalBaseMap(currentStandardType.getValue());
        signalStationBaseTypes.forEach(signalStationBaseTypeDTO -> {
            List<SignalBaseMapDTO> signalBaseMapDTOS = signalBaseMaps.stream().filter(f -> Objects.equals(f.getStandardDicId(), signalStationBaseTypeDTO.getStandardDicId()) &&
                    Objects.equals(f.getStationBaseType(), signalStationBaseTypeDTO.getId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(signalBaseMapDTOS)) {
                signalStationBaseTypeDTO.setBaseTypeId(signalBaseMapDTOS.stream().map(m -> String.valueOf(m.getBaseTypeId())).collect(Collectors.joining(",")));
                signalStationBaseTypeDTO.setBaseTypeName(signalBaseMapDTOS.stream().map(SignalBaseMapDTO::getBaseTypeName).collect(Collectors.joining(",")));
            }
        });
        return signalStationBaseTypes;
    }

    @Override
    public void saveSignalBaseMap(SignalStationBaseTypeDTO signalStationBaseTypeDTO) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        signalBaseMapService.remove(Wrappers.lambdaQuery(SignalBaseMap.class).eq(SignalBaseMap::getStandardDicId, signalStationBaseTypeDTO.getStandardDicId())
                .eq(SignalBaseMap::getStandardType, currentStandardType.getValue())
                .eq(SignalBaseMap::getStationBaseType, signalStationBaseTypeDTO.getId())
        );
        if (CharSequenceUtil.isBlank(signalStationBaseTypeDTO.getBaseTypeId())) {
            return;
        }
        List<Long> baseTypeIdList = Arrays.stream(signalStationBaseTypeDTO.getBaseTypeId().split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<SignalBaseMap> signalBaseMaps = baseTypeIdList.stream().map(baseTypeId -> {
            SignalBaseMap signalBaseMap = new SignalBaseMap();
            signalBaseMap.setStandardDicId(signalStationBaseTypeDTO.getStandardDicId());
            signalBaseMap.setStandardType(currentStandardType.getValue());
            signalBaseMap.setStationBaseType(signalStationBaseTypeDTO.getId());
            signalBaseMap.setBaseTypeId(new BigDecimal(baseTypeId));
            return signalBaseMap;
        }).collect(Collectors.toList());
        // 批量插入
        signalBaseMapService.saveBatch(signalBaseMaps,1000);
    }

    @Override
    public List<EventStationBaseTypeDTO> getEventStationBaseType() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        List<EventStationBaseTypeDTO> eventStationBaseTypeList = baseMapper.getEventStationBaseType(currentStandardType.getValue());
        List<EventBaseMapDTO> eventBaseMaps = eventBaseMapService.getEventBaseMap(currentStandardType.getValue());
        eventStationBaseTypeList.forEach(eventStationBaseTypeDTO -> {
            List<EventBaseMapDTO> eventBaseMapDTOS = eventBaseMaps.stream().filter(f -> Objects.equals(f.getStandardDicId(), eventStationBaseTypeDTO.getStandardDicId()) &&
                    Objects.equals(f.getStationBaseType(), eventStationBaseTypeDTO.getId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(eventBaseMapDTOS)) {
                eventStationBaseTypeDTO.setBaseTypeId(eventBaseMapDTOS.stream().map(m -> String.valueOf(m.getBaseTypeId())).collect(Collectors.joining(",")));
                eventStationBaseTypeDTO.setBaseTypeName(eventBaseMapDTOS.stream().map(EventBaseMapDTO::getBaseTypeName).collect(Collectors.joining(",")));
            }
        });
        return eventStationBaseTypeList;
    }

    @Override
    public void saveEventBaseMap(EventStationBaseTypeDTO eventStationBaseTypeDTO) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        eventBaseMapService.remove(Wrappers.lambdaQuery(EventBaseMap.class).eq(EventBaseMap::getStandardDicId, eventStationBaseTypeDTO.getStandardDicId())
                .eq(EventBaseMap::getStandardType, currentStandardType.getValue())
                .eq(EventBaseMap::getStationBaseType, eventStationBaseTypeDTO.getId())
        );
        if (CharSequenceUtil.isBlank(eventStationBaseTypeDTO.getBaseTypeId())) {
            return;
        }
        List<Long> baseTypeIdList = Arrays.stream(eventStationBaseTypeDTO.getBaseTypeId().split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<EventBaseMap> eventBaseMaps = baseTypeIdList.stream().map(baseTypeId -> {
            EventBaseMap eventBaseMap = new EventBaseMap();
            eventBaseMap.setStandardDicId(eventStationBaseTypeDTO.getStandardDicId());
            eventBaseMap.setStandardType(currentStandardType.getValue());
            eventBaseMap.setStationBaseType(eventStationBaseTypeDTO.getId());
            eventBaseMap.setBaseTypeId(new BigDecimal(baseTypeId));
            return eventBaseMap;
        }).collect(Collectors.toList());
        // 批量插入
        eventBaseMapService.saveBatch(eventBaseMaps,1000);
    }

    @Override
    public List<ControlStationBaseTypeDTO> getControlStationBaseType() {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        List<ControlStationBaseTypeDTO> controlStationBaseTypeList = baseMapper.getControlStationBaseType(currentStandardType.getValue());
        List<CommandBaseMapDTO> controlBaseMaps = commandBaseMapService.getControlBaseMap(currentStandardType.getValue());
        controlStationBaseTypeList.forEach(controlStationBaseTypeDTO -> {
            List<CommandBaseMapDTO> commandBaseMapDTOS = controlBaseMaps.stream().filter(f -> Objects.equals(f.getStandardDicId(), controlStationBaseTypeDTO.getStandardDicId()) &&
                    Objects.equals(f.getStationBaseType(), controlStationBaseTypeDTO.getId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(commandBaseMapDTOS)) {
                controlStationBaseTypeDTO.setBaseTypeId(commandBaseMapDTOS.stream().map(m -> String.valueOf(m.getBaseTypeId())).collect(Collectors.joining(",")));
                controlStationBaseTypeDTO.setBaseTypeName(commandBaseMapDTOS.stream().map(CommandBaseMapDTO::getBaseTypeName).collect(Collectors.joining(",")));
            }
        });
        return controlStationBaseTypeList;
    }

    @Override
    public void saveControlBaseMap(ControlStationBaseTypeDTO controlStationBaseTypeDTO) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        commandBaseMapService.remove(Wrappers.lambdaQuery(CommandBaseMap.class).eq(CommandBaseMap::getStandardDicId, controlStationBaseTypeDTO.getStandardDicId())
                .eq(CommandBaseMap::getStandardType, currentStandardType.getValue())
                .eq(CommandBaseMap::getStationBaseType, controlStationBaseTypeDTO.getId())
        );
        if (CharSequenceUtil.isBlank(controlStationBaseTypeDTO.getBaseTypeId())) {
            return;
        }
        List<Long> baseTypeIdList = Arrays.stream(controlStationBaseTypeDTO.getBaseTypeId().split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<CommandBaseMap> commandBaseMaps = baseTypeIdList.stream().map(baseTypeId -> {
            CommandBaseMap commandBaseMap = new CommandBaseMap();
            commandBaseMap.setStandardDicId(controlStationBaseTypeDTO.getStandardDicId());
            commandBaseMap.setStandardType(currentStandardType.getValue());
            commandBaseMap.setStationBaseType(controlStationBaseTypeDTO.getId());
            commandBaseMap.setBaseTypeId(new BigDecimal(baseTypeId));
            return commandBaseMap;
        }).collect(Collectors.toList());
        // 批量插入
        commandBaseMapService.saveBatch(commandBaseMaps,1000);
    }

    @Override
    public BaseMapCountWarnDTO getWarnCountsInBaseMap(Integer standardDicId, Integer stationBaseType, List<Long> baseTypeId, Integer baseType) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        Long count = switch (baseType) {
            case 1 -> {
                QueryWrapper<SignalBaseMap> wrapper = new QueryWrapper<>();
                wrapper.eq("StandardType", currentStandardType.getValue())
                       .eq("StationBaseType", stationBaseType)
                       .in("BaseTypeId", baseTypeId)
                       .ne("StandardDicId", standardDicId);
                yield signalBaseMapService.count(wrapper);
            }
            case 2 -> {
                QueryWrapper<EventBaseMap> wrapper = new QueryWrapper<>();
                wrapper.eq("StandardType", currentStandardType.getValue())
                       .eq("StationBaseType", stationBaseType)
                       .in("BaseTypeId", baseTypeId)
                       .ne("StandardDicId", standardDicId);
                yield eventBaseMapService.count(wrapper);
            }
            case 3 -> {
                QueryWrapper<CommandBaseMap> wrapper = new QueryWrapper<>();
                wrapper.eq("StandardType", currentStandardType.getValue())
                       .eq("StationBaseType", stationBaseType)
                       .in("BaseTypeId", baseTypeId)
                       .ne("StandardDicId", standardDicId);
                yield commandBaseMapService.count(wrapper);
            }
            default -> throw new IllegalArgumentException("Unexpected baseType: " + baseType);
        };
        if (count <= 0) {
            return BaseMapCountWarnDTO.builder().status(false).build();
        }
        return switch (baseType) {
            case 1 -> {
                QueryWrapper<SignalBaseMap> wrapper = new QueryWrapper<>();
                wrapper.eq("StandardType", currentStandardType.getValue())
                       .eq("StationBaseType", stationBaseType)
                       .in("BaseTypeId", baseTypeId)
                       .ne("StandardDicId", standardDicId);
                List<SignalBaseMap> signalBaseMaps = signalBaseMapService.list(wrapper);
                SignalBaseMap signalBaseMap = signalBaseMaps.stream().findFirst().orElseThrow();
                StandardDicSig standardDicSig = standardDicSigMapper.selectOne(
                    new QueryWrapper<StandardDicSig>()
                        .eq("StandardDicId", signalBaseMap.getStandardDicId())
                        .eq("StandardType", signalBaseMap.getStandardType())
                        .last(LIMIT_1)
                );
                SignalBaseDic signalBaseDic = signalBaseDicMapper.selectById(signalBaseMap.getBaseTypeId());
                yield BaseMapCountWarnDTO.builder()
                        .status(true)
                        .baseName(signalBaseDic.getBaseTypeName())
                        .standardName(standardDicSig.getSignalStandardName())
                        .warnMsg(CharSequenceUtil.indexedFormat("基类信号[{0}]已映射标准化信号[{1}]，请重新选择基类信号或取消原来选择信号",
                                signalBaseDic.getBaseTypeName(), standardDicSig.getSignalStandardName()))
                        .build();
            }
            case 2 -> {
                QueryWrapper<EventBaseMap> wrapper = new QueryWrapper<>();
                wrapper.eq("StandardType", currentStandardType.getValue())
                       .eq("StationBaseType", stationBaseType)
                       .in("BaseTypeId", baseTypeId)
                       .ne("StandardDicId", standardDicId);
                List<EventBaseMap> eventBaseMaps = eventBaseMapService.list(wrapper);
                EventBaseMap eventBaseMap = eventBaseMaps.stream().findFirst().orElseThrow();
                StandardDicEvent standardDicEvent = standardDicEventMapper.selectOne(
                    new QueryWrapper<StandardDicEvent>()
                        .eq("StandardDicId", eventBaseMap.getStandardDicId())
                        .eq("StandardType", eventBaseMap.getStandardType())
                        .last(LIMIT_1)
                );
                EventBaseDic eventBaseDic = eventBaseDicMapper.selectById(eventBaseMap.getBaseTypeId());
                yield BaseMapCountWarnDTO.builder()
                        .status(true)
                        .baseName(eventBaseDic.getBaseTypeName())
                        .standardName(standardDicEvent.getEventStandardName())
                        .warnMsg(CharSequenceUtil.indexedFormat("基类告警[{0}]已映射标准化告警[{1}]，请重新选择基类告警或取消原来选择告警",
                                eventBaseDic.getBaseTypeName(), standardDicEvent.getEventStandardName()))
                        .build();
            }
            case 3 -> {
                QueryWrapper<CommandBaseMap> wrapper = new QueryWrapper<>();
                wrapper.eq("StandardType", currentStandardType.getValue())
                       .eq("StationBaseType", stationBaseType)
                       .in("BaseTypeId", baseTypeId)
                       .ne("StandardDicId", standardDicId);
                List<CommandBaseMap> commandBaseMaps = commandBaseMapService.list(wrapper);
                CommandBaseMap commandBaseMap = commandBaseMaps.stream().findFirst().orElseThrow();
                StandardDicControl standardDicControl = standardDicControlMapper.selectOne(
                    new QueryWrapper<StandardDicControl>()
                        .eq("StandardDicId", commandBaseMap.getStandardDicId())
                        .eq("StandardType", commandBaseMap.getStandardType())
                        .last(LIMIT_1)
                );
                CommandBaseDic commandBaseDic = commandBaseDicMapper.selectById(commandBaseMap.getBaseTypeId());
                yield BaseMapCountWarnDTO.builder()
                        .status(true)
                        .baseName(commandBaseDic.getBaseTypeName())
                        .standardName(standardDicControl.getControlStandardName())
                        .warnMsg(CharSequenceUtil.indexedFormat("基类控制[{0}]已映射标准化控制[{1}]，请重新选择基类控制或取消原来选择控制",
                                commandBaseDic.getBaseTypeName(), standardDicControl.getControlStandardName()))
                        .build();
            }
            default -> throw new IllegalArgumentException("Unexpected baseType: " + baseType);
        };
    }

    @Override
    public Long applyStandard(ApplyStandardDTO applyStandardDTO) {
        StandardCategoryEnum currentStandardType = standardTypeService.findCurrentStandardType();
        Integer type = applyStandardDTO.getType();
        return switch (type) {
            case 1 -> // 信号标准化
                    signalService.applyStandard(currentStandardType.getValue(), applyStandardDTO.getEquipmentTemplateIds());
            case 2 -> // 告警标准化
                    eventService.applyStandard(currentStandardType.getValue(), applyStandardDTO.getEquipmentTemplateIds());
            case 3 -> // 控制标准化
                    controlService.applyStandard(currentStandardType.getValue(), applyStandardDTO.getEquipmentTemplateIds());
            default -> throw new IllegalArgumentException("Unexpected baseType: " + type);
        };
    }
} 