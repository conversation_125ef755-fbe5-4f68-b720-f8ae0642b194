package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.entity.LogicClassEntry;
import com.siteweb.tcs.siteweb.mapper.LogicClassEntryMapper;
import com.siteweb.tcs.siteweb.service.IDataItemService;
import com.siteweb.tcs.siteweb.service.ILogicClassEntryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Logic Class Entry Service Implementation
 */
@Slf4j
@Service
public class LogicClassEntryServiceImpl extends IsolatedServiceImpl<LogicClassEntryMapper, LogicClassEntry> implements ILogicClassEntryService {

    @Autowired
    private IDataItemService dataItemService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLogicClassByDataItem(Integer standardType, Integer entryCategory) {
        try {
            // 删除现有的逻辑分类条目
            remove(new LambdaQueryWrapper<LogicClassEntry>()
                    .eq(LogicClassEntry::getEntryCategory, entryCategory)
                    .eq(LogicClassEntry::getStandardType, standardType));

            // 获取数据项
            List<DataItem> dataItems = dataItemService.list(new LambdaQueryWrapper<DataItem>()
                    .eq(DataItem::getEntryId, entryCategory));

            if (CollectionUtils.isEmpty(dataItems)) {
                log.warn("No data items found for entryCategory: {} and standardType: {}", entryCategory, standardType);
                return true; // 没有数据项也算成功
            }

            // 创建新的逻辑分类条目
            List<LogicClassEntry> logicClassEntries = new ArrayList<>();
            for (DataItem dataItem : dataItems) {
                LogicClassEntry logicClassEntry = new LogicClassEntry();
                logicClassEntry.setEntryId(dataItem.getItemId());
                logicClassEntry.setEntryCategory(entryCategory);
                logicClassEntry.setLogicClassId(dataItem.getItemId());
                logicClassEntry.setLogicClass(dataItem.getItemValue());
                logicClassEntry.setStandardType(standardType);
                logicClassEntry.setDescription(dataItem.getDescription());
                logicClassEntries.add(logicClassEntry);
            }

            // 批量保存
            return saveBatch(logicClassEntries);
        } catch (Exception e) {
            log.error("Failed to update logic class by data item", e);
            return false;
        }
    }
}
