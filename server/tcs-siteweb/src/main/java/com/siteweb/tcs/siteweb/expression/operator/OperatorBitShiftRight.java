package com.siteweb.tcs.siteweb.expression.operator;

import cn.hutool.core.util.NumberUtil;
import com.siteweb.tcs.siteweb.expression.enums.OperatingDirectionEnum;

public class OperatorBitShiftRight extends OperatorBase{
    @Override
    public String operatorSymbol() {
        return ">>";
    }

    @Override
    public String operatorName() {
        return "右移";
    }

    @Override
    public int priority() {
        return 11;
    }

    @Override
    public OperatingDirectionEnum direction() {
        return OperatingDirectionEnum.LEFT_TO_RIGHT;
    }

    @Override
    public int operandCount() {
        return 2;
    }

    @Override
    public double onCalculate(double[] operands) {
        if (NumberUtil.isInteger(String.valueOf(operands[0])) && NumberUtil.isInteger(String.valueOf(operands[1]))) {
            return (int)operands[0] >> (int)operands[1];
        }
        throw new RuntimeException(">> 运算符必须用于两个整数。");
    }
}
