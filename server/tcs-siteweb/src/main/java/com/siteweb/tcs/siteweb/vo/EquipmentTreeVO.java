package com.siteweb.tcs.siteweb.vo;

import com.siteweb.tcs.siteweb.entity.Equipment;
import lombok.Data;

/**
 * 设备树形结构 VO
 * 用于采样器树的设备节点展示
 */
@Data
public class EquipmentTreeVO {
    private Integer equipmentId;
    private String equipmentName;
    private Integer samplerUnitId;

    /**
     * 从 Equipment 实体复制数据
     *
     * @param equipment 设备实体
     * @return 当前 VO 对象
     */
    public EquipmentTreeVO copy(Equipment equipment) {
        this.equipmentId = equipment.getEquipmentId();
        this.equipmentName = equipment.getEquipmentName();
        this.samplerUnitId = equipment.getSamplerUnitId();
        return this;
    }
}
