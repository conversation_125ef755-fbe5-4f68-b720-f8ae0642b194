package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.DbVersionRecord;

/**
 * Database Version Record Service Interface
 */
public interface IDbVersionRecordService extends IService<DbVersionRecord> {
    
    /**
     * 保存数据库版本记录
     * 
     * @param record 数据库版本记录
     * @return 是否保存成功
     */
    boolean saveDbVersionRecord(DbVersionRecord record);
}
