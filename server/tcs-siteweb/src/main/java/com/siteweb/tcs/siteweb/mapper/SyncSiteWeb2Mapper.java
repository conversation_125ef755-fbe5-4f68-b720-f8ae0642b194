package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Delete;

/**
 * SiteWeb2同步Mapper
 * 迁移自原SyncSiteWeb2Mapper
 */
@Mapper
public interface SyncSiteWeb2Mapper extends BaseMapper<Object> {

    /**
     * 获取是否SC值
     * @return SC值
     */
    @Select("SELECT is_sc FROM tbl_sysconfig WHERE config_key = 'is_sc'")
    Integer getIsScValue();

    /**
     * 更新资源结构为SC
     */
    @Update("UPDATE resourcestructure SET structuretypeid = 2 WHERE structuretypeid = 1")
    void updateResourceStructureForSc();

    /**
     * 恢复资源结构为SC
     */
    @Update("UPDATE resourcestructure SET structuretypeid = 1 WHERE structuretypeid = 2")
    void revertResourceStructureForSc();

    /**
     * 更新初始结构类型
     */
    @Update("UPDATE resourcestructure SET structuretypeid = 1 WHERE structuretypeid IS NULL")
    void updateInitialStructureType();

    /**
     * 插入中心结构
     */
    @Insert("INSERT INTO resourcestructure (resourcestructureid, structuretypeid, resourcestructurename, parentresourcestructureid, levelofpath, display, sortvalue, originid, originparentid) " +
            "SELECT DISTINCT structureid, 101, structurename, parentstructureid, levelpath, 1, 1, structureid, parentstructureid " +
            "FROM tbl_stationstructure WHERE structuretype = 101")
    void insertCenterStructure();

    /**
     * 更新中心结构名称
     */
    @Update("UPDATE resourcestructure rs SET resourcestructurename = ss.structurename " +
            "FROM tbl_stationstructure ss WHERE rs.originid = ss.structureid AND rs.structuretypeid = 101")
    void updateCenterStructureName();

    /**
     * 删除中心结构
     */
    @Delete("DELETE FROM resourcestructure WHERE structuretypeid = 101 AND originid NOT IN (SELECT structureid FROM tbl_stationstructure WHERE structuretype = 101)")
    void deleteCenterStructure();

    /**
     * 更新中心结构路径
     */
    @Update("UPDATE resourcestructure SET levelofpath = levelpath FROM tbl_stationstructure WHERE originid = structureid AND structuretypeid = 101")
    void updateCenterStructurePath();

    /**
     * 插入子结构
     */
    @Insert("INSERT INTO resourcestructure (resourcestructureid, structuretypeid, resourcestructurename, parentresourcestructureid, levelofpath, display, sortvalue, originid, originparentid) " +
            "SELECT DISTINCT structureid, 102, structurename, parentstructureid, levelpath, 1, 1, structureid, parentstructureid " +
            "FROM tbl_stationstructure WHERE structuretype = 102")
    void insertSubStructure();

    /**
     * 更新子结构名称
     */
    @Update("UPDATE resourcestructure rs SET resourcestructurename = ss.structurename " +
            "FROM tbl_stationstructure ss WHERE rs.originid = ss.structureid AND rs.structuretypeid = 102")
    void updateSubStructureName();

    /**
     * 删除子结构
     */
    @Delete("DELETE FROM resourcestructure WHERE structuretypeid = 102 AND originid NOT IN (SELECT structureid FROM tbl_stationstructure WHERE structuretype = 102)")
    void deleteSubStructure();

    /**
     * 更新子结构父路径
     */
    @Update("UPDATE resourcestructure SET levelofpath = levelpath FROM tbl_stationstructure WHERE originid = structureid AND structuretypeid = 102")
    void updateSubStructureParentPath();

    /**
     * 更新子结构第二级
     */
    @Update("UPDATE resourcestructure SET levelofpath = levelpath FROM tbl_stationstructure WHERE originid = structureid AND structuretypeid = 102")
    void updateSubStructureSecondLevel();

    /**
     * 插入站点结构
     */
    @Insert("INSERT INTO resourcestructure (resourcestructureid, structuretypeid, resourcestructurename, parentresourcestructureid, levelofpath, display, sortvalue, originid, originparentid) " +
            "SELECT DISTINCT structureid, 103, structurename, parentstructureid, levelpath, 1, 1, structureid, parentstructureid " +
            "FROM tbl_stationstructure WHERE structuretype = 103")
    void insertStationStructure();

    /**
     * 更新站点结构
     */
    @Update("UPDATE resourcestructure rs SET resourcestructurename = ss.structurename " +
            "FROM tbl_stationstructure ss WHERE rs.originid = ss.structureid AND rs.structuretypeid = 103")
    void updateStationStructure();

    /**
     * 删除站点结构
     */
    @Delete("DELETE FROM resourcestructure WHERE structuretypeid = 103 AND originid NOT IN (SELECT structureid FROM tbl_stationstructure WHERE structuretype = 103)")
    void deleteStationStructure();

    /**
     * 更新站点结构路径
     */
    @Update("UPDATE resourcestructure SET levelofpath = levelpath FROM tbl_stationstructure WHERE originid = structureid AND structuretypeid = 103")
    void updateStationStructurePath();

    /**
     * 插入房屋结构
     */
    @Insert("INSERT INTO resourcestructure (resourcestructureid, structuretypeid, resourcestructurename, parentresourcestructureid, levelofpath, display, sortvalue, originid, originparentid) " +
            "SELECT DISTINCT houseid, 105, housename, stationid, CONCAT(rs.levelofpath, '.', houseid), 1, 1, houseid, stationid " +
            "FROM tbl_house h " +
            "JOIN resourcestructure rs ON rs.originid = h.stationid AND rs.structuretypeid = 104")
    void insertHouseStructure();

    /**
     * 更新房屋结构
     */
    @Update("UPDATE resourcestructure rs SET resourcestructurename = h.housename " +
            "FROM tbl_house h WHERE rs.originid = h.houseid AND rs.structuretypeid = 105")
    void updateHouseStructure();

    /**
     * 删除房屋结构
     */
    @Delete("DELETE FROM resourcestructure WHERE structuretypeid = 105 AND originid NOT IN (SELECT houseid FROM tbl_house)")
    void deleteHouseStructure();

    /**
     * 更新房屋结构路径
     */
    @Update("UPDATE resourcestructure rs SET levelofpath = CONCAT(prs.levelofpath, '.', rs.resourcestructureid) " +
            "FROM resourcestructure prs, tbl_house h " +
            "WHERE rs.originid = h.houseid AND rs.structuretypeid = 105 " +
            "AND prs.originid = h.stationid AND prs.structuretypeid = 104")
    void updateHouseStructurePath();

    /**
     * 更新设备资源结构
     */
    @Update("UPDATE tbl_equipment e SET resourcestructureid = rs.resourcestructureid " +
            "FROM resourcestructure rs, tbl_house h " +
            "WHERE e.houseid = h.houseid AND h.houseid = rs.originid AND rs.structuretypeid = 105")
    void updateEquipmentResourceStructure();

    /**
     * 插入配置变更日志
     */
    @Insert("INSERT INTO tbl_configchangemacrolog (objectid, configid, changeoperator, changetime, description) " +
            "VALUES ('-1', 27, 1, NOW(), 'SiteWeb2资源结构同步'), ('-1', 3, 1, NOW(), 'SiteWeb2配置同步')")
    void insertConfigChangeLog();
}
