package com.siteweb.tcs.siteweb.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表达式DTO
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ExpressionDTO {
    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    
    /**
     * 需要解析的表达式
     */
    private String expression;

    /**
     * 是否是跨站监控单元
     */
    private Boolean isCrossStationMonitorUnit;
}
