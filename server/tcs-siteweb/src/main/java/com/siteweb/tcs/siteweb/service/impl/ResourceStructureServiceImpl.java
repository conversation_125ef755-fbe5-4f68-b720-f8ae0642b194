package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.ResourceStructureMapper;
import com.siteweb.tcs.siteweb.dto.ResourceStructureDTO;
import com.siteweb.tcs.siteweb.dto.StationDTO;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.entity.House;
import com.siteweb.tcs.siteweb.entity.Station;
import com.siteweb.tcs.siteweb.entity.StationStructure;
import com.siteweb.tcs.siteweb.entity.StationStructureMap;
import com.siteweb.tcs.siteweb.entity.SysConfig;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import com.siteweb.tcs.siteweb.enums.StructureTypeEnum;
import com.siteweb.tcs.siteweb.enums.SysConfigEnum;
import com.siteweb.tcs.siteweb.service.*;
import com.siteweb.tcs.siteweb.util.I18n;
import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Resource Structure Service Implementation
 */
@Slf4j
@Service
public class ResourceStructureServiceImpl extends IsolatedServiceImpl<ResourceStructureMapper, ResourceStructure> implements IResourceStructureService {

    @Autowired
    private ResourceStructureMapper resourceStructureMapper;

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private IOperationDetailService operationDetailService;

    @Autowired
    private I18n i18n;

    @Autowired
    private IDataItemService dataItemService;

    @Autowired
    private IStationService stationService;

    @Autowired
    private IStationStructureMapService stationStructureMapService;

    @Autowired
    private IStationStructureService stationStructureService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IHouseService houseService;

    @Value("${resource.structure.generate.uuid:false}")
    private boolean generateUUID;

    // private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public ResourceStructure getStructureByID(Integer structureId) {
        if (structureId == null) {
            return null;
        }
        return getById(structureId);
    }

    @Override
    public ResourceStructure getRootStructure() {
        // Assuming root is identified by parentResourceStructureId being null or a specific value (e.g., 0)
        // Adjust this logic if root identification is different (e.g., a specific ID like 1)
        return getOne(new LambdaQueryWrapper<ResourceStructure>()
                .and(wrapper -> wrapper
                        .isNull(ResourceStructure::getParentResourceStructureId)
                        .or()
                        .eq(ResourceStructure::getParentResourceStructureId, 0)
                )
                .last("LIMIT 1"));
    }

    @Override
    public List<ResourceStructure> getAllStructures() {
        return list(); // Simply returns all records from the table
    }

    @Override
    public List<ResourceStructure> getChildrenByParentId(Integer parentId) {
        if (parentId == null) {
            return List.of(); // Or handle as an error/empty list depending on requirements
        }
        return list(new LambdaQueryWrapper<ResourceStructure>()
                .eq(ResourceStructure::getParentResourceStructureId, parentId)
                .orderByAsc(ResourceStructure::getSortValue)); // Assuming children should be sorted
    }

    @Override
    public Integer getTreeRootId() {
        QueryWrapper<ResourceStructure> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parentresourcestructureid", 0);
        queryWrapper.select("resourcestructureid");
        try {
            ResourceStructure resourceStructure = baseMapper.selectOne(queryWrapper);
            return resourceStructure.getResourceStructureId();
        } catch (Exception e) {
            log.error("Query resource structure tree root error: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public ResourceStructure findByOriginIdAndStructureType(Integer originId, Integer structureType) {
        if (originId == null || structureType == null) {
            return null;
        }

        return getOne(new LambdaQueryWrapper<ResourceStructure>()
                .eq(ResourceStructure::getOriginId, originId)
                .eq(ResourceStructure::getStructureTypeId, structureType)
                .last("LIMIT 1"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByID(Integer resourceStructureId) {
        if (resourceStructureId == null) {
            log.warn("Cannot delete resource structure: ID is null");
            return false;
        }

        try {
            // First check if the resource structure exists
            ResourceStructure resourceStructure = getById(resourceStructureId);
            if (resourceStructure == null) {
                log.warn("Resource structure not found with ID: {}", resourceStructureId);
                return false;
            }

            // Delete the resource structure
            boolean result = removeById(resourceStructureId);
            if (result) {
                log.info("Resource structure deleted successfully: {}", resourceStructureId);
            } else {
                log.warn("Failed to delete resource structure: {}", resourceStructureId);
            }

            return result;
        } catch (Exception e) {
            log.error("Error deleting resource structure: {}", e.getMessage(), e);
            throw e; // Re-throw to trigger transaction rollback
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ResourceStructure resourceStructure) {
        if (resourceStructure == null || resourceStructure.getResourceStructureId() == null) {
            log.warn("Cannot update resource structure: entity or ID is null");
            return false;
        }

        try {
            // First check if the resource structure exists
            ResourceStructure existingStructure = getById(resourceStructure.getResourceStructureId());
            if (existingStructure == null) {
                log.warn("Resource structure not found with ID: {}", resourceStructure.getResourceStructureId());
                return false;
            }

            // Update the resource structure
            boolean result = updateById(resourceStructure);
            if (result) {
                log.info("Resource structure updated successfully: {}", resourceStructure.getResourceStructureId());
            } else {
                log.warn("Failed to update resource structure: {}", resourceStructure.getResourceStructureId());
            }

            return result;
        } catch (Exception e) {
            log.error("Error updating resource structure: {}", e.getMessage(), e);
            throw e; // Re-throw to trigger transaction rollback
        }
    }

    @Override
    public List<ResourceStructure> findByParentResourceStructureId(Integer parentResourceStructureId) {
        if (parentResourceStructureId == null) {
            log.warn("Cannot find resource structures: parent resource structure ID is null");
            return List.of();
        }

        return list(new LambdaQueryWrapper<ResourceStructure>()
                .eq(ResourceStructure::getParentResourceStructureId, parentResourceStructureId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(ResourceStructure resourceStructure) {
        try {
            // 如果 resourceStructureId 为 null 或 0，则生成一个新的 ID
            if (resourceStructure.getResourceStructureId() == null || resourceStructure.getResourceStructureId() == 0) {
                Integer resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
                resourceStructure.setResourceStructureId(resourceStructureId);
            }

            // 主动清空 sceneId
            resourceStructure.setSceneId(null);

            // 设置层级路径
            String levelOfPath = buildLevelOfPath(resourceStructure.getParentResourceStructureId(), resourceStructure.getResourceStructureId());
            resourceStructure.setLevelOfPath(levelOfPath);

            // 如果 generateUUID 为 true，则设置 extendedField 字段
            // 设置guid
            if (generateUUID) {
                JsonNode extValue = resourceStructure.getExtendedField();
                ObjectMapper mapper = new ObjectMapper();

                ObjectNode newValue = mapper.createObjectNode();
                newValue.put("guid", UUID.randomUUID().toString());

                ArrayNode arrayNode;
                if (extValue != null && extValue.isArray()) {
                    arrayNode = (ArrayNode) extValue;
                } else {
                    arrayNode = mapper.createArrayNode();
                }

                arrayNode.add(newValue);

                resourceStructure.setExtendedField(arrayNode);
            }

            // 插入资源结构
            int result = resourceStructureMapper.insert(resourceStructure);

            if (result > 0) {
                // 发送创建事件
                changeEventService.sendCreate(resourceStructure);

                // 记录操作日志todo xsx 权限后面要测试
                operationDetailService.recordOperationLog(resourceStructure.getResourceStructureId().toString(), OperationObjectTypeEnum.RESOURCE_STRUCTURE, i18n.T("resource.structure.name"), i18n.T("add"), "", resourceStructure.getResourceStructureName());

                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("Failed to create resource structure: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ResourceStructure findResourceStructureByOriginIdAndParentIdAndStructureTypeId(Integer originId, Integer parentId, Integer structureTypeId) {
        return resourceStructureMapper.selectOne(Wrappers.lambdaQuery(ResourceStructure.class)
                .eq(ResourceStructure::getOriginId, originId)
                .eq(ResourceStructure::getOriginParentId, parentId)
                .eq(ResourceStructure::getStructureTypeId, structureTypeId));
    }

    /**
     * 构建层级路径
     *
     * @param parentResourceStructureId 父资源结构ID
     * @param resourceStructureId 当前资源结构ID
     * @return 层级路径
     */
    private String buildLevelOfPath(Integer parentResourceStructureId, Integer resourceStructureId) {
        if (parentResourceStructureId == null || parentResourceStructureId == 0) {
            // 根节点
            return resourceStructureId.toString();
        }

        // 获取父节点的层级路径
        ResourceStructure parentStructure = getById(parentResourceStructureId);
        if (parentStructure == null) {
            // 父节点不存在，返回当前节点ID
            return resourceStructureId.toString();
        }

        String parentLevelOfPath = parentStructure.getLevelOfPath();
        if (parentLevelOfPath == null || parentLevelOfPath.trim().isEmpty()) {
            // 父节点没有层级路径，使用父节点ID
            return parentResourceStructureId + "." + resourceStructureId;
        }

        // 追加到父节点的层级路径
        return parentLevelOfPath + "." + resourceStructureId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Station createDefaultStation(ResourceStructureDTO resourceStructureDTO) {
        // 查找中心数据项
        List<DataItem> centerDataItem = dataItemService.findByEntryId(DataEntryEnum.DATA_ENTRY);
        if (centerDataItem.isEmpty()) {
            throw new RuntimeException("中心数据项不存在");
        }

        if (resourceStructureDTO.getStation() == null) {
            throw new RuntimeException("局站信息不存在");
        }

        StationDTO stationDTO = resourceStructureDTO.getStation();
        Station station = new Station();
        BeanUtil.copyProperties(stationDTO, station);
        station.setCenterId(centerDataItem.get(0).getItemId());
        station.setUpdateTime(java.time.LocalDateTime.now());

        // 查询系统配置
        SysConfig sysConfig = sysConfigService.findByKey(SysConfigEnum.STANDAR_CATEGORY);
        if (sysConfig != null && "2".equals(sysConfig.getConfigValue())) {
            station.setStationCategory(101);
        } else if (station.getStationCategory() == null) {
            station.setStationCategory(1);
        }

        if (station.getStationGrade() == null) {
            station.setStationGrade(1);
        }
        if (station.getStationName() == null) {
            station.setStationName(resourceStructureDTO.getResourceStructureName());
        }

        // 设置默认值
        station.setStationState(1);
        station.setEnable(true);
        station.setConnectState(2);
        station.setContainNode(false);

        stationService.create(station);

        // 增加station structure map记录
        StationStructureMap stationStructureMap = new StationStructureMap();
        stationStructureMap.setStationId(station.getStationId());

        // StructureId为StructureGroupId = 1 的StationStructure的记录
        if (resourceStructureDTO.getStationStructureId() == null) {
            List<StationStructure> stationStructures = stationStructureService.getStructureByStructureGroupId(1);
            if (!stationStructures.isEmpty()) {
                stationStructureMap.setStructureId(stationStructures.get(0).getStructureId());
            }
        } else {
            stationStructureMap.setStructureId(resourceStructureDTO.getStationStructureId());
        }

        stationStructureMapService.create(stationStructureMap);

        // 记录操作日志 - 假设当前用户ID为-1，实际应该从上下文获取
        operationDetailService.recordOperationLog(station.getStationId().toString(),
                OperationObjectTypeEnum.STATION, i18n.T("station.name"), i18n.T("add"), "", station.getStationName());

        return station;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createStationTemplate(ResourceStructure resourceStructure) {
        // 非电信场景不处理
        if (resourceStructure.getSceneId() == null || resourceStructure.getSceneId() == 1) {
            return false;
        }

        if (resourceStructure.getOriginId() == null) {
            return false;
        }

        Station station = stationService.findByStationId(resourceStructure.getOriginId());
        if (station == null) {
            return false;
        }

        List<House> houses = houseService.findHouseByStationId(station.getStationId());
        if (houses.isEmpty()) {
            return false;
        }

        for (House house : houses) {
            ResourceStructure houseStructure = getResourceStructure(resourceStructure, house, station);
            this.create(houseStructure);
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<ResourceStructure> createStationResourceStructure(Integer stationStructureId) {
        // 查询所有的ResourceStructure
        List<ResourceStructure> resourceStructures = findResourceStructures();

        // 查找根节点，即parentResourceStructureId为0的ResourceStructure
        ResourceStructure rootResourceStructure = resourceStructures.stream()
                .filter(resource -> resource.getParentResourceStructureId() != null
                        && resource.getParentResourceStructureId() == 0)
                .findFirst()
                .orElse(null);

        // 查询所有的局站结构
        List<StationStructure> stationStructureMaps = stationStructureService.list();

        // 查找指定局站及其所有父节点
        List<StationStructure> stationAndParents = findStationAndParents(stationStructureId, stationStructureMaps);

        // 找出所有缺失的ResourceStructure，即局站结构中未在ResourceStructure中存在的节点
        List<StationStructure> missingStructures = new ArrayList<>(stationAndParents.stream()
                .filter(stationStructure -> resourceStructures.stream()
                        .noneMatch(resource -> Objects.equals(resource.getOriginId(), stationStructure.getStructureId()) && resource.getStructureTypeId() == 103))
                .filter(stationStructure -> !Objects.equals(stationStructure.getParentStructureId(), 0))
                .toList());

        // 按照父子关系对缺失的局站结构进行排序，确保父节点在前
        missingStructures.sort((a, b) -> {
            if (Objects.equals(a.getParentStructureId(), b.getStructureId())) {
                return 1;
            }
            if (Objects.equals(b.getParentStructureId(), a.getStructureId())) {
                return -1;
            }
            return 0;
        });

        // 用于存储新创建的ResourceStructure
        List<ResourceStructure> createdResources = new ArrayList<>();

        // 遍历缺失的局站结构，创建对应的ResourceStructure
        return missingStructures.stream().map(stationStructure -> {
            ResourceStructure newResource = new ResourceStructure();
            Integer resourceStructureId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.RESOURCE_STRUCTURE, 0);
            newResource.setResourceStructureId(resourceStructureId);
            newResource.setStructureTypeId(103);
            newResource.setResourceStructureName(stationStructure.getStructureName());

            // 查找父级ResourceStructure的ID，如果找不到则使用根节点作为父级
            Integer parentId = findParentResourceStructureId(stationStructure.getParentStructureId(),
                    Stream.concat(resourceStructures.stream(), createdResources.stream())
                            .collect(Collectors.toList()));
            if (parentId == null) {
                newResource.setParentResourceStructureId(rootResourceStructure.getResourceStructureId());
            } else {
                newResource.setParentResourceStructureId(parentId);
            }

            newResource.setPhoto(null);
            newResource.setPosition(null);

            // 构建路径层级信息
            String levelOfPath = buildLevelOfPath(stationStructure.getParentStructureId(), rootResourceStructure.getResourceStructureId(),
                    Stream.concat(resourceStructures.stream(), createdResources.stream())
                            .collect(Collectors.toList()));
            newResource.setLevelOfPath(levelOfPath);

            newResource.setDisplay(true);
            newResource.setSortValue(1);
            newResource.setOriginId(stationStructure.getStructureId());
            newResource.setOriginParentId(stationStructure.getParentStructureId());

            // 将新创建的ResourceStructure添加到临时列表中
            createdResources.add(newResource);
            return newResource;
        }).collect(Collectors.toList());
    }

    /**
     * 递归查找父节点的 LevelOfPath，并拼接当前节点。
     *
     * 该函数通过递归查找给定站点的父节点，并构建其 LevelOfPath 路径。路径由根节点和当前节点的 ID 组成，使用点号（.）进行拼接。
     *
     * @param stationStructureId 当前站点的结构 ID，用于查找对应的资源结构。
     * @param rootResourceStructureId 根资源结构的 ID，作为路径的起始点。
     * @param resourceStructures 资源结构列表，包含所有可能的资源结构信息。
     * @return 返回拼接后的 LevelOfPath 路径，格式为 "rootResourceStructureId.currentResourceStructureId"。
     */
    private String buildLevelOfPath(Integer stationStructureId, Integer rootResourceStructureId, List<ResourceStructure> resourceStructures) {
        // 初始化路径段列表，并添加根节点 ID 作为路径的起始点
        List<String> pathSegments = new ArrayList<>();
        pathSegments.add(rootResourceStructureId.toString());

        // 在资源结构列表中查找与当前站点 ID 和结构类型匹配的资源结构
        ResourceStructure current = resourceStructures.stream()
                .filter(resource -> Objects.equals(resource.getOriginId(), stationStructureId) && resource.getStructureTypeId() == 103)
                .findFirst()
                .orElse(null);

        // 如果找到匹配的资源结构，将其 ID 添加到路径段列表中
        if (current != null) {
            pathSegments.add(current.getResourceStructureId().toString());
        }

        // 将路径段列表中的元素用点号拼接成完整的 LevelOfPath 路径
        return String.join(".", pathSegments);
    }

    // 根据父级，查找父级在 resourceStructures 中的位置
    private Integer findParentResourceStructureId(Integer parentStructureId, List<ResourceStructure> resourceStructures) {
        return resourceStructures.stream()
                .filter(resource -> Objects.equals(resource.getOriginId(), parentStructureId) && resource.getStructureTypeId() == 103)
                .map(ResourceStructure::getResourceStructureId)
                .findFirst()
                .orElse(null); // 如果父级不存在于 resourceStructures 中，则返回 null
    }

    /**
     * 递归查找指定站点结构及其所有父级站点结构，包括当前节点。
     *
     * @param stationStructureId 要查找的站点结构的ID
     * @param allStructures 所有站点结构的列表，用于查找父级节点
     * @return 包含指定站点结构及其所有父级站点结构的列表，按从子节点到父节点的顺序排列
     */
    private List<StationStructure> findStationAndParents(Integer stationStructureId, List<StationStructure> allStructures) {
        List<StationStructure> result = new ArrayList<>();

        // 查找当前节点
        StationStructure current = allStructures.stream()
                .filter(station -> station.getStructureId().equals(stationStructureId))
                .findFirst()
                .orElse(null);

        // 从当前节点开始，逐级向上查找父节点，直到没有父节点为止
        while (current != null) {
            result.add(current);

            // 查找当前节点的父级节点
            StationStructure finalCurrent = current;
            current = allStructures.stream()
                    .filter(station -> station.getStructureId().equals(finalCurrent.getParentStructureId()))
                    .findFirst()
                    .orElse(null);
        }

        return result;
    }

    @Override
    public List<ResourceStructure> findResourceStructures() {
//        return resourceStructureCache.values()
//                .access()
//                .sorted(Comparator.comparing(ResourceStructure::getSortValue).thenComparing(ResourceStructure::getResourceStructureName))
//                .toList();
        return resourceStructureMapper.selectList(Wrappers.lambdaQuery(ResourceStructure.class)
                .orderByAsc(ResourceStructure::getSortValue)
                .orderByAsc(ResourceStructure::getResourceStructureName));
    }

    @Override
    public boolean batchInsert(List<ResourceStructure> resourceStructures) {
        if (CollectionUtil.isEmpty(resourceStructures)) {
            log.warn("Cannot batch insert: resource structures list is null or empty");
            return false;
        }

        try {
            // 使用MyBatis Plus的批量插入方法
            boolean result = saveBatch(resourceStructures);
            
            if (result) {
                log.info("Successfully batch inserted {} resource structures", resourceStructures.size());
                // 发送变更事件
                for (ResourceStructure structure : resourceStructures) {
                    changeEventService.sendCreate(structure);
                }
            } else {
                log.error("Failed to batch insert resource structures");
            }
            
            return result;
        } catch (Exception e) {
            log.error("Error during batch insert of resource structures", e);
            throw e;
        }
    }

    private static ResourceStructure getResourceStructure(ResourceStructure resourceStructure, House house, Station station) {
        ResourceStructure houseStructure = new ResourceStructure();
        houseStructure.setResourceStructureName(house.getHouseName());
        houseStructure.setParentResourceStructureId(resourceStructure.getResourceStructureId());
        houseStructure.setSortValue(1);
        houseStructure.setOriginId(house.getHouseId());
        houseStructure.setOriginParentId(station.getStationId());
        houseStructure.setSceneId(2);
        houseStructure.setDisplay(true);
        houseStructure.setLevelOfPath(resourceStructure.getLevelOfPath());
        houseStructure.setStructureTypeId(StructureTypeEnum.STATION_HOUSE.getValue());
        return houseStructure;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeResourceStructureHierarchy(Integer resourceStructureId, Integer newParentId) {
        if (resourceStructureId == null) {
            log.warn("Cannot change hierarchy: resource structure ID is null");
            return false;
        }

        try {
            // 1. 验证层级关系是否合法
            if (!validateHierarchyChange(resourceStructureId, newParentId)) {
                log.warn("Invalid hierarchy change: resourceStructureId={}, newParentId={}", resourceStructureId, newParentId);
                return false;
            }

            // 2. 获取要移动的资源结构
            ResourceStructure resourceStructure = getById(resourceStructureId);
            if (resourceStructure == null) {
                log.warn("Resource structure not found: {}", resourceStructureId);
                return false;
            }

            // 3. 获取新的父级资源结构（如果newParentId不为null）
            ResourceStructure newParentStructure = null;
            if (newParentId != null) {
                newParentStructure = getById(newParentId);
                if (newParentStructure == null) {
                    log.warn("New parent resource structure not found: {}", newParentId);
                    return false;
                }
            }

            // 4. 更新父级ID
            Integer oldParentId = resourceStructure.getParentResourceStructureId();
            resourceStructure.setParentResourceStructureId(newParentId);

            // 5. 构建新的LevelOfPath
            String newLevelOfPath = buildLevelOfPath(newParentId, resourceStructureId);
            resourceStructure.setLevelOfPath(newLevelOfPath);

            // 6. 更新资源结构
            boolean updateResult = updateById(resourceStructure);
            if (!updateResult) {
                log.error("Failed to update resource structure: {}", resourceStructureId);
                return false;
            }

            // 7. 递归更新所有子节点的LevelOfPath
            updateResourceStructurePath(resourceStructureId, newLevelOfPath);

            // 8. 同步更新相关表数据
            syncRelatedTableData(resourceStructure, newParentId);

            // 9. 记录操作日志
            String operationDesc = String.format("移动资源结构 %s 到 %s",
                    resourceStructure.getResourceStructureName(),
                    newParentStructure != null ? newParentStructure.getResourceStructureName() : "根节点");
            operationDetailService.recordOperationLog(
                    resourceStructureId.toString(),
                    OperationObjectTypeEnum.RESOURCE_STRUCTURE,
                    i18n.T("resource.structure.name"),
                    i18n.T("modify"),
                    oldParentId != null ? oldParentId.toString() : "",
                    operationDesc
            );

            log.info("Successfully changed hierarchy: resourceStructureId={}, newParentId={}", resourceStructureId, newParentId);
            return true;

        } catch (Exception e) {
            log.error("Error changing resource structure hierarchy: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateResourceStructurePath(Integer resourceStructureId, String newLevelOfPath) {
        if (resourceStructureId == null || newLevelOfPath == null) {
            log.warn("Cannot update path: resourceStructureId or newLevelOfPath is null");
            return false;
        }

        try {
            // 1. 更新当前节点的LevelOfPath
            ResourceStructure currentStructure = getById(resourceStructureId);
            if (currentStructure == null) {
                log.warn("Resource structure not found: {}", resourceStructureId);
                return false;
            }

            currentStructure.setLevelOfPath(newLevelOfPath);
            boolean updateResult = updateById(currentStructure);
            if (!updateResult) {
                log.error("Failed to update resource structure path: {}", resourceStructureId);
                return false;
            }

            // 2. 递归更新所有子节点
            List<ResourceStructure> children = getChildrenByParentId(resourceStructureId);
            for (ResourceStructure child : children) {
                String childNewPath = newLevelOfPath + "." + child.getResourceStructureId();
                updateResourceStructurePath(child.getResourceStructureId(), childNewPath);
            }

            log.info("Successfully updated resource structure path: resourceStructureId={}, newPath={}",
                    resourceStructureId, newLevelOfPath);
            return true;

        } catch (Exception e) {
            log.error("Error updating resource structure path: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncRelatedTableData(ResourceStructure resourceStructure, Integer newParentId) {
        if (resourceStructure == null) {
            log.warn("Cannot sync related table data: resource structure is null");
            return false;
        }

        try {
            Integer structureTypeId = resourceStructure.getStructureTypeId();

            // 根据资源结构类型同步不同的表
            switch (structureTypeId) {
                case 103: // 片区
                    return syncStationStructureData(resourceStructure, newParentId);
                case 104: // 基站
                    return syncStationData(resourceStructure, newParentId);
                case 105: // 局房
                    return syncHouseData(resourceStructure, newParentId);
                default:
                    log.info("No related table data to sync for structure type: {}", structureTypeId);
                    return true;
            }

        } catch (Exception e) {
            log.error("Error syncing related table data: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 同步片区相关表数据
     */
    private boolean syncStationStructureData(ResourceStructure resourceStructure, Integer newParentId) {
        try {
            Integer originId = resourceStructure.getOriginId();
            if (originId == null) {
                log.warn("Origin ID is null for station structure: {}", resourceStructure.getResourceStructureId());
                return false;
            }

            // 更新tbl_stationstructure表
            StationStructure stationStructure = stationStructureService.getById(originId);
            if (stationStructure == null) {
                log.warn("Station structure not found: {}", originId);
                return false;
            }

            // 计算新的ParentStructureId
            Integer newParentStructureId = null;
            if (newParentId != null) {
                // 查找新父级对应的StationStructure
                ResourceStructure newParentResource = getById(newParentId);
                if (newParentResource != null && newParentResource.getStructureTypeId() == 103) {
                    newParentStructureId = newParentResource.getOriginId();
                }
            }

            // 更新ParentStructureId
            stationStructure.setParentStructureId(newParentStructureId);

            // 更新LevelPath
            String newLevelPath = buildStationStructureLevelPath(newParentStructureId, originId);
            stationStructure.setLevelPath(newLevelPath);

            // 保存更新
            boolean updateResult = stationStructureService.updateById(stationStructure);
            if (!updateResult) {
                log.error("Failed to update station structure: {}", originId);
                return false;
            }

            // 递归更新子节点的LevelPath
            updateStationStructureChildrenPath(originId, newLevelPath);

            log.info("Successfully synced station structure data: originId={}, newParentStructureId={}",
                    originId, newParentStructureId);
            return true;

        } catch (Exception e) {
            log.error("Error syncing station structure data: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 同步基站相关表数据
     */
    private boolean syncStationData(ResourceStructure resourceStructure, Integer newParentId) {
        try {
            Integer originId = resourceStructure.getOriginId();
            if (originId == null) {
                log.warn("Origin ID is null for station: {}", resourceStructure.getResourceStructureId());
                return false;
            }

            // 更新tbl_stationstructuremap表
            StationStructureMap stationStructureMap = stationStructureMapService.getById(originId);
            if (stationStructureMap == null) {
                log.warn("Station structure map not found: {}", originId);
                return false;
            }

            // 计算新的StructureId
            Integer newStructureId = null;
            if (newParentId != null) {
                // 查找新父级对应的StationStructure
                ResourceStructure newParentResource = getById(newParentId);
                if (newParentResource != null && newParentResource.getStructureTypeId() == 103) {
                    newStructureId = newParentResource.getOriginId();
                }
            }

            // 更新StructureId
            stationStructureMap.setStructureId(newStructureId);

            // 保存更新
            boolean updateResult = stationStructureMapService.updateById(stationStructureMap);
            if (!updateResult) {
                log.error("Failed to update station structure map: {}", originId);
                return false;
            }

            log.info("Successfully synced station data: originId={}, newStructureId={}",
                    originId, newStructureId);
            return true;

        } catch (Exception e) {
            log.error("Error syncing station data: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 同步局房相关表数据
     */
    private boolean syncHouseData(ResourceStructure resourceStructure, Integer newParentId) {
        try {
            Integer originId = resourceStructure.getOriginId();
            if (originId == null) {
                log.warn("Origin ID is null for house: {}", resourceStructure.getResourceStructureId());
                return false;
            }

            // 更新tbl_house表
            House house = houseService.getById(originId);
            if (house == null) {
                log.warn("House not found: {}", originId);
                return false;
            }

            // 计算新的StationId
            Integer newStationId = null;
            if (newParentId != null) {
                // 查找新父级对应的Station
                ResourceStructure newParentResource = getById(newParentId);
                if (newParentResource != null && newParentResource.getStructureTypeId() == 104) {
                    newStationId = newParentResource.getOriginId();
                }
            }

            // 更新StationId
            house.setStationId(newStationId);

            // 保存更新
            boolean updateResult = houseService.updateById(house);
            if (!updateResult) {
                log.error("Failed to update house: {}", originId);
                return false;
            }

            log.info("Successfully synced house data: originId={}, newStationId={}",
                    originId, newStationId);
            return true;

        } catch (Exception e) {
            log.error("Error syncing house data: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getResourceStructureFullPath(Integer resourceStructureId) {
        if (resourceStructureId == null) {
            return "";
        }

        try {
            ResourceStructure resourceStructure = getById(resourceStructureId);
            if (resourceStructure == null) {
                return "";
            }

            return resourceStructure.getLevelOfPath() != null ? resourceStructure.getLevelOfPath() : "";

        } catch (Exception e) {
            log.error("Error getting resource structure full path: {}", e.getMessage(), e);
            return "";
        }
    }

    @Override
    public boolean validateHierarchyChange(Integer resourceStructureId, Integer newParentId) {
        if (resourceStructureId == null) {
            return false;
        }

        try {
            // 1. 检查不能移动到自己的子节点下
            if (newParentId != null && isDescendant(resourceStructureId, newParentId)) {
                log.warn("Cannot move resource structure to its own descendant: resourceStructureId={}, newParentId={}",
                        resourceStructureId, newParentId);
                return false;
            }

            // 2. 检查不能移动到根节点（如果当前就是根节点）
            ResourceStructure currentStructure = getById(resourceStructureId);
            if (currentStructure == null) {
                return false;
            }

            // 3. 检查业务规则
            Integer structureTypeId = currentStructure.getStructureTypeId();
            if (newParentId != null) {
                ResourceStructure newParentStructure = getById(newParentId);
                if (newParentStructure == null) {
                    return false;
                }

                Integer newParentStructureTypeId = newParentStructure.getStructureTypeId();

                // 业务规则验证
                switch (structureTypeId) {
                    case 103: // 片区
                        // 片区可以属于园区(2)或其他片区(103)
                        if (newParentStructureTypeId != 2 && newParentStructureTypeId != 103) {
                            log.warn("Invalid parent for area: structureTypeId={}, newParentStructureTypeId={}",
                                    structureTypeId, newParentStructureTypeId);
                            return false;
                        }
                        break;
                    case 104: // 基站
                        // 基站可以属于片区(103)
                        if (newParentStructureTypeId != 103) {
                            log.warn("Invalid parent for station: structureTypeId={}, newParentStructureTypeId={}",
                                    structureTypeId, newParentStructureTypeId);
                            return false;
                        }
                        break;
                    case 105: // 局房
                        // 局房可以属于基站(104)
                        if (newParentStructureTypeId != 104) {
                            log.warn("Invalid parent for house: structureTypeId={}, newParentStructureTypeId={}",
                                    structureTypeId, newParentStructureTypeId);
                            return false;
                        }
                        break;
                    default:
                        log.warn("Unknown structure type: {}", structureTypeId);
                        return false;
                }
            }

            return true;

        } catch (Exception e) {
            log.error("Error validating hierarchy change: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查resourceStructureId是否是descendantId的祖先节点
     */
    private boolean isDescendant(Integer resourceStructureId, Integer descendantId) {
        if (resourceStructureId == null || descendantId == null) {
            return false;
        }

        try {
            // 递归向上查找，看是否能找到resourceStructureId
            Integer currentId = descendantId;
            while (currentId != null) {
                if (currentId.equals(resourceStructureId)) {
                    return true;
                }

                ResourceStructure currentStructure = getById(currentId);
                if (currentStructure == null) {
                    break;
                }

                currentId = currentStructure.getParentResourceStructureId();
            }

            return false;

        } catch (Exception e) {
            log.error("Error checking descendant relationship: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建StationStructure的LevelPath
     */
    private String buildStationStructureLevelPath(Integer parentStructureId, Integer structureId) {
        if (parentStructureId == null || parentStructureId == 0) {
            return structureId.toString();
        }

        // 获取父节点的LevelPath
        StationStructure parentStructure = stationStructureService.getById(parentStructureId);
        if (parentStructure == null) {
            return structureId.toString();
        }

        String parentLevelPath = parentStructure.getLevelPath();
        if (parentLevelPath == null || parentLevelPath.trim().isEmpty()) {
            return parentStructureId + "." + structureId;
        }

        return parentLevelPath + "." + structureId;
    }

    /**
     * 递归更新StationStructure子节点的LevelPath
     */
    private void updateStationStructureChildrenPath(Integer parentStructureId, String parentLevelPath) {
        try {
            // 查找所有子节点
            List<StationStructure> children = stationStructureService.list(
                    new LambdaQueryWrapper<StationStructure>()
                            .eq(StationStructure::getParentStructureId, parentStructureId)
            );

            for (StationStructure child : children) {
                String childLevelPath = parentLevelPath + "." + child.getStructureId();
                child.setLevelPath(childLevelPath);
                stationStructureService.updateById(child);

                // 递归更新子节点的子节点
                updateStationStructureChildrenPath(child.getStructureId(), childLevelPath);
            }

        } catch (Exception e) {
            log.error("Error updating station structure children path: {}", e.getMessage(), e);
        }
    }

}
