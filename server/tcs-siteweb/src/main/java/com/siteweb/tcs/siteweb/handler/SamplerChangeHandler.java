package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.Sampler;
import com.siteweb.tcs.siteweb.service.IMonitorUnitStateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 采集器数据变更处理器
 *
 * <AUTHOR> (2024-04-12)
 **/
@Slf4j
@Component
public class SamplerChangeHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private IMonitorUnitStateService monitorUnitStateService;

    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(Sampler.class);
    }

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        Sampler sampler = changeRecord.readMessageBody(Sampler.class);
        // 创建采集器无需更新
        // monitorUnitStateService.updateSampler(sampler.getSamplerId());
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        Sampler sampler = changeRecord.readMessageBody(Sampler.class);
        monitorUnitStateService.updateSampler(sampler.getSamplerId());
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        Sampler sampler = changeRecord.readMessageBody(Sampler.class);
        monitorUnitStateService.updateSampler(sampler.getSamplerId());
    }
}
