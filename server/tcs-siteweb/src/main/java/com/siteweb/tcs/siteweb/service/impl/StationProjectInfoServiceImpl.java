package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.entity.StationProjectInfo;
import com.siteweb.tcs.siteweb.mapper.StationProjectInfoMapper;
import com.siteweb.tcs.siteweb.service.IStationProjectInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Station Project Info Service Implementation
 */
@Slf4j
@Service
public class StationProjectInfoServiceImpl extends IsolatedServiceImpl<StationProjectInfoMapper, StationProjectInfo> implements IStationProjectInfoService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StationProjectInfo findOrCreateStationProjectInfo(Integer stationId, String projectName, String contractNo) {
        if (stationId == null) {
            log.warn("Cannot find or create station project info: station ID is null");
            return null;
        }

        try {
            // 查找现有的站点项目信息
            StationProjectInfo projectInfo = getById(stationId);

            if (projectInfo == null) {
                // 创建新的站点项目信息
                projectInfo = new StationProjectInfo();
                projectInfo.setStationId(stationId);
                projectInfo.setProjectName(projectName);
                projectInfo.setContractNo(contractNo);
                save(projectInfo);
            } else {
                // 更新现有的站点项目信息
                boolean needUpdate = false;

                if (projectName != null && !projectName.equals(projectInfo.getProjectName())) {
                    projectInfo.setProjectName(projectName);
                    needUpdate = true;
                }

                if (contractNo != null && !contractNo.equals(projectInfo.getContractNo())) {
                    projectInfo.setContractNo(contractNo);
                    needUpdate = true;
                }

                if (needUpdate) {
                    updateById(projectInfo);
                }
            }

            return projectInfo;
        } catch (Exception e) {
            log.error("Failed to find or create station project info: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectNameAndContractNo(int stationId, String projectName, String ContractNo) {
        baseMapper.update(Wrappers.lambdaUpdate(StationProjectInfo.class)
                .eq(StationProjectInfo::getStationId, stationId)
                .set(StationProjectInfo::getProjectName, projectName)
                .set(StationProjectInfo::getContractNo, ContractNo));
    }
}
