package com.siteweb.tcs.siteweb.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.tcs.siteweb.entity.EventCondition;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 事件配置项DTO
 */
@Data
public class EventConfigItem {
    
    /**
     * ID
     */
    private Integer id;
    
    /**
     * 事件ID
     */
    private Integer eventId;
    
    /**
     * 事件名称
     */
    private String eventName;
    
    /**
     * 设备模板ID
     */
    private Integer equipmentTemplateId;
    
    /**
     * 信号ID
     */
    private Integer signalId;
    
    /**
     * 启动表达式
     */
    private String startExpression;

    /**
     * 启动类型
     */
    private Integer startType;

    /**
     * 结束类型
     */
    private Integer endType;

    /**
     * 抑制表达式
     */
    private String suppressExpression;

    /**
     * 事件类别
     */
    private Integer eventCategory;

    /**
     * 启用状态
     */
    private Boolean enable;

    /**
     * 可见状态
     */
    private Boolean visible;

    /**
     * 描述
     */
    private String description;

    /**
     * 显示索引
     */
    private Integer displayIndex;

    /**
     * 模块号
     */
    private Integer moduleNo;

    /**
     * 翻转时间
     */
    private Integer turnover;
    
    /**
     * 事件条件列表
     */
    private List<EventConditionDTO> eventConditionList;

    @JsonIgnore
    public List<EventCondition>  getEventCondition() {
        return  Optional.ofNullable(this.eventConditionList).orElseGet(ArrayList::new).stream()
                .map(eventCondition -> BeanUtil.copyProperties(eventCondition, EventCondition.class))
                .toList();
    }

    @JsonIgnore
    public List<EventConditionDTO>  getEventConditionList(List<EventCondition> EventConditionList) {
//        return  Optional.ofNullable(this.eventConditionList).orElseGet(ArrayList::new).stream()
//                .map(eventCondition -> BeanUtil.copyProperties(eventCondition, EventCondition.class))
//                .toList();
        if(CollectionUtil.isNotEmpty(EventConditionList)){
            List<EventConditionDTO> eventConditionDTOS = Optional.ofNullable(EventConditionList).orElseGet(ArrayList::new).stream()
                    .map(e -> BeanUtil.copyProperties(e, EventConditionDTO.class))
                    .toList();
            return eventConditionDTOS;
        }else{
            return Collections.EMPTY_LIST;
        }
    }
}
