package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Station base map entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("tbl_stationbasemap")
public class StationBaseMap implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("StationBaseType")
    private Integer stationBaseType;

    @TableField("StationCategory")
    private Integer stationCategory;

    @TableId(value = "StandardType")
    private Integer standardType;
}
