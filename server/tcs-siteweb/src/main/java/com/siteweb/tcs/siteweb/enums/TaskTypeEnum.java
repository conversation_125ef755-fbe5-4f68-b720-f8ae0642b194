package com.siteweb.tcs.siteweb.enums;

/**
 * 任务类型枚举
 * 对应原来的WebSocketBusinessTypeEnum
 */
public enum TaskTypeEnum {

    /**
     * 配置生成
     */
    CONFIGURATION_GENERATION("CONFIGURATION_GENERATION", "配置生成"),

    /**
     * 配置下发
     */
    CONFIGURATION_DISTRIBUTION("CONFIGURATION_DISTRIBUTION", "配置下发"),

    /**
     * 配置备份
     */
    CONFIGURATION_BACKUP("CONFIGURATION_BACKUP", "配置备份"),

    /**
     * 配置导入
     */
    CONFIGURATION_IMPORT("CONFIGURATION_IMPORT", "配置导入"),

    /**
     * 配置拉取
     */
    CONFIGURATION_PULL("CONFIGURATION_PULL", "配置拉取"),

    /**
     * Telnet连接
     */
    TELNET_CONNECTION("TELNET_CONNECTION", "Telnet连接"),

    /**
     * 文件上传
     */
    FILE_UPLOAD("FILE_UPLOAD", "文件上传"),

    /**
     * 文件下载
     */
    FILE_DOWNLOAD("FILE_DOWNLOAD", "文件下载"),

    /**
     * 系统操作
     */
    SYSTEM_OPERATION("SYSTEM_OPERATION", "系统操作");

    private final String code;
    private final String description;

    TaskTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static TaskTypeEnum fromCode(String code) {
        for (TaskTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown task type code: " + code);
    }
}
