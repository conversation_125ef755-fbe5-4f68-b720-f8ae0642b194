package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 设备子类型实体类 (CMCC)
 */
@Data
@TableName("tbl_devicesubtypecmcc")
public class DeviceSubTypeCmcc implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备类型ID
     */
    @TableField("DeviceTypeID")
    private Integer deviceTypeId;

    /**
     * 设备子类型ID (作为主键处理)
     */
    @TableField("DeviceSubTypeID")
    private Integer deviceSubTypeId;

    /**
     * 设备子类型名称
     */
    @TableField("DeviceSubTypeName")
    private String deviceSubTypeName;

    /**
     * 描述
     */
    @TableField("Description")
    private String description;
}