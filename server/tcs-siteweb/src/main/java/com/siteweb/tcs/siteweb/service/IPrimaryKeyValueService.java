package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.PrimaryKeyValue;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;

/**
 * Primary Key Value Service Interface
 */
public interface IPrimaryKeyValueService extends IService<PrimaryKeyValue> {

    /**
     * Get global identity value for a table
     * Corresponds to stored procedure: PBL_GenerateId
     *
     * @param tableIdentityEnum Table identity enum
     * @param postalCode Postal code
     * @return Generated primary key value
     */
    int getGlobalIdentity(TableIdentityEnum tableIdentityEnum, int postalCode);

    /**
     * Initialize primary key value.
     * In siteweb6-config-server, this initializes TBL_Siganl, TBL_Event, TSL_MonitorUnit.
     * Corresponds to stored procedure: PCT_InitPrimaryKeyValue
     *
     * @param centerId   Center ID (used as postalCode for specific tables)
     * @param defaultValue Default initial value
     */
    void initPrimaryIdentityValue(int centerId, int defaultValue);

    /**
     * 获取全局主键值 (新增方法，用于兼容新的调用方式)
     *
     * @param tableIdentityEnum 表标识枚举
     * @param centerId 中心ID
     * @return 全局主键值
     */
    Integer getGlobalIdentity(TableIdentityEnum tableIdentityEnum, Integer centerId);
}
