package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Event condition entity
 */
@Data
@TableName("tbl_eventcondition")
public class EventCondition implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("EventConditionId")
    private Integer eventConditionId;

    @TableField("EquipmentTemplateId")
    private Integer equipmentTemplateId;

    @TableField("EventId")
    private Integer eventId;

    @TableField("StartOperation")
    private String startOperation;

    @TableField("StartCompareValue")
    private Double startCompareValue;

    @TableField("StartDelay")
    private Integer startDelay;

    @TableField("EndOperation")
    private String endOperation;

    @TableField("EndCompareValue")
    private Double endCompareValue;

    @TableField("EndDelay")
    private Integer endDelay;

    @TableField("Frequency")
    private Integer frequency;

    @TableField("FrequencyThreshold")
    private Integer frequencyThreshold;

    @TableField("Meanings")
    private String meanings;

    @TableField("EquipmentState")
    private Integer equipmentState;

    @TableField("BaseTypeId")
    private Long baseTypeId;

    @TableField("EventSeverity")
    private Integer eventSeverity;

    @TableField("StandardName")
    private Integer standardName;
}
