package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.entity.Station;

import java.util.List;

/**
 * Resource Structure Service Interface
 */
public interface IResourceStructureService extends IService<ResourceStructure> {

    /**
     * Get structure by ID
     *
     * @param structureId Structure ID
     * @return Resource structure
     */
    ResourceStructure getStructureByID(Integer structureId);

    /**
     * Get the root resource structure.
     * The root is typically identified by a null ParentResourceStructureId or a specific predefined ID.
     * @return The root ResourceStructure, or null if not found.
     */
    ResourceStructure getRootStructure();

    /**
     * Get all resource structures.
     * @return A list of all ResourceStructure entities.
     */
    List<ResourceStructure> getAllStructures();

    /**
     * Get child resource structures by parent ID.
     * @param parentId The ID of the parent resource structure.
     * @return A list of child ResourceStructure entities.
     */
    List<ResourceStructure> getChildrenByParentId(Integer parentId);

    /**
     * Get tree root ID
     * @return Root ID
     */
    Integer getTreeRootId();

    /**
     * Find resource structure by origin ID and structure type
     *
     * @param originId Origin ID
     * @param structureType Structure type
     * @return Resource structure
     */
    ResourceStructure findByOriginIdAndStructureType(Integer originId, Integer structureType);

    /**
     * Delete resource structure by ID
     *
     * @param resourceStructureId Resource structure ID
     * @return True if deleted successfully
     */
    boolean deleteByID(Integer resourceStructureId);

    /**
     * Update resource structure
     *
     * @param resourceStructure Resource structure to update
     * @return True if updated successfully
     */
    boolean update(ResourceStructure resourceStructure);

    /**
     * Find resource structures by parent resource structure ID
     *
     * @param parentResourceStructureId Parent resource structure ID
     * @return List of resource structures
     */
    List<ResourceStructure> findByParentResourceStructureId(Integer parentResourceStructureId);

    /**
     * Create resource structure
     *
     * @param resourceStructure Resource structure to create
     * @return True if created successfully
     */
    Boolean create(ResourceStructure resourceStructure);

    ResourceStructure findResourceStructureByOriginIdAndParentIdAndStructureTypeId(Integer houseId, Integer stationId, Integer value);

    /**
     * Create default station for resource structure
     *
     * @param resourceStructureDTO Resource structure DTO
     * @return Created station
     */
    Station createDefaultStation(com.siteweb.tcs.siteweb.dto.ResourceStructureDTO resourceStructureDTO);

    /**
     * Create station template room for resource structure
     *
     * @param resourceStructure Resource structure
     * @return True if created successfully
     */
    boolean createStationTemplate(ResourceStructure resourceStructure);

    List<ResourceStructure> createStationResourceStructure(Integer stationStructureId);

    List<ResourceStructure> findResourceStructures();

    /**
     * 修改资源结构层级关系
     * 支持片区所属片区、局站所属片区的功能
     *
     * @param resourceStructureId 要移动的资源结构ID
     * @param newParentId 新的父级资源结构ID
     * @return 是否修改成功
     */
    boolean changeResourceStructureHierarchy(Integer resourceStructureId, Integer newParentId);

    /**
     * 更新资源结构的LevelOfPath路径
     * 递归更新当前节点及其所有子节点的路径
     *
     * @param resourceStructureId 资源结构ID
     * @param newLevelOfPath 新的路径
     * @return 是否更新成功
     */
    boolean updateResourceStructurePath(Integer resourceStructureId, String newLevelOfPath);

    /**
     * 同步更新相关表数据
     * 根据资源结构类型更新对应的原始表数据
     *
     * @param resourceStructure 资源结构
     * @param newParentId 新的父级ID
     * @return 是否同步成功
     */
    boolean syncRelatedTableData(ResourceStructure resourceStructure, Integer newParentId);

    /**
     * 获取资源结构的完整路径
     * 从根节点到当前节点的完整路径
     *
     * @param resourceStructureId 资源结构ID
     * @return 完整路径字符串
     */
    String getResourceStructureFullPath(Integer resourceStructureId);

    /**
     * 验证层级关系是否合法
     * 检查移动操作是否符合业务规则
     *
     * @param resourceStructureId 要移动的资源结构ID
     * @param newParentId 新的父级资源结构ID
     * @return 是否合法
     */
    boolean validateHierarchyChange(Integer resourceStructureId, Integer newParentId);

    /**
     * 批量插入资源结构
     *
     * @param resourceStructures 资源结构列表
     * @return 是否插入成功
     */
    boolean batchInsert(List<ResourceStructure> resourceStructures);

}
