package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.Station;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Station Mapper
 */
@Mapper
@Repository
public interface StationMapper extends BaseMapper<Station> {

    /**
     * 根据站点ID查找站点（包含项目信息和掩码信息）
     *
     * @param stationId 站点ID
     * @return 站点信息
     */
    Station findStationById(@Param("stationId") Integer stationId);
}
