package com.siteweb.tcs.siteweb.service;

/**
 * SiteWeb2同步服务接口
 * 用于同步SiteWeb2的站点资源结构到TCS系统
 */
public interface ISyncSiteWeb2Service {

    /**
     * 同步SiteWeb2配置
     * 同步站点资源结构，包括：
     * 1. 如果资源结构表为空，调用同步资源结构方法
     * 2. 如果站点表为空，直接返回
     * 3. 过滤掉未分组的站点
     * 4. 找到所有站点的公共父节点
     * 5. 根据公共父节点生成资源结构列表
     * 6. 批量插入资源结构
     * 7. 更新设备的resourceStructureId
     * 8. 记录配置变更日志
     */
    void syncSiteWeb2Config();
}
