package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.dto.StationDTO;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.entity.House;
import com.siteweb.tcs.siteweb.entity.Station;
import com.siteweb.tcs.siteweb.entity.StationStructureMap;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.exception.BusinessException;
import com.siteweb.tcs.siteweb.mapper.StationMapper;
import com.siteweb.tcs.siteweb.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Station Service Implementation
 */
@Slf4j
@Service
public class StationServiceImpl extends IsolatedServiceImpl<StationMapper, Station> implements IStationService {

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private IStationProjectInfoService stationProjectInfoService;

    @Autowired
    private IHouseService houseService;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private StationMapper stationMapper;

    @Autowired
    private IDataItemService dataItemService;

    @Autowired
    private IStationStructureMapService stationStructureMapService;

    @Autowired
    private IOperationDetailService operationDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(Station station) {
        if (station == null) {
            log.warn("Cannot create station: station is null");
            return false;
        }

        try {
            if (station.getStationId() == null || station.getStationId().equals(0)) {
                Integer stationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_STATION, 0);
                station.setStationId(stationId);
            }
            boolean status = stationMapper.insert(station) > 0;
            if (status) {
                // 创建局站项目信息
                stationProjectInfoService.findOrCreateStationProjectInfo(station.getStationId(), station.getProjectName(), station.getContractNo());
                createDefaultHouse(station);
                changeEventService.sendCreate(station);
            }
            return status;
        } catch (Exception e) {
            log.error("Failed to create station: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建默认房屋
     *
     * @param station 站点
     */
    private void createDefaultHouse(Station station) {
        if (station.getStationTemplateId() == null || station.getStationTemplateId() == 0) { // 空局站
            // 创建默认局房
            House house = new House();
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setStationId(station.getStationId());
            house.setHouseId(houseId);
            house.setHouseName("默认房屋"); // 对应 i18n.T("house.default.name")
            house.setDescription(house.getHouseName());
            house.setLastUpdateDate(LocalDateTime.now());
            houseService.save(house);
        } else if (station.getStationTemplateId() == 1) { // 空局站
            House house = new House();
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setStationId(station.getStationId());
            house.setHouseId(houseId);
            house.setHouseName("默认房屋"); // 对应 i18n.T("house.default.name")
            house.setDescription(house.getHouseName());
            house.setLastUpdateDate(LocalDateTime.now());
            houseService.save(house);

            House baseStationHouse = new House();
            Integer baseStationHouseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            baseStationHouse.setStationId(station.getStationId());
            baseStationHouse.setHouseId(baseStationHouseId);
            baseStationHouse.setHouseName("基站房屋"); // 对应 i18n.T("baseStation.house.name")
            baseStationHouse.setDescription(house.getHouseName());
            baseStationHouse.setLastUpdateDate(LocalDateTime.now());
            houseService.save(baseStationHouse);
        } else if (station.getStationTemplateId() == 2) { // 江苏基站
            // 默认具有"基站"局房
            House house = new House();
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setStationId(station.getStationId());
            house.setHouseId(houseId);
            house.setHouseName("默认房屋"); // 对应 i18n.T("house.default.name")
            house.setDescription(house.getHouseName());
            house.setLastUpdateDate(LocalDateTime.now());
            houseService.save(house);

            House baseStationHouse = new House();
            Integer baseStationHouseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            baseStationHouse.setStationId(station.getStationId());
            baseStationHouse.setHouseId(baseStationHouseId);
            baseStationHouse.setHouseName("基站房屋"); // 对应 i18n.T("baseStation.house.name")
            baseStationHouse.setDescription(house.getHouseName());
            baseStationHouse.setLastUpdateDate(LocalDateTime.now());
            houseService.save(baseStationHouse);

            House importantStationHouse = new House();
            Integer importantStationHouseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            importantStationHouse.setStationId(station.getStationId());
            importantStationHouse.setHouseId(importantStationHouseId);
            importantStationHouse.setHouseName("重要信号房屋"); // 对应 i18n.T("important.signal.house.name")
            importantStationHouse.setDescription(house.getHouseName());
            importantStationHouse.setLastUpdateDate(LocalDateTime.now());
            houseService.save(importantStationHouse);
        }
    }

    @Override
    public Station findByStationId(int stationId) {
        return stationMapper.findStationById(stationId);
    }

    @Override
    public Boolean createDTO(StationDTO station) {
        // 局站名称要唯一
        Station stationExit = findStationByName(station.getStationName());
        if (stationExit != null) {
            throw new BusinessException("局站名称已存在");
        }


        Station insertStation = new Station();
        if (station.getStationId() == null || station.getStationId().equals(0)) {
            Integer stationId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_STATION, 0);
            station.setStationId(stationId);
        }
        List<DataItem> centerDataItem = dataItemService.findByEntryId(DataEntryEnum.DATA_ENTRY);
        insertStation.setStationName(station.getStationName());
        insertStation.setConnectState(2);
        insertStation.setUpdateTime(LocalDateTime.now());
        if (station.getStationCategory() == null) {
            insertStation.setStationCategory(1);
        } else {
            insertStation.setStationCategory(station.getStationCategory());
        }
        insertStation.setStationGrade(1);
        insertStation.setStationState(1);
        insertStation.setCenterId(centerDataItem.get(0).getItemId());
        insertStation.setEnable(true);
        insertStation.setProjectName(station.getProjectName());
        insertStation.setContractNo(station.getContractNo());
        insertStation.setContainNode(false);
        insertStation.setStationTemplateId(station.getStationTemplateId());
        insertStation.setStationId(station.getStationId());
        if (station.getBordNumber() == null) {
            insertStation.setBordNumber(0);
        } else {
            insertStation.setBordNumber(station.getBordNumber());
        }
        StationStructureMap StationStructureMap = new StationStructureMap();
        StationStructureMap.setStationId(insertStation.getStationId());
        StationStructureMap.setStructureId(station.getStationStructureId());
        int insert = stationMapper.insert(insertStation);
        if (insert > 0) {
            stationStructureMapService.create(StationStructureMap);
            changeEventService.sendCreate(insertStation);
            insertStation.setStationTemplateId(null);
            createDefaultHouse(insertStation);
            // 创建局站项目信息
            stationProjectInfoService.findOrCreateStationProjectInfo(station.getStationId(), station.getProjectName(), station.getContractNo());
        }
        return insert > 0;
    }

    @Override
    public Station findStationByName(String stationName) {
        return stationMapper.selectOne(Wrappers.lambdaQuery(Station.class).eq(Station::getStationName, stationName));
    }

    @Override
    public Boolean updateDTO(StationDTO station) {
        Station cacheStation = stationMapper.findStationById(station.getStationId());
        if (cacheStation != null) {
            // 检查局站名称是否发生变更
            if (!cacheStation.getStationName().equals(station.getStationName())) {
                // 如果名称变更，检查新名称是否重复
                Station existingStation = findStationByName(station.getStationName());
                if (existingStation != null && !existingStation.getStationId().equals(station.getStationId())) {
                    throw new BusinessException("局站名称已存在，请使用其他名称");
                }
            }
            Station updateStation = new Station();
            BeanUtils.copyProperties(cacheStation, updateStation);
            station.copyTo(updateStation);
            updateStation.setUpdateTime(LocalDateTime.now());
            boolean result = update(updateStation);
            if (result) {
                // 更新tbl_stationprojectinfo表
                stationProjectInfoService.updateProjectNameAndContractNo(updateStation.getStationId(), station.getProjectName(), station.getContractNo());
                operationDetailService.compareEntitiesRecordLog(cacheStation, updateStation);
            }
            return result;
        }
        return false;
    }

    @Override
    public Boolean update(Station station) {
        if (stationMapper.updateById(station) > 0) {
            changeEventService.sendUpdate(station);
            return true;
        }
        return false;
    }

    @Override
    public List<Station> findAllStation() {
        return stationMapper.selectList(null);
    }

}
