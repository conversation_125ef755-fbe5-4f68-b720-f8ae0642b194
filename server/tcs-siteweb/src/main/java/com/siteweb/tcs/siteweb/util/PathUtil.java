package com.siteweb.tcs.siteweb.util;

import cn.hutool.core.text.CharSequenceUtil;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.StringJoiner;
import java.util.stream.Stream;

/**
 * <AUTHOR> (2025-07-07)
 **/
public class PathUtil {

    public static void ensureDirectoryExists(Path directory) throws IOException {
        if (directory != null && !Files.exists(directory)) {
            Files.createDirectories(directory);
        }
    }

    public static long getFolderSize(String folderPath) throws IOException {
        Path path = Paths.get(folderPath);
        try (Stream<Path> walk = Files.walk(path)) {
            return walk
                    .filter(Files::isRegularFile)
                    .mapToLong(p -> {
                        try {
                            return Files.size(p);
                        } catch (IOException e) {
                            System.err.println("Unable to get size of " + p + ": " + e.getMessage());
                            return 0L;
                        }
                    })
                    .sum();
        }
    }

    public static String getReadableSize(long size) {
        if (size <= 0) return "0";
        final String[] units = new String[] { "B", "KB", "MB", "GB", "TB" };
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return String.format("%.1f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }
    /**
     * 多个路径的连接
     *
     * @param paths 路径
     * @return {@link String}
     */
    public static String pathJoin(String... paths) {
        if (paths == null || paths.length == 0) {
            return "";
        }
        StringJoiner stringJoiner = new StringJoiner(File.separator);
        for (String path : paths) {
            if (CharSequenceUtil.isBlank(path)) {
                continue;  // 跳过空路径
            }
            // 去除路径中多余的分隔符
            path = path.replaceAll("[\\\\/]+", "/").replaceAll("/$", "").replaceAll("^/", "");
            stringJoiner.add(path);
        }
        return stringJoiner.toString();
    }
    /**
     * 规范化路径，处理 . 和 ..
     *
     * @param path 路径
     * @return 规范化后的路径
     */
    public static String normalize(String path) {
        if (CharSequenceUtil.isBlank(path)) {
            return "";
        }
        return Paths.get(path).normalize().toString();
    }

    /**
     * 获取文件名（不包含路径）
     *
     * @param path 路径
     * @return 文件名
     */
    public static String getFileName(String path) {
        if (CharSequenceUtil.isBlank(path)) {
            return "";
        }
        return new File(path).getName();
    }

    /**
     * 获取文件扩展名
     *
     * @param path 路径
     * @return 扩展名（不包含点）
     */
    public static String getFileExtension(String path) {
        if (CharSequenceUtil.isBlank(path)) {
            return "";
        }
        String fileName = getFileName(path);
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex + 1);
    }

    /**
     * 获取父目录
     *
     * @param path 路径
     * @return 父目录路径
     */
    public static String getParentPath(String path) {
        if (CharSequenceUtil.isBlank(path)) {
            return "";
        }
        File file = new File(path);
        File parent = file.getParentFile();
        return parent == null ? "" : parent.getPath();
    }

    /**
     * 判断是否为绝对路径
     *
     * @param path 路径
     * @return 是否为绝对路径
     */
    public static boolean isAbsolutePath(String path) {
        if (CharSequenceUtil.isBlank(path)) {
            return false;
        }
        return new File(path).isAbsolute();
    }

    /**
     * 创建多级目录
     *
     * @param path 目录路径
     * @return 是否创建成功
     */
    public static boolean createDirectories(String path) {
        if (CharSequenceUtil.isBlank(path)) {
            return false;
        }
        try {
            Files.createDirectories(Paths.get(path));
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
