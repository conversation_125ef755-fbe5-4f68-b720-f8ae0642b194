package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.EventBaseDic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Event Base Dictionary Mapper
 */
@Mapper
@Repository
public interface EventBaseDicMapper extends BaseMapper<EventBaseDic> {

    /**
     * 生成事件基类字典
     * @param baseTypeId 目标基类ID
     * @param sourceId 源基类ID
     */
    void generateEventBaseDic(@Param("baseTypeId") Long baseTypeId, @Param("sourceId") Long sourceId);
}
