package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.Account;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 账户Mapper接口
 */
@Mapper
@Repository(value = "SiteWebAccountMapper")
public interface AccountMapper extends BaseMapper<Account> {

    /**
     * 根据手机号查找账户
     *
     * @param mobile 手机号
     * @return 账户列表
     */
    List<Account> findByMobile(@Param("mobile") String mobile);

    // updateCenterIdForNegativeUserId 方法已迁移到 Service 层实现
} 