package com.siteweb.tcs.siteweb.vo;

import com.siteweb.tcs.siteweb.entity.WorkStation;
import lombok.Data;

/**
 * @Description:
 */
@Data
public class ServerSourceVO {
    private Integer workStationId;
    private String workStationName;
    private Integer workStationType;


    public static ServerSourceVO from(WorkStation workStation) {
        ServerSourceVO vo = new ServerSourceVO();
        vo.setWorkStationId(workStation.getWorkStationId());
        vo.setWorkStationName(workStation.getWorkStationName());
        vo.setWorkStationType(workStation.getWorkStationType());
        return vo;
    }


    /**
     * 过滤器，主要过滤 RDS、RMU、DS
     * @param workStation
     * @return
     */
    public static Boolean filter(WorkStation workStation) {
        Integer val = workStation.getWorkStationType();
        return (2 == val || 8 == val || 23 == val) && workStation.getIsUsed();
    }


}

