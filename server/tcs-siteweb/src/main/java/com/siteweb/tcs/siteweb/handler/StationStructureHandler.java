package com.siteweb.tcs.siteweb.handler;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.entity.Station;
import com.siteweb.tcs.siteweb.entity.StationStructure;
import com.siteweb.tcs.siteweb.entity.StationStructureMap;
import com.siteweb.tcs.siteweb.service.IResourceStructureService;
import com.siteweb.tcs.siteweb.service.IStationService;
import com.siteweb.tcs.siteweb.service.IStationStructureMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 站点结构变更处理器
 * 
 * <AUTHOR> (2024-03-14)
 */
@Slf4j
@RestController
@RequestMapping("/StationStructureHandler")
public class StationStructureHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private IStationStructureMapService stationStructureMapService;

    @Autowired
    private IStationService stationService;

    @Autowired
    private IResourceStructureService resourceStructureService;

    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(StationStructure.class);
    }

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        StationStructure stationStructure = changeRecord.readMessageBody(StationStructure.class);
        log.info("局站分组创建成功 stationStructureName:{}", stationStructure.getStructureName());
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        StationStructure stationStructure = changeRecord.readMessageBody(StationStructure.class);
        log.info("局站分组删除成功 stationStructureName:{}", stationStructure.getStructureName());
        // 删除对应的resourceStructure
        ResourceStructure resourceStructure = resourceStructureService.findByOriginIdAndStructureType(stationStructure.getStructureId(), 103);
        if (resourceStructure != null) {
            resourceStructureService.deleteByID(resourceStructure.getResourceStructureId());
        }
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        StationStructure stationStructure = changeRecord.readMessageBody(StationStructure.class);
        log.info("局站分组修改成功 stationStructureName:{}", stationStructure.getStructureName());
        ResourceStructure resourceStructure = resourceStructureService.findByOriginIdAndStructureType(stationStructure.getStructureId(), 103);
        if (resourceStructure != null && !resourceStructure.getResourceStructureName().equals(stationStructure.getStructureName())) {
            resourceStructure.setResourceStructureName(stationStructure.getStructureName());
            resourceStructureService.update(resourceStructure);
        }
    }
}
