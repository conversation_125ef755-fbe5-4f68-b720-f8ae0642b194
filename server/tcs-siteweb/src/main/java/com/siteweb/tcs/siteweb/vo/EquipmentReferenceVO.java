package com.siteweb.tcs.siteweb.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备引用VO
 * 从tcs-config迁移，保持字段一致
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EquipmentReferenceVO {
    /**
     * 设备id
     */
    @ExcelProperty("设备ID")
    private Integer equipmentId;
    /**
     * 设备名称
     */
    @ExcelProperty("设备名称")
    private String equipmentName;
    /**
     * 局站站id
     */
    @ExcelProperty("局站ID")
    private Integer stationId;
    /**
     * 局站名称
     */
    @ExcelProperty("局站名称")
    private String stationName;
    /**
     * 监视单元id
     */
    @ExcelProperty("监视单元ID")
    private Integer monitorUnitId;
    /**
     * 监视单元名称
     */
    @ExcelProperty("监视单元名称")
    private String monitorUnitName;
    /**
     * ip地址
     */
    @ExcelProperty("IP地址")
    private String ipAddress;
}
