package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.entity.StandardType;
import com.siteweb.tcs.siteweb.entity.SysConfig;
import com.siteweb.tcs.siteweb.enums.StandardCategoryEnum;
import com.siteweb.tcs.siteweb.enums.SysConfigEnum;
import com.siteweb.tcs.siteweb.mapper.StandardTypeMapper;
import com.siteweb.tcs.siteweb.service.IStandardTypeService;
import com.siteweb.tcs.siteweb.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 标准类型服务实现类
 */
@Service
public class StandardTypeServiceImpl extends IsolatedServiceImpl<StandardTypeMapper, StandardType> implements IStandardTypeService {

    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public StandardType findByStandardId(Integer standardId) {
        LambdaQueryWrapper<StandardType> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardType::getStandardId, standardId);
        return getOne(wrapper);
    }

    @Override
    public StandardCategoryEnum findCurrentStandardType() {
        SysConfig sysConfig = sysConfigService.findByKey(SysConfigEnum.STANDARD_VER);
        if (Objects.isNull(sysConfig) || CharSequenceUtil.isBlank(sysConfig.getConfigValue())) {
            //没有设置标准类型
            return StandardCategoryEnum.EMR;
        }
        StandardType standardType = findByStandardAlias(sysConfig.getConfigValue());
        if (Objects.isNull(standardType) || Objects.isNull(standardType.getStandardId())) {
            //没有设置标准类型
            return StandardCategoryEnum.EMR;
        }
        return StandardCategoryEnum.getByValue(standardType.getStandardId());
    }

    @Override
    public StandardType findByStandardAlias(String standardAlias) {
        LambdaQueryWrapper<StandardType> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardType::getStandardAlias, standardAlias);
        return getOne(wrapper);
    }
}
