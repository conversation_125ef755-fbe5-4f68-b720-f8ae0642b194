package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import com.siteweb.tcs.siteweb.annotation.ConfigId;
import lombok.Data;
import java.io.Serializable;

/**
 * 资源结构实体类
 * Resource structure table
 */
@Data
@TableName("resourcestructure")
@ChangeSource(channel = "tcs", product = "siteweb", source = "resourcestructure")
@ConfigId(27)
public class ResourceStructure implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 资源结构ID，主键，自增
     */
    @TableId(value = "resourcestructureid", type = IdType.AUTO)
    private Integer resourceStructureId;

    /**
     * 场景ID
     */
    @TableField("sceneid")
    private Integer sceneId;

    /**
     * 资源组类型
     */
    @TableField("structuretypeid")
    private Integer structureTypeId;

    /**
     * 分组名
     */
    @TableField("resourcestructurename")
    private String resourceStructureName;

    /**
     * 父分组Id
     */
    @TableField("parentresourcestructureid")
    private Integer parentResourceStructureId;

    /**
     * 图片
     */
    @TableField("photo")
    private String photo;

    /**
     * 位置信息
     */
    @TableField("\"position\"") // Use quotes as column name is reserved keyword
    private String position;

    /**
     * 连接路径
     */
    @TableField("levelofpath")
    private String levelOfPath;

    /**
     * 是否显示
     */
    @TableField("display")
    private Boolean display;

    /**
     * 排序Index
     */
    @TableField("sortvalue")
    private Integer sortValue;

    /**
     * 扩展信息
     */
    @TableField("extendedfield")
    private JsonNode extendedField;

    /**
     * 源对象ID
     */
    @TableField("originid")
    private Integer originId;

    /**
     * 源父对象ID
     */
    @TableField("originparentid")
    private Integer originParentId;
}