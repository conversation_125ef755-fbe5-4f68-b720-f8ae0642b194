package com.siteweb.tcs.siteweb.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.siteweb.tcs.siteweb.serializer.FlexibleLocalDateTimeDeserializer;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备详情DTO
 */
@Data
public class EquipmentDetailDTO {
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 设备ID
     */
    private Integer equipmentId;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 父设备ID
     */
    private String parentEquipmentId;
    /**
     * 设备模板ID
     */
    private Integer equipmentTemplateId;
    /**
     * 设备模板名称 tbl_equipmenttemplate
     */
    private String equipmentTemplateName;
    /**
     * 监控主机ID
     */
    private Integer workStationId;
    /**
     * 所属服务器 tbl_workstation
     */
    private String workStationName;
    /**
     * 所属局站 tbl_station
     */
    private String stationName;
    /**
     * 监控单元ID
     */
    private Integer monitorUnitId;
    /**
     * 所属监控单元 tsl_monitorunit
     */
    private String monitorUnitName;
    /**
     * 设备类型（非标准）
     */
    private Integer equipmentCategory;
    /**
     * 设备分类（自诊断，虚拟设备等）
     */
    private Integer equipmentType;
    /**
     * 电池类型
     */
    private Integer equipmentClass;
    /**
     * 电池工作状态
     */
    private Integer equipmentState;
    /**
     * 设备厂商
     */
    private String vendor;
    /**
     * 设备单位
     */
    private String unit;
    /**
     * 设备型号
     */
    private String equipmentStyle;
    /**
     * 设备模块
     */
    private String equipmentModule;
    /**
     * 告警开始延时
     */
    private Double startDelay;
    /**
     * 告警结束延时
     */
    private Double endDelay;
    /**
     * 设备属性 1/2/3格式
     */
    private String property;
    /**
     * 设备基类
     */
    private Integer equipmentBaseType;
    /**
     * 告警过滤表达式
     */
    private String eventExpression;
    /**
     * 资产编号
     */
    private String equipmentNo;
    /**
     * 资产状态
     */
    private Integer assetState;
    /**
     * 使用年限
     */
    private Double usedLimit;
    /**
     * 资产价格
     */
    private Double price;
    /**
     * 额定容量
     */
    private String ratedCapacity;
    /**
     * 已安装模块
     */
    private String installedModule;
    /**
     * 工程合同信息
     */
    private EquipmentProjectInfo equipmentProjectInfo;
    /**
     * 说明
     */
    private String description;
    // 屏蔽时间暂时不返回

    private Integer samplerUnitId;

    private Integer houseId;

    private Integer resourceStructureId;

    private Integer changeType;

    @Data
    public static class EquipmentProjectInfo {
        /**
         * 工程名 tbl_equipmentprojectinfo
         */
        private String projectName;
        /**
         * 合同号 tbl_equipmentprojectinfo
         */
        private String contractNo;
    }


    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    private LocalDateTime usedDate;

    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    private LocalDateTime buyDate;
    private Integer displayIndex;
    private Integer connectState;

    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;
    private String extValue;
    private String photo;
}
