package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Control meanings entity
 */
@Data
@TableName("tbl_controlmeanings")
public class ControlMeanings implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("EquipmentTemplateId")
    private Integer equipmentTemplateId;

    @TableField("ControlId")
    private Integer controlId;

    @TableField("ParameterValue")
    private Integer parameterValue;

    @TableField("Meanings")
    private String meanings;

    @TableField("BaseCondId")
    private Integer baseCondId;
}
