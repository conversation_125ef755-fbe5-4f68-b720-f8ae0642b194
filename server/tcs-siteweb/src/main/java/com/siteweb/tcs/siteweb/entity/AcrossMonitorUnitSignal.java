package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 跨站监控单元信号实体类
 */
@Data
@TableName("tsl_acrossmonitorunitsignal")
public class AcrossMonitorUnitSignal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("StationId")
    private Integer stationId;

    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("EquipmentId")
    private Integer equipmentId;

    @TableId(value = "SignalId")
    private Integer signalId;

    @TableField("Expression")
    private String expression;
}
