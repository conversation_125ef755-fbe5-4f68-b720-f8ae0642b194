package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import com.siteweb.tcs.siteweb.annotation.ConfigId;

/**
 * Station entity
 */
@Data
@TableName("tbl_station")
@ChangeSource(channel = "tcs", product = "siteweb", source = "station")
@ConfigId(1)
public class Station implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "StationId")
    private Integer stationId;

    @TableField("StationName")
    private String stationName;

    @TableField("Latitude")
    private Double latitude;

    @TableField("Longitude")
    private Double longitude;

    @TableField("SetupTime")
    private LocalDateTime setupTime;

    @TableField("CompanyId")
    private Integer companyId;

    @TableField("ConnectState")
    private Integer connectState;

    @TableField("UpdateTime")
    private LocalDateTime updateTime;

    @TableField("StationCategory")
    private Integer stationCategory;

    @TableField("StationGrade")
    private Integer stationGrade;

    @TableField("StationState")
    private Integer stationState;

    @TableField("ContactId")
    private Integer contactId;

    @TableField("SupportTime")
    private Integer supportTime;

    @TableField("OnWayTime")
    private Double onWayTime;

    @TableField("SurplusTime")
    private Double surplusTime;

    @TableField("FloorNo")
    private String floorNo;

    @TableField("PropList")
    private String propList;

    @TableField("Acreage")
    private Double acreage;

    @TableField("BuildingType")
    private Integer buildingType;

    @TableField("ContainNode")
    private Boolean containNode;

    @TableField("Description")
    private String description;

    @TableField("BordNumber")
    private Integer bordNumber;

    @TableField("CenterId")
    private Integer centerId;

    @TableField("Enable")
    private Boolean enable;

    @TableField("StartTime")
    private LocalDateTime startTime;

    @TableField("EndTime")
    private LocalDateTime endTime;

    @TableField("ProjectName")
    private String projectName;

    @TableField("ContractNo")
    private String contractNo;

    @TableField("InstallTime")
    private LocalDateTime installTime;

    @TableField(exist = false)
    private LocalDateTime maskStartTime;

    @TableField(exist = false)
    private LocalDateTime maskEndTime;

    @TableField(exist = false)
    private Integer stationTemplateId;

    /**
     * 关联的房屋列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<House> houses;
}
