package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Signal base confirmation entity
 */
@Data
@TableName("tbl_signalbaseconfirm")
public class SignalBaseConfirm implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("EquipmentTemplateId")
    private Integer equipmentTemplateId;

    @TableField("SignalId")
    private Integer signalId;

    @TableField("StateValue")
    private Integer stateValue;

    @TableField("SubState")
    private String subState;
}
