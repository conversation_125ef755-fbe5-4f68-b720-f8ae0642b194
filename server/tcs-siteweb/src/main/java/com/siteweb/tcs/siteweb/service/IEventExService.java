package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.EventEx;

import java.util.List;

/**
 * Event Extension Service Interface
 */
public interface IEventExService extends IService<EventEx> {
    
    /**
     * 根据事件删除扩展信息
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     */
    void deleteByEvent(Integer equipmentTemplateId, Integer eventId);
    
    /**
     * 更新事件扩展信息
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @param turnover 翻转时间
     */
    void updateEventx(Integer equipmentTemplateId, Integer eventId, Integer turnover);
    
    /**
     * 批量更新事件扩展信息
     * @param eventExList 事件扩展信息列表
     */
    void batchUpdate(List<EventEx> eventExList);

    void deleteByEquipmentTemplateId(Integer equipmentTemplateId);
}
