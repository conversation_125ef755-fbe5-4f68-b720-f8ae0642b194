package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.tcs.siteweb.annotation.ChangeSource;
import com.siteweb.tcs.siteweb.annotation.ConfigId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Sampler unit entity
 */
@Data
@TableName("tsl_samplerunit")
@ChangeSource(channel = "tcs", product = "siteweb", source = "samplerunit")
@ConfigId(17)
public class SamplerUnit implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("SamplerUnitId")
    private Integer samplerUnitId;

    @TableField("PortId")
    private Integer portId;

    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    @TableField("SamplerId")
    private Integer samplerId;

    @TableField("ParentSamplerUnitId")
    private Integer parentSamplerUnitId;

    @TableField("SamplerType")
    private Short samplerType;

    @TableField("SamplerUnitName")
    private String samplerUnitName;

    @TableField("Address")
    private Integer address;

    @TableField("SpUnitInterval")
    private Double spUnitInterval;

    @TableField("DllPath")
    private String dllPath;

    @TableField("ConnectState")
    private Integer connectState;

    @TableField("UpdateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @TableField("PhoneNumber")
    private String phoneNumber;

    @TableField("Description")
    private String description;
}
