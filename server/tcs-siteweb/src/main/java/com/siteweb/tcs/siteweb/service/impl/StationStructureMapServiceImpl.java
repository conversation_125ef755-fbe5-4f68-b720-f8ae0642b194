package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.entity.Station;
import com.siteweb.tcs.siteweb.entity.StationStructure;
import com.siteweb.tcs.siteweb.entity.StationStructureMap;
import com.siteweb.tcs.siteweb.mapper.StationMapper;
import com.siteweb.tcs.siteweb.mapper.StationStructureMapper;
import com.siteweb.tcs.siteweb.mapper.StationStructureMapMapper;
import com.siteweb.tcs.siteweb.service.IChangeEventService;
import com.siteweb.tcs.siteweb.service.IStationStructureMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Station Structure Map Service Implementation
 */
@Slf4j
@Service
public class StationStructureMapServiceImpl extends IsolatedServiceImpl<StationStructureMapMapper, StationStructureMap> implements IStationStructureMapService {

    @Autowired
    private StationStructureMapMapper stationStructureMapMapper;
    @Autowired
    private StationStructureMapper stationStructureMapper;
    @Autowired
    private StationMapper stationMapper;
    @Autowired
    private IChangeEventService changeEventService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByStationId(Integer stationId) {
        if (stationId == null) {
            log.warn("Cannot delete station structure map: station ID is null");
            return false;
        }

        try {
            int count = (int) count(new LambdaQueryWrapper<StationStructureMap>()
                    .eq(StationStructureMap::getStationId, stationId));

            if (count == 0) {
                log.info("No station structure map found for station ID: {}", stationId);
                return true;
            }

            boolean result = remove(new LambdaQueryWrapper<StationStructureMap>()
                    .eq(StationStructureMap::getStationId, stationId));

            if (result) {
                log.info("Deleted {} station structure maps for station ID: {}", count, stationId);
            } else {
                log.warn("Failed to delete station structure maps for station ID: {}", stationId);
            }

            return result;
        } catch (Exception e) {
            log.error("Error deleting station structure maps for station ID: {}", stationId, e);
            throw e;
        }
    }

    @Override
    public StationStructureMap findStationStructureMapByStationId(Integer stationId) {
        if (stationId == null) {
            log.warn("Cannot find station structure map: station ID is null");
            return null;
        }

        return getOne(new LambdaQueryWrapper<StationStructureMap>()
                .eq(StationStructureMap::getStationId, stationId)
                .last("LIMIT 1"));
    }

    @Override
    public List<StationStructureMap> findStationStructureMapByStructureId(Integer structureId) {
        if (structureId == null) {
            log.warn("Cannot find station structure maps: structure ID is null");
            return List.of();
        }

        return list(new LambdaQueryWrapper<StationStructureMap>()
                .eq(StationStructureMap::getStructureId, structureId));
    }

    @Override
    public boolean create(StationStructureMap stationStructureMap) {
        int result = stationStructureMapMapper.insert(stationStructureMap);
        if (result > 0) {
            changeEventService.sendCreate(stationStructureMap);
        }
        return result > 0;
    }

    @Override
    public List<Integer> findStationIdsByStructureId(Integer structureId) {
        if (structureId == null) {
            log.warn("Cannot find station IDs: structure ID is null");
            return List.of();
        }

        List<StationStructureMap> maps = list(new LambdaQueryWrapper<StationStructureMap>()
                .eq(StationStructureMap::getStructureId, structureId));

        return maps.stream()
                .map(StationStructureMap::getStationId)
                .collect(Collectors.toList());
    }

    @Override
    public StationStructure tree() {
        // 查询所有结构
        List<StationStructure> structures = stationStructureMapper.selectList(Wrappers.lambdaQuery(StationStructure.class));
        if (structures == null || structures.isEmpty()) {
            return null;
        }

        // 按ID映射
        Map<Integer, StationStructure> idToNode = structures.stream()
                .collect(Collectors.toMap(StationStructure::getStructureId, s -> {
                    s.setChildren(new java.util.ArrayList<>());
                    s.setStations(new java.util.ArrayList<>());
                    return s;
                }));

        // 绑定父子
        StationStructure root = null;
        for (StationStructure s : structures) {
            Integer parentId = s.getParentStructureId();
            if (parentId == null || Objects.equals(parentId, 0)) {
                root = s;
            } else {
                StationStructure parent = idToNode.get(parentId);
                if (parent != null) {
                    if (parent.getChildren() == null) parent.setChildren(new java.util.ArrayList<>());
                    parent.getChildren().add(s);
                }
            }
        }

        if (root == null) {
            // 兜底：选择LevelPath最短或Parent为null的作为root
            root = structures.stream()
                    .filter(s -> s.getParentStructureId() == null || Objects.equals(s.getParentStructureId(), 0))
                    .findFirst().orElse(structures.get(0));
        }

        // 绑定局站
        List<StationStructureMap> maps = stationStructureMapMapper.selectList(Wrappers.lambdaQuery(StationStructureMap.class));
        if (maps != null && !maps.isEmpty()) {
            Map<Integer, List<Integer>> structureIdToStationIds = maps.stream()
                    .collect(Collectors.groupingBy(StationStructureMap::getStructureId,
                            Collectors.mapping(StationStructureMap::getStationId, Collectors.toList())));

            List<Integer> allStationIds = maps.stream().map(StationStructureMap::getStationId).distinct().toList();
            if (!allStationIds.isEmpty()) {
                List<Station> stations = stationMapper.selectBatchIds(allStationIds);
                Map<Integer, Station> idToStation = stations.stream()
                        .collect(Collectors.toMap(Station::getStationId, s -> s));

                for (Map.Entry<Integer, List<Integer>> e : structureIdToStationIds.entrySet()) {
                    StationStructure node = idToNode.get(e.getKey());
                    if (node != null) {
                        if (node.getStations() == null) node.setStations(new java.util.ArrayList<>());
                        for (Integer sid : e.getValue()) {
                            Station st = idToStation.get(sid);
                            if (st != null) node.getStations().add(st);
                        }
                    }
                }
            }
        }

        return root;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeDivisionHierarchy(Integer divisionStructureId, Integer newParentStructureId) {
        if (divisionStructureId == null || newParentStructureId == null) return false;

        StationStructure division = stationStructureMapper.selectById(divisionStructureId);
        StationStructure newParent = stationStructureMapper.selectById(newParentStructureId);
        if (division == null || newParent == null) return false;

        // 校验结构类型：父可为二级中心(2)或二级中心区划(1)，自身必须为二级中心区划(1)
        if (!Objects.equals(division.getStructureType(), 1)) return false;
        if (!(Objects.equals(newParent.getStructureType(), 2) || Objects.equals(newParent.getStructureType(), 1))) return false;

        String oldLevelPath = division.getLevelPath();
        String newLevelPath = (newParent.getLevelPath() == null || newParent.getLevelPath().isEmpty())
                ? String.valueOf(division.getStructureId())
                : newParent.getLevelPath() + "." + division.getStructureId();

        // 更新自身父ID与路径
        StationStructure updateSelf = new StationStructure();
        updateSelf.setStructureId(division.getStructureId());
        updateSelf.setParentStructureId(newParent.getStructureId());
        updateSelf.setLevelPath(newLevelPath);
        stationStructureMapper.updateById(updateSelf);

        // 更新所有子孙的LevelPath：以 oldLevelPath 为前缀替换为 newLevelPath
        List<StationStructure> all = stationStructureMapper.selectList(Wrappers.lambdaQuery(StationStructure.class));
        for (StationStructure s : all) {
            if (s.getLevelPath() != null && oldLevelPath != null && (s.getLevelPath().equals(oldLevelPath) || s.getLevelPath().startsWith(oldLevelPath + "."))) {
                String suffix = s.getLevelPath().substring(oldLevelPath.length());
                String updatedPath = newLevelPath + suffix;
                StationStructure upd = new StationStructure();
                upd.setStructureId(s.getStructureId());
                upd.setLevelPath(updatedPath);
                stationStructureMapper.updateById(upd);
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeStationHierarchy(Integer stationId, Integer newDivisionStructureId) {
        if (stationId == null || newDivisionStructureId == null) return false;
        StationStructure newDivision = stationStructureMapper.selectById(newDivisionStructureId);
        if (newDivision == null || !Objects.equals(newDivision.getStructureType(), 1)) return false;

        StationStructureMap existing = getOne(new LambdaQueryWrapper<StationStructureMap>()
                .eq(StationStructureMap::getStationId, stationId)
                .last("LIMIT 1"));

        if (existing == null) {
            StationStructureMap n = new StationStructureMap();
            n.setStationId(stationId);
            n.setStructureId(newDivisionStructureId);
            stationStructureMapMapper.insert(n);
        } else {
            StationStructureMap upd = new StationStructureMap();
            upd.setStationId(stationId);
            upd.setStructureId(newDivisionStructureId);
            stationStructureMapMapper.update(upd, new LambdaQueryWrapper<StationStructureMap>().eq(StationStructureMap::getStationId, stationId));
        }

        return true;
    }
}

