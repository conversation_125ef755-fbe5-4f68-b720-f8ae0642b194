package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.entity.CommandBaseDic;
import com.siteweb.tcs.siteweb.mapper.CommandBaseDicMapper;
import com.siteweb.tcs.siteweb.service.ICommandBaseDicService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.IntFunction;

/**
 * Command Base Dictionary Service Implementation
 */
@Service
public class CommandBaseDicServiceImpl extends IsolatedServiceImpl<CommandBaseDicMapper, CommandBaseDic> implements ICommandBaseDicService {

    @Override
    public CommandBaseDic findByBaseTypeId(Long baseTypeId) {
        return getOne(Wrappers.lambdaQuery(CommandBaseDic.class)
                .eq(CommandBaseDic::getBaseTypeId, BigDecimal.valueOf(baseTypeId)));
    }

    @Override
    public void updateBaseClassStandardDictionary(Integer equipmentTemplateId) {
        // 由BaseDicServiceImpl统一处理
    }

    @Override
    public boolean existsByBaseTypeId(Long baseTypeId) {
        return count(Wrappers.lambdaQuery(CommandBaseDic.class)
                .eq(CommandBaseDic::getBaseTypeId, BigDecimal.valueOf(baseTypeId))) > 0;
    }

    @Override
    public void generateBaseDic(Long baseTypeId, Long sourceId) {
        baseMapper.generateCommandBaseDic(baseTypeId, sourceId);
        
        // 更新baseTypeName
        CommandBaseDic commandBaseDic = findByBaseTypeId(baseTypeId);
        if (commandBaseDic != null && commandBaseDic.getBaseNameExt() != null && !commandBaseDic.getBaseNameExt().isEmpty()) {
            Long moduleNo = baseTypeId % 1000;
            String baseTypeName = commandBaseDic.getBaseNameExt().replace("{0}", moduleNo.toString());
            commandBaseDic.setBaseTypeName(baseTypeName);
            updateById(commandBaseDic);
        }
    }

    @Override
    public void processBaseTypeIdList(IntFunction<List<Long>> findBaseTypeIdsFunction, com.siteweb.tcs.siteweb.service.IBaseDicService baseDicService, Integer equipmentTemplateId) {
        // 由BaseDicServiceImpl统一处理
    }
}
