package com.siteweb.tcs.siteweb.service.impl;

import com.siteweb.tcs.common.service.IsolatedServiceImpl;
import com.siteweb.tcs.siteweb.entity.House;
import com.siteweb.tcs.siteweb.enums.OperationObjectTypeEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.HouseMapper;
import com.siteweb.tcs.siteweb.service.IChangeEventService;
import com.siteweb.tcs.siteweb.service.IHouseService;
import com.siteweb.tcs.siteweb.service.IOperationDetailService;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import com.siteweb.tcs.siteweb.util.I18n;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * House Service Implementation
 */
@Service
public class HouseServiceImpl extends IsolatedServiceImpl<HouseMapper, House> implements IHouseService {

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Autowired
    private HouseMapper houseMapper;

    @Autowired
    private IChangeEventService changeEventService;

    @Autowired
    private IOperationDetailService operationDetailService;

    @Autowired
    private I18n i18n;

    @Override
    public House findByHouseId(Integer houseId) {
        if (houseId == null) {
            return null;
        }
        return getById(houseId);
    }

    @Override
    public House findStationDefaultHouse(Integer stationId) {
        if (stationId == null) {
            return null;
        }

        // Find the first house of the station (default house)
        return lambdaQuery()
                .eq(House::getStationId, stationId)
                .orderByAsc(House::getHouseId)
                .last("LIMIT 1")
                .one();
    }

    @Override
    public List<House> findHouseByStationId(Integer stationId) {
        if (stationId == null) {
            return List.of();
        }

        return lambdaQuery()
                .eq(House::getStationId, stationId)
                .orderByAsc(House::getHouseId)
                .list();
    }

    @Override
    public boolean createHouse(House house) {
        if (house.getHouseId() == null || house.getHouseId().equals(0)) {
            Integer houseId = primaryKeyValueService.getGlobalIdentity(TableIdentityEnum.TBL_HOUSE, 0);
            house.setHouseId(houseId);
        }
        house.setLastUpdateDate(LocalDateTime.now());
        if (houseMapper.insert(house) > 0) {
            changeEventService.sendCreate(house);
            operationDetailService.recordOperationLog(house.getHouseId().toString(), OperationObjectTypeEnum.HOUSE, i18n.T("house.houseName"), i18n.T("add"), "", house.getHouseName());
            return true;
        }
        return false;

    }

    @Override
    public Boolean updateHouse(House house) {
        House oldHouse = houseMapper.selectById(house.getHouseId());
        if (houseMapper.updateById(house) > 0) {
            changeEventService.sendUpdate(house);
            operationDetailService.compareEntitiesRecordLog(oldHouse, house);
            return true;
        }
        return false;
    }

}
