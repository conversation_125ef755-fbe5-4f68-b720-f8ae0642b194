<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.SamplerMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.Sampler">
        <id column="SamplerId" property="samplerId" />
        <result column="SamplerName" property="samplerName" />
        <result column="SamplerType" property="samplerType" />
        <result column="ProtocolCode" property="protocolCode" />
        <result column="DLLCode" property="dllCode" />
        <result column="DLLVersion" property="dllVersion" />
        <result column="ProtocolFilePath" property="protocolFilePath" />
        <result column="DLLFilePath" property="dllFilePath" />
        <result column="DllPath" property="dllPath" />
        <result column="Setting" property="setting" />
        <result column="Description" property="description" />
        <result column="SoCode" property="soCode" />
        <result column="SoPath" property="soPath" />
        <result column="UploadProtocolFile" property="uploadProtocolFile" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, 
        ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, 
        SoPath, UploadProtocolFile
    </sql>
    
    <!-- 根据协议代码查询采集器 -->
    <select id="findByProtocolCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tsl_sampler
        WHERE ProtocolCode = #{protocolCode}
        Limit 1
    </select>

    <select id="findPage" resultType="com.siteweb.tcs.siteweb.vo.SamplerVO">
        SELECT a.samplerid,
        a.samplername,
        a.samplertype,
        a.protocolcode,
        a.dllcode,
        a.dllversion,
        a.protocolfilepath,
        a.dllfilepath,
        a.dllpath,
        a.setting,
        a.description,
        a.socode,
        a.sopath,
        b.EquipmentTemplateName
        FROM tsl_sampler a
        LEFT JOIN (SELECT
        et.EquipmentTemplateId,
        et.EquipmentTemplateName,
        et.ProtocolCode
        FROM
        TBL_EquipmentTemplate et
        INNER JOIN (
        SELECT
        MIN(EquipmentTemplateId) AS MinEquipmentTemplateId,
        ProtocolCode
        FROM
        TBL_EquipmentTemplate
        WHERE
        ParentTemplateId = 0
        AND protocolcode IS NOT NULL
        GROUP BY
        ProtocolCode
        ) AS grouped_et ON et.EquipmentTemplateId = grouped_et.MinEquipmentTemplateId) b ON a.ProtocolCode = b.protocolcode
        LEFT JOIN tbl_dataitem c ON c.EntryId = 37 AND c.ItemId = a.SamplerType
        <where>
            <if test="keyword != null and keyword != ''">
                AND (a.SamplerName LIKE
                <choose>
                    <when test="_databaseId == 'mysql'">
                        concat('%',#{keyword},'%')
                    </when>
                    <when test="_databaseId == 'postgresql'">
                        '%' || #{keyword} || '%'
                    </when>
                    <otherwise>
                        concat('%',#{keyword},'%')
                    </otherwise>
                </choose>
                or (a.DLLPath LIKE
                <choose>
                    <when test="_databaseId == 'mysql'">
                        concat('%',#{keyword},'%')
                    </when>
                    <when test="_databaseId == 'postgresql'">
                        '%' || #{keyword} || '%'
                    </when>
                    <otherwise>
                        concat('%',#{keyword},'%')
                    </otherwise>
                </choose>) or
                (b.EquipmentTemplateName LIKE
                <choose>
                    <when test="_databaseId == 'mysql'">
                        concat('%',#{keyword},'%')
                    </when>
                    <when test="_databaseId == 'postgresql'">
                        '%' || #{keyword} || '%'
                    </when>
                    <otherwise>
                        concat('%',#{keyword},'%')
                    </otherwise>
                </choose>) or (c.ItemValue LIKE
                <choose>
                    <when test="_databaseId == 'mysql'">
                        concat('%',#{keyword},'%')
                    </when>
                    <when test="_databaseId == 'postgresql'">
                        '%' || #{keyword} || '%'
                    </when>
                    <otherwise>
                        concat('%',#{keyword},'%')
                    </otherwise>
                </choose>))
            </if>
        </where>
    </select>
    <select id="findDllPathByEquipmentTemplateIds" resultType="com.siteweb.tcs.siteweb.entity.Sampler">
        SELECT b.samplerid, b.samplername, b.samplertype, b.protocolcode, b.dllcode, b.dllversion, b.protocolfilepath, b.dllfilepath,
        b.dllpath, b.setting, b.description, b.socode, b.sopath, b.uploadprotocolfile
        FROM tbl_equipmenttemplate a inner join tsl_sampler b on a.protocolcode = b.ProtocolCode
        WHERE a.EquipmentTemplateId IN
        <foreach collection="equipmentTemplateIdList" item="equipmentTemplateId" open="(" close=")" separator=",">
            #{equipmentTemplateId}
        </foreach>
    </select>
    <select id="findNamesByIds" resultType="com.siteweb.tcs.siteweb.dto.IdValueDTO">
        SELECT SamplerId as id,SamplerName as value FROM tsl_sampler WHERE SamplerId IN
        <foreach collection="samplerIds" item="samplerId" open="(" close=")" separator=",">
            #{samplerId}
        </foreach>
    </select>
    <select id="findByEquipmentTemplateId" resultType="com.siteweb.tcs.siteweb.entity.Sampler">
        SELECT a.SamplerId,
        a.SamplerName,
        a.SamplerType,
        a.ProtocolCode,
        a.DLLCode,
        a.DLLVersion,
        a.ProtocolFilePath,
        a.DLLFilePath,
        a.DLLPath,
        a.Setting,
        a.Description,
        a.SoCode,
        a.SoPath,
        a.UploadProtocolFile
        FROM tsl_sampler a
        INNER JOIN tbl_equipmenttemplate b ON a.ProtocolCode = b.protocolcode
        WHERE b.EquipmentTemplateId = #{equipmentTemplateId}
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT 1
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT 1
            </when>
            <otherwise>
                LIMIT 1
            </otherwise>
        </choose>;
    </select>
    <select id="findByNameAndDllPath" resultType="com.siteweb.tcs.siteweb.entity.Sampler">
        SELECT samplerid,
        samplername,
        samplertype,
        protocolcode,
        dllcode,
        dllversion,
        protocolfilepath,
        dllfilepath,
        dllpath,
        setting,
        description,
        socode,
        sopath,
        uploadprotocolfile
        FROM tsl_sampler
        WHERE SamplerName = #{samplerName}
        AND LOWER(dllPath) = LOWER(#{dllPath});
    </select>
    <select id="forUpdateById" resultType="com.siteweb.tcs.siteweb.entity.Sampler">
        SELECT samplerid,
        samplername,
        samplertype,
        protocolcode,
        dllcode,
        dllversion,
        protocolfilepath,
        dllfilepath,
        dllpath,
        setting,
        description,
        socode,
        sopath,
        uploadprotocolfile
        FROM tsl_sampler
        WHERE SamplerId = #{samplerId} FOR UPDATE
    </select>
    <select id="findSamplerIdByProtocolCode" resultType="java.lang.Integer">
        SELECT t.SamplerId
        FROM TSL_Sampler t
        WHERE t.ProtocolCode = #{protocolCode}
    </select>

    <insert id="batchInsertLianTongSampler">
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000013,'直流操作电源柜',18,'VirtualSampler_755000013','','','','','101.dll',NULL,'102','13','17');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000014,'10KV/400V',18,'VirtualSampler_755000014','','','','','102.dll',NULL,'1601','14','18');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000015,'10KV配电设备',18,'VirtualSampler_755000015','','','','','102.dll',NULL,'101','15','11');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000016,'低压配电柜',18,'VirtualSampler_755000016','','','','','103.dll',NULL,'201','16','12');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000017,'电容补偿柜',18,'VirtualSampler_755000017','','','','','104.dll',NULL,'204','17','27');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000018,'谐波抑制柜',18,'VirtualSampler_755000018','','','','','105.dll',NULL,'205','18','28');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000019,'备用电源投入装置',18,'VirtualSampler_755000019','','','','','106.dll',NULL,'203','19','30');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000020,'柴油发电机组',18,'VirtualSampler_755000020','','','','','107.dll',NULL,'301','20','13');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000021,'燃气轮发电机组',18,'VirtualSampler_755000021','','','','','108.dll',NULL,'302','21','65');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000022,'UPS',18,'VirtualSampler_755000022','','','','','109.dll',NULL,'501','22','31');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000023,'模块化UPS',18,'VirtualSampler_755000023','','','','','110.dll',NULL,'504','23','35');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000024,'交流配电屏',18,'VirtualSampler_755000024','','','','','111.dll',NULL,'1701','24','37');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000025,'交流配电箱',18,'VirtualSampler_755000025','','','','','112.dll',NULL,'1702','25','38');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000026,'冷冻系统',18,'VirtualSampler_755000026','','','','','113.dll',NULL,'705','26','42');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000027,'防雷箱',18,'VirtualSampler_755000027','','','','','115.dll',NULL,'2105','27','72');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000028,'环境',18,'VirtualSampler_755000028','','','','','116.dll',NULL,'1004','28','51');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000031,'48V交流屏',18,'VirtualSampler_755000031','','','','','119.dll',NULL,'1701','31','21');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000032,'空调系统',18,'VirtualSampler_755000032','','','','','120.dll',NULL,'704','32','42');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000034,'门禁系统',18,'VirtualSampler_755000034','','','','','122.dll',NULL,'1001','34','82');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000036,'逆变器',18,'VirtualSampler_755000036','','','','','124.dll',NULL,'2109','36','41');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000038,'热管设备',18,'VirtualSampler_755000038','','','','','126.dll',NULL,'806','38','69');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000039,'热交换设备',18,'VirtualSampler_755000039','','','','','127.dll',NULL,'805','39','68');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000040,'湿膜新风系统',18,'VirtualSampler_755000040','','','','','128.dll',NULL,'807','40','88');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000041,'舒适性空调',18,'VirtualSampler_755000041','','','','','129.dll',NULL,'701','41','45');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000042,'太阳能供电',18,'VirtualSampler_755000042','','','','','130.dll',NULL,'1801','42','33');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000044,'新风系统',18,'VirtualSampler_755000044','','','','','132.dll',NULL,'801','44','86');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000045,'蓄电池分区温控',18,'VirtualSampler_755000045','','','','','133.dll',NULL,'804','45','67');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000046,'48V整流屏',18,'VirtualSampler_755000046','','','','','134.dll',NULL,'404','46','20');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000048,'48V直流屏',18,'VirtualSampler_755000048','','','','','136.dll',NULL,'405','48','23');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000049,'直流远供局端电源',18,'VirtualSampler_755000049','','','','','137.dll',NULL,'604','49','32');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000050,'直流直流变换器',18,'VirtualSampler_755000050','','','','','138.dll',NULL,'602','50','34');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000051,'专用空调（风冷）',18,'VirtualSampler_755000051','','','','','139.dll',NULL,'702','51','43');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000052,'48V组合开关电源',18,'VirtualSampler_755000052','','','','','140.dll',NULL,'401','52','22');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000055,'48V配电屏',18,'VirtualSampler_755000055','','','','','155.dll',NULL,'601','55','39');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000056,'48V配电箱',18,'VirtualSampler_755000056','','','','','156.dll',NULL,'603','56','40');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000057,'48V铅酸阀控蓄电池组',18,'VirtualSampler_755000057','','','','','157.dll',NULL,'1101','57','24');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000059,'UPS铅酸阀控蓄电池组',18,'VirtualSampler_755000059','','','','','159.dll',NULL,'503','59','36');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000060,'动环监控系统',18,'VirtualSampler_755000060','','','','','113.dll',NULL,'1301','60','99');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000061,'240V铅酸阀控蓄电池组',18,'VirtualSampler_755000061','','','','','161.dll',NULL,'1103','61','26');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000062,'240V交流屏',18,'VirtualSampler_755000062','','','','','162.dll',NULL,'407','62','14');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000063,'240V整流屏',18,'VirtualSampler_755000063','','','','','163.dll',NULL,'408','63','15');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000064,'240V直流屏',18,'VirtualSampler_755000064','','','','','164.dll',NULL,'409','64','16');
        INSERT INTO TSL_Sampler (SamplerId, SamplerName, SamplerType, ProtocolCode, DLLCode, DLLVersion, ProtocolFilePath, DLLFilePath, DllPath, Setting, Description, SoCode, SoPath)	VALUES (123000065,'低压联络柜',18,'VirtualSampler_755000065','','','','','165.dll',NULL,'208','65','29');
    </insert>
</mapper>
