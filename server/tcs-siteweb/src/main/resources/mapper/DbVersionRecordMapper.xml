<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.DbVersionRecordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.DbVersionRecord">
        <id column="Id" property="id" />
        <result column="Version" property="version" />
        <result column="Module" property="module" />
        <result column="LastModifyTime" property="lastModifyTime" />
        <result column="UpdateTime" property="updateTime" />
        <result column="Feature" property="feature" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        Id, Version, Module, LastModifyTime, UpdateTime, Feature
    </sql>
</mapper>
