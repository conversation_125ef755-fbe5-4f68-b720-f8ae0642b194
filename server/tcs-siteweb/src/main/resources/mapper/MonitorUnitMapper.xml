<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.MonitorUnitMapper">
    <!-- 通用查询映射结果 -->

    <select id="selectByMonitorUnitId" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        ts.StationName,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        pi.MonitorUnitId,
        pi.ProjectName,
        pi.ContractNo,
        pi.InstallTime,
        ws.WorkStationName,
        <!-- 使用 databaseId 处理字符串聚合函数差异 -->
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        LEFT JOIN tbl_station ts ON mu.StationId = ts.StationId
        WHERE
        mu.MonitorUnitId = #{monitorUnitId}
        GROUP BY
        <!-- PostgreSQL 需要更多字段在 GROUP BY 中 -->
        <choose>
            <when test="_databaseId == 'postgresql'">
                mu.MonitorUnitId, ts.StationName, pi.MonitorUnitId, pi.ProjectName, pi.ContractNo,
                pi.InstallTime, ws.WorkStationName
            </when>
            <otherwise>
                mu.MonitorUnitId
            </otherwise>
        </choose>
        order by mu.UpdateTime DESC
    </select>

    <select id="selectByMonitorIds" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        pi.MonitorUnitId,
        pi.ProjectName,
        pi.ContractNo,
        pi.InstallTime,
        ws.WorkStationName,
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu
        LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        WHERE
        mu.MonitorUnitId IN
        <foreach collection="monitorUnitIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        <choose>
            <when test="_databaseId == 'postgresql'">
                mu.MonitorUnitId, pi.MonitorUnitId, pi.ProjectName, pi.ContractNo,
                pi.InstallTime, ws.WorkStationName
            </when>
            <otherwise>
                mu.MonitorUnitId
            </otherwise>
        </choose>
        order by mu.UpdateTime DESC
    </select>

    <select id="selectByMonitorIdsOMC" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        WHERE
        mu.MonitorUnitId IN
        <foreach collection="monitorUnitIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY mu.MonitorUnitId
        order by mu.UpdateTime DESC
    </select>

    <select id="selectAll" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        ts.StationName,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        pi.MonitorUnitId,
        pi.ProjectName,
        pi.ContractNo,
        pi.InstallTime,
        ws.WorkStationName,
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tbl_station ts ON mu.StationId = ts.StationId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        WHERE mu.MonitorUnitCategory != 0
        GROUP BY
        <choose>
            <when test="_databaseId == 'postgresql'">
                mu.MonitorUnitId, ts.StationName, pi.MonitorUnitId, pi.ProjectName, pi.ContractNo,
                pi.InstallTime, ws.WorkStationName
            </when>
            <otherwise>
                mu.MonitorUnitId
            </otherwise>
        </choose>
        order by mu.UpdateTime DESC
    </select>

    <select id="selectAllWithoutStation" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        WHERE mu.MonitorUnitCategory != 0
        GROUP BY mu.MonitorUnitId
        order by mu.UpdateTime DESC
    </select>

    <!-- 分页查询监控单元（包含站点信息） -->
    <select id="selectAllPage" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        ts.StationName,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        pi.MonitorUnitId,
        pi.ProjectName,
        pi.ContractNo,
        pi.InstallTime,
        ws.WorkStationName,
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tbl_station ts ON mu.StationId = ts.StationId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        WHERE mu.MonitorUnitCategory != 0
        GROUP BY
        <choose>
            <when test="_databaseId == 'postgresql'">
                mu.MonitorUnitId, ts.StationName, pi.MonitorUnitId, pi.ProjectName, pi.ContractNo,
                pi.InstallTime, ws.WorkStationName
            </when>
            <otherwise>
                mu.MonitorUnitId
            </otherwise>
        </choose>
        order by mu.UpdateTime DESC
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT #{offset}, #{size}
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT #{size} OFFSET #{offset}
            </when>
            <when test="_databaseId == 'h2'">
                LIMIT #{size} OFFSET #{offset}
            </when>
            <otherwise>
                LIMIT #{offset}, #{size}
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询监控单元（不包含站点信息） -->
    <select id="selectAllPageWithoutStation" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        WHERE mu.MonitorUnitCategory != 0
        GROUP BY mu.MonitorUnitId
        order by mu.UpdateTime DESC
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT #{offset}, #{size}
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT #{size} OFFSET #{offset}
            </when>
            <when test="_databaseId == 'h2'">
                LIMIT #{size} OFFSET #{offset}
            </when>
            <otherwise>
                LIMIT #{offset}, #{size}
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询监控单元（包含站点信息，带筛选条件） -->
    <select id="selectPageWithQuery" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        ts.StationName,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        pi.MonitorUnitId,
        pi.ProjectName,
        pi.ContractNo,
        pi.InstallTime,
        ws.WorkStationName,
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tbl_station ts ON mu.StationId = ts.StationId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        <where>
            AND mu.MonitorUnitCategory != 0
            <if test="queryDTO.monitorUnitName != null and queryDTO.monitorUnitName != ''">
                AND mu.MonitorUnitName LIKE CONCAT('%', #{queryDTO.monitorUnitName}, '%')
            </if>
            <if test="queryDTO.monitorUnitCategory != null">
                AND mu.MonitorUnitCategory = #{queryDTO.monitorUnitCategory}
            </if>
            <if test="queryDTO.ipAddress != null and queryDTO.ipAddress != ''">
                AND mu.IpAddress LIKE CONCAT('%', #{queryDTO.ipAddress}, '%')
            </if>
            <if test="queryDTO.connectState != null">
                AND mu.ConnectState = #{queryDTO.connectState}
            </if>
            <if test="queryDTO.isConfigOK != null">
                AND mu.IsConfigOK = #{queryDTO.isConfigOK}
            </if>
            <if test="queryDTO.isSync != null">
                AND mu.IsSync = #{queryDTO.isSync}
            </if>
            <if test="queryDTO.runMode != null">
                AND mu.RunMode = #{queryDTO.runMode}
            </if>
            <if test="queryDTO.workStationId != null">
                AND mu.WorkStationId = #{queryDTO.workStationId}
            </if>
            <if test="queryDTO.stationId != null">
                AND mu.StationId = #{queryDTO.stationId}
            </if>
            <if test="queryDTO.enable != null">
                AND mu.Enable = #{queryDTO.enable}
            </if>
        </where>
        GROUP BY
        <choose>
            <when test="_databaseId == 'postgresql'">
                mu.MonitorUnitId, ts.StationName, pi.MonitorUnitId, pi.ProjectName, pi.ContractNo,
                pi.InstallTime, ws.WorkStationName
            </when>
            <otherwise>
                mu.MonitorUnitId
            </otherwise>
        </choose>
        order by mu.UpdateTime DESC
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT #{offset}, #{size}
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT #{size} OFFSET #{offset}
            </when>
            <when test="_databaseId == 'h2'">
                LIMIT #{size} OFFSET #{offset}
            </when>
            <otherwise>
                LIMIT #{offset}, #{size}
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询监控单元（不包含站点信息，带筛选条件） -->
    <select id="selectPageWithoutStationWithQuery" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        <where>
            AND mu.MonitorUnitCategory != 0
            <if test="queryDTO.monitorUnitName != null and queryDTO.monitorUnitName != ''">
                AND mu.MonitorUnitName LIKE CONCAT('%', #{queryDTO.monitorUnitName}, '%')
            </if>
            <if test="queryDTO.monitorUnitCategory != null">
                AND mu.MonitorUnitCategory = #{queryDTO.monitorUnitCategory}
            </if>
            <if test="queryDTO.ipAddress != null and queryDTO.ipAddress != ''">
                AND mu.IpAddress LIKE CONCAT('%', #{queryDTO.ipAddress}, '%')
            </if>
            <if test="queryDTO.connectState != null">
                AND mu.ConnectState = #{queryDTO.connectState}
            </if>
            <if test="queryDTO.isConfigOK != null">
                AND mu.IsConfigOK = #{queryDTO.isConfigOK}
            </if>
            <if test="queryDTO.isSync != null">
                AND mu.IsSync = #{queryDTO.isSync}
            </if>
            <if test="queryDTO.runMode != null">
                AND mu.RunMode = #{queryDTO.runMode}
            </if>
            <if test="queryDTO.workStationId != null">
                AND mu.WorkStationId = #{queryDTO.workStationId}
            </if>
            <if test="queryDTO.stationId != null">
                AND mu.StationId = #{queryDTO.stationId}
            </if>
            <if test="queryDTO.enable != null">
                AND mu.Enable = #{queryDTO.enable}
            </if>
        </where>
        GROUP BY mu.MonitorUnitId
        order by mu.UpdateTime DESC
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT #{offset}, #{size}
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT #{size} OFFSET #{offset}
            </when>
            <when test="_databaseId == 'h2'">
                LIMIT #{size} OFFSET #{offset}
            </when>
            <otherwise>
                LIMIT #{offset}, #{size}
            </otherwise>
        </choose>
    </select>

    <!-- 统计监控单元总数（包含站点信息） -->
    <select id="countAll" resultType="long">
        SELECT COUNT(DISTINCT mu.MonitorUnitId)
        FROM TSL_MonitorUnit mu
        LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tbl_station ts ON mu.StationId = ts.StationId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        WHERE mu.MonitorUnitCategory != 0
    </select>

    <!-- 统计监控单元总数（不包含站点信息） -->
    <select id="countAllWithoutStation" resultType="long">
        SELECT COUNT(DISTINCT mu.MonitorUnitId)
        FROM TSL_MonitorUnit mu
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        WHERE mu.MonitorUnitCategory != 0
    </select>

    <!-- 统计监控单元总数（包含站点信息，带筛选条件） -->
    <select id="countWithQuery" resultType="long">
        SELECT COUNT(DISTINCT mu.MonitorUnitId)
        FROM TSL_MonitorUnit mu
        LEFT JOIN TBL_MonitorUnitProjectInfo pi ON mu.MonitorUnitId = pi.MonitorUnitId
        LEFT JOIN tbl_station ts ON mu.StationId = ts.StationId
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        LEFT JOIN tbl_workstation ws ON mu.WorkStationId = ws.WorkStationId
        <where>
            AND mu.MonitorUnitCategory != 0
            <if test="queryDTO.monitorUnitName != null and queryDTO.monitorUnitName != ''">
                AND mu.MonitorUnitName LIKE CONCAT('%', #{queryDTO.monitorUnitName}, '%')
            </if>
            <if test="queryDTO.monitorUnitCategory != null">
                AND mu.MonitorUnitCategory = #{queryDTO.monitorUnitCategory}
            </if>
            <if test="queryDTO.ipAddress != null and queryDTO.ipAddress != ''">
                AND mu.IpAddress LIKE CONCAT('%', #{queryDTO.ipAddress}, '%')
            </if>
            <if test="queryDTO.connectState != null">
                AND mu.ConnectState = #{queryDTO.connectState}
            </if>
            <if test="queryDTO.isConfigOK != null">
                AND mu.IsConfigOK = #{queryDTO.isConfigOK}
            </if>
            <if test="queryDTO.isSync != null">
                AND mu.IsSync = #{queryDTO.isSync}
            </if>
            <if test="queryDTO.runMode != null">
                AND mu.RunMode = #{queryDTO.runMode}
            </if>
            <if test="queryDTO.workStationId != null">
                AND mu.WorkStationId = #{queryDTO.workStationId}
            </if>
            <if test="queryDTO.stationId != null">
                AND mu.StationId = #{queryDTO.stationId}
            </if>
            <if test="queryDTO.enable != null">
                AND mu.Enable = #{queryDTO.enable}
            </if>
        </where>
    </select>

    <!-- 统计监控单元总数（不包含站点信息，带筛选条件） -->
    <select id="countWithoutStationWithQuery" resultType="long">
        SELECT COUNT(DISTINCT mu.MonitorUnitId)
        FROM TSL_MonitorUnit mu
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        <where>
            AND mu.MonitorUnitCategory != 0
            <if test="queryDTO.monitorUnitName != null and queryDTO.monitorUnitName != ''">
                AND mu.MonitorUnitName LIKE CONCAT('%', #{queryDTO.monitorUnitName}, '%')
            </if>
            <if test="queryDTO.monitorUnitCategory != null">
                AND mu.MonitorUnitCategory = #{queryDTO.monitorUnitCategory}
            </if>
            <if test="queryDTO.ipAddress != null and queryDTO.ipAddress != ''">
                AND mu.IpAddress LIKE CONCAT('%', #{queryDTO.ipAddress}, '%')
            </if>
            <if test="queryDTO.connectState != null">
                AND mu.ConnectState = #{queryDTO.connectState}
            </if>
            <if test="queryDTO.isConfigOK != null">
                AND mu.IsConfigOK = #{queryDTO.isConfigOK}
            </if>
            <if test="queryDTO.isSync != null">
                AND mu.IsSync = #{queryDTO.isSync}
            </if>
            <if test="queryDTO.runMode != null">
                AND mu.RunMode = #{queryDTO.runMode}
            </if>
            <if test="queryDTO.workStationId != null">
                AND mu.WorkStationId = #{queryDTO.workStationId}
            </if>
            <if test="queryDTO.stationId != null">
                AND mu.StationId = #{queryDTO.stationId}
            </if>
            <if test="queryDTO.enable != null">
                AND mu.Enable = #{queryDTO.enable}
            </if>
        </where>
    </select>

    <!-- 分页查询非RMU监控单元（不包含站点信息，排除monitorUnitCategory为1或24的记录） -->
    <select id="selectNonRmuPageWithoutStationWithQuery" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        <where>
            <!-- 排除RMU监控单元：monitorUnitCategory不能为1或24，也不能为0（中心自诊断） -->
            AND mu.MonitorUnitCategory NOT IN (0, 1, 24)
            <if test="queryDTO.monitorUnitName != null and queryDTO.monitorUnitName != ''">
                AND mu.MonitorUnitName LIKE CONCAT('%', #{queryDTO.monitorUnitName}, '%')
            </if>
            <if test="queryDTO.monitorUnitCategory != null">
                AND mu.MonitorUnitCategory = #{queryDTO.monitorUnitCategory}
            </if>
            <if test="queryDTO.ipAddress != null and queryDTO.ipAddress != ''">
                AND mu.IpAddress LIKE CONCAT('%', #{queryDTO.ipAddress}, '%')
            </if>
            <if test="queryDTO.connectState != null">
                AND mu.ConnectState = #{queryDTO.connectState}
            </if>
            <if test="queryDTO.isConfigOK != null">
                AND mu.IsConfigOK = #{queryDTO.isConfigOK}
            </if>
            <if test="queryDTO.isSync != null">
                AND mu.IsSync = #{queryDTO.isSync}
            </if>
            <if test="queryDTO.runMode != null">
                AND mu.RunMode = #{queryDTO.runMode}
            </if>
            <if test="queryDTO.workStationId != null">
                AND mu.WorkStationId = #{queryDTO.workStationId}
            </if>
            <if test="queryDTO.stationId != null">
                AND mu.StationId = #{queryDTO.stationId}
            </if>
            <if test="queryDTO.enable != null">
                AND mu.Enable = #{queryDTO.enable}
            </if>
        </where>
        GROUP BY mu.MonitorUnitId
        order by mu.UpdateTime DESC
        <choose>
            <when test="_databaseId == 'mysql'">
                LIMIT #{offset}, #{size}
            </when>
            <when test="_databaseId == 'postgresql'">
                LIMIT #{size} OFFSET #{offset}
            </when>
            <when test="_databaseId == 'h2'">
                LIMIT #{size} OFFSET #{offset}
            </when>
            <otherwise>
                LIMIT #{offset}, #{size}
            </otherwise>
        </choose>
    </select>

    <!-- 统计非RMU监控单元总数（不包含站点信息，排除monitorUnitCategory为1或24的记录） -->
    <select id="countNonRmuWithoutStationWithQuery" resultType="long">
        SELECT COUNT(DISTINCT mu.MonitorUnitId)
        FROM TSL_MonitorUnit mu
        LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        <where>
            <!-- 排除RMU监控单元：monitorUnitCategory不能为1或24，也不能为0（中心自诊断） -->
            AND mu.MonitorUnitCategory NOT IN (0, 1, 24)
            <if test="queryDTO.monitorUnitName != null and queryDTO.monitorUnitName != ''">
                AND mu.MonitorUnitName LIKE CONCAT('%', #{queryDTO.monitorUnitName}, '%')
            </if>
            <if test="queryDTO.monitorUnitCategory != null">
                AND mu.MonitorUnitCategory = #{queryDTO.monitorUnitCategory}
            </if>
            <if test="queryDTO.ipAddress != null and queryDTO.ipAddress != ''">
                AND mu.IpAddress LIKE CONCAT('%', #{queryDTO.ipAddress}, '%')
            </if>
            <if test="queryDTO.connectState != null">
                AND mu.ConnectState = #{queryDTO.connectState}
            </if>
            <if test="queryDTO.isConfigOK != null">
                AND mu.IsConfigOK = #{queryDTO.isConfigOK}
            </if>
            <if test="queryDTO.isSync != null">
                AND mu.IsSync = #{queryDTO.isSync}
            </if>
            <if test="queryDTO.runMode != null">
                AND mu.RunMode = #{queryDTO.runMode}
            </if>
            <if test="queryDTO.workStationId != null">
                AND mu.WorkStationId = #{queryDTO.workStationId}
            </if>
            <if test="queryDTO.stationId != null">
                AND mu.StationId = #{queryDTO.stationId}
            </if>
            <if test="queryDTO.enable != null">
                AND mu.Enable = #{queryDTO.enable}
            </if>
        </where>
    </select>

    <select id="selectByMonitorUnitIdWithoutStation" resultType="com.siteweb.tcs.siteweb.dto.MonitorUnitDTO">
        SELECT
        mu.MonitorUnitId,
        mu.MonitorUnitName,
        mu.MonitorUnitCategory,
        mu.MonitorUnitCode,
        mu.WorkStationId,
        mu.StationId,
        mu.IpAddress,
        mu.RunMode,
        mu.ConfigFileCode,
        mu.ConfigUpdateTime,
        mu.SampleConfigCode,
        mu.SoftwareVersion,
        mu.Description,
        mu.StartTime,
        mu.HeartbeatTime,
        mu.ConnectState,
        mu.UpdateTime,
        mu.IsSync,
        mu.SyncTime,
        mu.IsConfigOK,
        mu.ConfigFileCode_Old,
        mu.SampleConfigCode_Old,
        mu.AppCongfigId as appConfigId,
        mu.CanDistribute,
        mu.Enable,
        mu.ProjectName as rdsServer,
        mu.ContractNo as dataServer,
        mu.FSU,
        <choose>
            <when test="_databaseId == 'mysql'">
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </when>
            <when test="_databaseId == 'postgresql'">
                string_agg(tp.PortNo::text, ',' ORDER BY tp.PortNo) as portNos
            </when>
            <otherwise>
                GROUP_CONCAT(tp.PortNo ORDER BY tp.PortNo ASC SEPARATOR ',') as portNos
            </otherwise>
        </choose>
        FROM
        TSL_MonitorUnit mu LEFT JOIN tsl_port tp ON mu.MonitorUnitId = tp.MonitorUnitId
        WHERE
        mu.MonitorUnitId = #{monitorUnitId}
        GROUP BY mu.MonitorUnitId
    </select>

    <insert id="insertDto">
        INSERT INTO TSL_MonitorUnit (MonitorUnitId, MonitorUnitName, MonitorUnitCategory, MonitorUnitCode,
        WorkStationId, StationId, IpAddress, RunMode, ConfigFileCode,
        ConfigUpdateTime, SampleConfigCode, SoftwareVersion, Description,
        StartTime, HeartbeatTime, ConnectState, UpdateTime, IsSync,
        SyncTime, IsConfigOK, ConfigFileCode_Old, SampleConfigCode_Old, AppCongfigId,
        CanDistribute, Enable, ProjectName, ContractNo, InstallTime, FSU)
        VALUES (#{monitorUnitId}, #{monitorUnitName}, #{monitorUnitCategory}, #{monitorUnitCode},
        #{workStationId}, #{stationId}, #{ipAddress}, #{runMode}, #{configFileCode},
        #{configUpdateTime}, #{sampleConfigCode}, #{softwareVersion}, #{description},
        #{startTime}, #{heartbeatTime}, #{connectState}, #{updateTime}, #{isSync},
        #{syncTime}, #{isConfigOK}, #{configFileCode_Old}, #{sampleConfigCode_Old}, #{appConfigId},
        #{canDistribute}, #{enable}, #{rdsServer}, #{dataServer}, #{installTime}, #{fsu});
    </insert>

    <update id="updateDto">
        UPDATE TSL_MonitorUnit SET
        MonitorUnitName = COALESCE(#{monitorUnitName}, MonitorUnitName),
        MonitorUnitCategory = COALESCE(#{monitorUnitCategory}, MonitorUnitCategory),
        MonitorUnitCode = COALESCE(#{monitorUnitCode}, MonitorUnitCode),
        WorkStationId = COALESCE(#{workStationId}, WorkStationId),
        StationId = COALESCE(#{stationId}, StationId),
        IpAddress = COALESCE(#{ipAddress}, IpAddress),
        RunMode = COALESCE(#{runMode}, RunMode),
        ConfigFileCode = COALESCE(#{configFileCode}, ConfigFileCode),
        ConfigUpdateTime = COALESCE(#{configUpdateTime}, ConfigUpdateTime),
        SampleConfigCode = COALESCE(#{sampleConfigCode}, SampleConfigCode),
        SoftwareVersion = COALESCE(#{softwareVersion}, SoftwareVersion),
        Description = COALESCE(#{description}, Description),
        StartTime = COALESCE(#{startTime}, StartTime),
        HeartbeatTime = COALESCE(#{heartbeatTime}, HeartbeatTime),
        ConnectState = COALESCE(#{connectState}, ConnectState),
        UpdateTime = COALESCE(#{updateTime}, UpdateTime),
        IsSync = COALESCE(#{isSync}, IsSync),
        SyncTime = COALESCE(#{syncTime}, SyncTime),
        IsConfigOK = COALESCE(#{isConfigOK}, IsConfigOK),
        ConfigFileCode_Old = COALESCE(#{configFileCode_Old}, ConfigFileCode_Old),
        SampleConfigCode_Old = COALESCE(#{sampleConfigCode_Old}, SampleConfigCode_Old),
        AppCongfigId = COALESCE(#{appConfigId}, AppCongfigId),
        CanDistribute = COALESCE(#{canDistribute}, CanDistribute),
        Enable = COALESCE(#{enable}, Enable),
        ProjectName = COALESCE(#{rdsServer}, ProjectName),
        ContractNo = COALESCE(#{dataServer}, ContractNo),
        InstallTime = COALESCE(#{installTime}, InstallTime),
        FSU = COALESCE(#{fsu}, FSU)
        WHERE MonitorUnitId = #{monitorUnitId};
    </update>

</mapper>
