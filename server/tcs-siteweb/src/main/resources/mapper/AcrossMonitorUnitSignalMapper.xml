<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.AcrossMonitorUnitSignalMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.AcrossMonitorUnitSignal">
        <id column="SignalId" property="signalId" />
        <result column="StationId" property="stationId" />
        <result column="MonitorUnitId" property="monitorUnitId" />
        <result column="EquipmentId" property="equipmentId" />
        <result column="Expression" property="expression" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        StationId, MonitorUnitId, EquipmentId, SignalId, Expression
    </sql>

    <!-- 根据条件查询跨站信号 -->
    <select id="findByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tsl_acrossmonitorunitsignal
        <where>
            <if test="condition.stationId != null">
                AND StationId = #{condition.stationId}
            </if>
            <if test="condition.monitorUnitId != null">
                AND MonitorUnitId = #{condition.monitorUnitId}
            </if>
            <if test="condition.equipmentId != null">
                AND EquipmentId = #{condition.equipmentId}
            </if>
            <if test="condition.signalId != null">
                AND SignalId = #{condition.signalId}
            </if>
        </where>
    </select>

    <!-- 根据设备ID和信号ID查询跨站信号 -->
    <select id="findByEquipmentIdAndSignalId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tsl_acrossmonitorunitsignal
        WHERE EquipmentId = #{equipmentId}
        AND SignalId = #{signalId}
    </select>

    <!-- 创建跨站信号 -->
    <insert id="createAcrossMonitorUnitSignal">
        INSERT INTO tsl_acrossmonitorunitsignal (
            StationId, MonitorUnitId, EquipmentId, SignalId, Expression
        ) VALUES (
            #{stationId}, #{monitorUnitId}, #{equipmentId}, #{signalId}, #{expression}
        )
    </insert>
</mapper>