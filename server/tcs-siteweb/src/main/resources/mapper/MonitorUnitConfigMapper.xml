<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.MonitorUnitConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.MonitorUnitConfig">
        <id column="Id" property="id" />
        <result column="AppConfigId" property="appConfigId" />
        <result column="SiteWebTimeOut" property="siteWebTimeOut" />
        <result column="RetryTimes" property="retryTimes" />
        <result column="HeartBeat" property="heartBeat" />
        <result column="EquipmentTimeOut" property="equipmentTimeOut" />
        <result column="PortInterruptCount" property="portInterruptCount" />
        <result column="PortInitializeInternal" property="portInitializeInternal" />
        <result column="MaxPortInitializeTimes" property="maxPortInitializeTimes" />
        <result column="PortQueryTimeOut" property="portQueryTimeOut" />
        <result column="DataSaveTimes" property="dataSaveTimes" />
        <result column="HistorySignalSavedTimes" property="historySignalSavedTimes" />
        <result column="HistoryBatterySavedTimes" property="historyBatterySavedTimes" />
        <result column="HistoryEventSavedTimes" property="historyEventSavedTimes" />
        <result column="CardRecordSavedCount" property="cardRecordSavedCount" />
        <result column="ControlLog" property="controlLog" />
        <result column="IpAddressDS" property="ipAddressDS" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        Id, AppConfigId, SiteWebTimeOut, RetryTimes, HeartBeat, EquipmentTimeOut,
        PortInterruptCount, PortInitializeInternal, MaxPortInitializeTimes, PortQueryTimeOut,
        DataSaveTimes, HistorySignalSavedTimes, HistoryBatterySavedTimes, HistoryEventSavedTimes,
        CardRecordSavedCount, ControlLog, IpAddressDS
    </sql>

    <!-- 根据监控单元ID查找配置 -->
    <select id="findByMonitorUnitId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tsl_monitorunitconfig
        WHERE AppConfigId IN (
        SELECT AppCongfigId FROM tsl_monitorunit WHERE MonitorUnitId = #{monitorUnitId}
        )
    </select>

</mapper>