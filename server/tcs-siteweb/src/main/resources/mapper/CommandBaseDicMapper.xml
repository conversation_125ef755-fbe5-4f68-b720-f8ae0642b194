<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.CommandBaseDicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.CommandBaseDic">
        <id column="BaseTypeId" property="baseTypeId" />
        <result column="BaseTypeName" property="baseTypeName" />
        <result column="BaseEquipmentId" property="baseEquipmentId" />
        <result column="EnglishName" property="englishName" />
        <result column="BaseLogicCategoryId" property="baseLogicCategoryId" />
        <result column="CommandType" property="commandType" />
        <result column="BaseStatusId" property="baseStatusId" />
        <result column="ExtendField1" property="extendField1" />
        <result column="ExtendField2" property="extendField2" />
        <result column="ExtendField3" property="extendField3" />
        <result column="Description" property="description" />
        <result column="BaseNameExt" property="baseNameExt" />
        <result column="IsSystem" property="isSystem" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        BaseTypeId, BaseTypeName, BaseEquipmentId, EnglishName, BaseLogicCategoryId, CommandType, BaseStatusId, ExtendField1, ExtendField2, ExtendField3, Description, BaseNameExt, IsSystem
    </sql>

    <!-- 生成控制基类字典 -->
    <insert id="generateCommandBaseDic">
        INSERT INTO TBL_CommandBaseDic
        (BaseTypeId,
         BaseTypeName,
         BaseEquipmentId,
         EnglishName,
         BaseLogicCategoryId,
         CommandType,
         BaseStatusId,
         ExtendField1,
         ExtendField2,
         ExtendField3,
         Description,
         BaseNameExt,
         IsSystem)
        SELECT #{baseTypeId},
               commandBaseDic.BaseTypeName,
               commandBaseDic.BaseEquipmentId,
               commandBaseDic.EnglishName,
               commandBaseDic.BaseLogicCategoryId,
               commandBaseDic.CommandType,
               commandBaseDic.BaseStatusId,
               commandBaseDic.ExtendField1,
               commandBaseDic.ExtendField2,
               commandBaseDic.ExtendField3,
               commandBaseDic.Description,
               commandBaseDic.BaseNameExt,
               0
        FROM TBL_CommandBaseDic commandBaseDic
        WHERE commandBaseDic.BaseTypeId = #{sourceId}
    </insert>

</mapper> 