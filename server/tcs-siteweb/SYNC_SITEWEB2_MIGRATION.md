# SiteWeb2同步功能迁移文档

## 概述

本文档描述了从 `C:\sql\siteweb6-config-server\server\primary\src\main\java\org\siteweb\config\primary\service\SyncSiteWeb2Service.java` 迁移到 `server/tcs-siteweb/` 目录下的SiteWeb2同步功能的实现。

## 迁移内容

### 1. 服务接口和实现

- **接口**: `ISyncSiteWeb2Service.java`
- **实现**: `SyncSiteWeb2ServiceImpl.java`
- **Mapper**: `SyncSiteWeb2Mapper.java`
- **控制器**: `SyncSiteWeb2Controller.java`

### 2. 核心功能

#### 主要方法：`syncSiteWeb2Config()`

该方法实现了以下功能：

1. **资源结构检查**: 检查资源结构表是否为空
2. **站点数据过滤**: 过滤掉未分组的站点（bordNumber != 0）
3. **公共父节点查找**: 找到所有站点的公共父节点
4. **资源结构创建**: 根据公共父节点递归创建资源结构列表
5. **批量插入**: 批量插入资源结构到数据库
6. **设备更新**: 更新设备的resourceStructureId
7. **日志记录**: 记录配置变更日志

#### 辅助方法：`syncResourceStructureFromStationData()`

该方法调用Mapper中的SQL方法进行数据同步：

- 更新初始结构类型
- 插入中心结构
- 插入子结构
- 插入站点结构
- 插入房屋结构
- 更新设备资源结构
- 插入配置变更日志

### 3. 实体类扩展

为了支持迁移功能，对以下实体类进行了扩展：

#### StationStructure
- 添加了 `children` 字段（子结构列表）
- 添加了 `stations` 字段（关联的站点列表）

#### Station
- 添加了 `houses` 字段（关联的房屋列表）

### 4. 服务接口扩展

为了支持迁移功能，对以下服务接口进行了扩展：

#### IStationStructureService
- `findUngroupedStructure(Integer structureGroupId)`: 查找未分组结构
- `tree()`: 获取站点结构树
- `findCommonParentByStationIds(List<Integer> stationIds, StationStructure stationStructureTree)`: 根据站点ID列表查找公共父节点

#### IStationStructureMapService
- `findStationIdsByStructureId(Integer structureId)`: 根据结构ID查找站点ID列表

#### IResourceStructureService
- `batchInsert(List<ResourceStructure> resourceStructures)`: 批量插入资源结构
- `findResourceStructures()`: 查找所有资源结构

#### IEquipmentService
- `updateResourceStructureIdIfNotMapped(Integer originId, Integer resourceStructureId)`: 更新设备资源结构ID（如果未映射）

### 5. Mapper实现

`SyncSiteWeb2Mapper` 包含了所有必要的SQL操作方法：

- 获取SC配置值
- 更新资源结构类型
- 插入各种结构（中心、子、站点、房屋）
- 更新结构名称和路径
- 删除无效结构
- 更新设备资源结构
- 插入配置变更日志

## 使用方法

### 1. 通过控制器调用

```http
POST /api/sync-siteweb2/sync-config
```

### 2. 直接调用服务

```java
@Autowired
private ISyncSiteWeb2Service syncSiteWeb2Service;

public void performSync() {
    syncSiteWeb2Service.syncSiteWeb2Config();
}
```

## 注意事项

1. **事务管理**: 同步操作使用 `@Transactional` 注解确保数据一致性
2. **错误处理**: 所有操作都包含适当的错误处理和日志记录
3. **性能考虑**: 使用批量插入操作提高性能
4. **数据完整性**: 在创建新结构前检查是否已存在，避免重复创建

## 依赖关系

该功能依赖以下服务和组件：

- `IResourceStructureService`: 资源结构服务
- `IStationService`: 站点服务
- `IStationStructureService`: 站点结构服务
- `IStationStructureMapService`: 站点结构映射服务
- `IConfigChangeMacroLogService`: 配置变更日志服务
- `IPrimaryKeyValueService`: 主键值服务
- `IEquipmentService`: 设备服务

## 测试

可以通过以下方式测试迁移功能：

1. 调用控制器接口进行同步
2. 检查数据库中的资源结构数据
3. 验证设备资源结构ID的更新
4. 检查配置变更日志记录

## 迁移完成状态

✅ 服务接口和实现类创建完成  
✅ Mapper接口和SQL方法实现完成  
✅ 实体类扩展完成  
✅ 服务接口扩展完成  
✅ 控制器创建完成  
✅ 错误处理和日志记录完成  
✅ 文档编写完成  

迁移功能已完全实现并可以投入使用。
