# 技术设计文档 - tcs-tracking

# 技术栈规范
- 依赖清单（按分类表格呈现）：
  | 类型 | 名称 | 版本 |
  |---|---|---|
  | 框架 | Spring Boot | 3.1.5 |
  | 数据库 | PostgreSQL | 15 |

# 架构设计

## tcs模块依赖关系
- tcs-tracking作为TCS平台的北向插件，负责收集、存储和分析用户行为和系统运行数据
- 依赖关系：
  - 依赖tcs-common：使用公共组件、异常处理、响应封装等基础功能
  - 依赖tcs-core：使用核心功能和插件管理机制
  - 被其他模块依赖：其他模块可以通过API调用tcs-tracking的埋点功能

## 内部模块或对象的依赖关系

### 模块层次结构
```
tcs-tracking
├── controller (依赖service)
├── service (依赖connector, dal)
├── connector (依赖domain)
│   ├── letter (消息定义)
│   └── process (Actor实现)
├── domain (核心领域模型)
├── dal (依赖domain)
│   ├── entity (数据库实体)
│   ├── mapper (数据库访问)
│   └── dto (数据传输对象)
└── util (工具类)
```

### 核心对象依赖关系
- **TrackingPlugin** → **TrackingGuard**：插件创建Actor系统的根Actor
- **TrackingGuard** → **TrackingDataAdapter**, **TrackingDataStore**, **TrackingDataSpout**, **TrackingStrategyManager**, **TrackingAlertManager**, **TrackingAggregator**：根Actor创建并管理子Actor
- **TrackingDataAdapter** → **TrackingDataStore**：数据适配器将验证后的数据发送给存储器
- **TrackingDataStore** → **TrackingDataSpout**：存储器将数据发送给数据分发器
- **TrackingDataService** → **TrackingGuard**：服务层将消息发送给根Actor
- **TrackingStrategyService** → **TrackingGuard**：服务层将策略变更消息发送给根Actor

## 目录/文件树结构及说明

### 主要目录结构
```
tcs-tracking/
├── src/
│   ├── main/
│   │   ├── java/com/siteweb/tcs/tracking/
│   │   │   ├── TrackingPlugin.java (插件入口类)
│   │   │   ├── controller/ (控制器层)
│   │   │   ├── service/ (服务层)
│   │   │   ├── connector/ (连接器层)
│   │   │   │   ├── letter/ (消息定义)
│   │   │   │   └── process/ (Actor实现)
│   │   │   ├── domain/ (领域模型)
│   │   │   ├── dal/ (数据访问层)
│   │   │   │   ├── entity/ (实体类)
│   │   │   │   ├── mapper/ (Mapper接口)
│   │   │   │   └── dto/ (数据传输对象)
│   │   │   └── util/ (工具类)
│   │   ├── resources/
│   │   │   ├── application.yml (应用配置)
│   │   │   └── db/migration/ (数据库迁移脚本)
│   ├── test/ (测试代码)
│   └── docs/ (文档)
└── pom.xml (项目依赖)
```

### 关键文件说明
- **TrackingPlugin.java**：插件入口类，继承自NorthPlugin，负责初始化Actor系统
- **TrackingGuard.java**：埋点系统的根Actor，负责协调其他Actor
- **TrackingStrategyController.java**：提供埋点策略管理的RESTful API
- **TrackingDataController.java**：提供埋点数据收集和查询的RESTful API
- **TrackingStrategyService.java**：实现埋点策略管理的业务逻辑
- **TrackingDataService.java**：实现埋点数据收集和查询的业务逻辑
- **TrackingDataMessage.java**：埋点数据消息类，用于Actor间通信
- **TrackingStrategy.java**：埋点策略实体类
- **TrackingPoint.java**：埋点点位实体类
- **TrackingData.java**：埋点数据实体类

# 对象设计

## 核心类（用文字描述继承/实现关系）

### Actor类继承关系
- **TrackingPlugin** ← **NorthPlugin**：埋点系统的入口类，负责创建和管理Actor系统
- **TrackingGuard** ← **AbstractActor**：埋点系统的根Actor，负责管理其他Actor
- **TrackingDataAdapter** ← **AbstractActor**：负责处理埋点数据的适配和验证
- **TrackingDataStore** ← **AbstractActor**：负责将埋点数据存储到数据库
- **TrackingDataSpout** ← **AbstractActor**：负责将埋点数据发送到订阅者
- **TrackingStrategyManager** ← **AbstractActor**：负责管理埋点策略
- **TrackingAlertManager** ← **AbstractActor**：负责管理告警规则和触发告警
- **TrackingAggregator** ← **AbstractActor**：负责聚合埋点数据

### 实体类关系
- **TrackingStrategy**：埋点策略实体类，包含策略的基本信息和状态
- **TrackingPoint**：埋点点位实体类，属于策略，定义具体的埋点位置
- **TrackingData**：埋点数据实体类，记录具体的埋点数据
- **TrackingAggregation**：埋点数据聚合实体类，存储埋点数据的聚合结果
- **TrackingAlertRule**：埋点告警规则实体类，定义告警触发条件

### 消息类关系
- **TrackingDataMessage** ← **WindowLogItem**：埋点数据消息，用于Actor间传递埋点数据
- **TrackingStrategyMessage**：埋点策略消息，用于Actor间传递策略变更
- **SubscribeAction**：订阅动作消息，用于外部Actor订阅埋点数据
- **UnsubscribeAction**：取消订阅动作消息，用于外部Actor取消订阅

## 对象职责矩阵
| 类名 | 职责 | 协作对象 | 生命周期管理策略 |
|---|---|---|---|
| TrackingPlugin | 插件入口，创建和管理Actor系统 | TrackingGuard | 由TCS平台管理，随应用启动和关闭 |
| TrackingGuard | 根Actor，管理其他Actor | TrackingDataAdapter, TrackingDataStore, TrackingDataSpout, TrackingStrategyManager, TrackingAlertManager, TrackingAggregator | 由TrackingPlugin创建和管理 |
| TrackingDataAdapter | 处理埋点数据的适配和验证 | TrackingDataStore | 由TrackingGuard创建和管理 |
| TrackingDataStore | 将埋点数据存储到数据库 | TrackingDataMapper | 由TrackingGuard创建和管理 |
| TrackingDataSpout | 将埋点数据发送到订阅者 | 外部Actor | 由TrackingGuard创建和管理 |
| TrackingStrategyManager | 管理埋点策略 | TrackingStrategyMapper | 由TrackingGuard创建和管理 |
| TrackingAlertManager | 管理告警规则和触发告警 | TrackingAlertRuleMapper | 由TrackingGuard创建和管理 |
| TrackingAggregator | 聚合埋点数据 | TrackingAggregationMapper | 由TrackingGuard创建和管理 |
| TrackingStrategyService | 提供埋点策略的业务逻辑 | TrackingStrategyMapper, TrackingPointMapper | 由Spring容器管理 |
| TrackingDataService | 提供埋点数据的业务逻辑 | TrackingDataMapper, TrackingGuard | 由Spring容器管理 |
| TrackingAlertService | 提供告警规则的业务逻辑 | TrackingAlertRuleMapper | 由Spring容器管理 |

# 数据规范

## 表结构设计

### tcs_tracking_strategy（埋点策略表）
| 字段名 | 类型 | 约束 | 索引 | 说明 |
|---|---|---|---|---|
| id | INT | PK, AUTO_INCREMENT | PRIMARY | 策略ID |
| name | VARCHAR(100) | NOT NULL | UNIQUE | 策略名称 |
| description | VARCHAR(500) | | | 策略描述 |
| type | VARCHAR(50) | NOT NULL | | 策略类型：SERVER/WEB/HYBRID |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'INACTIVE' | | 策略状态：ACTIVE/INACTIVE/PAUSED |
| config | TEXT | | | 策略配置JSON |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | 更新时间 |

### tcs_tracking_point（埋点点位表）
| 字段名 | 类型 | 约束 | 索引 | 说明 |
|---|---|---|---|---|
| id | INT | PK, AUTO_INCREMENT | PRIMARY | 点位ID |
| strategy_id | INT | NOT NULL, FK | INDEX | 关联的策略ID |
| code | VARCHAR(100) | NOT NULL | | 埋点编码 |
| name | VARCHAR(100) | NOT NULL | | 埋点名称 |
| description | VARCHAR(500) | | | 埋点描述 |
| type | VARCHAR(50) | NOT NULL | | 埋点类型：EVENT/METRIC/STATE |
| source | VARCHAR(50) | NOT NULL | | 埋点来源：SERVER/WEB/PLUGIN |
| plugin_id | VARCHAR(100) | | | 插件ID（如果来源是插件） |
| config | TEXT | | | 埋点配置JSON |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | 更新时间 |

### tcs_tracking_data（埋点数据表）
| 字段名 | 类型 | 约束 | 索引 | 说明 |
|---|---|---|---|---|
| id | BIGINT | PK, AUTO_INCREMENT | PRIMARY | 数据ID |
| point_id | INT | NOT NULL, FK | INDEX | 埋点点位ID |
| strategy_id | INT | NOT NULL, FK | INDEX | 策略ID |
| timestamp | TIMESTAMP | NOT NULL | INDEX | 埋点时间 |
| source_id | VARCHAR(100) | | INDEX | 数据来源ID |
| user_id | VARCHAR(100) | | INDEX | 用户ID |
| session_id | VARCHAR(100) | | INDEX | 会话ID |
| data | TEXT | | | 埋点数据JSON |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | | 创建时间 |

### tcs_tracking_aggregation（埋点数据聚合表）
| 字段名 | 类型 | 约束 | 索引 | 说明 |
|---|---|---|---|---|
| id | INT | PK, AUTO_INCREMENT | PRIMARY | 聚合ID |
| point_id | INT | NOT NULL, FK | INDEX | 埋点点位ID |
| strategy_id | INT | NOT NULL, FK | INDEX | 策略ID |
| aggregation_type | VARCHAR(20) | NOT NULL | | 聚合类型：COUNT/SUM/AVG/MIN/MAX |
| time_window | VARCHAR(20) | NOT NULL | | 时间窗口：MINUTE/HOUR/DAY/WEEK/MONTH |
| window_start | TIMESTAMP | NOT NULL | INDEX | 窗口开始时间 |
| window_end | TIMESTAMP | NOT NULL | | 窗口结束时间 |
| dimension | VARCHAR(100) | | INDEX | 聚合维度 |
| dimension_value | VARCHAR(100) | | INDEX | 维度值 |
| value | DOUBLE | | | 聚合值 |
| sample_count | INT | | | 样本数量 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | | 创建时间 |

### tcs_tracking_alert_rule（埋点告警规则表）
| 字段名 | 类型 | 约束 | 索引 | 说明 |
|---|---|---|---|---|
| id | INT | PK, AUTO_INCREMENT | PRIMARY | 规则ID |
| point_id | INT | NOT NULL, FK | INDEX | 埋点点位ID |
| strategy_id | INT | NOT NULL, FK | INDEX | 策略ID |
| name | VARCHAR(100) | NOT NULL | | 规则名称 |
| description | VARCHAR(500) | | | 规则描述 |
| condition_type | VARCHAR(20) | NOT NULL | | 条件类型：THRESHOLD/TREND/ANOMALY |
| condition_value | VARCHAR(100) | NOT NULL | | 条件值 |
| time_window | VARCHAR(20) | NOT NULL | | 时间窗口：MINUTE/HOUR/DAY/WEEK/MONTH |
| severity | VARCHAR(20) | NOT NULL | | 严重程度：INFO/WARNING/ERROR/CRITICAL |
| notification_channels | VARCHAR(500) | | | 通知渠道JSON |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'ACTIVE' | | 规则状态：ACTIVE/INACTIVE |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | 更新时间 |

## 实体状态转换图

### TrackingStrategy状态转换
- **INACTIVE** → **ACTIVE**：通过activateStrategy方法激活策略
- **ACTIVE** → **PAUSED**：通过pauseStrategy方法暂停策略
- **PAUSED** → **ACTIVE**：通过resumeStrategy方法恢复策略
- **ACTIVE/PAUSED** → **INACTIVE**：通过deactivateStrategy方法停用策略

### TrackingPoint状态
埋点点位没有独立的状态，它的状态由所属的策略决定。当策略处于ACTIVE状态时，点位可以收集数据；当策略处于INACTIVE或PAUSED状态时，点位不收集数据。

### TrackingAlertRule状态转换
- **INACTIVE** → **ACTIVE**：通过activateRule方法激活规则
- **ACTIVE** → **INACTIVE**：通过deactivateRule方法停用规则

# API规范

## 必须实现的端点清单

### 策略管理API
- **POST /api/tracking/strategies**：创建埋点策略
- **GET /api/tracking/strategies**：查询埋点策略列表
- **GET /api/tracking/strategies/{id}**：查询埋点策略详情
- **PUT /api/tracking/strategies/{id}**：更新埋点策略
- **DELETE /api/tracking/strategies/{id}**：删除埋点策略
- **POST /api/tracking/strategies/{id}/activate**：激活埋点策略
- **POST /api/tracking/strategies/{id}/deactivate**：停用埋点策略
- **POST /api/tracking/strategies/{id}/pause**：暂停埋点策略
- **POST /api/tracking/strategies/{id}/resume**：恢复埋点策略

### 点位管理API
- **POST /api/tracking/strategies/{strategyId}/points**：创建埋点点位
- **GET /api/tracking/strategies/{strategyId}/points**：查询埋点点位列表
- **GET /api/tracking/points/{id}**：查询埋点点位详情
- **PUT /api/tracking/points/{id}**：更新埋点点位
- **DELETE /api/tracking/points/{id}**：删除埋点点位

### 数据收集API
- **POST /api/tracking/data**：收集埋点数据
- **GET /api/tracking/data**：查询埋点数据
- **GET /api/tracking/data/aggregation**：查询埋点数据聚合结果

### 告警规则API
- **POST /api/tracking/alert-rules**：创建告警规则
- **GET /api/tracking/alert-rules**：查询告警规则列表
- **GET /api/tracking/alert-rules/{id}**：查询告警规则详情
- **PUT /api/tracking/alert-rules/{id}**：更新告警规则
- **DELETE /api/tracking/alert-rules/{id}**：删除告警规则
- **POST /api/tracking/alert-rules/{id}/activate**：激活告警规则
- **POST /api/tracking/alert-rules/{id}/deactivate**：停用告警规则

## 安全策略矩阵

| API路径 | 认证方式 | 鉴权规则 | 加密要求 |
|---|---|---|---|
| /api/tracking/strategies* | JWT | ROLE_ADMIN, ROLE_TRACKING_ADMIN | HTTPS |
| /api/tracking/points* | JWT | ROLE_ADMIN, ROLE_TRACKING_ADMIN | HTTPS |
| /api/tracking/data (POST) | API Key | 验证API Key有效性 | HTTPS |
| /api/tracking/data (GET) | JWT | ROLE_ADMIN, ROLE_TRACKING_ADMIN, ROLE_TRACKING_USER | HTTPS |
| /api/tracking/data/aggregation | JWT | ROLE_ADMIN, ROLE_TRACKING_ADMIN, ROLE_TRACKING_USER | HTTPS |
| /api/tracking/alert-rules* | JWT | ROLE_ADMIN, ROLE_TRACKING_ADMIN | HTTPS |

# 异常处理

## 错误分类

### 业务错误
- **StrategyNotFoundException**：埋点策略不存在
- **PointNotFoundException**：埋点点位不存在
- **InvalidStrategyStatusException**：无效的策略状态
- **DuplicateStrategyNameException**：重复的策略名称
- **DuplicatePointCodeException**：重复的点位编码
- **InvalidDataFormatException**：无效的数据格式

### 技术错误
- **DatabaseException**：数据库操作异常
- **CacheException**：缓存操作异常
- **ActorSystemException**：Actor系统异常
- **SerializationException**：序列化异常
- **ValidationException**：数据验证异常

## tcs.common的异常继承树

### 异常继承关系
- **BaseException**：所有自定义异常的基类，继承自RuntimeException
  - **BusinessException**：业务异常基类
    - **UserValidException**：用户验证异常
    - **StrategyNotFoundException**：埋点策略不存在异常
    - **PointNotFoundException**：埋点点位不存在异常
    - **InvalidStrategyStatusException**：无效策略状态异常
    - **DuplicateStrategyNameException**：重复策略名称异常
    - **DuplicatePointCodeException**：重复点位编码异常
  - **TechnicalException**：技术异常基类（未在代码中找到，但在设计中提及）
    - **DatabaseException**：数据库操作异常
    - **CacheException**：缓存操作异常
    - **ActorSystemException**：Actor系统异常
    - **SerializationException**：序列化异常
    - **ValidationException**：数据验证异常

### 异常职责
- **BaseException**：提供基础异常功能，包含错误码、错误消息和详细信息
- **BusinessException**：表示业务逻辑错误，可以包含ExceptionCode枚举
- **TechnicalException**：表示技术实现错误，如数据库连接失败、缓存访问失败等

### 错误码
- 使用ExceptionCode枚举定义错误码，包括：
  - 系统级错误码：SYSTEM_ERROR, PARAM_ERROR, UNAUTHORIZED等
  - 业务级错误码：BUSINESS_ERROR, DATA_NOT_FOUND, DATA_ALREADY_EXISTS等
  - 用户相关错误码：USER_AUTHORIZE_FAIL, USER_NOT_FOUND, PASSWORD_ERROR等
  - 设备相关错误码：DEVICE_NOT_FOUND, DEVICE_OFFLINE, DEVICE_BUSY等

## 异常传播规范
- **Controller层**：捕获所有异常，转换为HTTP响应
- **Service层**：可以抛出业务异常，但应捕获技术异常并转换为业务异常
- **DAO层**：捕获数据库异常，转换为DatabaseException
- **Actor层**：捕获异常，记录日志，并向监督者报告

## 遵守规范
- 所有业务异常应继承自BusinessException
- 所有技术异常应继承自TechnicalException
- 异常应包含错误码、错误消息和详细信息
- 异常应记录到日志系统
- 使用ResponseHelper.failed()方法统一处理异常响应

# 开发规范

## 代码标准
- 遵循《阿里巴巴Java开发手册》规范
- 使用Google Java Style Guide进行代码格式化
- 类、方法、字段必须添加Javadoc注释
- 复杂逻辑添加行内注释
- 变量命名使用驼峰命名法
- 常量使用全大写，单词间用下划线分隔
- 包名使用全小写

## 设计模式
- **单例模式**：用于TrackingPlugin等全局唯一的对象
- **工厂模式**：用于创建Actor实例
- **策略模式**：用于实现不同类型的埋点数据处理策略
- **观察者模式**：用于实现埋点数据的订阅和通知
- **命令模式**：用于封装埋点操作
- **责任链模式**：用于埋点数据的处理流程

## 代码复杂度管理
- 方法圈复杂度不超过15
- 方法行数不超过80行
- 类行数不超过1000行
- 参数数量不超过5个
- 嵌套层级不超过4层

# 核心设计

## 关键数据流序列

### 埋点数据收集流程
1. 客户端调用TrackingDataService.collectData方法
2. TrackingDataService创建TrackingDataMessage消息
3. TrackingDataService将消息发送给TrackingGuard Actor
4. TrackingGuard将消息转发给TrackingDataAdapter Actor
5. TrackingDataAdapter验证消息并转发给TrackingDataStore Actor
6. TrackingDataStore将数据保存到数据库
7. TrackingDataStore将数据转发给TrackingDataSpout Actor
8. TrackingDataSpout将数据发送给所有订阅者
9. TrackingAggregator接收数据并进行聚合
10. TrackingAlertManager检查数据是否触发告警规则

```
客户端 -> TrackingDataService -> TrackingGuard -> TrackingDataAdapter -> TrackingDataStore -> 数据库
                                                                      ↓
                                                                TrackingDataSpout -> 订阅者
                                                                      ↓
                                                                TrackingAggregator
                                                                      ↓
                                                                TrackingAlertManager
```

### 埋点策略管理流程
1. 客户端调用TrackingStrategyService.createStrategy方法
2. TrackingStrategyService将策略保存到数据库
3. TrackingStrategyService创建TrackingStrategyMessage消息
4. TrackingStrategyService将消息发送给TrackingGuard Actor
5. TrackingGuard将消息转发给TrackingStrategyManager Actor
6. TrackingStrategyManager更新内存中的策略缓存

```
客户端 -> TrackingStrategyService -> 数据库
                                ↓
                          TrackingGuard -> TrackingStrategyManager -> 策略缓存
```

# 技术决策

## 核心决策

### 技术栈选择原因
- **Java Spring Boot**：
  - 提供完整的Web应用开发框架
  - 简化配置和部署
  - 提供丰富的集成能力
  - 与TCS平台的其他模块保持技术一致性

- **Pekko Actor**：
  - 提供强大的并发和分布式处理能力
  - 适合处理高并发的埋点数据
  - 提供容错和自愈能力
  - Actor模型适合埋点数据的异步处理流程

### 设计模式选择原因
- **单例模式**：确保TrackingPlugin等全局对象的唯一性
- **工厂模式**：灵活创建Actor实例，便于扩展
- **观察者模式**：实现埋点数据的订阅和通知机制
- **责任链模式**：构建埋点数据的处理流水线，便于扩展处理步骤

## 遗留问题
- 埋点数据的长期存储和归档策略尚未确定
- 大规模数据下的性能优化需要进一步测试和调优
- 跨集群部署时的数据一致性保证需要进一步设计
- 与第三方分析工具的集成需要进一步规划

# 安全规范

## 安全考虑及实现
- **认证与授权**
  - 使用JWT进行用户认证
  - 基于角色的访问控制（ROLE_ADMIN, ROLE_TRACKING_ADMIN, ROLE_TRACKING_USER）
  - 所有管理类接口需要管理员权限
  - 数据查询接口可使用普通用户权限

- **数据安全**
  - 敏感数据在传输和存储时进行加密
  - 用户ID等敏感信息进行脱敏处理
  - 数据库访问权限最小化原则

- **API安全**
  - 所有API使用HTTPS加密传输
  - 数据收集API使用API Key进行认证
  - 请求参数进行严格验证，防止注入攻击
  - 实现请求频率限制，防止流量攻击

- **审计与监控**
  - 记录所有关键操作的审计日志
  - 实时监控异常访问行为
  - 定期安全审计和漏洞扫描

- **安全运维**
  - 遵循安全最佳实践配置服务器和应用
  - 定期更新依赖库，修复已知安全漏洞
  - 实现安全事件响应机制
