package com.siteweb.tcs.tracking.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.tracking.dal.entity.TrackingStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 埋点策略Mapper接口
 */
@Mapper
public interface TrackingStrategyMapper extends BaseMapper<TrackingStrategy> {

    /**
     * 根据状态查询策略
     */
    @Select("SELECT * FROM tcs_tracking_strategy WHERE status = #{status}")
    List<TrackingStrategy> selectByStatus(@Param("status") String status);
    
    /**
     * 根据类型查询策略
     */
    @Select("SELECT * FROM tcs_tracking_strategy WHERE type = #{type}")
    List<TrackingStrategy> selectByType(@Param("type") String type);
    
    /**
     * 根据名称查询策略
     */
    @Select("SELECT * FROM tcs_tracking_strategy WHERE name = #{name}")
    TrackingStrategy selectByName(@Param("name") String name);
}
