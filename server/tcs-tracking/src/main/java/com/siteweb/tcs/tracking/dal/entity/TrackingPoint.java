package com.siteweb.tcs.tracking.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 埋点点位实体类
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("tcs_tracking_point")
public class TrackingPoint {
    
    /**
     * 点位ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 关联的策略ID
     */
    private Integer strategyId;
    
    /**
     * 埋点编码
     */
    private String code;
    
    /**
     * 埋点名称
     */
    private String name;
    
    /**
     * 埋点描述
     */
    private String description;
    
    /**
     * 埋点类型：EVENT/METRIC/STATE
     */
    private String type;
    
    /**
     * 埋点来源：SERVER/WEB/PLUGIN
     */
    private String source;
    
    /**
     * 插件ID（如果来源是插件）
     */
    private String pluginId;
    
    /**
     * 埋点配置JSON
     */
    private String config;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 埋点类型枚举
     */
    public enum PointType {
        EVENT,     // 事件型埋点
        METRIC,    // 指标型埋点
        STATE      // 状态型埋点
    }
    
    /**
     * 埋点来源枚举
     */
    public enum PointSource {
        SERVER,    // 服务端
        WEB,       // Web端
        PLUGIN     // 插件
    }
}
