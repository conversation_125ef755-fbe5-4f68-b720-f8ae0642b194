package com.siteweb.tcs.tracking.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 埋点告警规则实体类
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("tcs_tracking_alert_rule")
public class TrackingAlertRule {
    
    /**
     * 规则ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 埋点点位ID
     */
    private Integer pointId;
    
    /**
     * 规则名称
     */
    private String name;
    
    /**
     * 规则描述
     */
    private String description;
    
    /**
     * 条件类型：THRESHOLD/CHANGE_RATE/ANOMALY
     */
    private String conditionType;
    
    /**
     * 条件值
     */
    private String conditionValue;
    
    /**
     * 严重程度：INFO/WARNING/ERROR/CRITICAL
     */
    private String severity;
    
    /**
     * 规则状态：ACTIVE/INACTIVE
     */
    private String status;
    
    /**
     * 通知配置JSON
     */
    private String notificationConfig;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 关联的埋点点位（非数据库字段）
     */
    @TableField(exist = false)
    private TrackingPoint trackingPoint;
    
    /**
     * 条件类型枚举
     */
    public enum ConditionType {
        THRESHOLD,    // 阈值条件
        CHANGE_RATE,  // 变化率条件
        ANOMALY       // 异常检测条件
    }
    
    /**
     * 严重程度枚举
     */
    public enum Severity {
        INFO,       // 信息
        WARNING,    // 警告
        ERROR,      // 错误
        CRITICAL    // 严重
    }
    
    /**
     * 规则状态枚举
     */
    public enum RuleStatus {
        ACTIVE,     // 活跃
        INACTIVE    // 非活跃
    }
}
