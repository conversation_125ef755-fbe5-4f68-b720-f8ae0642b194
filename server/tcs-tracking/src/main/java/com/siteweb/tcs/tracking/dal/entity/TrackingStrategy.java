package com.siteweb.tcs.tracking.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 埋点策略实体类
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("tcs_tracking_strategy")
public class TrackingStrategy {
    
    /**
     * 策略ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 策略名称
     */
    private String name;
    
    /**
     * 策略描述
     */
    private String description;
    
    /**
     * 策略类型：SERVER/WEB/HYBRID
     */
    private String type;
    
    /**
     * 策略状态：ACTIVE/INACTIVE/PAUSED
     */
    private String status;
    
    /**
     * 策略配置JSON
     */
    private String config;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 关联的埋点点位列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<TrackingPoint> trackingPoints;
    
    /**
     * 策略类型枚举
     */
    public enum StrategyType {
        SERVER,    // 服务端埋点
        WEB,       // Web端埋点
        HYBRID     // 混合埋点
    }
    
    /**
     * 策略状态枚举
     */
    public enum StrategyStatus {
        ACTIVE,    // 活跃状态
        INACTIVE,  // 非活跃状态
        PAUSED     // 暂停状态
    }
}
