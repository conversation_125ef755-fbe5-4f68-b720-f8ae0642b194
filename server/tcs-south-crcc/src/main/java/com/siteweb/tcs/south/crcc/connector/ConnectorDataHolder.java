package com.siteweb.tcs.south.crcc.connector;

import lombok.Data;
import org.apache.pekko.actor.ActorRef;
import org.springframework.stereotype.Component;

/**
 * 连接器数据持有者
 * <p>
 * The connector data holder, used to share data between components
 * </p>
 */
@Data
@Component
public class ConnectorDataHolder {

    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 根Actor引用
     */
    private ActorRef rootActor;
} 