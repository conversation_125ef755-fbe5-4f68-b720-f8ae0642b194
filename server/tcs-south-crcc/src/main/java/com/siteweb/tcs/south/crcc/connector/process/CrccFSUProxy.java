package com.siteweb.tcs.south.crcc.connector.process;

import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

/**
 * 设备代理Actor
 * <p>
 * 负责处理设备的消息和状态
 * </p>
 */
@Slf4j
public class CrccFSUProxy extends AbstractActor {

    /**
     * Returns the props for creating a {@link CrccFSUProxy} Actor.
     *
     * @return a Props for creating a CrccFSUProxy Actor
     */
    public static Props props() {
        return Props.create(CrccFSUProxy.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .matchAny(message -> {
                    log.debug("CrccFSUProxy received message: {}", message);
                    // 处理各种消息
                })
                .build();
    }
} 