package com.siteweb.tcs.south.crcc.web.service;

import com.siteweb.tcs.south.crcc.dal.dto.DeviceDTO;

import java.util.List;

/**
 * 设备服务接口
 * <p>
 * 定义设备管理相关操作
 * </p>
 */
public interface DeviceService {

    /**
     * 根据ID获取设备
     *
     * @param id 设备ID
     * @return 设备信息，如果未找到则返回null
     */
    DeviceDTO getDevice(String id);
    
    /**
     * 获取所有设备列表
     *
     * @return 设备列表
     */
    List<DeviceDTO> listDevices();
    
    /**
     * 创建新设备
     *
     * @param device 设备信息
     * @return 创建后的设备信息
     */
    DeviceDTO createDevice(DeviceDTO device);
    
    /**
     * 更新设备信息
     *
     * @param id 设备ID
     * @param device 更新的设备信息
     * @return 更新后的设备信息
     */
    DeviceDTO updateDevice(String id, DeviceDTO device);
    
    /**
     * 删除设备
     *
     * @param id 设备ID
     * @return 是否删除成功
     */
    boolean deleteDevice(String id);
} 