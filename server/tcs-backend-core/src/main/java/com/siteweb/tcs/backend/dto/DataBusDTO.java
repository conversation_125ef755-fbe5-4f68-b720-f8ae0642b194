package com.siteweb.tcs.backend.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@NoArgsConstructor
@Data
public class DataBusDTO {


    //  "主机状态",
    Map<String, Long> realTimeData;
    //  "采集器状态",
    Map<String, Long> collectorState;
    //  "设备状态",
    Map<String, Long> deviceState;
    //  "告警",
    Map<String, Long> alarm;
    //  "控制命令返回",
    Map<String, Long> controlCommandReturn;
    //  "控制命令请求",
    Map<String, Long> controlCommandRequest;


}
