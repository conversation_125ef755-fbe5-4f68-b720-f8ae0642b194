package com.siteweb.tcs.backend.config;

import com.siteweb.tcs.backend.plugin.DynamicClassLoader;
import com.siteweb.tcs.common.exception.code.StandardTechnicalErrorCode;
import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.hub.domain.GatewayRegistry;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.ActorSystem;
import org.apache.pekko.cluster.Cluster;
import org.apache.pekko.cluster.sharding.ClusterSharding;
import org.apache.pekko.http.javadsl.Http;
import org.apache.pekko.management.javadsl.PekkoManagement;
import org.apache.pekko.stream.Materializer;
import org.apache.pekko.stream.SystemMaterializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (2025-04-18)
 **/


@Configuration
public class PekkoClusterConfig {

    private static final Logger logger = LoggerFactory.getLogger(PekkoClusterConfig.class);

    @Value("${tcs.system.name:TcsSystem}")
    private String tcsSystemName;

    @Value("${pekko.cluster.is-seed-node:false}")
    private boolean isSeedNode;


    /**
     * 动态类加载器
     * 用于访问插件内部的类对象
     *
     * @return
     */
    @Bean
    public DynamicClassLoader initDynamicClassLoader() {
        var classLoader = this.getClass().getClassLoader();
        return new DynamicClassLoader(classLoader);
    }


    /**
     * 创建默认ActorSystem
     *
     * @return
     */
    @Bean
    public ActorSystem createActorSystem(DynamicClassLoader classLoader) throws IOException {
        logger.info("Starting to create ActorSystem: {}", tcsSystemName);
        //加载application.conf配置文件
        String confFile = "application.conf";
        Config baseConfig = ConfigFactory.load();
        if (Files.exists(Path.of(confFile))) {
            logger.info("从外部加载配置文件: {}", confFile);
            baseConfig = ConfigFactory.parseFile(new File(confFile)).resolve();
        }
        var system = ActorSystem.create(tcsSystemName, baseConfig, classLoader);
        logger.info("ActorSystem created successfully: {}", system.name());
        return system;
    }

    /**
     * 创建默认Cluster集群
     *
     * @param system
     * @return
     */
    @Bean
    public Cluster createCluster(ActorSystem system) {
        logger.info("Initializing cluster for system: {}", system.name());

        Cluster cluster = Cluster.get(system);

        // Add cluster event logging
        cluster.registerOnMemberUp(() -> {
            logger.info("Cluster member is UP: {}", cluster.selfAddress());

            // Initialize sharding coordinators after cluster is up
            CompletableFuture.delayedExecutor(2, TimeUnit.SECONDS).execute(() -> {
                try {
                    logger.info("Cluster is UP, sharding coordinators should be ready");
                } catch (Exception e) {
                    logger.error("Error during post-cluster-up initialization", e);
                }
            });
        });

        cluster.registerOnMemberRemoved(() -> {
            logger.warn("Cluster member was REMOVED: {}", cluster.selfAddress());
        });

        // If this is not a seed node, wait a bit for seed nodes to be available
        if (!isSeedNode) {
            logger.info("This is not a seed node, waiting for seed nodes to be available...");
            CompletableFuture.delayedExecutor(5, TimeUnit.SECONDS).execute(() -> {
                logger.info("Attempting to join cluster...");
            });
        } else {
            logger.info("This is a SEED node, cluster should initialize automatically");
        }

        return cluster;
    }


    /**
     * 创建默认sharding
     *
     * @param system
     * @return
     */
    @Bean
    public ClusterSharding createSharding(ActorSystem system) {
        logger.info("Initializing cluster sharding");
        ClusterSharding sharding = ClusterSharding.get(system);
        return sharding;
    }


    /**
     * 启动PekkoManagement
     *
     * @param system
     * @return
     */
    @Bean
    public PekkoManagement startPekkoManagement(ActorSystem system) {
        logger.info("Starting Pekko Management");
        try {
            var management = PekkoManagement.get(system);
            management.start();
            logger.info("Pekko Management started successfully");
            return management;
        } catch (Exception e) {
            throw StandardTechnicalErrorCode.SYSTEM_ERROR.toException("Failed to start Pekko Management", e);
        }
    }

    /**
     * 创建Materializer
     *
     * @param actorSystem
     * @return
     */
    @Bean()
    public Materializer createMaterializer(ActorSystem actorSystem) {
        return SystemMaterializer.get(actorSystem).materializer();
    }


    /**
     * 启动HttpSingen
     *
     * @param system
     * @return
     */
    @Bean
    public Http createHttp(ActorSystem system) {
        return Http.get(system);
    }


    /**
     * 创建全局唯一实例  GatewayHub
     *
     * @param context
     * @return
     */
    @Bean("gateway-registry")
    public ActorRef createGatewayHub(ClusterContext context, RedisTemplate<String, Object> redisTemplate) {
        return ClusterContext.createSingleton(GatewayRegistry.class);
    }

}
