package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.backend.service.DashboardService;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    @GetMapping("/plugins_count")
    public ResponseEntity<ResponseResult> getPluginCount() {
        return ResponseHelper.successful(dashboardService.getPluginCount());
    }

    @GetMapping("/probes_count")
    public ResponseEntity<ResponseResult> getProbeCount() {
        return ResponseHelper.successful(dashboardService.getProbeCount());
    }

    @GetMapping("/probes_logs_top")
    public ResponseEntity<ResponseResult> getProbeLogSizeTop() {
        return ResponseHelper.successful(dashboardService.getProbeLogSizeTop(20));
    }

    @GetMapping("/runtime")
    public ResponseEntity<ResponseResult> getRuntimeData() {
        return ResponseHelper.successful(dashboardService.getRuntime());
    }

    @GetMapping("/metrics_realtime")
    public ResponseEntity<ResponseResult> getMetricsData() {
        return ResponseHelper.successful(dashboardService.getRealTimeMetrics());
    }
    //获取数据总线数据
    @GetMapping("/data_bus")
    public ResponseEntity<ResponseResult> getDataBus() {
        return ResponseHelper.successful(dashboardService.getDataBus());
    }


}
