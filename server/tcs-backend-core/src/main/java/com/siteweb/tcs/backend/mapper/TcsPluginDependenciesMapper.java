package com.siteweb.tcs.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.backend.entity.TcsPluginDependencies;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;


/**
 * <AUTHOR> (2024-10-25)
 **/
public interface TcsPluginDependenciesMapper extends BaseMapper<TcsPluginDependencies> {


    @Update("update tcs_plugin_dependencies set dependencies = #{depends.dependencies,typeHandler=com.siteweb.tcs.backend.json.handlers.StringListHandler} where applicationName = #{depends.applicationName} and pluginId = #{depends.pluginId}")
    int updateOne(@Param("depends") TcsPluginDependencies depends);

}