package com.siteweb.tcs.backend.plugin;

import com.siteweb.tcs.backend.mapper.TcsPluginMapper;
import com.siteweb.tcs.common.util.StringUtils;
import org.pf4j.PluginStatusProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (2024-06-08)
 **/

@Component
public class ThingConnectPluginStatusProvider implements PluginStatusProvider {

    @Autowired
    private TcsPluginMapper tcsPluginMapper;

    @Value("${spring.plugins.runtime-mode}")
    private String runtimeMode;

    private static final String RUNTIME_MODE_DEVELOPMENT = "development";

    @Override
    public boolean isPluginDisabled(String pluginId) {
        System.out.println(pluginId);
        if(StringUtils.equals(RUNTIME_MODE_DEVELOPMENT, runtimeMode)){
            return false;
        }
        Boolean isEnable = tcsPluginMapper.isEnable(pluginId);
        return isEnable == null || !isEnable;
    }

    @Override
    public void disablePlugin(String pluginId) {
        tcsPluginMapper.disablePlugin(pluginId);
    }

    @Override
    public void enablePlugin(String pluginId) {
        tcsPluginMapper.enablePlugin(pluginId);
    }
}
