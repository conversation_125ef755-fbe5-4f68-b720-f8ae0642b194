package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.backend.service.EquipmentStateService;
import com.siteweb.tcs.backend.service.MonitorUnitService;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.hub.domain.letter.LiveEquipmentState;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> zhou
 * @description EquipmentProvider
 * @createTime 2024-02-22 14:31:17
 */
@RestController(value = "EquipmentProvider")
@RequestMapping("/equipment")
@Api(value = "EquipmentProvider", tags = "视频设备管理")
public class EquipmentController {


    @Autowired
    private MonitorUnitService monitorUnitService;

    @Autowired
    private EquipmentStateService equipmentStateService;


    public EquipmentController() {

    }

    //根据设备id和采集器id获取设备信息
    @GetMapping(value = "/{monitorUnitId}/{equipmentId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMonitorUnitByForeignDeviceId(@PathVariable("equipmentId") String equipmentId,
                                                                          @PathVariable("monitorUnitId") Integer monitorUnitId) {
        return ResponseHelper.successful(monitorUnitService.getForeignDeviceByID(monitorUnitId, equipmentId));
    }

    //获取所有设备数量
    @GetMapping(value = "/count", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEquipmentCount() {
        List<LiveEquipmentState> liveEquipmentStates = equipmentStateService.getAllEquipmentState();
        return ResponseHelper.successful(liveEquipmentStates.size());
    }

    //启动设备
    @PostMapping(value = "/start/{monitorUnitId}/{equipmentId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> startEquipment(@PathVariable("equipmentId") Integer equipmentId,
                                                         @PathVariable("monitorUnitId") Integer monitorUnitId) {
        equipmentStateService.control(monitorUnitId, LifeCycleEventType.START, List.of(equipmentId));
        return ResponseHelper.successful();
    }

    //停止采集器
    @PostMapping(value = "/stop/{monitorUnitID}/{equipmentId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> stopMonitorUnit(@PathVariable("monitorUnitID") Integer monitorUnitID,
                                                          @PathVariable("equipmentId") Integer equipmentId) {
        equipmentStateService.control(monitorUnitID, LifeCycleEventType.STOP, List.of(equipmentId));
        return ResponseHelper.successful();
    }

    //重启采集器
    @PostMapping(value = "/restart/{monitorUnitID}/{equipmentId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> restartMonitorUnit(@PathVariable("monitorUnitID") Integer monitorUnitID,
                                                             @PathVariable("equipmentId") Integer equipmentId) {
        equipmentStateService.control(monitorUnitID, LifeCycleEventType.RESTART, List.of(equipmentId));
        return ResponseHelper.successful();
    }


    @ApiOperation("获取所有视频设备")
    @GetMapping("all")
    public ResponseEntity<ResponseResult> findAll() {
        return ResponseHelper.successful("OK");
    }


//    @Autowired
//    EquipmentService equipmentService;
//
//    @ApiOperation("获取所有视频设备")
//    @GetMapping
//    public ResponseEntity<ResponseResult> findAll() {
//        return ResponseHelper.successful(equipmentService.findTopTen());
//    }

}
