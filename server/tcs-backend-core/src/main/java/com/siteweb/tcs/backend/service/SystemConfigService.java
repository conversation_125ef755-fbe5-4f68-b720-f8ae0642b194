package com.siteweb.tcs.backend.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;

@Service
public class SystemConfigService {

    private static final Logger log = LoggerFactory.getLogger(SystemConfigService.class);

    private static final String DOCKER_CONFIG_PATH = "/home/<USER>";
    private static final String DEV_CONFIG_PATH = "tcs-core/src/main/resources";

    @Autowired
    private ApplicationContext context;

    private Path getConfigPath(String fileName) {
        // 检查是否在Docker环境中运行
        if (new File(DOCKER_CONFIG_PATH).exists()) {
            return Path.of(DOCKER_CONFIG_PATH).resolve(fileName);
        } else {
            return Path.of(DEV_CONFIG_PATH).resolve(fileName);
        }
    }

    public String getConfigFileContent(String fileName) throws IOException {
        Path filePath = getConfigPath(fileName);
        if (!Files.exists(filePath)) {
            throw new IOException("无法找到配置文件: " + fileName);
        }
        return Files.readString(filePath, StandardCharsets.UTF_8);
    }

    public String getApplicationConfig() throws IOException {
        return getConfigFileContent("application.yml");
    }

    public String getApplicationMysqlConfig() throws IOException {
        return getConfigFileContent("application-mysql.yml");
    }

    public void saveConfigFile(String fileName, String content) throws IOException {
        Path filePath = getConfigPath(fileName);

        try {
            // 确保目录存在
            Files.createDirectories(filePath.getParent());

            // 写入新内容
            Files.writeString(filePath, content, StandardCharsets.UTF_8);
            log.info("配置文件 {} 已成功保存到 {}", fileName, filePath);
            restartApplication();
        } catch (IOException e) {
            log.error("保存配置文件 {} 时发生错误", fileName, e);
            throw new IOException("保存配置文件失败: " + e.getMessage(), e);
        }
    }

    public void restartApplication() {
        //todo lqp 后续考虑重启docker容器的方式重启应用
    }
}