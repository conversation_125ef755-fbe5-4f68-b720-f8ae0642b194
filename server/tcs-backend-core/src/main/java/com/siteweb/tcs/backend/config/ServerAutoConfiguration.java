/*
 * Copyright 2014-2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.siteweb.tcs.backend.config;


import com.siteweb.tcs.backend.controller.UiController;
import com.siteweb.tcs.backend.extensions.PluginResourceScanner;
import com.siteweb.tcs.common.runtime.PathDefine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.config.WebFluxConfigurer;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.thymeleaf.spring6.templateresolver.SpringResourceTemplateResolver;
import org.thymeleaf.templatemode.TemplateMode;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(ServerProperties.class)
public class ServerAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(ServerAutoConfiguration.class);


    @Autowired
    private ServerProperties serverProperties;


    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    @ConditionalOnMissingBean
    public UiController homeUiController(PluginResourceScanner pluginResourceScanner) throws IOException {
        return new UiController(pluginResourceScanner, serverProperties);
    }


    @Bean
    PluginResourceScanner pluginResourceScanner() throws IOException {
        PluginResourceScanner scanner = new PluginResourceScanner(
                this.applicationContext,
                PathDefine.CLASSPATH_EXTENSION_RESOURCE_LOCATION
        );
//        scanner.scan();
        return scanner;
    }


    @Bean
    public SpringResourceTemplateResolver adminTemplateResolver() {
        SpringResourceTemplateResolver resolver = new SpringResourceTemplateResolver();
        resolver.setApplicationContext(this.applicationContext);
        resolver.setPrefix(PathDefine.STATIC_RESOURCE_LOCATION);
        resolver.setSuffix(".html");
        resolver.setTemplateMode(TemplateMode.HTML);
        resolver.setCharacterEncoding(StandardCharsets.UTF_8.name());
        resolver.setCacheable(this.serverProperties.isCacheTemplates());
        resolver.setOrder(10);
        resolver.setCheckExistence(true);
        return resolver;
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
    public static class ReactiveUiConfiguration {

        @Configuration(proxyBeanMethods = false)
        public static class AdminUiWebfluxConfig implements WebFluxConfigurer {

            private final ServerProperties adminUi;


            public AdminUiWebfluxConfig(ServerProperties adminUi) {
                this.adminUi = adminUi;
            }

            @Override
            public void addResourceHandlers(org.springframework.web.reactive.config.ResourceHandlerRegistry registry) {
                registry.addResourceHandler("/**")
                        .addResourceLocations(PathDefine.CLASSPATH_RESOURCE_LOCATIONS)
                        .setCacheControl(this.adminUi.getCache().toCacheControl());
                registry.addResourceHandler(PathDefine.PLUGINS_HANDLER)
                        .addResourceLocations(PathDefine.CLASSPATH_EXTENSION_RESOURCE_LOCATIONS)
                        .setCacheControl(this.adminUi.getCache().toCacheControl());
            }
        }

    }


    @Configuration(proxyBeanMethods = false)
    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    public static class ServletUiConfiguration {

        @Configuration(proxyBeanMethods = false)
        public static class AdminUiWebMvcConfig implements WebMvcConfigurer {
            private final ServerProperties adminUi;

            public AdminUiWebMvcConfig(ServerProperties adminUi) {
                this.adminUi = adminUi;
            }

            @Override
            public void addViewControllers(ViewControllerRegistry registry) {
                registry.addViewController("/").setViewName("forward:/index.html");
            }

            @Override
            public void configurePathMatch(PathMatchConfigurer configure) {
                configure.addPathPrefix("/api/thing", this::shouldAddPrefix);
            }

            private boolean shouldAddPrefix(Class<?> controllerClass) {
                // 这里可以添加你自己的逻辑，例如基于包名、注解等条件
                return controllerClass.isAnnotationPresent(RestController.class);
            }

            @Override
            public void addResourceHandlers(org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry registry) {
                registry.addResourceHandler("/**")
                        .addResourceLocations(PathDefine.CLASSPATH_RESOURCE_LOCATIONS)
                        .setCacheControl(this.adminUi.getCache().toCacheControl());
//                registry.addResourceHandler(PathDefine.PLUGINS_HANDLER)
//                        .addResourceLocations(PathDefine.CLASSPATH_EXTENSION_RESOURCE_LOCATIONS)
//                        .setCacheControl(this.adminUi.getCache().toCacheControl());
            }

            @Override
            public void configureAsyncSupport(AsyncSupportConfigurer configure) {
                configure.setDefaultTimeout(-1);
                configure.setTaskExecutor(asyncTaskExecutor());
            }

            @Bean
            public AsyncTaskExecutor asyncTaskExecutor() {
                return new SimpleAsyncTaskExecutor("async");
            }


        }

    }

}
