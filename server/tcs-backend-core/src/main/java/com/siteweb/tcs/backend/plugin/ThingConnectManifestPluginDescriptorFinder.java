package com.siteweb.tcs.backend.plugin;

/**
 * <AUTHOR> (2024-06-07)
 **/

import com.siteweb.tcs.common.runtime.ThingConnectPluginDescriptor;
import lombok.SneakyThrows;
import org.pf4j.ManifestPluginDescriptorFinder;
import org.pf4j.PluginDescriptor;
import org.pf4j.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.jar.Attributes;
import java.util.jar.JarFile;
import java.util.jar.Manifest;

public class ThingConnectManifestPluginDescriptorFinder extends ManifestPluginDescriptorFinder {

    public static final String PLUGIN_BUILD_TIME = "Plugin-BuildTime";
    public static final String PLUGIN_NAME_CLASS = "Plugin-Name";


    @SneakyThrows
    @Override
    public PluginDescriptor find(Path pluginPath)  {
        try (JarFile jar = new JarFile(pluginPath.toFile())) {
            Manifest manifest = jar.getManifest();
            return createPluginDescriptor(manifest);
        }
    }

    public PluginDescriptor find(File pluginPath) throws IOException {
        try (JarFile jar = new JarFile(pluginPath)) {
            Manifest manifest = jar.getManifest();
            return createPluginDescriptor(manifest);
        }
    }


    protected PluginDescriptor createPluginDescriptor(Manifest manifest) {
        ThingConnectPluginDescriptor pluginDescriptor = new ThingConnectPluginDescriptor();

        // TODO validate !!!
        Attributes attributes = manifest.getMainAttributes();
        String id = attributes.getValue(PLUGIN_ID);
        pluginDescriptor.setPluginId(id);

        String description = attributes.getValue(PLUGIN_DESCRIPTION);
        if (StringUtils.isNullOrEmpty(description)) {
            pluginDescriptor.setPluginDescription("");
        } else {
            pluginDescriptor.setPluginDescription(description);
        }

        String name = attributes.getValue(PLUGIN_NAME_CLASS);
        if (StringUtils.isNotNullOrEmpty(name)) {
            pluginDescriptor.setPluginName(name);
        }

        String clazz = attributes.getValue(PLUGIN_CLASS);
        if (StringUtils.isNotNullOrEmpty(clazz)) {
            pluginDescriptor.setPluginClass(clazz);
            pluginDescriptor.setPackageName(clazz.substring(0, clazz.lastIndexOf(".")));
        }

        String version = attributes.getValue(PLUGIN_VERSION);
        if (StringUtils.isNotNullOrEmpty(version)) {
            pluginDescriptor.setVersion(version);
        }

        String buildTime = attributes.getValue(PLUGIN_BUILD_TIME);
        if (StringUtils.isNotNullOrEmpty(buildTime)) {
            pluginDescriptor.setBuildTime(buildTime);
        }

        String provider = attributes.getValue(PLUGIN_PROVIDER);
        pluginDescriptor.setProvider(provider);
        String dependencies = attributes.getValue(PLUGIN_DEPENDENCIES);
        pluginDescriptor.setDependencies(dependencies);

        String requires = attributes.getValue(PLUGIN_REQUIRES);
        if (StringUtils.isNotNullOrEmpty(requires)) {
            pluginDescriptor.setRequires(requires);
        }

        pluginDescriptor.setLicense(attributes.getValue(PLUGIN_LICENSE));

        return pluginDescriptor;
    }

}


/**
 * <AUTHOR> (2024-05-11)
 **/
//public class ThingConnectManifestPluginDescriptorFinder implements PluginDescriptorFinder {
//
//    @Override
//    public List<PluginDescriptor> find(Path pluginsRoot) {
//        // 实现自定义的插件加载逻辑
//        // 这里可以根据需要排序或过滤插件描述符列表
//
//        List<PluginDescriptor> descriptors = new ArrayList<>();
//
//        // 假设按照某种逻辑从插件目录中查找插件描述符，并添加到列表中
//        // 这里仅作示例，实际应用中需要根据具体需求实现
//
//        // 插件1
//        PluginDescriptor descriptor1 = new DefaultPluginDescriptor();
//        descriptor1.setPluginId("plugin1");
//        descriptors.add(descriptor1);
//
//        // 插件2
//        PluginDescriptor descriptor2 = new DefaultPluginDescriptor();
//        descriptor2.setPluginId("plugin2");
//        descriptors.add(descriptor2);
//
//        // 插件3
//        PluginDescriptor descriptor3 = new DefaultPluginDescriptor();
//        descriptor3.setPluginId("plugin3");
//        descriptors.add(descriptor3);
//
//        // 返回插件描述符列表
//        return descriptors;
//    }
//}
