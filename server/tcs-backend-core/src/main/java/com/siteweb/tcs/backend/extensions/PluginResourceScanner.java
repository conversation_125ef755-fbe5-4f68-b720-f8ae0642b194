/*
 * Copyright 2014-2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.siteweb.tcs.backend.extensions;


import com.siteweb.tcs.common.runtime.PathDefine;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.lang.Nullable;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class PluginResourceScanner {
    private final ResourcePatternResolver resolver;

    private final String defaultLocation;

    @Getter
    private final List<String> styleResources = new ArrayList<>();

    @Getter
    private final List<String> scriptResources = new ArrayList<>();

    @Getter
    private final List<String> otherResources = new ArrayList<>();


    public PluginResourceScanner(ResourcePatternResolver resolver, String defaultLocations) {
        this.resolver = resolver;
        this.defaultLocation = defaultLocations;
    }

   public void scan() throws IOException {
       styleResources.clear();
       scriptResources.clear();
       for (Resource resource : resolveAssets(defaultLocation)) {
           String resourcePath = this.getResourcePath(defaultLocation, resource);
           if (resourcePath != null && resource.isReadable()) {
               String resourceUri = PathDefine.EXTENSION_NAME + resourcePath;
               if (resourcePath.endsWith(".js")) {
                   scriptResources.add(resourceUri);
               } else if (resourcePath.endsWith(".css")) {
                   styleResources.add(resourceUri);
               } else {
                   otherResources.add(resourceUri);
               }
               log.debug(PathDefine.EXTENSION_NAME + resourcePath);
           }
       }
   }



    public void scanPrefix(String urlPrefix, ResourcePatternResolver resolver) throws IOException {
        if(resolver == null) resolver = this.resolver;
        String targetUrl = defaultLocation + urlPrefix;
        String widerLocation = toPattern(targetUrl);
        List<Resource>  resources = List.of(resolver.getResources(widerLocation + "**/*"));
        for (Resource resource : resources) {
            String resourcePath = this.getResourcePath(defaultLocation, resource);
            if (resourcePath != null && resource.isReadable()) {
                String resourceUri = PathDefine.EXTENSION_NAME + resourcePath;
                if (resourcePath.endsWith(".js")) {
                    if (!scriptResources.contains(resourceUri)) scriptResources.add(resourceUri);
                } else if (resourcePath.endsWith(".css")) {
                    if (!styleResources.contains(resourceUri)) styleResources.add(resourceUri);
                } else {
                    if (!otherResources.contains(resourceUri)) otherResources.add(resourceUri);
                }
                log.debug(PathDefine.EXTENSION_NAME + resourcePath);
            }
        }
    }


    public void cleanPrefix(String urlPrefix) {
        String path = PathDefine.EXTENSION_NAME + urlPrefix;
        scriptResources.removeIf(element -> element.startsWith(path));
        styleResources.removeIf(element -> element.startsWith(path));
        otherResources.removeIf(element -> element.startsWith(path));
    }


    private List<Resource> resolveAssets(String location) throws IOException {
        String widerLocation = toPattern(location);
        return List.of(this.resolver.getResources(widerLocation + "**/*"));
    }

    private String toPattern(String location) {
        return location.replace("classpath:", "classpath*:");
    }

    @Nullable
    private String getResourcePath(String location, Resource resource) throws IOException {
        String locationWithoutPrefix = location.replaceFirst("^[^:]+:", "");
        Matcher m = Pattern.compile(Pattern.quote(locationWithoutPrefix) + "(.+)$")
                .matcher(resource.getURI().toString());
        if (m.find()) {
            return m.group(1);
        } else {
            return null;
        }
    }

}
