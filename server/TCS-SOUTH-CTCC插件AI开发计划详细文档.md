# TCS-SOUTH-CTCC插件AI开发计划详细文档

## 1. 项目概述

### 1.1 项目目标
基于现有的TCS-SOUTH-CMCC插件，开发一个全新的TCS-SOUTH-CTCC插件，以支持中国电信动环监控系统B接口技术规范。

### 1.2 开发策略
采用AI辅助开发模式，通过系统化的任务分解和专业化的提示词模板，逐步完成插件开发工作。

### 1.3 技术基础
- **参考实现**: server/tcs-south-cmcc 插件
- **目标规范**: 中国电信动环监控系统B接口技术规范20230515
- **架构复用**: 约80%的代码可直接复用或参考改造
- **开发周期**: 预计8周，320工作小时

---

## 2. CMCC vs CTCC 核心差异分析

### 2.1 协议枚举值差异

#### 2.1.1 EnumType (监控点种类)
**CMCC规范:**
```
AI=3, DI=4, DO=1, AO=2
```

**CTCC规范:**
```
TI=1 (阈值生成信号)
DI=2 (遥信信号)
AI=3 (遥测信号)
DO=4 (遥控信号) 
AO=5 (遥调信号)
```

**影响**: 需要重写所有EnumType相关的代码

#### 2.1.2 失败原因枚举
**CTCC新增**: EnumFailureCode枚举，包含24种详细的失败原因代码
**CMCC**: 只有简单的FAILURE/SUCCESS

### 2.2 数据结构差异

#### 2.2.1 告警消息结构 (TAlarm)
**CMCC字段:**
- serialNo, nmAlarmId, deviceId, tSignalId, alarmTime, alarmLevel, alarmFlag, alarmDesc, eventValue, alarmRemark

**CTCC字段:**
- SerialNo, SUID, DeviceID, SPID, StartTime, EndTime, TriggerVal, AlarmLevel, AlarmFlag, AlarmDesc, AlarmFriDesc

**核心差异:**
1. 字段命名: tSignalId -> SPID, nmAlarmId -> 无直接对应
2. 新增字段: SUID, EndTime, AlarmFriDesc
3. 字段类型: eventValue(Float) -> TriggerVal(Float)

#### 2.2.2 新增数据结构
**CTCC特有结构:**
- SOResource: 监控对象资源信息
- SmartDoorValue: 智能门禁配置信息  
- SDoorAuthData: 智能门禁授权信息
- SDoorEvent: 智能门禁事件信息
- TSUStatus: SU状态参数

### 2.3 消息类型差异

#### 2.3.1 消息数量对比
- **CMCC**: 约20+种消息类型
- **CTCC**: 27种明确定义的消息类型 (101-1208)

#### 2.3.2 主要新增消息类型
```
CTCC新增智能门禁相关消息:
- GET_SmartDOOR (1201)
- SET_SmartDOOR (1203) 
- SEND_SmartDOOR (1205)
- SEND_DOOREvent (1207)

CTCC新增配置相关消息:
- ASK_SCHEMECONFIG (201)
- ASK_FACTORYCONFIG (301)
- GET_SPCONFIGOPTION (401)
```

#### 2.3.3 命名差异
- **CMCC**: FSUID (FSU ID)
- **CTCC**: SUID (SU ID)
- **长度限制**: FSUID vs SUID_LEN(17字节)

---

## 3. 详细开发计划

### 3.1 第一阶段：项目架构搭建 (第1周)

#### 任务3.1.1: 创建项目基础结构
**工作量**: 8小时
**目标**: 基于CMCC插件创建CTCC插件的基础项目结构

**AI提示词模板:**
```
# CTCC插件项目结构创建

## 背景信息
我需要基于现有的TCS-SOUTH-CMCC插件创建一个新的TCS-SOUTH-CTCC插件项目。请帮我完成以下任务：

## 具体任务
1. 复制整个server/tcs-south-cmcc目录结构
2. 重命名为server/tcs-south-ctcc
3. 修改所有相关的配置文件中的命名引用
4. 更新Maven pom.xml配置
5. 修改包名从com.siteweb.tcs.south.cmcc到com.siteweb.tcs.south.ctcc

## 要求
- 保持目录结构完全一致
- 确保所有引用路径正确
- Maven artifactId改为tcs-south-ctcc
- 插件ID改为south-ctcc-plugin
- 更新Spring配置文件中的组件扫描包名

## 输出结果
完成后提供修改清单，包括：
- 重命名的文件列表
- 修改的配置项清单
- Maven依赖验证结果
```

#### 任务3.1.2: 更新Spring配置
**工作量**: 4小时
**目标**: 调整Spring Boot配置以适配CTCC插件

**AI提示词模板:**
```
# Spring配置适配CTCC插件

## 当前配置文件分析
请分析以下CMCC插件的Spring配置文件，并提供CTCC插件的对应配置：

[将src/main/resources/application.yml等配置文件内容贴入]

## 修改要求
1. 更新所有cmcc相关的配置键名为ctcc
2. 修改数据库表前缀从cmcc_为ctcc_  
3. 调整插件相关的Bean名称
4. 更新日志配置中的包名
5. 修改插件描述信息

## 特别注意
- 保持端口配置不变，避免冲突
- 数据库连接配置保持通用性
- 插件元数据需要体现CTCC特色

## 输出格式
提供完整的配置文件内容，并列出所有修改点
```

#### 任务3.1.3: 验证基础架构
**工作量**: 8小时  
**目标**: 确保新建的CTCC插件可以正常启动

**AI提示词模板:**
```
# CTCC插件基础架构验证

## 验证目标
确保新创建的TCS-SOUTH-CTCC插件能够在TCS系统中正常加载和启动。

## 验证步骤
1. Maven编译验证
2. Spring Boot启动测试
3. 插件系统加载测试
4. 基础API接口测试

## 问题排查指导
如果遇到以下问题，请提供解决方案：
- Maven依赖冲突
- Spring Bean定义冲突
- 插件加载失败
- 配置文件解析错误

## 成功标准
- mvn clean compile 成功
- 插件能在tcs-core中正常加载
- 可以看到插件相关的日志输出
- 插件管理界面显示CTCC插件信息

## 输出结果
如果发现问题，请提供：
1. 错误日志分析
2. 具体修改建议  
3. 修正后的文件内容
```

### 3.2 第二阶段：协议定义层重写 (第2-4周)

#### 任务3.2.1: 重写枚举定义类 (第2周)
**工作量**: 24小时
**目标**: 根据CTCC规范重写所有枚举类

**AI提示词模板:**
```
# CTCC协议枚举类重写

## 参考资料
基于中国电信动环监控系统B接口技术规范，需要重写以下枚举类：

### CMCC现有枚举 (参考)
[贴入EnumResult.java等现有枚举类内容]

### CTCC规范要求
根据规范文档5.5.3节，需要实现以下枚举：

1. **EnumResult**: FAILURE=0, SUCCESS=1
2. **EnumFailureCode**: 包含24种失败原因 (USERNAME_ERROR=1到OTHER_ERROR=9999)
3. **EnumType**: TI=1, DI=2, AI=3, DO=4, AO=5  
4. **EnumAlarmLevel**: NOALARM=0, CRITICAL=1, MAJOR=2, MINOR=3, HINT=4
5. **EnumState**: NOALARM=0到INVALID=6，共7种状态
6. **EnumFlag**: BEGIN=0, END=1
7. **EnumConfigOption**: ONE=1, TWO=2, THREE=3, FOUR=4
8. **EnumDeviceMeanings**: Meanings=0到8，共9种信号意义

## 实现要求
1. 使用@JsonValue和@EnumValue注解支持序列化
2. 提供getByCode静态方法用于反序列化
3. 包含中文描述信息
4. 遵循现有的代码风格和命名约定

## 输出要求
为每个枚举类提供完整的Java代码，包括：
- 完整的枚举值定义
- 序列化/反序列化支持
- 错误处理机制
- Javadoc注释
```

#### 任务3.2.2: 重写数据结构定义 (第2周)
**工作量**: 16小时
**目标**: 根据CTCC规范重写所有数据结构类

**AI提示词模板:**
```
# CTCC数据结构类重写

## 参考实现
现有CMCC的TAlarm类定义：
[贴入TAlarm.java内容]

## CTCC规范要求
根据技术规范第5.5.4节数据结构定义，需要实现：

### 核心数据结构
1. **TTime**: 时间结构 (Year, Month, Day, Hour, Minute, Second)
2. **TAlarm**: 当前告警数据结构 (11个字段)
3. **TSemaphore**: 实时数据/控制命令值结构 (6个字段)
4. **SOResource**: 监控对象资源信息 (6个字段) - CMCC没有
5. **SmartDoorValue**: 智能门禁配置信息 (4个字段) - CMCC没有
6. **SDoorAuthData**: 智能门禁授权信息 (5个字段) - CMCC没有  
7. **SDoorEvent**: 智能门禁事件信息 (7个字段) - CMCC没有
8. **TSUStatus**: SU状态参数 (3个字段) - CMCC没有

### 关键差异
- TAlarm字段: SerialNo, SUID(新增), DeviceID, SPID, StartTime, EndTime(新增), TriggerVal, AlarmLevel, AlarmFlag, AlarmDesc, AlarmFriDesc(新增)
- 字段长度约束: SUID_LEN=17, DEVICEID_LEN=7, SPID_LENGTH=12等

## 实现要求
1. 使用Jackson XML注解支持XML序列化
2. 使用Lombok简化代码
3. 字段验证和约束注解
4. 完整的Javadoc文档

## 输出要求
提供所有数据结构类的完整Java代码
```

#### 任务3.2.3: 重写消息类型枚举 (第3周) 
**工作量**: 16小时
**目标**: 根据CTCC规范重写PK_TypeName枚举

**AI提示词模板:**
```
# CTCC消息类型枚举重写

## 现有CMCC实现
[贴入PK_TypeName.java内容]

## CTCC规范要求  
根据技术规范第6节，CTCC协议包含27种消息类型：

### 网络联接参数设置 (101-110)
LOGIN(101) -> LOGIN_ACK(102)
SUREADY(103) -> SUREADY_ACK(104)
SET_SCIP(105) -> SET_SCIP_ACK(106)
GET_SUFTP(107) -> GET_SUFTP_ACK(108)
SET_SUFTP(109) -> SET_SUFTP_ACK(110)

### 标准化配置文件 (201-206)
ASK_SCHEMECONFIG(201) -> ASK_SCHEMECONFIG_ACK(202)
GET_SCHEMECONFIG(203) -> GET_SCHEMECONFIG_ACK(204)
SET_SCHEMECONFIG(205) -> SET_SCHEMECONFIG_ACK(206)

### 厂家配置文件 (301-308)
ASK_FACTORYCONFIG(301) -> ASK_FACTORYCONFIG_ACK(302)
SEND_FACTORYCONFIG(303) -> SEND_FACTORYCONFIG_ACK(304)
GET_FACTORYCONFIG(305) -> GET_FACTORYCONFIG_ACK(306)
SET_FACTORYCONFIG(307) -> SET_FACTORYCONFIG_ACK(308)

### FSU监控点标准化配置模板选型 (401-404)
GET_SPCONFIGOPTION(401) -> GET_SPCONFIGOPTION_ACK(402)
SET_SPCONFIGOPTION(403) -> SET_SPCONFIGOPTION_ACK(404)

### 实时监测数据与当天历史监测数据 (501-504)
GET_DATA(501) -> GET_DATA_ACK(502)
ASK_TODAYHISDATA(503) -> ASK_TODAYHISDATA_ACK(504)

### 告警信息 (601-604)
SEND_ALARM(601) -> SEND_ALARM_ACK(602)
GET_ACTIVEALARM(603) -> GET_ACTIVEALARM_ACK(604)

### 控制命令 (701-702)
SET_RMCTRLCMD(701) -> SET_RMCTRLCMD_ACK(702)

### 系统或辅助命令 (901-1102)
SET_TIME(901) -> SET_TIME_ACK(902)
GET_SUINFO(1001) -> GET_SUINFO_ACK(1002)
SET_SUREBOOT(1101) -> SET_SUREBOOT_ACK(1102)

### 智能门禁 (1201-1208)
GET_SmartDOOR(1201) -> GET_SmartDOOR_ACK(1202)
SET_SmartDOOR(1203) -> SET_SmartDOOR_ACK(1204)
SEND_SmartDOOR(1205) -> SEND_SmartDOOR_ACK(1206)
SEND_DOOREvent(1207) -> SEND_DOOREvent_ACK(1208)

## 实现要求
1. 保持现有的请求-响应配对机制
2. 添加数字编码支持 (Code字段)
3. 支持按名称和编码查找
4. 兼容Jackson序列化

## 输出要求
提供完整的PK_TypeName枚举类代码，包括所有27种消息类型
```

#### 任务3.2.4: 重写消息类定义 (第3-4周)
**工作量**: 40小时
**目标**: 根据CTCC规范重写所有消息类

**AI提示词模板 - 分批处理:**
```
# CTCC消息类重写 - 第{X}批

## 参考CMCC实现
[贴入对应的CMCC消息类，如LoginMessage.java]

## CTCC规范要求
### 本批次处理消息: {消息类型列表}

以LOGIN消息为例：
#### LOGIN消息结构 (代码101)
```xml
<Request>
    <PK_Type>
        <Name>LOGIN</Name>  
        <Code>101</Code>
    </PK_Type>
    <Info>
        <UserName/> <!-- USER_LENGTH(20字节) -->
        <Password/> <!-- PASSWORD_LEN(20字节) --> 
        <SUID/>     <!-- char[SUID_LEN](17字节) -->
        <SUIP/>     <!-- Char[IP_LENGTH](15字节) -->
        <SUPort/>   <!-- Sizeof(long) -->
        <SUVendor/> <!-- char[VENDOR_LENGTH] -->
        <SUModel/>  <!-- char[MODEL_LENGTH] -->
        <SUHardVer/> <!-- char[VER_LEN] -->
        <SUSoftVer/> <!-- char[VER_LEN] -->
    </Info>
</Request>
```

## 关键差异点
1. **字段命名**: CMCC的fsuId -> CTCC的SUID
2. **新增字段**: Code字段在PK_Type中
3. **字段约束**: 明确的长度限制定义
4. **XML结构**: 完全按照CTCC规范的XML格式

## 实现要求
1. Jackson XML注解正确配置
2. 字段验证注解 (@Size, @NotNull等)
3. 与枚举类正确关联
4. 序列化/反序列化测试

## 输出要求  
每个消息类提供：
1. Request消息类完整代码
2. Response消息类完整代码 (如果有)
3. 相关的内部Info类定义
4. XML序列化示例
```

### 3.3 第三阶段：业务逻辑层适配 (第5-6周)

#### 任务3.3.1: 消息处理器重写 (第5周)
**工作量**: 32小时
**目标**: 改造CmccFsuMessageProcessor为CtccSuMessageProcessor

**AI提示词模板:**
```
# CTCC消息处理器重写

## 现有CMCC处理器分析
[贴入CmccFsuMessageProcessor.java关键方法]

## CTCC协议适配要求
### 主要变化
1. **命名变更**: FSU -> SU, CMCC -> CTCC
2. **新增消息处理**: 智能门禁相关的4个消息类型
3. **配置消息增强**: 标准化配置、厂家配置的完整实现
4. **错误处理增强**: 支持EnumFailureCode的24种错误类型

### 核心处理方法需要重写
1. **连接管理类**:
   - handleLoginMessage() -> 支持SUID和新字段
   - handleSureadyMessage() -> 新增的SU就绪验证
   - handleSetScipMessage() -> SC网络参数设置

2. **配置管理类**:
   - handleAskSchemeConfigMessage() -> FSU请求标准化配置
   - handleGetSchemeConfigMessage() -> SC请求上传标准化配置
   - handleSetSchemeConfigMessage() -> SC下发标准化配置
   - handleAskFactoryConfigMessage() -> FSU请求厂家配置
   - handleGetSpConfigOptionMessage() -> 监控点配置模板选项

3. **数据处理类**:
   - handleGetDataMessage() -> 保持核心逻辑，调整数据结构
   - handleSendAlarmMessage() -> 适配新的TAlarm结构
   - handleAskTodayHisDataMessage() -> 当天历史数据请求

4. **智能门禁类** (新增):
   - handleGetSmartDoorMessage() -> 获取门禁配置
   - handleSetSmartDoorMessage() -> 设置门禁配置  
   - handleSendSmartDoorMessage() -> 门禁授权结果
   - handleSendDoorEventMessage() -> 门禁事件上报

## 实现策略
1. 保持异步处理架构不变
2. 复用HTTP和FTP通信机制
3. 增强错误处理和日志记录
4. 添加消息验证逻辑

## 输出要求
提供CtccSuMessageProcessor类的完整实现，包含所有消息处理方法
```

#### 任务3.3.2: 数据库层适配 (第5周)
**工作量**: 24小时
**目标**: 设计和实现CTCC的数据库表结构

**AI提示词模板:**
```
# CTCC数据库层设计和实现

## 现有CMCC数据库分析
[贴入CMCC的数据库迁移脚本V1.0.0__cmcc_table_create.sql]

## CTCC数据库设计要求
### 表结构调整
1. **前缀变更**: cmcc_ -> ctcc_
2. **字段调整**: fsuid -> suid, 长度17字节
3. **新增表**: 智能门禁相关表

### 核心表结构
1. **ctcc_sus**: SU基础信息表
   - 字段调整: suid(17), su_ip, su_port, su_vendor, su_model等
   - 新增字段: su_hard_ver, su_soft_ver, cpu_usage, mem_usage

2. **ctcc_devices**: 设备信息表
   - 调整device_id长度为7字节
   - 新增SO相关字段: so_name, so_manufac, so_model, so_capacity等

3. **ctcc_signals**: 监控点信息表  
   - spid字段长度12字节
   - 支持5种监控点类型: TI=1, DI=2, AI=3, DO=4, AO=5

4. **ctcc_alarms**: 告警信息表
   - 新增字段: end_time, alarm_fri_desc, trigger_val
   - 调整字段: serial_no(10字节), alarm_desc(200字节)

5. **ctcc_smart_doors**: 智能门禁配置表 (新增)
   - device_id, door_type, card_type, smart_door_des

6. **ctcc_door_auth**: 智能门禁授权表 (新增)  
   - card_number, card_code, start_date, end_date, other_data

7. **ctcc_door_events**: 智能门禁事件表 (新增)
   - serial_no, device_id, event_type, event_time, card_info

### 数据迁移考虑
如果需要从CMCC数据迁移到CTCC：
- 字段映射关系
- 数据类型转换
- 默认值设置

## 实现要求
1. 使用Flyway管理数据库版本
2. 支持多数据库方言 (MySQL, PostgreSQL, H2等)
3. 添加索引和约束
4. 提供数据迁移脚本

## 输出要求
1. 完整的数据库创建脚本
2. 对应的实体类 (Entity) 
3. MyBatis Plus的Mapper接口
4. 数据迁移脚本 (如果需要)
```

#### 任务3.3.3: 服务层重构 (第6周)
**工作量**: 24小时
**目标**: 适配业务服务层以支持CTCC协议

**AI提示词模板:**
```
# CTCC服务层重构

## 现有服务分析
[贴入相关的CMCC服务类，如AlarmService, DeviceService等]

## CTCC服务层适配要求
### 核心服务类需要调整
1. **CtccSuService**: SU管理服务
   - su注册和状态管理
   - su信息查询和更新
   - su重启和参数配置

2. **CtccAlarmService**: 告警管理服务
   - 新增EndTime和AlarmFriDesc字段处理
   - 支持24种失败原因码
   - 告警历史记录管理

3. **CtccDeviceService**: 设备管理服务
   - 设备资源信息管理 (SOResource)
   - 设备配置文件处理
   - 监控点管理 (SP)

4. **CtccConfigService**: 配置管理服务
   - 标准化配置文件处理
   - 厂家配置文件管理
   - 监控点配置模板选项管理

5. **CtccSmartDoorService**: 智能门禁服务 (新增)
   - 门禁设备配置管理
   - 门禁授权信息管理
   - 门禁事件处理

### 业务逻辑调整
1. **数据验证增强**: 
   - 字段长度验证 (SUID 17字节等)
   - 枚举值验证 (新的EnumType值)
   - 业务规则验证

2. **错误处理完善**:
   - EnumFailureCode的24种错误类型处理
   - 详细的错误信息返回
   - 异常情况记录和报告

3. **FTP文件处理**:
   - 配置文件上传下载
   - 历史数据文件处理
   - 图像文件管理

## 实现策略
1. 保持事务管理机制
2. 异步处理和缓存优化
3. 完整的单元测试覆盖
4. API接口向后兼容

## 输出要求
提供所有服务类的完整实现，包括：
1. 服务接口定义
2. 服务实现类
3. 单元测试用例
4. API文档更新
```

### 3.4 第四阶段：前端界面适配 (第7周)

#### 任务3.4.1: 前端组件适配 (第7周)
**工作量**: 24小时
**目标**: 调整前端界面以支持CTCC协议的新特性

**AI提示词模板:**
```
# CTCC前端界面适配

## 现有CMCC前端分析
[贴入相关Vue组件，如设备管理、告警管理等页面]

## CTCC前端适配要求
### 核心界面调整
1. **SU管理界面**:
   - FSUID -> SUID字段显示
   - 新增SU状态参数显示 (CPU、内存使用率)
   - SU重启功能增强

2. **告警管理界面**:
   - 新增告警结束时间字段
   - 新增告警详细描述字段 (AlarmFriDesc)
   - 支持24种失败原因显示

3. **设备管理界面**:
   - 新增SO资源信息管理
   - 设备容量和投产日期字段
   - 监控点配置模板选项管理

4. **智能门禁管理界面** (全新):
   - 门禁设备配置界面
   - 门禁授权管理界面
   - 门禁事件监控界面
   - 门禁卡片管理功能

### 数据字段映射
1. **API接口调整**: /api/cmcc/* -> /api/ctcc/*
2. **数据模型更新**: 前端TypeScript接口定义
3. **表单验证**: 新的字段长度和格式要求
4. **下拉选项**: 新的枚举值选项

### UI组件新增
1. **智能门禁组件库**:
   - 门禁配置表单
   - 门禁状态图表
   - 门禁事件时间线
   - 门禁卡片管理表格

2. **配置管理增强**:
   - 标准化配置文件上传组件
   - 厂家配置文件管理界面
   - 配置模板选项设置组件

## 技术实现要求
1. 使用Vue 3 Composition API
2. TypeScript类型安全
3. Element Plus组件库
4. 响应式设计适配
5. 国际化支持 (中英文)

## 输出要求
1. 更新所有相关Vue组件
2. TypeScript接口定义文件
3. 路由配置更新
4. Pinia状态管理更新
```

### 3.5 第五阶段：集成测试和优化 (第8周)

#### 任务3.5.1: 单元测试编写
**工作量**: 16小时

**AI提示词模板:**
```
# CTCC插件单元测试编写

## 测试范围
为CTCC插件的核心功能编写完整的单元测试用例。

## 测试类别
### 1. 协议层测试
- 枚举类序列化/反序列化测试
- 消息类XML转换测试
- 数据结构验证测试

### 2. 业务逻辑测试
- 消息处理器功能测试
- 服务层业务逻辑测试
- 数据库操作测试

### 3. 集成测试
- HTTP/WebService接口测试
- FTP文件传输测试  
- 完整消息流程测试

## 测试要求
1. 使用JUnit 5和Mockito
2. 测试覆盖率不低于80%
3. 包含正常和异常情况测试
4. 性能测试和压力测试

## 输出要求
提供完整的测试类和测试报告
```

#### 任务3.5.2: 集成测试和问题修复
**工作量**: 16小时

**AI提示词模板:**
```
# CTCC插件集成测试和问题修复

## 测试场景
### 1. 基础连接测试
- SU注册登录流程
- 心跳保持机制
- 网络异常恢复

### 2. 数据传输测试
- 实时数据获取
- 告警信息上报
- 配置文件传输

### 3. 智能门禁测试
- 门禁配置管理
- 门禁事件处理
- 门禁授权验证

## 问题排查
如果发现问题，请提供：
1. 详细的错误分析
2. 问题根本原因
3. 修复方案建议
4. 回归测试验证

## 输出要求
1. 测试报告
2. 问题清单和修复记录
3. 性能优化建议
```

---

## 4. AI提示词模板库

### 4.1 通用提示词模板

#### 代码重构模板
```
# 代码重构任务

## 原始代码
[贴入需要重构的代码]

## 重构要求
1. 重构目标: {具体目标}
2. 技术要求: {技术约束}
3. 兼容性要求: {兼容性说明}

## 输出格式
1. 重构后的完整代码
2. 重构要点说明
3. 测试用例建议
```

#### 配置文件修改模板
```
# 配置文件修改

## 现有配置
[贴入现有配置文件内容]

## 修改要求
{具体的修改需求描述}

## 注意事项
- 保持配置向后兼容
- 遵循最佳实践
- 添加必要注释

## 输出要求
提供完整的修改后配置文件
```

### 4.2 专项任务提示词

#### 数据库设计提示词
```
# 数据库表结构设计

## 业务需求
{具体业务场景描述}

## 技术约束
- 数据库类型: {MySQL/PostgreSQL等}
- 命名规范: {表名和字段命名规则}
- 性能要求: {查询性能要求}

## 设计要求
1. 表结构设计合理
2. 索引配置恰当
3. 外键约束正确
4. 数据类型选择合适

## 输出内容
1. DDL创建脚本
2. 索引创建语句
3. 约束定义
4. 测试数据脚本
```

#### 接口设计提示词
```
# REST API接口设计

## 功能需求
{具体的API功能描述}

## 设计标准
- RESTful设计原则
- 统一的响应格式
- 完整的错误处理
- 合理的HTTP状态码

## 技术要求
- Spring Boot框架
- JSON数据格式
- 参数验证
- 接口文档

## 输出内容
1. Controller类完整代码
2. DTO类定义
3. API接口文档
4. 单元测试用例
```

### 4.3 测试相关提示词

#### 单元测试模板
```
# 单元测试用例设计

## 测试目标
{需要测试的类或方法}

## 测试场景
1. 正常情况测试
2. 边界条件测试
3. 异常情况测试
4. 性能测试

## 技术要求
- 使用JUnit 5
- Mockito模拟依赖
- 断言准确有效
- 测试数据准备

## 输出要求
提供完整的测试类代码，包括所有测试方法
```

---

## 5. 开发里程碑和验收标准

### 5.1 第1周验收标准
- [ ] 项目结构完整创建
- [ ] Maven编译通过
- [ ] Spring Boot能够启动
- [ ] 插件系统能够识别CTCC插件

### 5.2 第2-4周验收标准
- [ ] 所有枚举类重写完成并通过测试
- [ ] 所有数据结构类实现完成
- [ ] 所有消息类定义完成
- [ ] XML序列化/反序列化功能正常

### 5.3 第5-6周验收标准
- [ ] 消息处理器完整实现
- [ ] 数据库表结构创建完成
- [ ] 基础的CRUD操作功能正常
- [ ] 核心业务逻辑测试通过

### 5.4 第7周验收标准
- [ ] 前端界面适配完成
- [ ] 智能门禁管理功能可用
- [ ] 前后端接口联调成功

### 5.5 第8周验收标准
- [ ] 单元测试覆盖率≥80%
- [ ] 集成测试全部通过
- [ ] 性能指标满足要求
- [ ] 文档完整，代码规范

---

## 6. 风险控制和应急预案

### 6.1 技术风险
- **协议兼容性风险**: 定期与规范文档对照验证
- **数据迁移风险**: 准备回滚方案和数据备份
- **性能风险**: 及时进行性能测试和优化

### 6.2 进度风险
- **复杂度估算不足**: 预留20%缓冲时间
- **技术难点攻关**: 准备技术预研和POC验证
- **测试时间不够**: 提前开始单元测试编写

### 6.3 质量风险
- **代码质量控制**: 使用代码审查和静态分析工具
- **测试覆盖不足**: 制定详细的测试计划和用例
- **文档不完整**: 在开发过程中同步完善文档

---

## 7. 总结

本AI开发计划为TCS-SOUTH-CTCC插件的开发提供了详细的任务分解和专业的提示词模板。通过系统化的方法和AI辅助，预计可以在8周内高质量完成插件开发工作。

关键成功要素：
1. **充分利用现有CMCC插件的架构优势**
2. **严格按照CTCC技术规范进行协议实现**
3. **通过专业化提示词确保AI输出质量**
4. **建立完善的测试和验收机制**

这个详细的开发计划将确保CTCC插件的成功交付，并为后续其他运营商插件的开发提供宝贵经验。