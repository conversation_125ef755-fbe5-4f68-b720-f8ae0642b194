# SWAP南向接入插件 (tcs-south-swap)

## 项目简介

SWAP南向接入插件(tcs-south-swap)是基于TCS平台开发的南向插件，用于连接和管理SWAP设备和系统。该插件提供了设备接入、数据采集、指令下发等功能，使TCS平台能够与SWAP设备进行无缝集成。

## 功能特点

- 设备接入与管理：支持SWAP设备的注册、认证和管理
- 数据采集与处理：采集设备实时数据，并进行格式转换和处理
- 指令下发：支持向设备发送控制指令
- 告警管理：实时监控设备状态，生成相应告警信息
- 数据存储：将设备数据持久化存储到数据库

## 环境要求

- JDK 17+
- Maven 3.8+
- MySQL 8.0+ 或其他兼容的数据库
- TCS平台环境

## 安装使用

### 构建插件

```bash
cd server/tcs-south-swap
mvn clean package
```

构建成功后，将在 target 目录下生成 tcs-south-swap-[version].jar 文件。

### 部署插件

将生成的 JAR 文件复制到 TCS 平台的 plugins 目录下：

```bash
cp target/tcs-south-swap-[version].jar [TCS_HOME]/plugins/
```

### 启用插件

1. 修改 TCS 平台的 application.yml 文件，在 plugins.enabled 配置项中添加本插件ID
2. 重启 TCS 平台即可启用插件

## 开发调试

### 在IDE中调试

1. 在 IDEA 中打开项目
2. 配置运行参数：`-PDebug-Plugin(SWAP)`
3. 启动 TCS 主应用

### 测试接口

插件提供以下测试接口：

- **GET** `/api/south/swap/test/helloworld` - 基本连接测试
- **GET** `/api/south/swap/test/info` - 获取插件信息
- **GET** `/api/south/swap/test/database` - 数据库连接测试

## 技术架构

- **Spring Boot**: 应用框架
- **MyBatis-Plus**: 数据持久化
- **Pekko Actor**: 并发处理
- **Maven**: 构建工具

## 目录结构

```
tcs-south-swap/
├── src/main/java/
│   └── com/siteweb/tcs/south/swap/
│       ├── SouthSwapPlugin.java          # 插件主类
│       ├── config/                       # 配置类
│       │   └── DataSourceConfig.java     # 数据源配置
│       ├── connector/                    # 连接器相关
│       │   └── ConnectorDataHolder.java  # 数据持有者
│       ├── dal/                          # 数据访问层
│       │   ├── mapper/                   # Mapper接口
│       │   └── entity/                   # 实体类
│       └── web/                          # Web层
│           └── controller/               # 控制器
├── src/main/resources/
│   ├── mapper/south-swap-plugin/         # MyBatis映射文件
│   ├── i18n/                            # 国际化文件
│   └── plugin.yml                       # 插件配置
└── pom.xml                              # Maven配置
```

## 版本历史

- v1.0.0: 初始版本，基础功能实现

## 许可证

本项目受公司内部协议保护，未经授权不得分发。 
