package com.siteweb.tcs.south.swap.connector;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 连接器数据持有者
 * <p>
 * 用于存储插件运行时的数据和状态信息
 * </p>
 */
@Data
@Component
public class ConnectorDataHolder {
    
    /**
     * 插件ID
     */
    private String pluginId;
    
    /**
     * 插件状态
     */
    private boolean active = false;
    
    /**
     * 连接状态
     */
    private boolean connected = false;
}
