# SWAP模块异常管理使用指南

## 概述

SWAP模块的异常管理体系参考CMCC模块设计，提供了三种类型的错误码：
- **SwapBusinessErrorCode**: 业务异常错误码
- **SwapTechnicalErrorCode**: 技术异常错误码  
- **SwapPluginErrorCode**: 插件异常错误码

所有错误码都支持 `.toException()` 方法，可以方便地转换为对应的异常对象。

## 使用方式

### 1. 业务异常使用示例

```java
// 基本使用
throw SwapBusinessErrorCode.MONITORUNIT_NOT_FOUND.toException();

// 带详情信息
throw SwapBusinessErrorCode.MONITORUNIT_NOT_FOUND.toException("监控单元ID: 123");

// 带原因异常
throw SwapBusinessErrorCode.HUB_SYNC_FAILURE.toException(ex);

// 带详情和原因
throw SwapBusinessErrorCode.EQUIPMENT_CREATE_FAILURE.toException("设备名称: TestDevice", ex);
```

### 2. 技术异常使用示例

```java
// 基本使用
throw SwapTechnicalErrorCode.DATABASE_CONNECTION_FAILURE.toException();

// 带组件信息
throw SwapTechnicalErrorCode.DATABASE_CONNECTION_FAILURE.toException("SwapRepository");

// 带详情信息
throw SwapTechnicalErrorCode.HTTP_REQUEST_FAILURE.toException("连接超时");

// 带原因异常
throw SwapTechnicalErrorCode.MESSAGE_PARSE_ERROR.toException(ex);

// 带详情和原因
throw SwapTechnicalErrorCode.NETWORK_TIMEOUT.toException("请求超时", ex);

// 带详情、原因和组件
throw SwapTechnicalErrorCode.SERIALIZATION_ERROR.toException("JSON序列化失败", ex, "MessageProcessor");
```

### 3. 插件异常使用示例

```java
// 基本使用（需要插件ID）
throw SwapPluginErrorCode.PLUGIN_SETUP_FAILURE.toException("swap-plugin");

// 带插件名称
throw SwapPluginErrorCode.PLUGIN_LOAD_FAILURE.toException("swap-plugin", "SWAP插件");

// 带详情信息
throw SwapPluginErrorCode.PLUGIN_CONFIG_INVALID.toException("配置文件格式错误", "swap-plugin", "SWAP插件");

// 带原因异常
throw SwapPluginErrorCode.PLUGIN_EXECUTION_FAILURE.toException(ex, "swap-plugin");

// 带详情和原因
throw SwapPluginErrorCode.PLUGIN_INITIALIZATION_FAILURE.toException("依赖注入失败", ex, "swap-plugin", "SWAP插件");
```

## 错误码分类

### 业务异常错误码 (SwapBusinessErrorCode)

#### Hub映射相关
- `HUB_MAPPING_NOT_FOUND`: Hub映射关系不存在
- `HUB_MAPPING_ALREADY_EXISTS`: Hub映射关系已存在
- `HUB_SYNC_FAILURE`: Hub同步失败

#### 基础实体相关
- `MONITORUNIT_NOT_FOUND`: 监控单元不存在
- `EQUIPMENT_NOT_FOUND`: 设备不存在
- `ALARM_NOT_FOUND`: 告警不存在
- `SIGNAL_NOT_FOUND`: 信号不存在
- `CONTROL_NOT_FOUND`: 控制点不存在

#### 操作相关
- `CONTROL_EXECUTE_FAILURE`: 控制执行失败
- `INVALID_PARAMETER`: 参数无效
- `PROTOCOL_RESULT_FAILURE`: 协议返回失败

### 技术异常错误码 (SwapTechnicalErrorCode)

#### 网络连接相关
- `SERVER_CONNECTION_FAILURE`: 服务器连接失败
- `NETWORK_TIMEOUT`: 网络超时
- `HTTP_REQUEST_FAILURE`: HTTP请求失败

#### 数据库相关
- `DATABASE_CONNECTION_FAILURE`: 数据库连接失败
- `DATABASE_QUERY_FAILURE`: 数据库查询失败

#### 消息处理相关
- `MESSAGE_PARSE_ERROR`: 消息解析错误
- `MESSAGE_NULL_OR_EMPTY`: 消息为空

#### 系统相关
- `CONFIG_LOAD_FAILURE`: 配置加载失败
- `STARTUP_FAILURE`: 启动失败
- `INITIALIZATION_FAILURE`: 初始化失败
- `PROTOCOL_ERROR`: 协议错误
- `INTERNAL_ERROR`: 内部错误

### 插件异常错误码 (SwapPluginErrorCode)

#### 插件核心错误
- `PLUGIN_SETUP_FAILURE`: 插件启动失败
- `PLUGIN_INITIALIZATION_FAILURE`: 插件初始化失败
- `PLUGIN_CONFIG_INVALID`: 插件配置无效
- `PLUGIN_EXECUTION_FAILURE`: 插件执行失败

## 最佳实践

1. **选择合适的错误码类型**：
   - 业务逻辑错误使用 `SwapBusinessErrorCode`
   - 技术/系统错误使用 `SwapTechnicalErrorCode`
   - 插件相关错误使用 `SwapPluginErrorCode`

2. **提供详细的错误信息**：
   - 使用 `toException(details)` 提供具体的错误上下文
   - 包含相关的ID、名称等关键信息

3. **保留原始异常**：
   - 使用 `toException(details, cause)` 保留原始异常堆栈
   - 便于问题追踪和调试

4. **统一异常处理**：
   - 在服务层统一捕获和处理异常
   - 记录详细的错误日志
   - 向上层返回用户友好的错误信息
