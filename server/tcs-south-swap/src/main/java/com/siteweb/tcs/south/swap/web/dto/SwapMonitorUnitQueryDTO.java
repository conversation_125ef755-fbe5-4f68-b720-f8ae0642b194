package com.siteweb.tcs.south.swap.web.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * SWAP监控单元查询条件DTO
 * 
 * <AUTHOR> for SWAP Plugin
 */
@Data
@Accessors(chain = true)
public class SwapMonitorUnitQueryDTO {

    /**
     * 监控单元ID（精确查询）
     */
    private Long monitorUnitId;

    /**
     * 关键词（模糊查询）
     * 支持对监控单元名称、IP地址、位置、备注等字段进行模糊查询
     */
    private String keywords;


    /**
     * Hub接入状态筛选
     * true: 只查询已接入Hub的监控单元
     * false: 只查询未接入Hub的监控单元
     * null: 查询所有监控单元
     */
    private Boolean hubConnected;

}
