package com.siteweb.tcs.south.swap.connector.letter;

import com.siteweb.tcs.south.swap.enums.MessageType;
import com.siteweb.tcs.south.swap.enums.SiteWebEncoding;
import com.siteweb.tcs.south.swap.connector.protocol.DataTransferObject;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> zhou
 * @description SiteWebMessage
 * @createTime 2025-02-15 14:44:21
 */
@NoArgsConstructor
public abstract class SiteWebMessage {

    @Getter
    @Setter
    /**
     * 原始数据包
     */
    private byte[] rawData;

    @Getter
    @Setter
    /**
     * 获取或设置起始位置
     */
    private int offset;

    @Getter
    @Setter
    /**
     * 获取或设置数据传输对象
     */
    private DataTransferObject rawObject;

    @Getter
    @Setter
    /**
     * 获取或设置消息类型
     */
    private MessageType messageType;

    @Getter
    @Setter
    /**
     * 获取或设置编码方式
     */
    private SiteWebEncoding encoding;

    @Getter
    @Setter
    /**
     * 获取或设置发送方主机Id
     */
    private int sourceHostId;

    @Getter
    @Setter
    /**
     * 获取或设置接受方主机Id
     */
    private int destinationHostId;

    @Getter
    @Setter
    /**
     * 获取或设置局站ID
     */
    private int stationId;

    @Getter
    @Setter
    /**
     * 获取或设置主机ID
     */
    private int hostId;

    public void loadRawData(byte[] rawData, int start) {
        this.setRawData(rawData);
        this.setOffset(start);
    }

    public void loadDataTransferObject(DataTransferObject dto) {
        this.setMessageType(dto.getMessageType());
        this.setRawObject(dto);
    }

    public byte[] toByteArray() {
        switch (this.getEncoding()) {
            case SITE_WEB:
                return this.getRawObject().toByteArray();
            case MS_SERIAL, PER:
            default:
                throw new RuntimeException("NotSupportedEncoding");
        }
    }
}
