package com.siteweb.tcs.south.swap.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * SWAP控制与Hub的映射关系实体
 * 对应siteweb-omc中的tbl_control表
 *
 * <AUTHOR> for SWAP Plugin
 */
@Data
@NoArgsConstructor
@TableName("swap_control_hub_mapping")
public class SwapControlHubMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 监控单元ID
     */
    @TableField("monitorunit_id")
    private Long monitorunitId;

    /**
     * 设备ID
     */
    @TableField("equipment_id")
    private Long equipmentId;

    /**
     * 控制ID(来自siteweb-omc的tbl_control表)
     */
    @TableField("control_id")
    private Long controlId;

    /**
     * Hub中的ID
     */
    @TableField("hub_id")
    private Long hubId;
}
