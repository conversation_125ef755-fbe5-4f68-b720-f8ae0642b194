package com.siteweb.tcs.south.swap.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.south.swap.dal.entity.SwapMonitorunitHubMapping;
import com.siteweb.tcs.south.swap.web.dto.SwapMonitorUnitQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * SWAP监控单元Hub映射Mapper
 * 
 * <AUTHOR> for SWAP Plugin
 */
@Mapper
public interface SwapMonitorunitHubMappingMapper extends BaseMapper<SwapMonitorunitHubMapping> {

    /**
     * 根据监控单元ID查找映射关系
     * @param monitorunitId 监控单元ID
     * @return 映射关系
     */
    SwapMonitorunitHubMapping selectByMonitorunitId(@Param("monitorunitId") Long monitorunitId);

    /**
     * 根据Hub ID查找映射关系
     * @param hubId Hub ID
     * @return 映射关系
     */
    SwapMonitorunitHubMapping selectByHubId(@Param("hubId") String hubId);

    /**
     * 查找有Hub ID的监控单元
     * @return 有Hub ID的映射关系列表
     */
    List<SwapMonitorunitHubMapping> selectWithHubId();

    /**
     * 查找没有Hub ID的监控单元
     * @return 没有Hub ID的映射关系列表
     */
    List<SwapMonitorunitHubMapping> selectWithoutHubId();

    /**
     * 根据监控单元ID列表查找映射关系
     * @param monitorunitIds 监控单元ID列表
     * @return 映射关系列表
     */
    List<SwapMonitorunitHubMapping> selectByMonitorunitIds(@Param("monitorunitIds") List<Long> monitorunitIds);

    /**
     * 批量插入映射关系
     * @param mappings 映射关系列表
     * @return 插入的记录数
     */
    int insertBatch(@Param("list") List<SwapMonitorunitHubMapping> mappings);

    /**
     * 批量更新Hub ID
     * @param monitorunitIds 监控单元ID列表
     * @param hubId 新的Hub ID
     * @return 更新的记录数
     */
    int updateHubIdByMonitorunitIds(@Param("monitorunitIds") List<Long> monitorunitIds,
                                   @Param("hubId") Long hubId);

    /**
     * 检查监控单元是否已存在映射关系
     * @param monitorunitId 监控单元ID
     * @return 是否存在
     */
    int existsByMonitorunitId(@Param("monitorunitId") Long monitorunitId);

    /**
     * 统计Hub连接状态的数量
     * @return 连接状态统计
     */
    List<Object[]> countByHubConnection();

    /**
     * 删除指定监控单元的映射关系
     * @param monitorunitId 监控单元ID
     * @return 删除的记录数
     */
    int deleteByMonitorunitId(@Param("monitorunitId") Long monitorunitId);


    int getMonitorunitCount();

    int getMappingCount();

    /**
     * 分页查询监控单元及Hub接入状态
     * @param offset 偏移量
     * @param size 页大小
     * @param queryDTO 查询条件
     * @return 监控单元列表
     */
    List<com.siteweb.tcs.south.swap.web.dto.SwapMonitorUnitDTO> selectMonitorUnitPageWithHubStatus(
            @Param("offset") long offset,
            @Param("size") long size,
            @Param("queryDTO") SwapMonitorUnitQueryDTO queryDTO);

    /**
     * 统计监控单元总数（带查询条件）
     * @param queryDTO 查询条件
     * @return 总数
     */
    long countMonitorUnitWithHubStatus(@Param("queryDTO") com.siteweb.tcs.south.swap.web.dto.SwapMonitorUnitQueryDTO queryDTO);
}