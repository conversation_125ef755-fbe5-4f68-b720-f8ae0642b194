package com.siteweb.tcs.south.swap.service;

import com.siteweb.tcs.south.swap.dal.entity.EventEquipmentItem;
import com.siteweb.tcs.south.swap.enums.EventReport;

import java.util.List;

/**
 * 事件展示服务接口
 * 
 * <AUTHOR> z<PERSON>
 * @description EventDisplayService
 * @createTime 2025-01-21 16:00:00
 */
public interface EventDisplayService {

    /**
     * 展示事件数据
     * 
     * @param equipmentItems 设备事件项列表
     * @param eventReport 事件上报类型
     * @param stationId 局站ID
     */
    void displayEventData(List<EventEquipmentItem> equipmentItems, EventReport eventReport, int stationId);
}
