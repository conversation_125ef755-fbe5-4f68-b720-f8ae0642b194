package com.siteweb.tcs.south.swap.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 监控单元同步状态DTO
 * 用于前端显示监控单元的Hub同步状态
 * 
 * <AUTHOR> for SWAP Plugin
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class MonitorunitSyncStatusDTO {

    /**
     * 监控单元ID(来自siteweb-omc)
     */
    private Long monitorunitId;

    /**
     * 监控单元名称(来自siteweb-omc)
     */
    private String monitorunitName;

    /**
     * 监控单元描述(来自siteweb-omc)
     */
    private String monitorunitDescription;

    /**
     * 监控单元类型(来自siteweb-omc)
     */
    private String monitorunitType;

    /**
     * 监控单元状态(来自siteweb-omc)
     */
    private String monitorunitStatus;

    /**
     * Hub中的ID
     */
    private String hubId;

    /**
     * 是否已连接到Hub
     */
    private Boolean hubConnected;

    /**
     * Hub连接状态显示名称
     */
    private String hubConnectionStatusDisplay;

    /**
     * 设置Hub连接状态并自动计算相关字段
     * @param connected 是否连接
     */
    public void setHubConnected(Boolean connected) {
        this.hubConnected = connected;

        // 设置显示名称
        if (connected != null && connected) {
            this.hubConnectionStatusDisplay = "已连接";
        } else {
            this.hubConnectionStatusDisplay = "未连接";
        }
    }

    /**
     * 获取连接状态的CSS类名(用于前端样式)
     * @return CSS类名
     */
    public String getConnectionStatusClass() {
        if (hubConnected != null && hubConnected) {
            return "status-connected";
        } else {
            return "status-not-connected";
        }
    }

    /**
     * 获取连接状态的图标(用于前端显示)
     * @return 图标名称
     */
    public String getConnectionStatusIcon() {
        if (hubConnected != null && hubConnected) {
            return "check-circle";
        } else {
            return "close-circle";
        }
    }
}
