package com.siteweb.tcs.south.swap.enums;

/**
 * <AUTHOR> zhou
 * @description ResultCode
 * @createTime 2025-01-22 10:33:06
 */
public enum ResultCode {
    /**
     * 成功
     */
    SUCCESS(0),

    /**
     * 失败
     */
    FAIL(1),

    /**
     * 拒绝注册，因为底端采集配置变更
     */
    UNREGISTER(2),

    /**
     * 非法协议版本
     */
    PROTOCOL_ERROR(200),

    /**
     * MU无法路由
     */
    UNAVAILABLE_ROUTE(201),

    /**
     * MU不可达
     */
    MU_UNREACHABLE(202),

    /**
     * 该MU不允许在该DS上注册
     */
    MU_NOT_ACCESS_DATA_SERVER(203);

    private final int value;

    ResultCode(int value) {
        this.value = value;
    }
}
