package com.siteweb.tcs.south.swap.connector.runtime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.south.swap.dal.entity.HostInfo;
import com.siteweb.tcs.south.swap.enums.CommunicationState;
import com.siteweb.tcs.south.swap.enums.ResultCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> zhou
 * @description HostInfoProxy
 * @createTime 2025-01-22 11:01:28
 */
public class HostInfoProxy {

    @Getter
    @Setter
    private HostInfo host;

    /**
     * 主机注册次数
     */
    @Getter
    @Setter
    private Integer registerCount;

    /**
     * 上次注册时间
     */
    @Getter
    @Setter
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastRegisterTime;

    /**
     * 上次心跳时间
     */
    @Getter
    @Setter
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastHeartbeatTime;

    /**
     * 注册结果
     */
    @Getter
    @Setter
    private ResultCode registerResult;

    public HostInfoProxy() {
        //
    }

    public HostInfoProxy(HostInfo host) {
        this.setRegisterResult(ResultCode.SUCCESS);
        this.setHost(host);
        this.setRegisterCount(0);
    }

    /*
     * 注册
     */
    public void register() {
        this.setLastRegisterTime(new Date());
        this.setRegisterCount(this.getRegisterCount() + 1);
    }

    /**
     * 心跳
     */
    public void heartbeat() {
        HostInfo hostInfo = this.getHost();
        hostInfo.setState(CommunicationState.ONLINE);
        this.setHost(hostInfo);
        this.setLastHeartbeatTime(new Date());
    }

    /**
     * 更新主机状态
     *
     * @param state
     */
    public void changeState(CommunicationState state) {
        this.getHost().setState(state);
    }

    public static HostInfoProxy fromStr(String str) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(str, HostInfoProxy.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String toStr(HostInfoProxy hostInfoProxy) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(hostInfoProxy);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return "";
    }
}
