package com.siteweb.tcs.south.swap.connector.letter;

import com.scaleout.client.util.BitConverter;
import com.siteweb.tcs.south.swap.enums.*;
import lombok.Getter;
import lombok.Setter;


/**
 * 报文格式如下所示：
 * 0                   1                   2                   3
 * 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 * |         SOI = 0x7E6D          |        Sequence Number        |
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 * |       |       |A|A|I|P|   | S |               |               |
 * |Ver = 1|   PT  |C|K|C|A|   | E |  Message Type |      TTL      |
 * |       |       |K|M|T|D|   | C |               |               |
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 * | Pipeline Type |                                               |
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 * |                         Source HostId                         |
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 * |                         Destination HostId                    |
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 * |          Info Length          |            Checksum           |
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 * |                           Information                         |
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 */
public class SiteWebPacket {

    /**
     * SWTP首部长度
     */
    public static final int HEAD_LENGTH = 24;

    /**
     * 16位起始标志位
     */
    private static final short SOI = 0x7E6D;

    /**
     * 16位起始标志位
     */
    private static final int SOI_INDEX = 0;

    /**
     * 16位起始标志位长度
     */
    private static final int SOI_LEN = 2;

    /**
     * 16位序号
     */
    private static final int SEQUENCE_NUMBER_INDEX = 2;

    /**
     * 16位序号长度
     */
    private static final int SEQUENCE_NUMBER_LEN = 2;

    /**
     * VerPT标志位
     */
    private static final int VER_PT_INDEX = 4;

    /**
     * ACK标志位
     */
    private static final int ACK_INDEX = 5;

    /**
     * 消息类型索引
     */
    private static final int MESSAGE_TYPE_INDEX = 6;

    /**
     * TTL字段索引
     */
    private static final int TTL_INDEX = 7;

    /**
     * 消息管道类型索引
     */
    private static final int PIPE_TYPE_INDEX = 8;

    /**
     * Source HostId索引
     */
    private static final int SOURCE_HOST_ID_INDEX = 12;

    /**
     * Source HostId长度
     */
    public static final int SOURCE_HOST_ID_LEN = 4;

    /**
     * Destination HostId索引
     */
    private static final int DESTINATION_HOST_ID_INDEX = 16;

    /**
     * Destination HostId长度
     */
    private static final int DESTINATION_HOST_ID_LEN = 4;

    /**
     * 信息域长度字段索引
     */
    private static final int INFO_LEN_INDEX = 20;

    /**
     * 信息域长度字段长度
     */
    private static final int INFO_LEN_LEN = 2;

    /**
     * 校验和字段索引
     */
    private static final int CHECKSUM_INDEX = 22;

    /**
     * 校验和字段长度
     */
    private static final int CHECKSUM_LEN = 2;

    /**
     * SWTP协议版本号（1～15）
     */
    private static final byte VERSION_ID = 1;
    /**
     * STATIONID_INDEX字段索引
     */
    private static final int STATION_ID_INDEX = 24;

    /**
     * HOSTID字段索引
     */
    private static final int HOST_ID_INDEX = 28;

//    /**
//     * 编码类型字段索引
//     */
//    private static final int ENCODING_INDEX = 32;
//
//    /**
//     * RDN字段索引
//     */
//    private static final int RDN_INDEX = 51;
//
//    private static final int MAX_TTL = 50;

    /**
     * SWDP协议偏移量
     */
    public static final int SWDP_OFFSET = 9;

    @Getter
    @Setter
    /**
     * SWTP协议版本号（1～15）
     */
    private byte version = 1;

    @Getter
    @Setter
    /**
     * 原始数据包
     */
    private byte[] rawPacket;

    @Getter
    @Setter
    /**
     * 序列号
     */
    private short sequenceNumber;

    @Getter
    @Setter
    /**
     * 数据包是否需要确认
     */
    private boolean requireAcknowledge;

    @Getter
    @Setter
    /**
     * 数据包是否自动确认
     */
    private boolean autoAcknowledge;

    @Getter
    @Setter
    /**
     * 数据包是否有填充字段
     */
    private boolean padding;

    @Getter
    @Setter
    /**
     * 是否忙
     */
    private boolean busy;

    @Getter
    @Setter
    /**
     * 数据包是否压缩
     */
    private boolean compressed;

    @Getter
    @Setter
    /**
     * 消息管道类型
     */
    private PipelineType pipelineType;

    @Getter
    @Setter
    /**
     * 数据包的加密类型
     */
    private EncryptType encryptType;

    @Getter
    @Setter
    /**
     * 数据包的协议类型
     */
    private ProtocolType protocolType;

    @Getter
    @Setter
    /**
     * 数据包消息类型
     */
    private MessageType messageType;

    @Getter
    @Setter
    /**
     * 数据包的生存期
     */
    private byte ttl;

    @Getter
    @Setter
    /**
     * 发送方主机Id
     */
    private int sourceHostId;

    @Getter
    @Setter
    /**
     * 接收方主机Id
     */
    private int destinationHostId;

    @Getter
    @Setter
    private byte[] bakPacket;

    @Getter
    @Setter
    private SiteWebMessage siteWebMessage;

    public SiteWebPacket() {
        this.setSourceHostId(0);
        this.setDestinationHostId(0);
        this.setBakPacket(new byte[3]);
        this.setMessageType(MessageType.UN_DEFINED);
    }

    public void parseHeader() {

        int index = SEQUENCE_NUMBER_INDEX;
        byte[] tmpRawPacket = this.getRawPacket();
        this.setSequenceNumber(BitConverter.getShort(tmpRawPacket, index));

        this.setVersion((byte) ((tmpRawPacket[index + 2] & 0xF0) >> 4));

        this.setProtocolType(ProtocolType.fromInt(tmpRawPacket[index + 2] & 0xF));

        this.setRequireAcknowledge((tmpRawPacket[index + 3] & 0x80) == 0x80);

        this.setAutoAcknowledge((tmpRawPacket[index + 3] & 0x40) == 0x40);

        this.setCompressed((tmpRawPacket[index + 3] & 0x20) == 0x20);
        this.setPadding((tmpRawPacket[index + 3] & 0x10) == 0x10);
        this.setBusy((tmpRawPacket[index + 3] & 0x0) != 0x0);
        this.setEncryptType(EncryptType.fromInt(tmpRawPacket[index + 3] & 0x3));
        this.setMessageType(MessageType.fromInt(tmpRawPacket[index + 4] & 0xFF));
        this.setTtl(tmpRawPacket[TTL_INDEX]);
        this.setPipelineType(PipelineType.fromInt(tmpRawPacket[index + 6]));
        //liangyj 20170911
        this.getBakPacket()[0] = tmpRawPacket[index + 7];
        this.getBakPacket()[1] = tmpRawPacket[index + 8];
        this.getBakPacket()[2] = tmpRawPacket[index + 9];

        this.setSourceHostId(BitConverter.getInt(tmpRawPacket, SOURCE_HOST_ID_INDEX));
        this.setDestinationHostId(BitConverter.getInt(tmpRawPacket, DESTINATION_HOST_ID_INDEX));
    }

    /**
     * 提取数据报中的消息体
     */
    public void pickMessage() {
        switch (this.getProtocolType()) {
            case SWDP:
                var dataMessage = new DataMessage(SiteWebEncoding.SITE_WEB);
                dataMessage.setSourceHostId(this.getSourceHostId());
                dataMessage.setDestinationHostId(this.getDestinationHostId());
                dataMessage.setStationId(BitConverter.getInt(rawPacket, STATION_ID_INDEX));
                dataMessage.setHostId(BitConverter.getInt(rawPacket, HOST_ID_INDEX));
                dataMessage.setMessageType(this.getMessageType());
                dataMessage.loadRawData(this.getRawPacket(), HEAD_LENGTH);
                this.setSiteWebMessage(dataMessage);
                break;
            case SWCP:
                break;
            case ACKNOWLEDGE:
            default:
                break;
        }
    }

    /**
     * 计算校验和
     *
     * @return
     */
    public short checkSum() {
        short checksum = 0;
        byte[] tmpRawPacket = this.getRawPacket();
        for (int i = 2; i < tmpRawPacket.length; i++) {
            checksum += (short) (tmpRawPacket[i] & 0xFF);
        }
        return checksum;
    }

    /**
     * 判断数据包是否合法
     *
     * @return
     */
    public boolean isValid() {
        byte[] tmpRawPacket = this.getRawPacket();
        int pktLen = tmpRawPacket.length;
        // 若数据报的长度大于首部长度
        if (pktLen >= HEAD_LENGTH) {
            //检验校验和
            short checksum = BitConverter.getShort(tmpRawPacket, CHECKSUM_INDEX);
            // 原始数据包的校验和置为0
            tmpRawPacket[CHECKSUM_INDEX] = 0;
            tmpRawPacket[CHECKSUM_INDEX + 1] = 0;

            short sum = 0;
            for (int i = 2; i < tmpRawPacket.length; i++) {
                sum += (short) (tmpRawPacket[i] & 0xFF);
            }

            // 还原原始数据包的校验和
            System.arraycopy(BitConverter.getBytes(checksum), 0, tmpRawPacket, CHECKSUM_INDEX, CHECKSUM_LEN);

            if (sum != checksum) {
                // log.warn(string.Format("Packet checkout fail required {0} send {1}", checksum, sum))
                return false;
            }
        } else {
            // log.warn("Packet doesn't siteweb protocol")
            return false;
        }
        return true;
    }

    /**
     * 把消息封装为数据包
     *
     * @param packet
     * @param info
     * @return
     */
    public byte[] pack(SiteWebPacket packet, byte[] info) {
        int infoLen = info.length;
        byte[] tmpRawPacket = new byte[HEAD_LENGTH + infoLen];

        // 封装协议首部
        System.arraycopy(BitConverter.getBytes(SOI), 0, tmpRawPacket, SOI_INDEX, SOI_LEN);

        // 若数据包需要确认，则产生SN
        if (packet.isRequireAcknowledge()) {
            System.arraycopy(BitConverter.getBytes(packet.getSequenceNumber()), 0, tmpRawPacket, SEQUENCE_NUMBER_INDEX, SEQUENCE_NUMBER_LEN);
        }

        byte aByte = 0;
        aByte |= (byte) (packet.getVersion() << 4);
        aByte |= (byte) packet.getProtocolType().value();
        tmpRawPacket[VER_PT_INDEX] = aByte;

        aByte = 0;
        aByte |= (byte) (packet.isRequireAcknowledge() ? 0x80 : 0x0);
        aByte |= (byte) (packet.isAutoAcknowledge() ? 0x40 : 0x0);
        aByte |= (byte) (packet.isCompressed() ? 0x20 : 0x0);
        aByte |= (byte) (packet.isPadding() ? 0x10 : 0x0);
        aByte |= (byte) (packet.getEncryptType().value());
        tmpRawPacket[ACK_INDEX] = aByte;

        // m_messageType = messageType
        tmpRawPacket[MESSAGE_TYPE_INDEX] = (byte) packet.getMessageType().value();
        tmpRawPacket[TTL_INDEX] = packet.getTtl();
        tmpRawPacket[PIPE_TYPE_INDEX] = (byte) packet.getPipelineType().value();
        System.arraycopy(BitConverter.getBytes(packet.getSourceHostId()), 0, tmpRawPacket, SOURCE_HOST_ID_INDEX, SOURCE_HOST_ID_LEN);
        System.arraycopy(BitConverter.getBytes(packet.getDestinationHostId()), 0, tmpRawPacket, DESTINATION_HOST_ID_INDEX, DESTINATION_HOST_ID_LEN);
        System.arraycopy(BitConverter.getBytes(infoLen), 0, tmpRawPacket, INFO_LEN_INDEX, INFO_LEN_LEN);
        System.arraycopy(info, 0, tmpRawPacket, HEAD_LENGTH, info.length);
        // TODO: 压缩和加密信息域
        packet.setRawPacket(tmpRawPacket);
        // TODO: 计算校验和，并填充该字段
        short checksum = packet.checkSum();
        System.arraycopy(BitConverter.getBytes(checksum), 0, tmpRawPacket, CHECKSUM_INDEX, CHECKSUM_LEN);
        return tmpRawPacket;
    }

    /**
     * 制作通用的确认包
     *
     * @return
     */
    public byte[] createCommonAckPacket(short sequenceNumber, boolean busy, int hostId) {
        byte[] packet = new byte[HEAD_LENGTH];
        System.arraycopy(BitConverter.getBytes(SOI), 0, packet, SOI_INDEX, SOI_LEN);
        System.arraycopy(BitConverter.getBytes(sequenceNumber), 0, packet, SEQUENCE_NUMBER_INDEX, SEQUENCE_NUMBER_LEN);
        System.arraycopy(BitConverter.getBytes(8888), 0, packet, SOURCE_HOST_ID_INDEX, SOURCE_HOST_ID_LEN);
        System.arraycopy(BitConverter.getBytes(hostId), 0, packet, DESTINATION_HOST_ID_INDEX, DESTINATION_HOST_ID_LEN);


        packet[4] = (byte) ((VERSION_ID << 4) | ProtocolType.ACKNOWLEDGE.value());
        packet[5] = (byte) (busy ? 0x40 : 0x0);
        // TODO: 计算校验和，并填充该字段
        // add by yangpu,加入packet[5]
        short checksum = (short) ((packet[2] & 0xFF) + (packet[3] & 0xFF) + (packet[4] & 0xFF) + (packet[5] & 0xFF) +
                (packet[12] & 0xFF) + (packet[13] & 0xFF) + (packet[14] & 0xFF) + (packet[15] & 0xFF) +
                (packet[16] & 0xFF) + (packet[17] & 0xFF) + (packet[18] & 0xFF) + (packet[19] & 0xFF));

        System.arraycopy(BitConverter.getBytes(checksum), 0, packet, CHECKSUM_INDEX, CHECKSUM_LEN);


        return packet;
    }
}
