# SWAP南向插件前端

基于Vue 3 + TypeScript + Element Plus + TailwindCSS构建的SWAP南向插件管理前端。

## 功能特性

### 🔄 监控单元Hub同步管理
- **同步状态展示**: 实时显示监控单元与Hub的同步状态
- **批量同步**: 支持多选监控单元进行批量同步操作
- **单个同步**: 支持单个监控单元的同步操作
- **同步统计**: 显示总数、已同步、未同步、同步失败的统计信息
- **搜索筛选**: 支持按名称、同步状态、设备类型进行筛选
- **错误处理**: 显示同步错误信息并支持重置操作

## 版本选择

当前是非国际化版本，如果您需要国际化版本 [请点击](https://github.com/pure-admin/tcs-hub/tree/i18n)

## `js` 版本

[点我查看 js 版本](https://pure-admin.cn/pages/js/)

## `max` 版本

[点我查看 max 版本](https://pure-admin.cn/pages/max/)

## 配套视频

[点我查看 UI 设计](https://www.bilibili.com/video/BV17g411T7rq)  
[点我查看快速开发教程](https://www.bilibili.com/video/BV1kg411v7QT)

## 配套保姆级文档

[点我查看 vue-pure-admin 文档](https://pure-admin.cn/)  
[点我查看 @pureadmin/utils 文档](https://pure-admin-utils.netlify.app)

## 优质服务、软件外包、赞助支持

[点我查看详情](https://pure-admin.cn/pages/service/)

## 预览

[查看预览](https://tcs-hub.netlify.app/#/login)

## 维护者

[xiaoxian521](https://github.com/xiaoxian521)

## ⚠️ 注意

精简版不接受任何 `issues` 和 `pr`，如果有问题请到完整版 [issues](https://github.com/pure-admin/vue-pure-admin/issues/new/choose) 去提，谢谢！

## 许可证

[MIT © 2020-present, pure-admin](./LICENSE)
