<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.swap.dal.mapper.SwapEquipmentMapper">

    <!-- 根据监控单元ID列表批量查询设备 -->
    <select id="selectByMonitorUnitIds" resultType="com.siteweb.tcs.siteweb.entity.Equipment">
        SELECT * FROM tbl_equipment 
        WHERE MonitorUnitId IN
        <foreach collection="monitorUnitIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据监控单元ID查询设备 -->
    <select id="selectByMonitorUnitId" resultType="com.siteweb.tcs.siteweb.entity.Equipment">
        SELECT * FROM tbl_equipment 
        WHERE MonitorUnitId = #{monitorUnitId}
    </select>

</mapper>
