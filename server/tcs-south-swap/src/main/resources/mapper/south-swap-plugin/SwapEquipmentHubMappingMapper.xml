<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.swap.dal.mapper.SwapEquipmentHubMappingMapper">

    <!-- 根据设备ID查找映射关系 -->
    <select id="selectByEquipmentId" resultType="com.siteweb.tcs.south.swap.dal.entity.SwapEquipmentHubMapping">
        SELECT * FROM swap_equipment_hub_mapping 
        WHERE equipment_id = #{equipmentId}
    </select>

    <!-- 根据设备ID列表批量查找映射关系 -->
    <select id="selectByEquipmentIds" resultType="com.siteweb.tcs.south.swap.dal.entity.SwapEquipmentHubMapping">
        SELECT * FROM swap_equipment_hub_mapping 
        WHERE equipment_id IN
        <foreach collection="equipmentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据监控单元ID查找设备映射关系 -->
    <select id="selectByMonitorunitId" resultType="com.siteweb.tcs.south.swap.dal.entity.SwapEquipmentHubMapping">
        SELECT * FROM swap_equipment_hub_mapping 
        WHERE monitorunit_id = #{monitorunitId}
    </select>

    <!-- 根据监控单元ID列表批量查找设备映射关系 -->
    <select id="selectByMonitorunitIds" resultType="com.siteweb.tcs.south.swap.dal.entity.SwapEquipmentHubMapping">
        SELECT * FROM swap_equipment_hub_mapping 
        WHERE monitorunit_id IN
        <foreach collection="monitorunitIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据Hub ID查找映射关系 -->
    <select id="selectByHubId" resultType="com.siteweb.tcs.south.swap.dal.entity.SwapEquipmentHubMapping">
        SELECT * FROM swap_equipment_hub_mapping 
        WHERE hub_id = #{hubId}
    </select>

    <!-- 批量插入映射关系 -->
    <insert id="insertBatch">
        INSERT INTO swap_equipment_hub_mapping (monitorunit_id, equipment_id, hub_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.monitorunitId}, #{item.equipmentId}, #{item.hubId})
        </foreach>
    </insert>

    <!-- 批量更新Hub ID -->
    <update id="batchUpdateHubIds">
        <foreach collection="equipmentIds" item="equipmentId" index="index" separator=";">
            UPDATE swap_equipment_hub_mapping 
            SET hub_id = #{hubIds[${index}]}
            WHERE equipment_id = #{equipmentId}
        </foreach>
    </update>

    <!-- 根据设备ID更新Hub ID -->
    <update id="updateHubIdByEquipmentId">
        UPDATE swap_equipment_hub_mapping 
        SET hub_id = #{hubId}
        WHERE equipment_id = #{equipmentId}
    </update>

    <!-- 检查设备是否已存在映射关系 -->
    <select id="existsByEquipmentId" resultType="int">
        SELECT COUNT(1) FROM swap_equipment_hub_mapping 
        WHERE equipment_id = #{equipmentId}
    </select>

    <!-- 删除指定设备的映射关系 -->
    <delete id="deleteByEquipmentId">
        DELETE FROM swap_equipment_hub_mapping 
        WHERE equipment_id = #{equipmentId}
    </delete>

    <!-- 删除指定监控单元下的所有设备映射关系 -->
    <delete id="deleteByMonitorunitId">
        DELETE FROM swap_equipment_hub_mapping 
        WHERE monitorunit_id = #{monitorunitId}
    </delete>

    <!-- 统计设备映射关系数量 -->
    <select id="countMappings" resultType="int">
        SELECT COUNT(1) FROM swap_equipment_hub_mapping
    </select>

    <!-- 统计已连接Hub的设备数量 -->
    <select id="countConnectedEquipments" resultType="int">
        SELECT COUNT(1) FROM swap_equipment_hub_mapping 
        WHERE hub_id IS NOT NULL
    </select>

</mapper>
