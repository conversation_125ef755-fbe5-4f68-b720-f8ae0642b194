package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.hub.dal.dto.MonitorUnitDTO;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@Data
@Deprecated
public class HandleGatewayLifeCycleResponse implements Serializable {

    private ForeignGateway foreignGateway;

    private MonitorUnitDTO monitorUnitDTO;

//    private List<ForeignDeviceConfigChange> foreignDeviceConfigChange;
}
