package com.siteweb.tcs.hub.dal.entity;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@TableName(value = "tcs_foreign_device")
public class ForeignDevice implements Serializable {
    @TableField(exist = false)
    private String foreignGatewayID;
    @TableField(value = "ForeignDeviceId")
    private String foreignDeviceID;
    @TableField(value = "MonitorUnitId")
    private int monitorUnitId;
    @TableField(value = "EquipmentId")
    private int equipmentId;
    @TableField(value = "EquipmentTemplateId")
    private Integer equipmentTemplateId;
    @TableField(exist = false)
    private Integer stationId;

    @TableField(exist = false)
    private List<ForeignSignal> foreignSignalList;
    @TableField(exist = false)
    private List<ForeignAlarm> foreignAlarmList;
    @TableField(exist = false)
    private List<ForeignControl> foreignControlList;
    @TableField(exist = false)
    private String pluginId;

    public ForeignDevice setForeignGatewayID(String foreignGatewayID) {
        this.foreignGatewayID = foreignGatewayID;
        if(CollectionUtil.isNotEmpty(foreignSignalList))
            foreignSignalList.forEach(e->e.setForeignGatewayID(foreignGatewayID));
        if(CollectionUtil.isNotEmpty(foreignAlarmList))
            foreignAlarmList.forEach(e->e.setForeignGatewayID(foreignGatewayID));
        if(CollectionUtil.isNotEmpty(foreignControlList))
            foreignControlList.forEach(e->e.setForeignGatewayId(foreignGatewayID));
        return this;
    }

    public String getCacheKey(){
        return StringUtils.join(pluginId,"_",foreignGatewayID,"_",foreignDeviceID);
    }

    public ForeignDevice clone() {
        ForeignDevice foreignDevice = new ForeignDevice();
        foreignDevice.setForeignGatewayID(foreignGatewayID);
        foreignDevice.setForeignDeviceID(foreignDeviceID);
        foreignDevice.setMonitorUnitId(monitorUnitId);
        foreignDevice.setEquipmentId(equipmentId);
        foreignDevice.setEquipmentTemplateId(equipmentTemplateId);
        foreignDevice.setStationId(stationId);
        foreignDevice.setForeignSignalList(foreignSignalList);
        foreignDevice.setForeignAlarmList(foreignAlarmList);
        foreignDevice.setForeignControlList(foreignControlList);
        foreignDevice.setPluginId(pluginId);
        return foreignDevice;
    }
}
