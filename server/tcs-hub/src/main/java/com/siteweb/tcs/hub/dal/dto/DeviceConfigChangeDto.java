package com.siteweb.tcs.hub.dal.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备配置变更DTO
 */
@Data
public class DeviceConfigChangeDto {

    /**
     * 设备ID
     */
    private Long id;

    /**
     * 网关ID
     */
    private Long gatewayId;

    /**
     * 南向设备ID
     */
    private String southDeviceId;

    /**
     * 南向设备名称
     */
    private String southDeviceName;

    /**
     * 南向设备类型
     */
    private Integer southDeviceType;

    /**
     * 南向元数据
     */
    private JsonNode metadata;

    /**
     * 逻辑删除标志
     */
    private Boolean deleted;

    /**
     * 生命周期事件类型
     */
    private LifeCycleEventEnum lifeCycleEvent;

    /**
     * 信号配置变更列表（可选）
     */
    private List<SignalConfigChangeDto> signals = new ArrayList<>();

    /**
     * 告警配置变更列表（可选）
     */
    private List<AlarmConfigChangeDto> alarms = new ArrayList<>();

    /**
     * 控制配置变更列表（可选）
     */
    private List<ControlConfigChangeDto> controls = new ArrayList<>();

    public void getCreateIdMapDto(CreateIdMapDto createIdMapDto) {
        if (lifeCycleEvent == LifeCycleEventEnum.CREATE)
            createIdMapDto.getSouthDeviceIdHubIdMap().put(southDeviceId, id);
        if (CollectionUtil.isNotEmpty(signals)) {
            signals.forEach(e -> e.getCreateIdMapDto(southDeviceId, createIdMapDto));
        }
        if (CollectionUtil.isNotEmpty(alarms)) {
            alarms.forEach(e -> e.getCreateIdMapDto(southDeviceId, createIdMapDto));
        }
        if (CollectionUtil.isNotEmpty(controls)) {
            controls.forEach(e -> e.getCreateIdMapDto(southDeviceId, createIdMapDto));
        }
    }

    private static final String uniqueKey = "%s.%s";

    public String getUniqueKey(){
        return String.format(uniqueKey,gatewayId,id);
    }
}