package com.siteweb.tcs.hub.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.hub.dal.entity.TcsGateway;
import com.siteweb.tcs.hub.dal.dto.ConfigChangeResult;
import com.siteweb.tcs.hub.dal.dto.GatewayConfigChangeDto;

import java.util.List;

/**
 * 网关表服务接口
 */
public interface ITcsGatewayService extends IService<TcsGateway> {

    /**
     * 根据插件ID查询网关列表
     * @param pluginId 插件ID
     * @return 网关列表
     */
    List<TcsGateway> listByPluginId(String pluginId);

    /**
     * 根据南向网关ID查询网关
     * @param southGatewayId 南向网关ID
     * @return 网关信息
     */
    TcsGateway getBySouthGatewayId(String southGatewayId);

    /**
     * 查询网关及其设备信息
     * @param gatewayId 网关ID
     * @return 网关信息（包含设备列表）
     */
    TcsGateway getGatewayWithDevices(Long gatewayId);

    /**
     * 根据网关ID查询网关完整信息
     * @param gatewayId 网关ID
     * @return 网关信息（包含设备、信号、告警、控制完整信息）
     */
    TcsGateway getGatewayWithFullInfo(Long gatewayId);

    /**
     * 保存或更新网关信息
     * @param gateway 网关信息
     * @return 是否成功
     */
    boolean saveOrUpdateGateway(TcsGateway gateway);

    /**
     * 处理网关配置变更
     * @param configDto 网关配置变更DTO
     * @return 配置变更结果
     */
    ConfigChangeResult handleGatewayConfigChange(GatewayConfigChangeDto configDto);
} 