package com.siteweb.tcs.hub.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.dal.mapper.ForeignGatewayMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class ForeignGatewayService extends ServiceImpl<ForeignGatewayMapper, ForeignGateway> implements IService<ForeignGateway> {

    @Autowired
    private ForeignGatewayMapper foreignGatewayMapper;

    public ForeignGateway removeGateway(String foreignGatewayID, String pluginID) {

        ForeignGateway foreignGateway = getOneForeignGateway(foreignGatewayID,pluginID);
        if(ObjectUtil.isEmpty(foreignGateway)){
            return null;
        }
        QueryWrapper<ForeignGateway> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ForeignGatewayId",foreignGatewayID);
        queryWrapper.eq("PluginId",pluginID);
        foreignGatewayMapper.delete(queryWrapper);
        return foreignGateway;
    }

    public ForeignGateway getForeignGateway(String foreignGatewayID, String pluginID){
        return getOneForeignGateway(foreignGatewayID,pluginID);
    }

    public boolean updateForeignGatewayId(String  pluginId,String oldForeignGatewayId,String newForeignGatewayId){
        if(StringUtils.isEmpty(oldForeignGatewayId) || StringUtils.isEmpty(newForeignGatewayId)) return false;
        Map<String,Object> params = new HashMap<>();
        params.put("ForeignGatewayId",oldForeignGatewayId);
        params.put("PluginId",pluginId);
        QueryWrapper<ForeignGateway> queryWrapper = new QueryWrapper<>();
        queryWrapper.allEq(params);
        boolean isExist = foreignGatewayMapper.exists(queryWrapper);
        if(!isExist) return false;
        Map<String, Object> paramNameValuePairs = queryWrapper.getParamNameValuePairs();
        UpdateWrapper<ForeignGateway> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("ForeignGatewayId",newForeignGatewayId)
                .allEq(params);
        return foreignGatewayMapper.update(updateWrapper)>0;
    }

    private ForeignGateway getOneForeignGateway(String foreignGatewayID, String pluginID){
        QueryWrapper<ForeignGateway> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ForeignGatewayId",foreignGatewayID);
        queryWrapper.eq("PluginId",pluginID);
        ForeignGateway foreignGateway = foreignGatewayMapper.selectOne(queryWrapper);
        return foreignGateway;
    }


    public boolean hasForeignGateway(String foreignGatewayID){
        QueryWrapper<ForeignGateway> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ForeignGatewayId",foreignGatewayID);
        Long foreignGateway = foreignGatewayMapper.selectCount(queryWrapper);
        return foreignGateway > 0;
    }

}
