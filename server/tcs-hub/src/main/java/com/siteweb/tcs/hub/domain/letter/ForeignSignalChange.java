package com.siteweb.tcs.hub.domain.letter;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ForeignSignalChange {
    private String foreignDeviceId;
    private String foreignSignalId;
    private String value;
    private String meanings;
    private boolean valid;
    //默认正常
    private Integer alarmState = 0;
    private LocalDateTime timestamp;
}
