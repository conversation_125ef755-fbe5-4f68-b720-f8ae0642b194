package com.siteweb.tcs.hub.domain.v2.letter;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: tcs2
 * @description: 控制响应变化消息
 * @author: xsx
 * @create: 2025-07-12 10:53
 **/
@Data
public class ControlCommandResponseChange {
    private Long gatewayId;
    private Long deviceId;
    private Long controlId;
    private String sequenceNo;
    private LocalDateTime endTime;
    private Integer resultCode;
    private String resultDesc;
}
