package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@TableName("tcs_operation_log")
public class OperationLog {
    @TableId(value = "LogId", type = IdType.AUTO)
    private Integer logId;

    @TableField("UserId")
    private Integer userId;
    
    @TableField("ObjectId")
    private Integer objectId;
    
    @TableField("ObjectName")
    private String objectName;
    
    @TableField("ObjectType")
    private Integer objectType;
    
    @TableField("OperationTime")
    private LocalDateTime operationTime;
    
    @TableField("OperationType")
    private String operationType;
    
    @TableField("OldValue")
    private String oldValue;
    
    @TableField("NewValue")
    private String newValue;
}