package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.hub.domain.letter.enums.CmdDeviceCategoryEnum;
import lombok.Data;

import java.util.List;

@Data
public class EquipmentControlCommandRequest {
    private final Integer monitorUnitId;
    private final Integer equipmentId;
    private final CmdDeviceCategoryEnum category;
    private final List<ControlCommandRequest> commandRequestList;
}
