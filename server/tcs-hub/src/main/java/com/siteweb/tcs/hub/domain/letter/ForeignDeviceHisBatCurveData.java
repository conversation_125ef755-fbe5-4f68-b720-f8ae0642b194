package com.siteweb.tcs.hub.domain.letter;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class ForeignDeviceHisBatCurveData {

    private String foreignDeviceId;
    private LocalDateTime startTime;
    /** 格式举例：”130610010010, 130610020010“，前一个是电压spid，后一个是电流spid */
    private String foreignSignalIds;

    private List<ForeignDeviceHisBatRecord> recordLists;
}
