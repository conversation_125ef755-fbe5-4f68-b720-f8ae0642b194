package com.siteweb.tcs.hub.service.impl;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.hub.dal.dto.MenuItemWithRoleResult;
import com.siteweb.tcs.hub.dal.dto.RegionDTO;
import com.siteweb.tcs.hub.dal.dto.RolePermissionMapDTO;
import com.siteweb.tcs.hub.dal.entity.AuthCode;
import com.siteweb.tcs.hub.dal.entity.MenuItem;
import com.siteweb.tcs.hub.dal.entity.Region;
import com.siteweb.tcs.hub.dal.entity.RegionItem;
import com.siteweb.tcs.hub.dal.entity.Role;
import com.siteweb.tcs.common.enums.PermissionType;
import com.siteweb.tcs.hub.dal.mapper.AuthCodeMapper;
import com.siteweb.tcs.hub.dal.mapper.PermissionMapper;
import com.siteweb.tcs.hub.dal.mapper.PermissionMapper.MenuAuthCodeMapping;
import com.siteweb.tcs.hub.dal.mapper.RegionMapper;
import com.siteweb.tcs.hub.dal.mapper.RoleMapper;
import com.siteweb.tcs.hub.security.TokenUserUtil;
import com.siteweb.tcs.hub.service.PermissionService;
import com.siteweb.tcs.hub.service.RegionItemService;
import com.siteweb.tcs.hub.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PermissionServiceImpl implements PermissionService {
    @Autowired
    private PermissionMapper permissionMapper;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private RegionMapper regionMapper;
    @Autowired
    private RegionService regionService;
    @Autowired
    private RegionItemService regionItemService;
    @Autowired
    private AuthCodeMapper authCodeMapper;

    @Override
    public List<MenuItem> findMenuTree() {
        // 获取菜单项及其关联的角色信息
        List<MenuItemWithRoleResult> menuWithRoleResults = permissionMapper.getMenuItemsWithRoles();
        
        // 将查询结果转换为MenuItem并填充roles字段
        Map<Integer, MenuItem> allMenuItemMap = convertToMenuItemsWithRoles(menuWithRoleResults);
        
        // 获取所有菜单的权限代码映射并填充auths字段
        fillMenuAuthsFromAuthCodeTable(allMenuItemMap);
        
        // 构建菜单树
        buildMenuTree(allMenuItemMap);
        
        // 返回根菜单项
        return allMenuItemMap.values().stream()
            .filter(o -> o.getParentMenuItemId().equals(0))
            .sorted(Comparator.comparing(MenuItem::getRank, Comparator.nullsLast(Integer::compareTo)))
            .collect(Collectors.toList());
    }

    @Override
    public List<MenuItem> findMenuPermissionTreeById(Integer roleId) {
        if (roleId == -1)
            return permissionMapper.getAllMenuItem();
        return permissionMapper.getUserPermMenuByRoleId(roleId);
    }

    @Override
    public List<MenuItem> findMenuTreeForCurrentUser() {
        try {
            // 获取当前用户ID
            Integer userId = TokenUserUtil.getLoginUserId();
            
            // 获取完整的菜单树（包含所有菜单和完整的权限代码）
            List<MenuItem> menuTree = this.findMenuTree();
            
            // 获取用户角色
            List<Integer> userRoles = roleMapper.findRolesByUserId(userId).stream()
                    .map(Role::getRoleId)
                    .collect(Collectors.toList());
            
            // 如果是超级管理员，直接返回完整菜单树
            if (userRoles.contains(-1)) {
                return menuTree;
            }
            
            // 获取用户的权限代码
            List<String> userAuthCodes = authCodeMapper.getUserAuthCodes(userId);
            
            // 过滤菜单的 auths 字段
            filterMenuAuths(menuTree, userAuthCodes);
            
            return menuTree;
            
        } catch (Exception e) {
            log.error("获取当前用户菜单树失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createMenuPermissionRoleMap(RolePermissionMapDTO rolePermissionMapDTO, Integer permissionType) {
        try {
            Integer roleId = rolePermissionMapDTO.getRoleId();
            String bindIdS = rolePermissionMapDTO.getBindIds();
            if (roleId == null || bindIdS == null)
                return -1;
            
            // 使用枚举替代魔法值
            PermissionType type = PermissionType.fromCodeRequired(permissionType);
            String objectName = type.isMenu() ? "菜单ID" : type.isRegion() ? "区域ID" : "权限ID";
            
            List<String> bindIdList = Arrays.stream(bindIdS.split(","))
                    .map(String::trim)  // 去除空格
                    .filter(StringUtils::hasText)  // 过滤空字符串
                    .distinct()  // 去重
                    .collect(Collectors.toList());
            
            // 根据权限类型处理不同的插件ID映射逻辑
            if (type.isButton()) {
                // 按钮权限：根据权限代码ID查询插件ID
                return handleButtonPermissions(roleId, bindIdList, permissionType);
            } else if (type.isMenu()) {
                // 菜单权限：根据菜单ID查询插件ID
                return handleMenuPermissions(roleId, bindIdList, permissionType);
            } else {
                // 区域权限等其他类型，继续使用原有逻辑（使用tcs_hub）
                permissionMapper.deleteMenuRoleMap(roleId, "tcs_hub", permissionType);
                return permissionMapper.createMenuRoleMap(roleId, "tcs_hub", bindIdList, permissionType);
            }
        } catch (Exception ex) {
            log.error("createMenuPermissionRoleMap error! ", ex);
            throw new RuntimeException("createMenuPermissionRoleMap run error");
        }
    }
    
    /**
     * 处理按钮权限的插件ID映射
     */
    private int handleButtonPermissions(Integer roleId, List<String> bindIdList, Integer permissionType) {
        if (bindIdList.isEmpty()) {
            return 0;
        }
        
        // 将字符串ID转换为Long类型
        List<Long> authIds = bindIdList.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        
        // 查询权限ID对应的插件ID
        List<AuthCodeMapper.AuthPluginMapping> mappings = authCodeMapper.getPluginIdsByAuthIds(authIds);
        
        // 按插件ID分组
        Map<String, List<String>> pluginAuthMap = mappings.stream()
                .collect(Collectors.groupingBy(
                    AuthCodeMapper.AuthPluginMapping::getPluginId,
                    Collectors.mapping(
                        mapping -> String.valueOf(mapping.getAuthId()),
                        Collectors.toList()
                    )
                ));
        
        int totalAffectedRows = 0;
        
        // 先删除该角色的所有相关权限（按插件分组删除）
        Set<String> allPluginIds = pluginAuthMap.keySet();
        for (String pluginId : allPluginIds) {
            permissionMapper.deleteMenuRoleMap(roleId, pluginId, permissionType);
        }
        
        // 按插件ID分别插入权限映射
        for (Map.Entry<String, List<String>> entry : pluginAuthMap.entrySet()) {
            String pluginId = entry.getKey();
            List<String> authIdStrList = entry.getValue();
            
            log.info("为角色 {} 在插件 {} 中设置权限: {}", roleId, pluginId, authIdStrList);
            int affectedRows = permissionMapper.createMenuRoleMap(roleId, pluginId, authIdStrList, permissionType);
            totalAffectedRows += affectedRows;
        }
        
        return totalAffectedRows;
    }
    
    /**
     * 处理菜单权限的插件ID映射
     */
    private int handleMenuPermissions(Integer roleId, List<String> bindIdList, Integer permissionType) {
        if (bindIdList.isEmpty()) {
            return 0;
        }
        
        // 将字符串ID转换为Long类型
        List<Long> menuIds = bindIdList.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        
        // 查询菜单ID对应的插件ID
        List<PermissionMapper.MenuPluginMapping> mappings = permissionMapper.getPluginIdsByMenuIds(menuIds);
        
        // 按插件ID分组
        Map<String, List<String>> pluginMenuMap = mappings.stream()
                .collect(Collectors.groupingBy(
                    PermissionMapper.MenuPluginMapping::getPluginId,
                    Collectors.mapping(
                        mapping -> String.valueOf(mapping.getMenuItemId()),
                        Collectors.toList()
                    )
                ));
        
        int totalAffectedRows = 0;
        
        // 先删除该角色的所有相关权限（按插件分组删除）
        Set<String> allPluginIds = pluginMenuMap.keySet();
        for (String pluginId : allPluginIds) {
            permissionMapper.deleteMenuRoleMap(roleId, pluginId, permissionType);
        }
        
        // 按插件ID分别插入菜单权限映射
        for (Map.Entry<String, List<String>> entry : pluginMenuMap.entrySet()) {
            String pluginId = entry.getKey();
            List<String> menuIdStrList = entry.getValue();
            
            log.info("为角色 {} 在插件 {} 中设置菜单权限: {}", roleId, pluginId, menuIdStrList);
            int affectedRows = permissionMapper.createMenuRoleMap(roleId, pluginId, menuIdStrList, permissionType);
            totalAffectedRows += affectedRows;
        }
        
        return totalAffectedRows;
    }

    @Override
    public List<Region> findRegionPermissionTreeById(Integer roleId) {
        if (roleId == -1) {
            return regionMapper.selectAll();
        }
        return permissionMapper.getRegionListByRoleId(roleId);
    }

    @Override
    public MenuItem getMenuTreeByUserId(Integer userId, List<String> startPluginsName) {
        try {
            // 获取所有菜单项
            List<MenuItem> allMenus = permissionMapper.getMenuTreeWithPaths();
            
            // 过滤指定插件的菜单
            List<MenuItem> filteredMenus = allMenus.stream()
                    .filter(menu -> startPluginsName.contains(menu.getPluginId()) || menu.getPluginId().equals("tcs_hub"))
                    .collect(Collectors.toList());
            
            Map<Integer, MenuItem> menuMap = filteredMenus.stream()
                    .collect(Collectors.toMap(MenuItem::getMenuItemId, item -> item));
            
            // 检查用户角色权限
            List<Integer> userRoles = roleMapper.findRolesByUserId(userId).stream()
                    .map(Role::getRoleId)
                    .collect(Collectors.toList());
            
            List<MenuItem> accessibleMenus;
            if (userRoles.contains(-1)) {
                // 超级管理员，可访问所有菜单
                accessibleMenus = filteredMenus;
            } else {
                // 根据用户权限过滤菜单
                List<MenuItem> userPermMenus = permissionMapper.getUserPermMenuTree(userId);
                accessibleMenus = userPermMenus.stream()
                        .filter(menu -> menuMap.containsKey(menu.getMenuItemId()))
                        .map(menu -> menuMap.get(menu.getMenuItemId()))
                        .collect(Collectors.toList());
            }
            
            // 如果不是超级管理员，进行权限过滤
            if (!userRoles.contains(-1)) {
                // 获取用户的权限代码
                List<String> userAuthCodes = authCodeMapper.getUserAuthCodes(userId);
                // 过滤菜单的 auths 字段
                filterMenuAuths(accessibleMenus, userAuthCodes);
            }
            
            // 构建菜单树
            Map<Integer, MenuItem> accessibleMenuMap = accessibleMenus.stream()
                    .collect(Collectors.toMap(MenuItem::getMenuItemId, item -> item));
            
            buildMenuTree(accessibleMenuMap);
            
            // 返回根菜单
            return accessibleMenuMap.values().stream()
                    .filter(menu -> menu.getParentMenuItemId().equals(0))
                    .findFirst()
                    .orElse(null);
        } catch (Exception ex) {
            log.error("getMenuTreeByUserId Error !", ex);
        }
        return null;
    }

    @Override
    public List<RegionDTO> getRegionTreeByUserId() {
        List<RegionDTO> res = new ArrayList<>();
        try {
            Integer userId = TokenUserUtil.getLoginUserId();
            List<Integer> userRole = roleMapper.findRolesByUserId(userId).stream().map(Role::getRoleId).toList();

            Map<Long, RegionDTO> idmap = new HashMap<>();
            List<RegionDTO> dtoList = regionService.findAll().stream()
                    .map(RegionDTO::from)
                    .peek(e -> idmap.put(e.getRegionId(), e)).toList();

            if (!userRole.contains(-1)) {
                dtoList = permissionMapper.getRegionListByUserId(userId);
            }
            for (RegionDTO oneRegion : dtoList) {
                RegionDTO sonRegion = idmap.get(oneRegion.getRegionId());
                if (oneRegion.getParentId() != null && idmap.containsKey(oneRegion.getParentId())) {
                    RegionDTO parentMenu = idmap.get(oneRegion.getParentId());
                    parentMenu.getChildren().add(sonRegion);
                }
            }
            res.add(idmap.values().stream().filter(o -> o.getParentId() == null).findFirst().orElse(null));
            return res;
        } catch (Exception ex) {
            log.error("getRegionTreeByUserId Error !", ex);
        }
        return res;
    }

    @Override
    public List<RegionDTO> findItemTreeByUserId(String pluginId) {
        try {
            List<RegionDTO> res = new ArrayList<>();
            Integer userId = TokenUserUtil.getLoginUserId();
            List<Integer> userRole = roleMapper.findRolesByUserId(userId).stream().map(Role::getRoleId).toList();

            // 获取与插件相关的区域项
            List<RegionItem> relatedItems = null;
            if ("*".equals(pluginId)) {
                relatedItems = regionItemService.getAllRegionItems();
            } else {
                relatedItems = regionItemService.getRegionItemsByPluginId(pluginId);
            }
            // 按区域ID分组区域项
            Map<Long, List<RegionItem>> regionItemsMap = relatedItems.stream()
                    .collect(Collectors.groupingBy(RegionItem::getRegionId));

            Map<Long, RegionDTO> idmap = new HashMap<>();
            List<RegionDTO> dtoList = regionService.findAll().stream()
                    .map(RegionDTO::from)
                    .peek(e -> idmap.put(e.getRegionId(), e)).toList();
            if (!userRole.contains(-1)) {
                dtoList = permissionMapper.getRegionListByUserId(userId);
            }
            for (RegionDTO oneRegion : dtoList) {
                RegionDTO sonRegion = idmap.get(oneRegion.getRegionId());
                sonRegion.setItems(regionItemsMap.get(sonRegion.getRegionId()));
                if (oneRegion.getParentId() != null && idmap.containsKey(oneRegion.getParentId())) {
                    RegionDTO parentMenu = idmap.get(oneRegion.getParentId());
                    parentMenu.getChildren().add(sonRegion);
                }
            }
            res.add(idmap.values().stream().filter(o -> o.getParentId() == null).findFirst().orElse(null));
            return res;
        } catch (Exception ex) {
            log.error("findItemTreeByUserId error !", ex);
        }
        return null;
    }

    @Override
    public List<RegionDTO> getRegionsByUser() {
        List<RegionDTO> res = new ArrayList<>();
        try {
            Integer userId = TokenUserUtil.getLoginUserId();
            List<Integer> userRole = roleMapper.findRolesByUserId(userId).stream().map(Role::getRoleId).toList();
            List<RegionDTO> dtoList = regionService.findAll().stream()
                    .map(RegionDTO::from)
                    .toList();

            if (!userRole.contains(-1)) {
                dtoList = permissionMapper.getRegionListByUserId(userId);
            }return dtoList;
        }catch (Exception ex){
            log.error("getRegionsByUser Error ",ex);
        }
        return res;
    }

    public void buildMenuTree(Map<Integer, MenuItem> menuItemMap) {
        try {
            for (MenuItem oneMenuItem : menuItemMap.values()) {
                if (oneMenuItem.getParentMenuItemId() != 0 && menuItemMap.containsKey(oneMenuItem.getParentMenuItemId())) {
                    MenuItem parentMenu = menuItemMap.get(oneMenuItem.getParentMenuItemId());
                    parentMenu.getChildren().add(oneMenuItem);
                }
            }
        } catch (Exception ex) {
            log.error("Build Tree Error !", ex);
        }
    }

    /**
     * 将查询结果转换为MenuItem对象并填充roles字段
     * 
     * @param menuWithRoleResults 菜单与角色关联查询结果
     * @return 菜单项Map，key为menuItemId，value为MenuItem对象
     */
    private Map<Integer, MenuItem> convertToMenuItemsWithRoles(List<MenuItemWithRoleResult> menuWithRoleResults) {
        Map<Integer, MenuItem> menuItemMap = new HashMap<>();
        
        // 按menuItemId分组，每个菜单项可能对应多个角色
        Map<Integer, List<MenuItemWithRoleResult>> groupedByMenuId = menuWithRoleResults.stream()
                .collect(Collectors.groupingBy(MenuItemWithRoleResult::getMenuItemId));
        
        for (Map.Entry<Integer, List<MenuItemWithRoleResult>> entry : groupedByMenuId.entrySet()) {
            Integer menuItemId = entry.getKey();
            List<MenuItemWithRoleResult> roleResults = entry.getValue();
            
            // 取第一个结果作为基础菜单信息
            MenuItemWithRoleResult firstResult = roleResults.get(0);
            
            // 创建MenuItem对象
            MenuItem menuItem = new MenuItem();
            menuItem.setMenuItemId(firstResult.getMenuItemId());
            menuItem.setPluginId(firstResult.getPluginId());
            menuItem.setMenuItemName(firstResult.getMenuItemName());
            menuItem.setName(firstResult.getName());
            menuItem.setParentMenuItemId(firstResult.getParentMenuItemId());
            menuItem.setPath(firstResult.getPath());
            menuItem.setIcon(firstResult.getIcon());
            menuItem.setComponent(firstResult.getComponent());
            menuItem.setShowLink(firstResult.getShowLink());
            menuItem.setShowParent(firstResult.getShowParent());
            menuItem.setActivePath(firstResult.getActivePath());
            menuItem.setRedirect(firstResult.getRedirect());
            menuItem.setRank(firstResult.getRank());
            menuItem.setAuths(firstResult.getAuths());
            
            // 收集所有关联的角色代码
            List<String> roles = roleResults.stream()
                    .map(MenuItemWithRoleResult::getRoleCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            
            menuItem.setRoles(roles);
            menuItemMap.put(menuItemId, menuItem);
        }
        
        return menuItemMap;
    }

    /**
     * 从权限代码表填充菜单的auths字段
     * @param menuItemMap 菜单项映射
     */
    private void fillMenuAuthsFromAuthCodeTable(Map<Integer, MenuItem> menuItemMap) {
        try {
            // 获取所有菜单的权限代码映射
            List<MenuAuthCodeMapping> authCodeMappings = permissionMapper.getMenuAuthCodeMappings();
            
            // 按菜单ID分组权限代码
            Map<Long, List<String>> menuAuthCodesMap = authCodeMappings.stream()
                .collect(Collectors.groupingBy(
                    MenuAuthCodeMapping::getMenuItemId,
                    Collectors.mapping(MenuAuthCodeMapping::getAuthCode, Collectors.toList())
                ));
            
            // 为每个菜单填充完整的权限代码
            ObjectMapper mapper = new ObjectMapper();
            for (MenuItem menuItem : menuItemMap.values()) {
                List<String> authCodes = menuAuthCodesMap.get(Long.valueOf(menuItem.getMenuItemId()));
                if (authCodes != null && !authCodes.isEmpty()) {
                    // 将权限代码列表转换为JSON字符串
                    menuItem.setAuths(mapper.writeValueAsString(authCodes));
                    log.debug("菜单 {} 的权限代码: {}", menuItem.getMenuItemName(), authCodes);
                } else {
                    // 如果没有权限代码，设置为空数组
                    menuItem.setAuths("[]");
                }
            }
            
            log.info("成功为 {} 个菜单填充权限代码，权限映射总数: {}", 
                menuItemMap.size(), authCodeMappings.size());
                
        } catch (Exception e) {
            log.error("填充菜单权限代码失败", e);
            // 发生异常时，为所有菜单设置空的权限数组
            for (MenuItem menuItem : menuItemMap.values()) {
                menuItem.setAuths("[]");
            }
        }
    }

    /**
     * 过滤菜单的 auths 字段，只保留用户有权限的操作
     * @param menus 菜单列表
     * @param userAuthCodes 用户权限代码列表
     */
    private void filterMenuAuths(List<MenuItem> menus, List<String> userAuthCodes) {
        menus.forEach(menu -> {
            if (menu.getAuths() != null && !menu.getAuths().isEmpty()) {
                try {
                    // 解析菜单的权限列表
                    ObjectMapper mapper = new ObjectMapper();
                    List<String> allAuths = mapper.readValue(menu.getAuths(), 
                        new TypeReference<List<String>>() {});
                    
                    // 过滤：只保留用户有权限的代码
                    List<String> allowedAuths = allAuths.stream()
                        .filter(userAuthCodes::contains)
                        .collect(Collectors.toList());
                    
                    // 设置过滤后的权限列表
                    menu.setAuths(mapper.writeValueAsString(allowedAuths));
                } catch (Exception e) {
                    log.warn("解析菜单权限失败: menuId={}", menu.getMenuItemId());
                    menu.setAuths("[]");
                }
            }
            
            // 递归处理子菜单
            if (menu.getChildren() != null) {
                filterMenuAuths(menu.getChildren(), userAuthCodes);
            }
        });
    }
}
