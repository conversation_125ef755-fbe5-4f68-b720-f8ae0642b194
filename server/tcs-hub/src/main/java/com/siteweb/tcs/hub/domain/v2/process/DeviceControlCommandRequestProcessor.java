package com.siteweb.tcs.hub.domain.v2.process;


import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceControlCommandRequestChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-14 09:26
 **/

public class DeviceControlCommandRequestProcessor extends ProbeActor {
    private TcsDevice device;

    private ActorRef controlCommandHandlerActor;

    private DeviceControlCommandRequestProcessor(TcsDevice device,ActorRef controlCommandHandlerActor){
        this.device = device;
        this.controlCommandHandlerActor = controlCommandHandlerActor;
    }

    public static Props props(TcsDevice device, ActorRef controlCommandHandlerActor){
        return Props.create(DeviceControlCommandRequestProcessor.class,device,controlCommandHandlerActor);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DeviceControlCommandRequestChange.class,this::onDeviceControlCommandRequestChange)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build();
    }

    //接受控制请求处理后发送给南向控制器处理actor
    private void onDeviceControlCommandRequestChange(DeviceControlCommandRequestChange deviceControlCommandRequestChange) {
        controlCommandHandlerActor.tell(deviceControlCommandRequestChange,getSelf());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.device = (TcsDevice) needUpdateAction.getConfig();
        }
    }
}
