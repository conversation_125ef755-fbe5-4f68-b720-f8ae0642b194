package com.siteweb.tcs.hub.domain.v2.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceHistorySignalChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 11:06
 **/

public class DeviceHistoryDataProcessor extends ProbeActor {
    private TcsDevice device;
    private ActorRef storeActor;

    private DeviceHistoryDataProcessor(TcsDevice device,ActorRef storeActor){
        this.device = device;
        this.storeActor = storeActor;
    }

    public static Props props(TcsDevice device, ActorRef storeActor){
        return Props.create(DeviceHistoryDataProcessor.class,device,storeActor);
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(DeviceHistorySignalChange.class,this::onDeviceHistorySignalChange)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    //处理历史数据
    private void onDeviceHistorySignalChange(DeviceHistorySignalChange deviceHistorySignalChange) {
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.device = (TcsDevice) needUpdateAction.getConfig();
        }
    }

}
