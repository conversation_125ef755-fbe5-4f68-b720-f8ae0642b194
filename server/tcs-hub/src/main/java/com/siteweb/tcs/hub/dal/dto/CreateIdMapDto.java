package com.siteweb.tcs.hub.dal.dto;

import cn.hutool.core.lang.Pair;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-15 17:04
 **/
@Data
public class CreateIdMapDto {
    private Long gatewayId;
    private String southGatewayId;
    private Map<String,Long> southDeviceIdHubIdMap = new HashMap<>();
    private Map<Pair<String,String>,Long> southSignalIdHubIdMap = new HashMap<>();
    private Map<Pair<String,String>,Long> southAlarmIdHubIdMap = new HashMap<>();
    private Map<Pair<String,String>,Long> southControlIdHubIdMap = new HashMap<>();

    public Long getHubDeviceId(String southDeviceId) {
        return southDeviceIdHubIdMap.get(southDeviceId);
    }
    
    public Long getHubSignalId(String southDeviceId,String southSignalId){
        return southSignalIdHubIdMap.get(Pair.of(southDeviceId,southSignalId));
    }

    public Long getHubAlarmId(String southDeviceId,String southAlarmId){
        return southSignalIdHubIdMap.get(Pair.of(southDeviceId,southAlarmId));
    }

    public Long getHubControlId(String southDeviceId,String southControlId){
        return southSignalIdHubIdMap.get(Pair.of(southDeviceId,southControlId));
    }
}
