package com.siteweb.tcs.hub.service;

import com.siteweb.tcs.hub.dal.dto.DepartmentDTO;
import com.siteweb.tcs.hub.dal.entity.Department;

import java.util.List;

/**
 * 部门服务接口
 */
public interface DepartmentService {

    /**
     * 获取部门树形结构列表
     *
     * @param status 状态筛选，null表示不筛选
     * @param name 名称筛选，null表示不筛选
     * @return 部门树形结构列表
     */
    List<DepartmentDTO> getDepartmentTree(Integer status, String name);

    /**
     * 创建新部门
     *
     * @param departmentDTO 部门信息DTO
     * @return 创建成功的部门信息
     * @throws RuntimeException 当部门编码重复或创建失败时抛出异常
     */
    DepartmentDTO createDepartment(DepartmentDTO departmentDTO);

    /**
     * 更新部门信息
     *
     * @param departmentDTO 部门更新信息DTO
     * @return 更新后的部门信息，如果部门不存在返回null
     * @throws RuntimeException 当部门编码重复或更新失败时抛出异常
     */
    DepartmentDTO updateDepartment(DepartmentDTO departmentDTO);

    /**
     * 根据ID获取部门信息
     *
     * @param id 部门ID
     * @return 部门DTO
     */
    DepartmentDTO getById(Integer id);

    /**
     * 根据编码获取部门信息
     *
     * @param code 部门编码
     * @return 部门信息
     */
    Department getByCode(String code);

    /**
     * 更新部门状态
     *
     * @param id 部门ID
     * @param status 状态：1-启用，0-停用
     * @param cascadeChildren 是否级联更新子部门状态
     * @return 更新结果
     * @throws IllegalArgumentException 当必填信息为空时抛出异常
     */
    boolean updateStatus(Integer id, Integer status, boolean cascadeChildren);

    /**
     * 删除部门
     *
     * @param id 部门ID
     * @return 删除结果
     * @throws RuntimeException 当部门存在子部门或删除失败时抛出异常
     */
    boolean deleteDepartment(Integer id);

    /**
     * 检查部门编码是否存在
     *
     * @param code 部门编码
     * @param excludeId 排除的部门ID，新增时传null
     * @return 是否存在
     */
    boolean checkCodeExists(String code, Integer excludeId);

    /**
     * 检查部门是否有子部门
     *
     * @param parentId 父部门ID
     * @return 是否有子部门
     */
    boolean hasChildren(Integer parentId);

    /**
     * 获取部门的所有子部门ID（递归）
     *
     * @param parentId 父部门ID
     * @return 子部门ID列表
     */
    List<Integer> getAllChildrenIds(Integer parentId);

    /**
     * 根据父ID获取直接子部门列表
     *
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Department> getChildrenByParentId(Integer parentId);

    /**
     * 获取所有部门列表（扁平结构，用于下拉选择）
     *
     * @return 部门列表
     */
    List<DepartmentDTO> getAllDepartments();

    /**
     * 验证部门层级关系（避免循环引用）
     *
     * @param departmentId 部门ID
     * @param parentId 父部门ID
     * @return 是否合法
     */
    boolean validateParentRelation(Integer departmentId, Integer parentId);
}