package com.siteweb.tcs.hub.domain.process;

import com.google.common.collect.Lists;
import com.siteweb.tcs.common.o11y.ActorParameter;
import com.siteweb.tcs.common.o11y.ActorParameterDataType;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentRealSignal;
import com.siteweb.tcs.hub.domain.letter.GetEquipmentRealSignalAction;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.letter.RealSignal;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
public class EquipmentSignalStateStore extends ProbeActor {

    private final EquipmentSignalActiveState state = new EquipmentSignalActiveState();

    private static final String MISFIRE_DEVICE = "MISFIRE_DEVICE";
    private static final String MISFIRE_SIGNAL = "MISFIRE_SIGNAL";
    private final String REAL_DATA = "real_data";
    private final ActorRef spout;

    public EquipmentSignalStateStore(ActorRef spout, ForeignDevice deviceInfo) {
        getProbe().addWindowLog(MISFIRE_DEVICE);
        getProbe().addWindowLog(MISFIRE_SIGNAL);
        getProbe().addWindowLog(REAL_DATA);
        getProbe().setHasBypassFunctionality(true);
        this.spout = spout;
        this.state.setEquipmentId(deviceInfo.getEquipmentId());
        getProbe().newParameter("MinInterval","MinInterval", ActorParameterDataType.INTEGER, false, 2000);
    }


    public static Props props(ActorRef spout, ForeignDevice deviceInfo) {
        return Props.create(EquipmentSignalStateStore.class, () -> new EquipmentSignalStateStore(spout, deviceInfo));
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(EquipmentRealSignal.class, this::saveState)
                .match(GetEquipmentRealSignalAction.class, this::queryState)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        this.state.reset();
    }


    private void saveState(EquipmentRealSignal deviceSignal1) {
        EquipmentRealSignal deviceSignal = (EquipmentRealSignal) getProbe().bypass(deviceSignal1);
        if (deviceSignal == null) return;

        Integer saveInterval = getProbe().getParameter("MinInterval").getIntValue();

        HashMap<Integer, RealSignal> signalMap = this.state.getCache();
        //不直接在循环里面remove
        List<RealSignal> filterSignal = new ArrayList<>();
        for (RealSignal signal : deviceSignal.getRealSignalList()) {

            if (signalMap.containsKey(signal.getSignalId())) {
                RealSignal oldSignal = signalMap.get(signal.getSignalId());
                //如果有超过1s的新数据，则更新（down sample，降低无效写次数）
                Duration duration = Duration.between(oldSignal.getTimestamp(), signal.getTimestamp());
                if (duration.toMillis() > saveInterval) {
                    signalMap.put(signal.getSignalId(), signal);
                } else {
                    getProbe().enqueueWindowLogItem(MISFIRE_SIGNAL, signal);
                    filterSignal.add(signal);
                }
            } else {
                this.state.getCache().put(signal.getSignalId(),signal);
//                this.state.getMetricInstrument().enqueueWindowLogItem(MISFIRE_SIGNAL, signal);
            }
        }

        //tell spout to send change of device signals to watchers
        deviceSignal.getRealSignalList().removeAll(filterSignal);

        if (!deviceSignal.getRealSignalList().isEmpty()) {
            getProbe().enqueueWindowLogItem(REAL_DATA, deviceSignal);
            spout.tell(deviceSignal, self());
        }
    }

    private void queryState(GetEquipmentRealSignalAction action){
        EquipmentRealSignal equipmentRealSignal = new EquipmentRealSignal();
        equipmentRealSignal.setEquipmentId(this.state.getEquipmentId());
        equipmentRealSignal.setRealSignalList(Lists.newArrayList(this.state.getCache().values()));
        getSender().tell(equipmentRealSignal,getSelf());
    }

}

