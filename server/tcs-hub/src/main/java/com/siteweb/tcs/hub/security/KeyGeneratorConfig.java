package com.siteweb.tcs.hub.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

@Configuration
public class KeyGeneratorConfig {

    @Value("${webAuth.jwt.privateKey}")
    private String privateKey; //Encoded private key string

    @Value("${webAuth.jwt.publicKey}")
    private String publicKey;//Encoded public key string


    @Bean
    public KeyPair generateKeyPair() throws NoSuchAlgorithmException, InvalidKeySpecException {
        return new KeyPair(generatePublicKey(), generatePrivateKey());
    }

    private PrivateKey generatePrivateKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory kf = KeyFactory.getInstance("RSA");
        PKCS8EncodedKeySpec privateKeySpecPKCS8 = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey));
        return kf.generatePrivate(privateKeySpecPKCS8);
    }


    private PublicKey generatePublicKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory kf = KeyFactory.getInstance("RSA");
        X509EncodedKeySpec pubKeySpecX509EncodedKeySpec = new X509EncodedKeySpec(Base64.getDecoder().decode(publicKey));
        return kf.generatePublic(pubKeySpecX509EncodedKeySpec);
    }
}
