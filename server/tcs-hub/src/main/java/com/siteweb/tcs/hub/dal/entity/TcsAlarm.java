package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler;
import lombok.Data;
import java.io.Serializable;

/**
 * 告警表
 */
@Data
@TableName("tcs_alarm")
public class TcsAlarm implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("DeviceId")
    private Long deviceId;

    @TableField("SouthAlarmId")
    private String southAlarmId;

    @TableField("SouthAlarmName")
    private String southAlarmName;

    @TableField(value = "SouthAlarmMeaning")
    private String southAlarmMeaning;

    @TableField("RelatedSignalId")
    private Long relatedSignalId;

    @TableField(value = "Metadata", typeHandler = EnhancedJacksonTypeHandler.class )
    private JsonNode metadata;

    @TableLogic(value = "false", delval = "true")
    @TableField("Deleted")
    private Boolean deleted;
} 