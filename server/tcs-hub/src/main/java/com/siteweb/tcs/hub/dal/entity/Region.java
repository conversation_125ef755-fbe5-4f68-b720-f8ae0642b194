package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName("tcs_regions")
public class Region {
    @TableId(value = "regionId", type = IdType.AUTO)
    private Long regionId;
    @TableField("parentId")
    private Long parentId;
    @TableField("regionName")
    private String regionName;
    @TableField("description")
    private String description;
    @TableField("displayIndex")
    private Integer displayIndex;
    @TableField("resourceStructureId")
    private Integer resourceStructureId;
    @TableField(exist = false)
    private Integer parentResourceStructureId;
}