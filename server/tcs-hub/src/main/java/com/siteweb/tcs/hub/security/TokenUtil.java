package com.siteweb.tcs.hub.security;


import com.siteweb.tcs.common.util.DateUtil;
import com.siteweb.tcs.common.util.StringUtils;
import com.siteweb.tcs.hub.dal.dto.AccountDTO;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.security.KeyPair;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Service
@Slf4j
public class TokenUtil {

    /** 保存token的有效时间：key=token的唯一uuid； value=该token的失败时间(毫秒值)，-1表示永不过期 */
    private final ConcurrentHashMap<String, Long> tokenExpireCache = new ConcurrentHashMap<>();

    @Autowired
    private AuthConfig authConfig;

    @Autowired
    private KeyPair keyPair;

    public Map<Integer, Authentication> verifyToken(HttpServletRequest request, boolean refreshExpireTime) {
        Map<Integer, Authentication> hashMap = new HashMap<>();
        final String token = request.getHeader(authConfig.getTokenHeader());
        if (token != null && !token.isEmpty()) {
            Claims claims = parseFromToken(token.replace("Bearer", "").trim());
            final TokenUser user = getUserFromToken(claims);
            if (user == null) {
                hashMap.put(-1, null);
            } else {
                String ukey = ClaimsUtil.getUkey(claims);
                if(StringUtils.isBlank(ukey)) {
                    log.error("ukey is blank. userId={}, loginId={}", user.getUser().getUserId(), user.getUser().getLoginId());
                    hashMap.put(-2, null);
                } else {
                    Long expireMillSeconds = tokenExpireCache.get(ukey);
                    long currentTimeMillis = System.currentTimeMillis();
                    if(expireMillSeconds != null && (expireMillSeconds == -1 || expireMillSeconds > currentTimeMillis)) {//未过期
                        hashMap.put(1, new UserAuthentication(user));
                        if(refreshExpireTime) {//向后顺延token的有效期
                            Long tokenExpirationMillSeconds = getTokenExpirationMillSeconds();
                            tokenExpireCache.put(ukey, tokenExpirationMillSeconds);
                            logTokenTest("refresh", ukey, tokenExpirationMillSeconds);
                        }
                    } else {//已过期，从缓存中删除
                        tokenExpireCache.remove(ukey);
                        hashMap.put(-3, null);
                        logTokenTest("expire remove", ukey, null);
                    }
                }
            }
            return hashMap;
        }
        hashMap.put(0, null);
        return hashMap;
    }

    public void clearToken(HttpServletRequest request) {
        final String token = request.getHeader(authConfig.getTokenHeader());
        if (StringUtils.isNotBlank(token)) {
            Claims claims = parseFromToken(token.replace("Bearer", "").trim());
            String uKey = ClaimsUtil.getUkey(claims);
            if(StringUtils.isNotBlank(uKey)) {
                tokenExpireCache.remove(uKey);
                logTokenTest("logout remove", uKey, null);
            }
        }
    }

    private Claims parseFromToken(String token) {
        return Jwts.parser()
                .verifyWith(keyPair.getPublic())
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    /** Get User Info from the Token */
    public TokenUser getUserFromToken(Claims claims) {
        AccountDTO user = new AccountDTO();
        user.setUserId(ClaimsUtil.getUserId(claims));
        user.setUserName(ClaimsUtil.getUserName(claims));
        user.setLoginId(ClaimsUtil.getLoginId(claims));
        user.setRoleIds(ClaimsUtil.getRoles(claims));
        user.setLoginType(ClaimsUtil.getLoginType(claims));
        return new TokenUser(user);
    }

    public String createTokenForUser(TokenUser tokenUser, String loginType) {
        return createTokenForUser(tokenUser.getUser(), loginType);
    }

    private String createTokenForUser(AccountDTO user, String loginType) {
        String uuId = StringUtils.getUUId();
        JwtBuilder jwtBuilder = Jwts.builder()
                .subject(user.getUserName()) //TODO: 如果将客户端ip添加进jwt中，是否可以用来限制jwt只能在请求生成此jwt的客户端机器上使用
                .claim("generateTime", System.currentTimeMillis())
                .claim("userId", user.getUserId())
                .claim("loginId", user.getLoginId())
                .claim("role", user.getRoleIds())
                .claim("loginType", loginType)
                .claim("ukey", uuId)
                .signWith(keyPair.getPrivate());
        //获取超时的时刻毫秒值
        Long tokenExpirationTime = getTokenExpirationMillSeconds();
        tokenExpireCache.put(uuId, tokenExpirationTime);
        String token = jwtBuilder.compact();
        logTokenTest("create", uuId, tokenExpirationTime);
        return token;
    }

    private void logTokenTest(String optTypeDesc, String uuId, Long tokenExpirationTime) {//TODO: 测试用，上线前删除
        //String timeStr = tokenExpirationTime == null ? "NULL" : DateUtil.dateToString(new Date(tokenExpirationTime));
        //log.info("token {} :(uuId = {} , tokenExpirationTime = {} )", optTypeDesc, uuId, timeStr);
    }

    public Long getTokenExpirationMillSeconds() {
        Integer expireMinute = authConfig.getExpireMinute();
        if(expireMinute <= 0) {//永远不过期
            return -1L;
        } else {
            Date expirtDate = DateUtil.dateAddMinutes(new Date(), expireMinute);
            return expirtDate.getTime();
        }
    }

    /**
     * 每天凌晨清空过期token缓存
     */
    @Scheduled(cron = "0 13 3 * * ?")
    public void clearCache() {
        try {
            if(!tokenExpireCache.isEmpty()) {
                long currentTimeMillis = System.currentTimeMillis();
                List<String> needDelKeys = tokenExpireCache.entrySet().stream().filter(e -> e.getValue() != null && e.getValue() > 0 && e.getValue() <= currentTimeMillis)
                        .map(Map.Entry::getKey).toList();
                if(!needDelKeys.isEmpty()) {
                    for (String key : needDelKeys) {
                        tokenExpireCache.remove(key);
                    }
                    log.info("clear expire token cache, count={}", needDelKeys.size());
                }
            }
        } catch (Exception ex) {
            log.error("clear expire token cache throw exception: ", ex);
        }
    }
}
