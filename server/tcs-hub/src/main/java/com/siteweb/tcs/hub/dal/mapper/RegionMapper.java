package com.siteweb.tcs.hub.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.hub.dal.entity.Region;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * RegionMapper接口
 */
public interface RegionMapper extends BaseMapper<Region> {

    /**
     * 查询所有区域
     *
     * @return 区域列表
     */
    @Select("SELECT * FROM tcs_regions")
    List<Region> selectAll();



    @Select("SELECT * FROM tcs_regions WHERE regionId = #{regionId}")
    Region  selectByRegionId(Integer regionId);
}