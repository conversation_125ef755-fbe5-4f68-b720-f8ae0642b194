package com.siteweb.tcs.hub.domain.letter;

import lombok.Data;

import java.time.LocalDateTime;


@Data
public class ControlCommandResponse {
    private Integer monitorUnitId;
    private Integer equipmentId;
    private Integer controlId;
    private Integer stationId;
    private String sequenceNo;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer resultCode;
    private String resultDesc;





}
