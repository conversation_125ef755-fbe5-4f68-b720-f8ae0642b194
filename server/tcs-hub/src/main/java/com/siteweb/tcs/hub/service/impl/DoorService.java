//package com.siteweb.tcs.hub.service.impl;
//
//
//import com.siteweb.tcs.hub.dal.dto.EquipmentDTO;
//import com.siteweb.tcs.hub.domain.letter.ForeignDoorDeviceConfigChange;
//import com.siteweb.tcs.siteweb.entity.Door;
//import com.siteweb.tcs.siteweb.provider.DoorProvider;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
///**
// * @ClassName: DoorService
// * @descriptions: 门禁设备业务处理器
// * @author: xsx
// * @date: 2024/9/21 15:40
// **/
//@Slf4j
//@Service
//public class DoorService {
//    @Autowired
//    private DoorProvider doorProvider;
//
//    public Door createDoor(EquipmentDTO equipmentDTO, ForeignDoorDeviceConfigChange foreignDoorDeviceConfigChange){
//        Door tblDoor = foreignDoorDeviceConfigChange.toTblDoor(equipmentDTO.getStationId(), equipmentDTO.getSamplerUnitId(), equipmentDTO.getEquipmentId());
//        try {
//            Door res = doorProvider.addDoor(tblDoor);
//            return res;
//        }catch (Exception e){
//            log.error("[DEVICE LIFE CYCLE MANAGER]调用配置工具创建门设备，执行失败，设备信息是{}，外部门设备信息是{}",equipmentDTO,foreignDoorDeviceConfigChange);
//            return null;
//        }
//
//    }
//
//    public Door updateDoor(Integer equipmentId,ForeignDoorDeviceConfigChange foreignDoorDeviceConfigChange){
//        Door tblDoor = new Door();
//        tblDoor.setEquipmentId(equipmentId);
//        tblDoor.setDoorNo(foreignDoorDeviceConfigChange.getDoorNo());
//        tblDoor.setDoorName(foreignDoorDeviceConfigChange.getDeviceName());
//        try {
//            Door res = doorProvider.updateDoor(tblDoor);
//            return res;
//        }catch (Exception e){
//            log.error("[DEVICE LIFE CYCLE MANAGER]调用配置工具创建门设备，执行失败，设备Id是{}，外部门设备信息是{}",equipmentId,foreignDoorDeviceConfigChange);
//            return null;
//        }
//    }
//}
