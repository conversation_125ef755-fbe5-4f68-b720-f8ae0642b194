package com.siteweb.tcs.hub.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.hub.dal.entity.ForeignControl;
import com.siteweb.tcs.hub.dal.mapper.ForeignControlMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class ForeignControlService extends ServiceImpl<ForeignControlMapper, ForeignControl> implements IService<ForeignControl> {

    @Autowired
    private ForeignControlMapper foreignControlMapper;

    public List<ForeignControl> selectForeignControlByEquipmentId(Integer equipmentId) {
        if(ObjectUtil.isEmpty(equipmentId)) return Collections.emptyList();
        QueryWrapper<ForeignControl> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("EquipmentId",equipmentId);
        List<ForeignControl> foreignControlList = foreignControlMapper.selectList(queryWrapper);
        return foreignControlList;
    }

    public boolean batchDeleteForeignControl(List<ForeignControl> foreignControlList) {
        if(CollectionUtil.isEmpty(foreignControlList)) return false;
        return batchDeleteForeignControl(foreignControlList.get(0).getEquipmentId(),foreignControlList.stream().map(ForeignControl::getForeignControlId).toList());
    }

    public boolean batchDeleteForeignControl(Integer equipmentId,List<String> foreignControlIdList) {
        if(CollectionUtil.isEmpty(foreignControlIdList)) return false;
        QueryWrapper<ForeignControl> queryWrapper = new QueryWrapper<>();
        StringBuilder sql = new StringBuilder("(EquipmentId,ForeignControlId) in (");
        foreignControlIdList.forEach(c -> {
            sql.append("(").append(equipmentId).append(",").append(c).append("),");
        });
        sql.deleteCharAt(sql.length()-1).append(")");
        sql.append(")");
        queryWrapper.apply(sql.toString());
        return foreignControlMapper.delete(queryWrapper)>0;
    }

    public boolean deleteByEquipmentId(Integer equipmentId) {
        if(ObjectUtil.isEmpty(equipmentId)) return false;
        QueryWrapper<ForeignControl> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("EquipmentId",equipmentId);
        return foreignControlMapper.delete(queryWrapper)>0;
    }

    public boolean deleteByEquipmentIdList(List<Integer> equipmentIdList) {
        QueryWrapper<ForeignControl> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("EquipmentId",equipmentIdList);
        return foreignControlMapper.delete(queryWrapper)>0;
    }
}
