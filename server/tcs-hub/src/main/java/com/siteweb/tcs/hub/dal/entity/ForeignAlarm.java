package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@TableName(value = "tcs_foreign_alarm")
@Accessors(chain = true)
public class ForeignAlarm  implements Serializable {
    @TableField(exist = false)
    private String foreignGatewayID;
    @TableField(exist = false)
    private String foreignDeviceID;
    @TableField(value = "ForeignAlarmId")
    private String foreignAlarmID;
    @TableField(exist = false)
    private int monitorUnitId;
    @TableField(value = "EquipmentId")
    private int equipmentId;
    @TableField(value = "EventId")
    private int eventId;
    @TableField(value = "EventConditionId")
    private int eventConditionId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ForeignAlarm that = (ForeignAlarm) o;
        return equipmentId == that.equipmentId && Objects.equals(foreignAlarmID, that.foreignAlarmID);
    }

    @Override
    public int hashCode() {
        return Objects.hash(foreignAlarmID, equipmentId);
    }
}
