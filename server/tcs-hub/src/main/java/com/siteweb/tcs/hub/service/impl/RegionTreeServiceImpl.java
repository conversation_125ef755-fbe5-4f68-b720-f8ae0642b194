package com.siteweb.tcs.hub.service.impl;


import com.siteweb.tcs.hub.dal.dto.RegionDTO;
import com.siteweb.tcs.hub.dal.entity.RegionItem;
import com.siteweb.tcs.hub.service.RegionItemService;
import com.siteweb.tcs.hub.service.RegionService;
import com.siteweb.tcs.hub.service.RegionTreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class RegionTreeServiceImpl implements RegionTreeService {


    @Autowired
    private RegionItemService regionItemService;

    @Autowired
    private RegionService regionService;


    @Override
    public List<RegionDTO> findTree() {
        List<RegionDTO> roots = new ArrayList<>();
        Map<Long, RegionDTO> idmap = new HashMap<>();
        //
        List<RegionDTO> dtoList = regionService.findAll().stream()
                .map(RegionDTO::from)
                .peek(e -> idmap.put(e.getRegionId(), e)).toList();
        //
        for (RegionDTO dto : dtoList) {
            if (dto.getParentId() == null) {
                roots.add(dto);
            } else {
                RegionDTO parent = idmap.get(dto.getParentId());
                if (parent != null) parent.getChildren().add(dto);
            }
        }
        return roots;
    }

    @Override
    public List<RegionDTO> findItemTree(String pluginId) {
        // 获取与插件相关的区域项
        List<RegionItem> relatedItems = null;
        if ("*".equals(pluginId)) {
            relatedItems = regionItemService.getAllRegionItems();
        } else {
            relatedItems = regionItemService.getRegionItemsByPluginId(pluginId);
        }
        // 按区域ID分组区域项
        Map<Long, List<RegionItem>> regionItemsMap = relatedItems.stream()
                .collect(Collectors.groupingBy(RegionItem::getRegionId));

        // 创建ID到RegionDTO的映射和结果根列表
        Map<Long, RegionDTO> idToRegionMap = new HashMap<>();
        List<RegionDTO> rootRegions = new ArrayList<>();

        // 获取所有区域，转换为RegionDTO并过滤相关的区域
        regionService.findAll().stream()
                .map(RegionDTO::from)
//                .filter(regionDTO -> regionItemsMap.containsKey(regionDTO.getRegionId()))
                .forEach(regionDTO -> {
                    // 设置区域的项目列表
                    regionDTO.setItems(regionItemsMap.get(regionDTO.getRegionId()));
                    // 将RegionDTO添加到映射中
                    idToRegionMap.put(regionDTO.getRegionId(), regionDTO);

                    // 如果没有父ID，则为根区域，否则添加到其父区域的子区域列表中
                    if (regionDTO.getParentId() == null) {
                        rootRegions.add(regionDTO);
                    } else {
                        RegionDTO parentRegion = idToRegionMap.get(regionDTO.getParentId());
                        if (parentRegion != null) {
                            parentRegion.getChildren().add(regionDTO);
                        }
                    }
                });

        return rootRegions;
    }

    /**
     * 获取指定区域的子区域ID列表,不包含自己
     *
     * @param regionId 区域ID
     * @return 子区域ID列表
     */
    @Override
    public List<Long> findSubRegionIds(Long regionId) {
        List<RegionDTO> roots = findTree();
        return findAllSubRegionIds(regionId, roots);
    }
    private List<Long> findAllSubRegionIds(Long regionId, List<RegionDTO> roots) {
        List<Long> result = new ArrayList<>();
        for (RegionDTO root : roots) {
            if (root.getRegionId().equals(regionId)) {
                collectSubRegionIds(root, result);
                break;
            } else if (!root.getChildren().isEmpty()) {
                result.addAll(findAllSubRegionIds(regionId, root.getChildren()));
            }
        }
        return result;
    }
    private void collectSubRegionIds(RegionDTO node, List<Long> result) {
        for (RegionDTO child : node.getChildren()) {
            result.add(child.getRegionId());
            collectSubRegionIds(child, result);
        }
    }


}