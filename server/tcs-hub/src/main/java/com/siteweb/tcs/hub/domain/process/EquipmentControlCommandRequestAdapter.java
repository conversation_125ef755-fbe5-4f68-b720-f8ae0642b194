package com.siteweb.tcs.hub.domain.process;

import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentControlCommandRequest;
import com.siteweb.tcs.hub.domain.letter.ForeignControlCommandRequest;
import com.siteweb.tcs.hub.domain.letter.ForeignDeviceControlCommandRequest;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.HashMap;
import java.util.stream.Collectors;

@Slf4j
public class EquipmentControlCommandRequestAdapter extends ProbeActor {


    private final ActorRef requestSpout;
    private ForeignDevice foreignDevice;
    private final HashMap<Integer,String> controlMap = new HashMap<>();

    public EquipmentControlCommandRequestAdapter(ForeignDevice foreignDevice, ActorRef requestSpout) {
        this.requestSpout = requestSpout;
        this.foreignDevice = foreignDevice;
        this.foreignDevice.getForeignControlList().forEach(control -> {
            controlMap.put(control.getControlId(), control.getForeignControlId());
        });
        getProbe().addRateCalculator("controlCommandRequestRateIn",60);
    }

    // Props 工厂方法
    public static Props props(ForeignDevice foreignDevice, ActorRef requestSpout) {
        return Props.create(EquipmentControlCommandRequestAdapter.class, foreignDevice, requestSpout);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(EquipmentControlCommandRequest.class, this::onEquipmentControlCommandRequest)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.foreignDevice =(ForeignDevice) needUpdateAction.getConfig();
            this.controlMap.clear();
            this.foreignDevice.getForeignControlList().forEach(control -> {
                this.controlMap.put(control.getControlId(), control.getForeignControlId());
            });
        }
    }

    @Override
    public void postStop() throws Exception {
        super.postStop();
    }

    private void onEquipmentControlCommandRequest(EquipmentControlCommandRequest equipmentControlCommandRequest) {
        ForeignDeviceControlCommandRequest foreignDeviceControlCommandRequest = new ForeignDeviceControlCommandRequest();
        foreignDeviceControlCommandRequest.setForeignGatewayId(this.foreignDevice.getForeignGatewayID());
        foreignDeviceControlCommandRequest.setForeignDeviceId(this.foreignDevice.getForeignDeviceID());
        foreignDeviceControlCommandRequest.setCategory(equipmentControlCommandRequest.getCategory());
        foreignDeviceControlCommandRequest.setCommandRequestList(equipmentControlCommandRequest.getCommandRequestList().stream()
                .map(commandRequest -> {
                    ForeignControlCommandRequest foreignControlCommandRequest = new ForeignControlCommandRequest();
                    foreignControlCommandRequest.setForeignGatewayId(this.foreignDevice.getForeignGatewayID());
                    foreignControlCommandRequest.setForeignDeviceId(this.foreignDevice.getForeignDeviceID());
                    foreignControlCommandRequest.setForeignControlId(controlMap.get(commandRequest.getControlId()));
                    foreignControlCommandRequest.setControlType(commandRequest.getControlType());
                    foreignControlCommandRequest.setControlCategory(commandRequest.getControlCategory());
                    foreignControlCommandRequest.setParameter(commandRequest.getParameter());
                    foreignControlCommandRequest.setStartTime(commandRequest.getStartTime());
                    foreignControlCommandRequest.setSequenceNo(commandRequest.getSequenceNo());
                    return foreignControlCommandRequest;
                })
                .collect(Collectors.toList()));
        log.trace("发送至 RequestSpout：{}" ,foreignDeviceControlCommandRequest.getForeignGatewayId());
        requestSpout.tell(foreignDeviceControlCommandRequest, getSelf());
        getProbe().updateRateSource("controlCommandRequestRateIn",equipmentControlCommandRequest.getCommandRequestList().size());
    }

}

