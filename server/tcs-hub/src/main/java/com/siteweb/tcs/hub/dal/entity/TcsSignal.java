package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.siteweb.tcs.common.util.EnhancedJacksonTypeHandler;
import lombok.Data;
import java.io.Serializable;

/**
 * 信号表
 */
@Data
@TableName("tcs_signal")
public class TcsSignal implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("DeviceId")
    private Long deviceId;

    @TableField("SouthSignalId")
    private String southSignalId;

    @TableField("SouthSignalName")
    private String southSignalName;

    @TableField(value ="SouthSignalMeanings", typeHandler = EnhancedJacksonTypeHandler.class)
    private JsonNode southSignalMeanings;

    @TableField("SouthSignalUnit")
    private String southSignalUnit;

    @TableField("SignalType")
    private Integer signalType;

    @TableField(value = "Metadata", typeHandler = EnhancedJacksonTypeHandler.class)
    private JsonNode metadata;

    @TableLogic(value = "false", delval = "true")
    @TableField("Deleted")
    private Boolean deleted;
} 