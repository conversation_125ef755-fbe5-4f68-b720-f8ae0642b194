//package com.siteweb.tcs.hub.service.impl;
//
//import cn.hutool.core.util.ObjectUtil;
//import com.siteweb.tcs.hub.dal.dto.EquipmentDTO;
//import com.siteweb.tcs.hub.dal.entity.Region;
//import com.siteweb.tcs.hub.domain.letter.ForeignDeviceConfigChange;
//import com.siteweb.tcs.hub.service.RegionService;
//import com.siteweb.tcs.siteweb.dto.CreateEquipmentDto;
//import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;
//import com.siteweb.tcs.siteweb.entity.Equipment;
//import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
//import com.siteweb.tcs.siteweb.entity.Port;
//import com.siteweb.tcs.siteweb.entity.SamplerUnit;
//import com.siteweb.tcs.siteweb.provider.EquipmentProvider;
//import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.HashMap;
//import java.util.Map;
//
//@Service
//public class EquipmentService {
//
//    @Autowired
//    private EquipmentProvider equipmentProvider;
//
//    @Autowired
//    private PlatformDefaultIdService platformDefaultIdService;
//
//    @Autowired
//    private EquipmentTemplateService equipmentTemplateService;
//
//    @Autowired
//    private RegionService regionService;
//
//    @Autowired
//    private SamplerUnitService samplerUnitService;
//
//    @Autowired
//    private PortService portService;
//
//    private Map<Integer,Integer> categoryIdTemplateIdMap = new HashMap<>();
//
//    private static final Logger log = LoggerFactory.getLogger(EquipmentService.class);
//
//    public EquipmentDTO createEquipment(Integer monitorUnitId, ForeignDeviceConfigChange deviceConfigChange){
//        //判断，如果有带设备种类id，根据设备种类找对应的设备模板id，如果没找到就创建，在根据这个模板id去创建设备
//        //判断，如果没有带设备种类id，默认按照B接口设备模板创建对应设备
//        CreateEquipmentDto createEquipmentDto = null;
//        Integer resourceStructureId = null;
//        if(ObjectUtil.isEmpty(deviceConfigChange.getRegionId())){
//            resourceStructureId = platformDefaultIdService.getTCSDefaultResourceStructureId();
//        }else{
//            Region region = regionService.findByRegionId(deviceConfigChange.getRegionId());
//            resourceStructureId = region.getResourceStructureId();
//        }
//        //新建端口
//        Port tslPort = portService.createPort(monitorUnitId);
//        if(ObjectUtil.isEmpty(tslPort)){
//            log.error("[DEVICE LIFE CYCLE MANAGER]调用配置工具创建采集单元端口，执行失败，采集单元id是{}",monitorUnitId);
//            return null;
//        }
//        SamplerUnit samplerUnit = samplerUnitService.createDefaultSamplerUnit(monitorUnitId, deviceConfigChange.getDeviceName(), tslPort.getPortId());
//        if(ObjectUtil.isEmpty(samplerUnit)){
//            log.error("[DEVICE LIFE CYCLE MANAGER]调用配置工具创建采集单元，执行失败，采集单元id是{}，采集单元名称是{}，采集端口是{}",monitorUnitId,deviceConfigChange.getDeviceName(),tslPort.getPortId());
//            return null;
//        }
//        //新建采集单元
//        if(ObjectUtil.isNotEmpty(deviceConfigChange.getEquipmentCategory())){
//            if(categoryIdTemplateIdMap.containsKey(deviceConfigChange.getEquipmentCategory())){
//                createEquipmentDto = deviceConfigChange.toCreateEquipmentDto(monitorUnitId,resourceStructureId,categoryIdTemplateIdMap.get(deviceConfigChange.getEquipmentCategory()),samplerUnit.getSamplerUnitId());
//            }else{
//                //查配置工具有没有，有加进缓存，没有创建加缓存
//                EquipmentTemplateVO equipmentTemplateVO = new EquipmentTemplateVO();
//                equipmentTemplateVO.setEquipmentCategory(deviceConfigChange.getEquipmentCategory());
//                equipmentTemplateVO.setExtendField1(ForeignDeviceConfigChange.equipmentTemplateFlag);
//                equipmentTemplateVO.setParentTemplateId(0);
//                EquipmentTemplate tblEquipmentTemplate = equipmentTemplateService.queryEquipmentTemplate(equipmentTemplateVO);
//                if(ObjectUtil.isEmpty(tblEquipmentTemplate)){
//                    //创建模板
//                    EquipmentTemplateVO query = deviceConfigChange.toEquipmentTemplateVO();
//                    EquipmentTemplate equipmentTemplate = equipmentTemplateService.createEquipmentTemplate(query);
//                    if(ObjectUtil.isEmpty(equipmentTemplate)){
//                        log.error("[DEVICE LIFE CYCLE MANAGER]调用配置工具创建设备模板，执行失败，请求参数是{}",query);
//                        return null;
//                    }
//                    //添加缓存
//                    categoryIdTemplateIdMap.put(equipmentTemplate.getEquipmentCategory(),equipmentTemplate.getEquipmentTemplateId());
//                    createEquipmentDto = deviceConfigChange.toCreateEquipmentDto(monitorUnitId,resourceStructureId,equipmentTemplate.getEquipmentTemplateId(),samplerUnit.getSamplerUnitId());
//
//                }else {
//                    categoryIdTemplateIdMap.put(equipmentTemplateVO.getEquipmentCategory(),tblEquipmentTemplate.getEquipmentTemplateId());
//                    createEquipmentDto = deviceConfigChange.toCreateEquipmentDto(monitorUnitId,resourceStructureId,tblEquipmentTemplate.getEquipmentTemplateId(),samplerUnit.getSamplerUnitId());
//                }
//            }
//        }else{
//            createEquipmentDto = deviceConfigChange.toCreateEquipmentDto(monitorUnitId,resourceStructureId,platformDefaultIdService.getBInterfaceDeviceTemplateRootId(),samplerUnit.getSamplerUnitId());
//        }
//
//        Equipment equipment;
//        try{
//            equipment = equipmentProvider.createEquipment(createEquipmentDto);
//        }catch (Exception ex){
//            log.error("[DEVICE LIFE CYCLE MANAGER] CREATE DEVICE: -> error occurs during call config server, the error reason is {}, the call params is {}",ex.getCause(),createEquipmentDto);
//            return null;
//        }
//
//        // 将tcs-siteweb的Equipment转换为EquipmentDTO
//        EquipmentDTO equipmentDTO = convertToEquipmentDTO(equipment);
//        return equipmentDTO;
//    }
//
//    public boolean deleteEquipment(Integer equipmentId){
//        if(ObjectUtil.isEmpty(equipmentId)) return true;
//        return equipmentProvider.deleteEquipment(equipmentId);
//    }
//
//    public EquipmentDTO updateEquipment(EquipmentDetailDTO equipmentDetailDTO){
//        if(ObjectUtil.isEmpty(equipmentDetailDTO)) return null;
//        EquipmentDTO equipmentDTO = new EquipmentDTO();
//        try {
//            // 转换为tcs-siteweb的EquipmentDetailDTO
//            Equipment equipment = equipmentProvider.updateEquipment(equipmentDetailDTO);
//            // 将tcs-siteweb的Equipment转换为EquipmentDTO
//            if (equipment != null) {
//                equipmentDTO = convertToEquipmentDTO(equipment);
//            }
//        }catch (Exception ex){
//            log.error("[DEVICE LIFE CYCLE MANAGER] UPDATE DEVICE: -> error occurs during call config server, the error reason is {}, the call equipment template id is {}, and the update device info is {}",ex.getCause(),equipmentDetailDTO);
//        }
//        return equipmentDTO;
//    }
//
//    public EquipmentDTO getEquipment(Integer equipmentId){
//        if(ObjectUtil.isEmpty(equipmentId))return null;
//        Equipment equipment = equipmentProvider.getConfig(equipmentId);
//        return convertToEquipmentDTO(equipment);
//    }
//
//
//    /**
//     * 将siteweb的Equipment转换为EquipmentDTO
//     */
//    private EquipmentDTO convertToEquipmentDTO(Equipment source) {
//        if (source == null) return null;
//        EquipmentDTO target = new EquipmentDTO();
//        BeanUtils.copyProperties(source, target);
//        return target;
//    }
//}
