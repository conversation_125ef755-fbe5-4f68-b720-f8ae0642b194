//package com.siteweb.tcs.hub.domain.letter;
//
//import com.siteweb.tcs.common.util.StringUtils;
//import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
//import lombok.Data;
//import com.siteweb.tcs.siteweb.dto.CreateEquipmentDto;
//import com.siteweb.tcs.siteweb.dto.EquipmentDetailDTO;
//import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;
//
//import java.io.Serializable;
//import java.util.List;
//
//@Data
//public class ForeignDeviceConfigChange implements Serializable {
//
//    public static final String equipmentTemplateFlag = "TCS";
//    /**
//     * 外部设备id
//     */
//    private String foreignDeviceId;
//    /**
//     * 外部网关id
//     */
//    private String foreignGatewayId;
//    /**
//     * 设备名称
//     */
//    private String deviceName;
//    /**
//     * 设备种类id，非必需，为S6库中的设备种类id
//     */
//    private Integer equipmentCategory;
//    /**
//     * 设备种类名称，传南向设备的设备种类名称，非必需，主要用于创建设备模板时使用的模板名称
//     */
//    private String equipmentCategoryName;
//
//    private LifeCycleEventType eventType;
//
//    //区域id
//    private Integer regionId;
//
//    private List<ForeignSignalConfigChange> foreignSignalConfigChangeList;
//
//    private List<ForeignAlarmConfigChange> foreignAlarmConfigChangeList;
//
//    private List<ForeignControlConfigChange> foreignControlConfigChangeList;
//
//    public EquipmentTemplateVO toEquipmentTemplateVO(){
//        EquipmentTemplateVO equipmentTemplateVO = new EquipmentTemplateVO();
//        String templateName = StringUtils.isEmpty(equipmentCategoryName)?"TCS-Equipment-Template":equipmentCategoryName;
//        equipmentTemplateVO.setEquipmentTemplateName(templateName);
//        equipmentTemplateVO.setEquipmentCategory(equipmentCategory);
//        equipmentTemplateVO.setParentTemplateId(0);
//        equipmentTemplateVO.setMemo("TCS Pre-made Equipment Template");
//        //BInterface-HOST设备6-00，用于寻找采集器
//        equipmentTemplateVO.setProtocolCode("BInterface-HOST设备6-00");
//        equipmentTemplateVO.setExtendField1(equipmentTemplateFlag);
//        equipmentTemplateVO.setDescription("TCS Pre-made Equipment Template");
//        //1是监控设备额
//        equipmentTemplateVO.setEquipmentType(1);
//        return equipmentTemplateVO;
//    }
//
//
//    public CreateEquipmentDto toCreateEquipmentDto(Integer monitorUnitId,Integer resourceStructureRootId,Integer bInterfaceDeviceTemplateRootId,Integer samplerUnitId) {
//        CreateEquipmentDto createEquipmentDto = new CreateEquipmentDto();
//        createEquipmentDto.setMonitorUnitId(monitorUnitId);
//        createEquipmentDto.setEquipmentName(deviceName);
//        createEquipmentDto.setResourceStructureId(resourceStructureRootId);
//        createEquipmentDto.setEquipmentTemplateId(bInterfaceDeviceTemplateRootId);
//        createEquipmentDto.setSamplerUnitId(samplerUnitId);
//        //全实例化
//        createEquipmentDto.setInstantiated(true);
//        return createEquipmentDto;
//    }
//
//    public EquipmentDetailDTO toEquipmentDetailDTO(Integer monitorUnitId,Integer equipmentId){
//        EquipmentDetailDTO equipmentDetailDTO = new EquipmentDetailDTO();
//        equipmentDetailDTO.setEquipmentName(deviceName);
//        equipmentDetailDTO.setEquipmentId(equipmentId);
//        equipmentDetailDTO.setMonitorUnitId(monitorUnitId);
//        return equipmentDetailDTO;
//    }
//}
