package com.siteweb.tcs.hub.domain.v2.letter;

import com.siteweb.tcs.hub.domain.letter.DynamicValue;
import com.siteweb.tcs.hub.domain.letter.enums.ControlTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: tcs2
 * @description: 控制请求变化消息
 * @author: xsx
 * @create: 2025-07-12 10:52
 **/
@Data
public class ControlCommandRequestChange {
    private Long gatewayId;
    private Long deviceId;
    private Long controlId;
    private ControlTypeEnum controlType;
    private Integer controlCategory;
    private DynamicValue parameter;
    private LocalDateTime startTime;
    private String sequenceNo;
}
