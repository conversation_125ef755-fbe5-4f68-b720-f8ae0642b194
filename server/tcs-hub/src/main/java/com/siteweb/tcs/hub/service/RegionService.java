package com.siteweb.tcs.hub.service;

import com.siteweb.tcs.hub.dal.entity.Region;

import java.util.List;

/**
 * <AUTHOR> (2024-06-05)
 **/
public interface RegionService {
    List<Region> findAll();

    Region findByRegionId(Integer regionId);

    Region findByRsId(Integer rsId);

    boolean create(Region region);

    boolean update(Region region);

    boolean deleteBy(Integer regionId);

//    List<List<Region>> syncByS6();
}
