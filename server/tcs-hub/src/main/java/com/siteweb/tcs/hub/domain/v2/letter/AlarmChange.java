package com.siteweb.tcs.hub.domain.v2.letter;

import com.siteweb.tcs.common.o11y.WindowLogItem;
//import com.siteweb.tcs.siteweb.entity.EventResponseItem;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @program: tcs2
 * @description: 告警变化消息
 * @author: xsx
 * @create: 2025-07-12 10:51
 **/
@Data
@Deprecated
public class AlarmChange implements WindowLogItem {
    private Long gatewayId;
    private Long deviceId;
    private Long alarmId;
    private String alarmMeaningId = "0";
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String triggerValue;
    private Integer alarmLevel;
    private String alarmFlag;
    private String alarmDescription;
    //相当与S6中的SequenceId
    private String serialNo;
    private String meanings;

    @Override
    public String getWindowLogString() {
        return "AlarmChange{" +
                "gatewayId=" + gatewayId +
                ", deviceId=" + deviceId +
                ", alarmId=" + alarmId +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", triggerValue='" + triggerValue + '\'' +
                ", alarmLevel=" + alarmLevel +
                ", alarmDescription='" + alarmDescription + '\'' +
                ", serialNo='" + serialNo + '\'' +
                '}';
    }

    public String getUniqueId() {
        return String.format("%d_%d_%d_%d", gatewayId, deviceId, alarmId);
    }
//
//    public EventResponseItem toEventResponseItem(Integer stationId, Integer equipmentId, Integer eventId, Integer eventCondId){
//        EventResponseItem eventResponseItem = new EventResponseItem();
//        eventResponseItem.setEventId(eventId)
//                .setEquipmentId(equipmentId)
//                .setEventValue(Float.valueOf(triggerValue))
//                .setEventConditionId(eventCondId)
//                .setSequenceId(equipmentId + serialNo)
//                .setStartTime(startTime)
//                .setEndTime(endTime)
//                .setMeanings(meanings)
//                //翻转
//                .setOverturn(0)
//                .setStationId(stationId);
//        return eventResponseItem;
//    }
}
