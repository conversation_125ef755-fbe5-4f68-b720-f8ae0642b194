package com.siteweb.tcs.hub.domain.letter;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ForeignControlCommandResponse {
    private String foreignGatewayId;
    private String foreignDeviceId;
    private String foreignControlId;
    private String sequenceNo;
//    private String commandSequenceId;
//    private String parameter;
//    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer resultCode;
    private String resultDesc;

}
