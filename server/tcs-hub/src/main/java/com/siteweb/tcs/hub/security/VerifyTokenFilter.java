package com.siteweb.tcs.hub.security;

import com.siteweb.tcs.hub.security.util.ResponseUtil;
import io.jsonwebtoken.JwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.GenericFilterBean;

import java.io.IOException;
import java.util.Map;

@Slf4j
public class VerifyTokenFilter extends GenericFilterBean {

    private final TokenUtil tokenUtil;

    public VerifyTokenFilter(TokenUtil tokenUtil) {
        this.tokenUtil = tokenUtil;
    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain filterChain) throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) res;
        try {
            String path = request.getServletPath();//使用request取到请求的路径
            if(StringUtils.isNotBlank(path) && "/api/thing/logout".equals(path)) {
                tokenUtil.clearToken(request);
                SecurityContextHolder.getContext().setAuthentication(null);
                setResponse(response, HttpServletResponse.SC_OK, 0, "OK");
                return;
            }
            Map<Integer, Authentication> hashMap = tokenUtil.verifyToken(request, true);
            for (Map.Entry<Integer, Authentication> entry : hashMap.entrySet()) {
                if (entry.getKey().equals(1)) {
                    SecurityContextHolder.getContext().setAuthentication(entry.getValue());
                } else if (entry.getKey() < 0) {
                    setResponse(response, HttpServletResponse.SC_UNAUTHORIZED, -1, "Invalid token");
                    return;
                }
                filterChain.doFilter(req, res);
            }
        } catch (JwtException e) {
            SecurityContextHolder.getContext().setAuthentication(null);
            setResponse(response, HttpServletResponse.SC_UNAUTHORIZED, -2, "parse token exception");
            log.error("JwtException : ", e);
        }
    }

    /**
     * 设置HTTP响应
     *
     * @param res HTTP响应对象
     * @param statusCode HTTP状态码
     * @param code 业务状态码
     * @param message 响应消息
     * @throws IOException IO异常
     */
    private void setResponse(HttpServletResponse res, int statusCode, int code, String message) throws IOException {
        ResponseUtil.setJsonResponse(res, statusCode, code, message);
    }
}
