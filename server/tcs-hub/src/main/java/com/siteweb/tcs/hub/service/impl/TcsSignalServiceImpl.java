package com.siteweb.tcs.hub.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.common.util.LocaleMessageSourceUtil;
import com.siteweb.tcs.hub.dal.dto.ConfigChangeResult;
import com.siteweb.tcs.hub.dal.dto.SignalConfigChangeDto;
import com.siteweb.tcs.hub.dal.entity.TcsSignal;
import com.siteweb.tcs.hub.dal.mapper.TcsSignalMapper;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ThingType;
import com.siteweb.tcs.hub.service.ITcsSignalService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 信号表服务实现类
 */
@Service
public class TcsSignalServiceImpl extends ServiceImpl<TcsSignalMapper, TcsSignal> implements ITcsSignalService {

    @Resource
    private TcsSignalMapper tcsSignalMapper;

    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;

    @Override
    public List<TcsSignal> listByDeviceId(Long deviceId) {
        return tcsSignalMapper.selectByDeviceId(deviceId);
    }

    @Override
    public TcsSignal getBySouthSignalId(Long deviceId, String southSignalId) {
        return tcsSignalMapper.selectBySouthSignalId(deviceId, southSignalId);
    }

    @Override
    public List<TcsSignal> listBySignalType(Integer signalType) {
        return tcsSignalMapper.selectBySignalType(signalType);
    }

    @Override
    public List<TcsSignal> listByDeviceIdAndSignalType(Long deviceId, Integer signalType) {
        return tcsSignalMapper.selectByDeviceIdAndSignalType(deviceId, signalType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSignal(TcsSignal signal) {
        return this.saveOrUpdate(signal);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConfigChangeResult handleSignalConfigChange(SignalConfigChangeDto configDto) {
        try {
            // 参数验证
            if (configDto == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.data.empty"),
                        ThingType.SIGNAL);
            }

            if (configDto.getLifeCycleEvent() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.lifecycle.empty"),
                        ThingType.SIGNAL);
            }

            // 根据生命周期事件类型处理
            switch (configDto.getLifeCycleEvent()) {
                case CREATE:
                    return handleSignalCreate(configDto);
                case UPDATE:
                    return handleSignalUpdate(configDto);
                case DELETE:
                    return handleSignalDelete(configDto);
                default:
                    return ConfigChangeResult.failure(
                            messageSourceUtil.getMessage("config.lifecycle.unsupported", new Object[]{configDto.getLifeCycleEvent()}),
                            ThingType.SIGNAL);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("config.change.failed"),
                    e.getMessage(), ThingType.SIGNAL);
        }
    }

    /**
     * 处理信号创建
     */
    private ConfigChangeResult handleSignalCreate(SignalConfigChangeDto configDto) {
        try {
            // 检查南向信号ID是否已存在
            if (configDto.getSouthSignalId().equals("092316000")) {
                log.error("");
            }
            if (configDto.getSouthSignalId() != null) {
                TcsSignal existingSignal = getBySouthSignalId(configDto.getDeviceId(),configDto.getSouthSignalId());
                if (existingSignal != null) {
                    return ConfigChangeResult.failure(
                            messageSourceUtil.getMessage("signal.south.id.exists", new Object[]{configDto.getSouthSignalId()}),
                            ThingType.SIGNAL);
                }
            }

            // 创建信号实体
            TcsSignal signal = new TcsSignal();
            BeanUtils.copyProperties(configDto, signal);
            signal.setDeleted(false);
            if (MapUtil.isNotEmpty(configDto.getSouthSignalMeanings())) {
                signal.setSouthSignalMeanings(new ObjectMapper().valueToTree(configDto.getSouthSignalMeanings()));
            }

            // 保存信号
            boolean success = this.save(signal);
            if (success) {
                // 创建返回的ConfigChangeDto，包含southId和id
                SignalConfigChangeDto resultDto = new SignalConfigChangeDto();
                BeanUtils.copyProperties(signal, resultDto);
                resultDto.setLifeCycleEvent(configDto.getLifeCycleEvent());

                return ConfigChangeResult.success(signal.getSouthSignalId(), ThingType.SIGNAL, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("signal.create.failed"),
                        ThingType.SIGNAL);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("signal.create.failed"),
                    e.getMessage(), ThingType.SIGNAL);
        }
    }

    /**
     * 处理信号更新
     */
    private ConfigChangeResult handleSignalUpdate(SignalConfigChangeDto configDto) {
        try {
            if (configDto.getId() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("signal.update.id.required"),
                        ThingType.SIGNAL);
            }

            // 检查信号是否存在
            TcsSignal existingSignal = this.getById(configDto.getId());
            if (existingSignal == null) {
                return ConfigChangeResult.failure(
                        messageSourceUtil.getMessage("signal.not.exists", new Object[]{configDto.getId()}),
                        ThingType.SIGNAL);
            }

            // 检查南向信号ID是否与其他信号冲突
            if (configDto.getSouthSignalId() != null &&
                    !configDto.getSouthSignalId().equals(existingSignal.getSouthSignalId())) {
                TcsSignal conflictSignal = getBySouthSignalId(configDto.getDeviceId(),  configDto.getSouthSignalId());
                if (conflictSignal != null && !conflictSignal.getId().equals(configDto.getId())) {
                    return ConfigChangeResult.failure(
                            messageSourceUtil.getMessage("signal.south.id.conflict", new Object[]{configDto.getSouthSignalId()}),
                            ThingType.SIGNAL);
                }
            }

            // 更新信号实体
            BeanUtils.copyProperties(configDto, existingSignal, "id");

            // 保存更新
            boolean success = this.updateById(existingSignal);
            if (success) {
                // 创建返回的ConfigChangeDto，包含更新后的数据
                SignalConfigChangeDto resultDto = new SignalConfigChangeDto();
                BeanUtils.copyProperties(existingSignal, resultDto);
                resultDto.setLifeCycleEvent(LifeCycleEventEnum.UPDATE);

                return ConfigChangeResult.success(existingSignal.getSouthSignalId(), ThingType.SIGNAL, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("signal.update.failed"),
                        ThingType.SIGNAL);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("signal.update.failed"),
                    e.getMessage(), ThingType.SIGNAL);
        }
    }

    /**
     * 处理信号删除
     */
    private ConfigChangeResult handleSignalDelete(SignalConfigChangeDto configDto) {
        try {
            if (configDto.getId() == null) {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("signal.delete.id.required"),
                        ThingType.SIGNAL);
            }

            // 检查信号是否存在
            TcsSignal existingSignal = this.getById(configDto.getId());
            if (existingSignal == null) {
                return ConfigChangeResult.failure(
                        messageSourceUtil.getMessage("signal.not.exists", new Object[]{configDto.getId()}),
                        ThingType.SIGNAL);
            }

            // 逻辑删除信号
            boolean success = this.removeById(configDto.getId());
            if (success) {
                // 创建返回的ConfigChangeDto，包含要删除的信号ID
                SignalConfigChangeDto resultDto = new SignalConfigChangeDto();
                resultDto.setId(existingSignal.getId());
                resultDto.setSouthSignalId(existingSignal.getSouthSignalId());
                resultDto.setLifeCycleEvent(LifeCycleEventEnum.DELETE);

                return ConfigChangeResult.success(existingSignal.getSouthSignalId(), ThingType.SIGNAL, resultDto);
            } else {
                return ConfigChangeResult.failure(messageSourceUtil.getMessage("signal.delete.failed"),
                        ThingType.SIGNAL);
            }
        } catch (Exception e) {
            return ConfigChangeResult.failure(messageSourceUtil.getMessage("signal.delete.failed"),
                    e.getMessage(), ThingType.SIGNAL);
        }
    }
} 