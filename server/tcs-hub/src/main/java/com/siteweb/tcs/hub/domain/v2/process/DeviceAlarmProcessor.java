package com.siteweb.tcs.hub.domain.v2.process;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.dal.entity.TcsAlarm;
import com.siteweb.tcs.hub.dal.entity.TcsDevice;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceAlarmChange;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-07-11 11:12
 **/

public class DeviceAlarmProcessor extends ProbeActor {
    private TcsDevice device;

    private ActorRef storeActor;

    private Map<Long, TcsAlarm> alarmMap = new HashMap<>();

    private DeviceAlarmProcessor(TcsDevice device,ActorRef storeActor){
        this.device = device;
        this.storeActor = storeActor;
        if(CollectionUtil.isNotEmpty(device.getAlarms())){
            device.getAlarms().forEach(e -> alarmMap.put(e.getId(),e));
        }
    }

    public static Props props(TcsDevice device,ActorRef storeActor){
        return Props.create(DeviceAlarmProcessor.class,device,storeActor);
    }

    @Override
    public Receive createReceive() {
        return ReceiveBuilder.create()
                .match(DeviceAlarmChange.class,this::onDeviceAlarmChange)
                .match(NeedUpdateAction.class, this::onNeedUpdate)
                .build()
                .orElse(super.createReceive());
    }

    // 处理告警
    private void onDeviceAlarmChange(DeviceAlarmChange deviceAlarmChange) {
        /**
         * 1.判断告警id在不在
         * 2.判断时间是不是超前(待讨论)
         */
        deviceAlarmChange.getAlarmChangeList().removeIf(
                change -> !alarmMap.containsKey(change.getAlarmId())
        );
        storeActor.tell(deviceAlarmChange,getSelf());
    }

    private void onNeedUpdate(NeedUpdateAction needUpdateAction) {
        if (needUpdateAction != null && needUpdateAction.getConfig() != null) {
            this.device = (TcsDevice) needUpdateAction.getConfig();
            this.alarmMap.clear();
            if(CollectionUtil.isNotEmpty(device.getAlarms())){
                device.getAlarms().forEach(e -> alarmMap.put(e.getId(),e));
            }
        }
    }
}
