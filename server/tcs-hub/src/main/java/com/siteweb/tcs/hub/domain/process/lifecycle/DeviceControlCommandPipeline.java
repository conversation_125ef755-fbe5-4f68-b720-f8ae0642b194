package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.common.util.ActorPathBuilder;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.letter.EquipmentControlCommandRequest;
import com.siteweb.tcs.hub.domain.letter.EquipmentControlCommandResponse;
import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import com.siteweb.tcs.hub.domain.process.*;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.PoisonPill;
import org.apache.pekko.actor.Props;

/**
 * 设备控制命令管道
 * 负责处理设备控制命令的数据流
 */
public class DeviceControlCommandPipeline extends DataPipeline {
    private final ForeignDevice device;

    private ActorRef requestAdapter;
    private ActorRef responseAdapter;
    private ActorRef requestSpout;
    private ActorRef responseSpout;

    /**
     * 构造函数
     * @param context Actor上下文
     * @param device 外部设备实体
     */
    public DeviceControlCommandPipeline(ActorContext context, ForeignDevice device, ActorRef pipelinePublisher) {
        super(context, pipelinePublisher);
        this.device = device;
    }

    public void sendEquipmentControlCommandRequest(EquipmentControlCommandRequest request) {
        this.requestAdapter.tell(request, getContext().self());
    }

    public void sendEquipmentControlCommandResponse(EquipmentControlCommandResponse response) {
        this.responseAdapter.tell(response, getContext().self());
    }

    @Override
    public void create() {
        // 创建控制response命令发送Actor
        this.responseSpout = getContext().actorOf(
                Props.create(EquipmentControlCommandResponseSpout.class, device, getPipelinePublisher()),
                "EquipmentControlCommandResponseSpout"
        );

        // 创建控制命令request 发送Actor
        this.requestSpout = getContext().actorOf(
                Props.create(DeviceControlCommandRequestSpout.class, device, getPipelinePublisher()),
                "DeviceControlCommandRequestSpout"
        );

        // 创建控制命令request 适配器Actor
        this.requestAdapter = getContext().actorOf(
                Props.create(EquipmentControlCommandRequestAdapter.class, device, requestSpout),
                "EquipmentControlCommandRequestAdapter"
        );

        // 创建控制命令存储Actor
        ActorRef storeActor = getContext().actorOf(
                EquipmentControlCommandStateStore.props(this.requestAdapter, this.requestSpout, device),
                "EquipmentControlCommandStateStore"
        );
        setStoreActor(storeActor);

        // 创建控制命令response adapter
        this.responseAdapter = getContext().actorOf(
                Props.create(DeviceControlCommandResponseAdapter.class, device, storeActor),
                "DeviceControlCommandResponseAdapter"
        );
    }

    @Override
    public void update(NeedUpdateAction action) {
        this.getStoreActor().tell(action, getContext().self());
        this.requestAdapter.tell(action, getContext().self());
        this.responseAdapter.tell(action, getContext().self());
        this.requestSpout.tell(action, getContext().self());
        this.responseSpout.tell(action, getContext().self());
    }

    @Override
    public void destroy() {
        this.responseAdapter.tell(PoisonPill.getInstance(), getContext().self());
        this.requestAdapter.tell(PoisonPill.getInstance(), getContext().self());
        getStoreActor().tell(PoisonPill.getInstance(), getContext().self());
        this.requestSpout.tell(PoisonPill.getInstance(), getContext().self());
        this.responseSpout.tell(PoisonPill.getInstance(), getContext().self());
    }
}

