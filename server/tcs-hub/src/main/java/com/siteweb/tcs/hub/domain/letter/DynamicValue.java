package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.hub.domain.letter.enums.DynamicValueType;
import lombok.Data;

import java.nio.ByteBuffer;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR> (2024-08-09)
 **/
@Data
public class DynamicValue {
    private DynamicValueType valueType = DynamicValueType.Empty;
    private Float floatValue;
    private Integer intValue;
    private String stringValue;


    private Object objectValue;
    private byte[] bytesValue;
    private DoorControlRequestParam doorControlRequestParam;


    private static final DateTimeFormatter dataTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static LocalDateTime parseDateTime(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, dataTimeFormatter);
    }


    public String toString() {
        switch (valueType) {
            case Empty: {
                return "";
            }
            case Float: {
                return floatValue.toString();
            }
            case Integer: {
                return intValue.toString();
            }
            case NTPTime: {
                int result = ByteBuffer.wrap(bytesValue).getInt();
                LocalDateTime epochStart = LocalDateTime.ofEpochSecond(result, 0, ZoneOffset.UTC);
                return epochStart.format(dataTimeFormatter);
            }
            default: {
                if (stringValue != null && !stringValue.isEmpty()) return stringValue;
                if (intValue != null) return intValue.toString();
                if (floatValue != null) return floatValue.toString();
                if (bytesValue != null) return new String(bytesValue);
            }
        }
        return null;
    }


    public static DynamicValue parse(String segment, DynamicValueType valueType) {
        if (segment == null || segment.isEmpty()) return new DynamicValue();
        var index = segment.indexOf(',');
        if (index == -1) return new DynamicValue();
        String type = segment.substring(0, index);
        String value = segment.substring(index + 1);
        if (valueType == null) {
            valueType = DynamicValueType.fromInt(Integer.parseInt(type));
        }
        DynamicValue dynamicValue = new DynamicValue();
        dynamicValue.setValueType(valueType);
        switch (valueType) {
            case Float: {
                dynamicValue.setFloatValue(Float.parseFloat(value));
                break;
            }
            case Integer: {
                dynamicValue.setIntValue(Integer.parseInt(value));
                break;
            }
            case NTPTime: {
                byte[] buffer = new byte[4];
                LocalDateTime epochStart = LocalDateTime.ofEpochSecond(0, 0, ZoneOffset.UTC);
                // 计算从 epoch 到现在的总秒数
                long totalSeconds = ChronoUnit.SECONDS.between(epochStart, parseDateTime(value));
                ByteBuffer
                        .allocate(Long.BYTES)
                        .putLong(totalSeconds).get(buffer, 0, 4);
                dynamicValue.setBytesValue(buffer);
                break;
            }
            default: {
                dynamicValue.setStringValue(value);
                break;
            }
        }
        return dynamicValue;
    }

}
