package com.siteweb.tcs.hub.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.hub.dal.entity.TcsSignal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 信号表 Mapper 接口
 */
@Mapper
@Repository
public interface TcsSignalMapper extends BaseMapper<TcsSignal> {

    /**
     * 根据设备ID查询信号列表
     *
     * @param deviceId 设备ID
     * @return 信号列表
     */
    List<TcsSignal> selectByDeviceId(@Param("deviceId") Long deviceId);

    /**
     * 根据南向信号ID查询信号
     *
     * @param southSignalId 南向信号ID
     * @return 信号信息
     */
    TcsSignal selectBySouthSignalId(@Param("deviceId") Long deviceId, @Param("southSignalId") String southSignalId);

    /**
     * 根据信号类型查询信号列表
     *
     * @param signalType 信号类型
     * @return 信号列表
     */
    List<TcsSignal> selectBySignalType(@Param("signalType") Integer signalType);

    /**
     * 根据设备ID和信号类型查询信号列表
     *
     * @param deviceId   设备ID
     * @param signalType 信号类型
     * @return 信号列表
     */
    List<TcsSignal> selectByDeviceIdAndSignalType(@Param("deviceId") Long deviceId, @Param("signalType") Integer signalType);
} 