package com.siteweb.tcs.hub.domain.letter.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * @ClassName: DoorInAndOutEnum
 * @descriptions: 进出门标志枚举
 * @author: xsx
 * @date: 2024/9/21 15:20
 **/
public enum DoorInAndOutEnum {
    COME_IN(1,"进门"),
    GO_OUT(2,"出门"),
    NOT_MEANING(3,"无标志");
    private final int value;
    DoorInAndOutEnum(int _value, String _desc) {
        this.value = _value;
    }
    @JsonCreator
    public static DoorInAndOutEnum fromInt(int i) {
        for (DoorInAndOutEnum status : DoorInAndOutEnum.values()) {
            if (status.value == i) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with ordinal " + i);
    }
}
