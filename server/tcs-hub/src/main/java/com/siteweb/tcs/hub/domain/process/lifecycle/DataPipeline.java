package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.hub.domain.letter.NeedUpdateAction;
import lombok.Getter;
import lombok.Setter;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.PoisonPill;

@Setter
@Getter
public class DataPipeline {
    private final ActorContext context;
    private ActorRef adapterActor;
    private ActorRef storeActor;
    private ActorRef spoutActor;
    private ActorRef  pipelinePublisher;

    public DataPipeline(ActorContext context, ActorRef pipelinePublisher){
        this.context = context;
        this.pipelinePublisher = pipelinePublisher;
    }

    public void create() {

    }

    public void process(Object message) {
        this.adapterActor.tell(message, this.getContext().self());
    }

    public void update(NeedUpdateAction action) {
        this.adapterActor.tell(action, this.getContext().self());
        this.storeActor.tell(action, this.getContext().self());
        this.spoutActor.tell(action, this.getContext().self());
    }

    public void destroy() {
        adapterActor.tell(PoisonPill.getInstance(), ActorRef.noSender());
        storeActor.tell(PoisonPill.getInstance(), ActorRef.noSender());
        spoutActor.tell(PoisonPill.getInstance(), ActorRef.noSender());
    }
}

