# Configuration Change Messages
config.data.empty=éç½®æ°æ®ä¸è½ä¸ºç©º
config.lifecycle.empty=çå½å¨æäºä»¶ç±»åä¸è½ä¸ºç©º
config.lifecycle.unsupported=ä¸æ¯æççå½å¨æäºä»¶ç±»å: {0}
config.change.failed=éç½®åæ´å¤±è´¥

# Gateway Messages
gateway.south.id.exists=ååç½å³IDå·²å­å¨: {0}
gateway.create.failed=ç½å³åå»ºå¤±è´¥
gateway.update.id.required=æ´æ°æä½å¿é¡»æä¾ç½å³ID
gateway.not.exists=ç½å³ä¸å­å¨: {0}
gateway.south.id.conflict=ååç½å³IDå·²è¢«å¶ä»ç½å³ä½¿ç¨: {0}
gateway.update.failed=ç½å³æ´æ°å¤±è´¥
gateway.delete.id.required=å é¤æä½å¿é¡»æä¾ç½å³ID
gateway.delete.failed=ç½å³å é¤å¤±è´¥

# Device Messages
device.south.id.exists=ååè®¾å¤IDå·²å­å¨: {0}
device.create.failed=è®¾å¤åå»ºå¤±è´¥
device.update.id.required=æ´æ°æä½å¿é¡»æä¾è®¾å¤ID
device.not.exists=è®¾å¤ä¸å­å¨: {0}
device.south.id.conflict=ååè®¾å¤IDå·²è¢«å¶ä»è®¾å¤ä½¿ç¨: {0}
device.update.failed=è®¾å¤æ´æ°å¤±è´¥
device.delete.id.required=å é¤æä½å¿é¡»æä¾è®¾å¤ID
device.delete.failed=è®¾å¤å é¤å¤±è´¥
device.signal.change.failed=ä¿¡å·éç½®åæ´å¤±è´¥: {0}
device.alarm.change.failed=åè­¦éç½®åæ´å¤±è´¥: {0}
device.control.change.failed=æ§å¶éç½®åæ´å¤±è´¥: {0}

# Signal Messages
signal.south.id.exists=ååä¿¡å·IDå·²å­å¨: {0}
signal.create.failed=ä¿¡å·åå»ºå¤±è´¥
signal.update.id.required=æ´æ°æä½å¿é¡»æä¾ä¿¡å·ID
signal.not.exists=ä¿¡å·ä¸å­å¨: {0}
signal.south.id.conflict=ååä¿¡å·IDå·²è¢«å¶ä»ä¿¡å·ä½¿ç¨: {0}
signal.update.failed=ä¿¡å·æ´æ°å¤±è´¥
signal.delete.id.required=å é¤æä½å¿é¡»æä¾ä¿¡å·ID
signal.delete.failed=ä¿¡å·å é¤å¤±è´¥

# Alarm Messages
alarm.south.id.exists=åååè­¦IDå·²å­å¨: {0}
alarm.create.failed=åè­¦åå»ºå¤±è´¥
alarm.update.id.required=æ´æ°æä½å¿é¡»æä¾åè­¦ID
alarm.not.exists=åè­¦ä¸å­å¨: {0}
alarm.south.id.conflict=åååè­¦IDå·²è¢«å¶ä»åè­¦ä½¿ç¨: {0}
alarm.update.failed=åè­¦æ´æ°å¤±è´¥
alarm.delete.id.required=å é¤æä½å¿é¡»æä¾åè­¦ID
alarm.delete.failed=åè­¦å é¤å¤±è´¥

# Control Messages
control.south.id.exists=ååæ§å¶IDå·²å­å¨: {0}
control.create.failed=æ§å¶åå»ºå¤±è´¥
control.update.id.required=æ´æ°æä½å¿é¡»æä¾æ§å¶ID
control.not.exists=æ§å¶ä¸å­å¨: {0}
control.south.id.conflict=ååæ§å¶IDå·²è¢«å¶ä»æ§å¶ä½¿ç¨: {0}
control.update.failed=æ§å¶æ´æ°å¤±è´¥
control.delete.id.required=å é¤æä½å¿é¡»æä¾æ§å¶ID
control.delete.failed=æ§å¶å é¤å¤±è´¥ 