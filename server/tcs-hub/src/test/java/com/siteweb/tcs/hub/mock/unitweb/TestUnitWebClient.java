package com.siteweb.tcs.hub.mock.unitweb;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.mock.UnitWebCmdEnum;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @program: tcs2
 * @description: String消息测试客户端
 * @author: xsx
 * @create: 2025-06-19 10:48
 **/

public class TestUnitWebClient {
    public static void main(String[] args) {
        UnitWebClient unitWebClient = new UnitWebClient("*************", 6790);
        UnitWebCmdEnum type = UnitWebCmdEnum.cmd_get_ctrllist;
        String message = "{\"code\":64,\"equipid\":\"29000185\"}";
        CompletableFuture<String> response = unitWebClient.sendRequest(message);
        response.whenComplete((res, ex) -> {
            if (ObjectUtil.isEmpty(ex)) {
                switch (type){
                    case cmd_get_sigdata :
                        parseSignal(res);
                        break;
                    case cmd_get_eventlist:
                        parseEventData(res);
                        break;
                    case cmd_get_ctrllist:
                        parseCtrlData(res);
                        break;
                    case cmd_send_control:
                        break;
                }
//                System.out.println(res);
            } else {
                System.err.println("发生错误: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
    }

    private static void parseCtrlData(String res) {
        String[] lines = res.split("\\r?\\n");
        List<ControlData> result = new ArrayList<>();

        for (String line : lines) {
            String[] parts = line.split("`");
            Map<String, String> kvMap = Arrays.stream(parts)
                    .map(p -> p.split("=", 2))
                    .filter(kv -> kv.length == 2)
                    .collect(Collectors.toMap(kv -> kv[0], kv -> kv[1]));

            try {
                ControlData data = new ControlData(
                        Long.parseLong(kvMap.getOrDefault("CtrlId", "0")),
                        kvMap.getOrDefault("Name", ""),
                        kvMap.getOrDefault("BaseTypeId", ""),
                        Long.parseLong(kvMap.getOrDefault("SigId", "0")),
                        Double.parseDouble(kvMap.getOrDefault("FloatValue", "0")),
                        kvMap.getOrDefault("StrValue", ""),
                        Double.parseDouble(kvMap.getOrDefault("MaxValue", "0")),
                        Double.parseDouble(kvMap.getOrDefault("MinValue", "0")),
                        Integer.parseInt(kvMap.getOrDefault("Enable", "0")),
                        Integer.parseInt(kvMap.getOrDefault("Visible", "0")),
                        Integer.parseInt(kvMap.getOrDefault("CmdType", "0")),
                        Integer.parseInt(kvMap.getOrDefault("CtrlType", "0")),
                        Integer.parseInt(kvMap.getOrDefault("DataType", "0")),
                        kvMap.getOrDefault("Meanings", "")
                );
                result.add(data);
            } catch (Exception e) {
                System.err.println("解析失败行：" + line);
            }
        }

        // 打印或后续处理
        result.forEach(System.out::println);
    }

    private static void parseEventData(String res) {
        String[] lines = res.split("\\r?\\n");
        List<EventData> result = new ArrayList<>();

        for (String line : lines) {
            String[] parts = line.split("`");

            Map<String, String> kvMap = Arrays.stream(parts)
                    .map(p -> p.split("=", 2))
                    .filter(kv -> kv.length == 2)
                    .collect(Collectors.toMap(kv -> kv[0], kv -> kv[1]));

            try {
                EventData event = new EventData(
                        Long.parseLong(kvMap.getOrDefault("EventId", "0")),
                        kvMap.getOrDefault("Name", ""),
                        Double.parseDouble(kvMap.getOrDefault("FloatValue", "0")),
                        kvMap.getOrDefault("ValueType", ""),
                        kvMap.getOrDefault("Value", ""),
                        Integer.parseInt(kvMap.getOrDefault("CondId", "0")),
                        kvMap.getOrDefault("Meaning", ""),
                        Integer.parseInt(kvMap.getOrDefault("Level", "0")),
                        kvMap.getOrDefault("StartOper", ""),
                        kvMap.getOrDefault("StartValue", ""),
                        Integer.parseInt(kvMap.getOrDefault("StartDelay", "0")),
                        kvMap.getOrDefault("EndOper", ""),
                        kvMap.getOrDefault("EndValue", ""),
                        Integer.parseInt(kvMap.getOrDefault("EndDelay", "0")),
                        kvMap.getOrDefault("BaseTypeId", ""),
                        Integer.parseInt(kvMap.getOrDefault("State", "0")),
                        Integer.parseInt(kvMap.getOrDefault("TrigValue", "0")),
                        kvMap.getOrDefault("StartTime", ""),
                        kvMap.getOrDefault("EndTime", ""),
                        Integer.parseInt(kvMap.getOrDefault("Valid", "0"))
                );
                result.add(event);
            } catch (Exception e) {
                System.err.println("解析失败行：" + line);
            }
        }

        // 打印结果
        result.forEach(System.out::println);
    }

    private static void parseSignal(String res) {
        String[] lines = res.split("\\r?\\n");

        List<SignalData> result = new ArrayList<>();

        for (String line : lines) {
            String[] parts = line.split("`");
            Map<String, String> kvMap = Arrays.stream(parts)
                    .map(p -> p.split("=", 2))
                    .filter(kv -> kv.length == 2)
                    .collect(Collectors.toMap(kv -> kv[0], kv -> kv[1]));

            try {
                SignalData signal = new SignalData(
                        Long.parseLong(kvMap.getOrDefault("SigId", "0")),
                        Integer.parseInt(kvMap.getOrDefault("ValType", "0")),
                        Double.parseDouble(kvMap.getOrDefault("FloatValue", "0")),
                        kvMap.getOrDefault("StrValue", ""),
                        Long.parseLong(kvMap.getOrDefault("SampleTime", "0")),
                        Integer.parseInt(kvMap.getOrDefault("Level", "0")),
                        Integer.parseInt(kvMap.getOrDefault("Valid", "0"))
                );
                result.add(signal);
            } catch (Exception e) {
                System.err.println("解析失败行：" + line);
            }
        }

        // 打印结果
        result.forEach(System.out::println);
    }
}
