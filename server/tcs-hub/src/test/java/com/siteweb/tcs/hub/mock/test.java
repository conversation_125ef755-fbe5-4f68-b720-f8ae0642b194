package com.siteweb.tcs.hub.mock;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @program: tcs2
 * @description:
 * @author: xsx
 * @create: 2025-06-18 11:16
 **/
@Slf4j
public class test {
    public static String getTcpResponse(String ipAddress) {

        // 配置参数

        final int PORT = 6790;

        final int COMMAND_ID = 202;

        final String REQUEST_MESSAGE = "{\"code\": 24}";

        try (

                // 使用try-with-resources自动关闭资源

                Socket socket = new Socket()

        ) {

            // 设置连接超时时间

            socket.connect(new InetSocketAddress(ipAddress, PORT), 6000);

            // 设置读取数据超时时间

            socket.setSoTimeout(15000);

            try (

                    OutputStream outputStream = socket.getOutputStream();

                    InputStream inputStream = socket.getInputStream()

            ) {

                // 发送请求

                sendRequest(outputStream, COMMAND_ID, REQUEST_MESSAGE);

                // 读取并解析响应

                String response = readResponse(inputStream);

                log.info("Successfully received response from {}:{}", ipAddress, PORT);

                return response;

            }

        } catch (SocketTimeoutException ste) {

            log.info("Connection timed out: {}, IP: {}", ste.getMessage(), ipAddress, ste);

        } catch (SocketException se) {

            log.info("TCP connection failed: {} - {}, IP: {}", se.getMessage(), se.getCause(), ipAddress, se);

        } catch (IOException ioe) {

            log.info("IO error occurred: {}, IP: {}", ioe.getMessage(), ipAddress, ioe);

        } catch (Exception ex) {

            log.info("Unexpected error: {}, IP: {}", ex.getMessage(), ipAddress, ex);

        }

        return "";

    }

    /**

     * 发送TCP请求

     *

     * @param outputStream 输出流

     * @param commandId 命令ID

     * @param message 请求消息内容

     * @throws IOException 如果发送失败

     */

    private static void sendRequest(OutputStream outputStream, int commandId, String message) throws IOException {

        // 1. 准备命令ID (4字节)

        ByteBuffer cmdIdBuffer = ByteBuffer.allocate(4);

        cmdIdBuffer.order(ByteOrder.BIG_ENDIAN);

        cmdIdBuffer.putInt(commandId);

        // 2. 准备消息内容和长度

        byte[] msgBytes = message.getBytes(StandardCharsets.UTF_8);

        ByteBuffer msgLenBuffer = ByteBuffer.allocate(4);

        msgLenBuffer.order(ByteOrder.BIG_ENDIAN);

        msgLenBuffer.putInt(msgBytes.length);

        // 3. 组装完整的TCP请求包 (cmdId + msgLen + msg)

        ByteBuffer requestPacket = ByteBuffer.allocate(8 + msgBytes.length);

        requestPacket.put(cmdIdBuffer.array());   // 命令ID (4字节)

        requestPacket.put(msgLenBuffer.array());  // 消息长度 (4字节)

        requestPacket.put(msgBytes);              // 消息内容

        // 4. 发送数据

        outputStream.write(requestPacket.array());

        outputStream.flush();

    }

    /**
     * 读取TCP响应并解析
     * 使用与UnitWebTcpClient.parseResponse相同的实现逻辑
     *
     * @param inputStream 输入流
     * @return 解析后的响应消息
     * @throws IOException 如果读取失败
     */
    private static String readResponse(InputStream inputStream) throws IOException {
        // 1. 读取头部(8字节): 命令ID(4字节) + 消息长度(4字节)
        byte[] headerBuffer = new byte[8];
        int headerBytesRead = 0;
        int bytesRead;

        // 确保读取完整的头部
        while (headerBytesRead < 8 && (bytesRead = inputStream.read(headerBuffer, headerBytesRead, 8 - headerBytesRead)) > 0) {
            headerBytesRead += bytesRead;
        }

        if (headerBytesRead != 8) {
            throw new IOException("Failed to read complete header: expected 8 bytes, got " + headerBytesRead);
        }

        // 2. 读取消息内容长度
        ByteBuffer headerByteBuffer = ByteBuffer.wrap(headerBuffer);
        headerByteBuffer.order(ByteOrder.BIG_ENDIAN);
        int responseCommandId = headerByteBuffer.getInt();
        int responseLength = headerByteBuffer.getInt();

        // 3. 读取完整的消息内容
        byte[] messageBuffer = new byte[responseLength];
        int messageBytesRead = 0;

        while (messageBytesRead < responseLength &&
                (bytesRead = inputStream.read(messageBuffer, messageBytesRead, responseLength - messageBytesRead)) > 0) {
            messageBytesRead += bytesRead;
        }

        if (messageBytesRead != responseLength) {
            throw new IOException("Failed to read complete message: expected " + responseLength +
                    " bytes, got " + messageBytesRead);
        }

        // 4. 组装完整的响应数据（头部 + 消息体）
        byte[] responseData = new byte[8 + responseLength];
        System.arraycopy(headerBuffer, 0, responseData, 0, 8);
        System.arraycopy(messageBuffer, 0, responseData, 8, responseLength);

        // 5. 使用与UnitWebTcpClient.parseResponse相同的解析逻辑
        return parseResponse(responseData);
    }

    /**
     * 解析响应数据
     * 与UnitWebTcpClient.parseResponse方法实现相同的逻辑
     *
     * @param responseData 完整的响应数据（包含头部和消息体）
     * @return 解析后的响应消息
     */
    private static String parseResponse(byte[] responseData) {
        if (responseData == null || responseData.length < 8) {
            throw new IllegalArgumentException("Response data too short: expected at least 8 bytes.");
        }

        ByteBuffer buffer = ByteBuffer.wrap(responseData);
        buffer.order(ByteOrder.BIG_ENDIAN);

        // 1. 解析头部（命令ID + 消息长度）
        int responseCommandId = buffer.getInt();
        int responseLength = buffer.getInt();

        log.info("Response header: Command ID={}, Length={}", responseCommandId, responseLength);

        // 2. 校验长度
        if (responseLength <= 0 || responseLength > 1024 * 1024) {
            throw new IllegalArgumentException("Invalid response length: " + responseLength);
        }

        // 3. 校验总数据是否足够
        if (responseData.length < 8 + responseLength) {
            throw new IllegalArgumentException("Incomplete response data: expected " + (8 + responseLength) + " bytes, got " + responseData.length);
        }

        // 4. 提取消息体内容
        byte[] messageBuffer = Arrays.copyOfRange(responseData, 8, 8 + responseLength);
        String response = new String(messageBuffer, StandardCharsets.UTF_8);

        // 5. 检查尾部特殊字符并移除（\0\0、\r\n 或其他）
        if (responseLength >= 2) {
            if (response.endsWith("\0\0")) {
                response = response.substring(0, response.length() - 2);
            } else if (response.endsWith("\r\n")) {
                response = response.substring(0, response.length() - 2);
            } else {
                // 默认去掉最后两个字节（如果协议定义就是这样）
                response = new String(messageBuffer, 0, responseLength - 2, StandardCharsets.UTF_8);
            }
        }

        // 输出或写入缓存
        System.out.println("收到响应数据内容: " + response);
        return response;
    }

}
