package com.siteweb.tcs.s6.access.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.s6.access.dal.entity.AlarmMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 告警映射表Mapper接口
 */
@Mapper
@Repository
public interface AlarmMapMapper extends BaseMapper<AlarmMap> {

    /**
     * 根据设备ID查询告警映射
     * @param deviceId 设备ID
     * @return 告警映射列表
     */
    List<AlarmMap> selectByDeviceId(@Param("deviceId") Integer deviceId);

    /**
     * 根据北向设备ID查询告警映射
     * @param northEquipmentId 北向设备ID
     * @return 告警映射列表
     */
    List<AlarmMap> selectByNorthEquipmentId(@Param("northEquipmentId") Integer northEquipmentId);

    /**
     * 根据告警ID查询告警映射
     * @param alarmId 告警ID
     * @return 告警映射列表
     */
    List<AlarmMap> selectByAlarmId(@Param("alarmId") Long alarmId);

    /**
     * 根据北向事件ID查询告警映射
     * @param northEventId 北向事件ID
     * @return 告警映射列表
     */
    List<AlarmMap> selectByNorthEventId(@Param("northEventId") Integer northEventId);


    /**
     * 根据复合主键删除告警映射
     * @param deviceId 设备ID
     * @param northEquipmentId 北向设备ID
     * @param alarmId 告警ID
     * @param northEventId 北向事件ID
     * @param northEventConditionId 北向事件条件ID
     * @return 删除成功数量
     */
    int deleteByCompositeKey(@Param("deviceId") Integer deviceId,
                            @Param("northEquipmentId") Integer northEquipmentId,
                            @Param("alarmId") Long alarmId,
                            @Param("northEventId") Integer northEventId,
                            @Param("northEventConditionId") Integer northEventConditionId);
} 