package com.siteweb.tcs.s6.access.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.siteweb.entity.StationStructure;
import com.siteweb.tcs.siteweb.service.IStationStructureMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 局站结构控制器
 */
@Slf4j
@RestController
@RequestMapping(value = "/siteweb-station-structure")
public class SiteWebStationStructureController {

    @Autowired
    private IStationStructureMapService stationStructureMapService;

    /**
     * 获取局站结构树
     */
    @GetMapping(value = "/tree")
    public ResponseEntity<ResponseResult> getStationStructureTree() {
        try {
            StationStructure tree = stationStructureMapService.tree();
            return ResponseHelper.successful(tree);
        } catch (Exception e) {
            log.error("获取局站结构树失败", e);
            return ResponseHelper.failed("获取局站结构树失败: " + e.getMessage());
        }
    }

    /**
     * 调整二级中心区划层级
     */
    @PostMapping(value = "/change-division-hierarchy")
    public ResponseEntity<ResponseResult> changeDivisionHierarchy(
            @RequestParam("divisionStructureId") Integer divisionStructureId,
            @RequestParam("newParentStructureId") Integer newParentStructureId) {
        try {
            boolean success = stationStructureMapService.changeDivisionHierarchy(divisionStructureId, newParentStructureId);
            if (success) {
                return ResponseHelper.successful("调整层级关系成功");
            } else {
                return ResponseHelper.failed("调整层级关系失败");
            }
        } catch (Exception e) {
            log.error("调整二级中心区划层级失败", e);
            return ResponseHelper.failed("调整层级关系失败: " + e.getMessage());
        }
    }

    /**
     * 调整局站层级（变更所属二级中心区划）
     */
    @PostMapping(value = "/change-station-hierarchy")
    public ResponseEntity<ResponseResult> changeStationHierarchy(
            @RequestParam("stationId") Integer stationId,
            @RequestParam("newDivisionStructureId") Integer newDivisionStructureId) {
        try {
            boolean success = stationStructureMapService.changeStationHierarchy(stationId, newDivisionStructureId);
            if (success) {
                return ResponseHelper.successful("调整层级关系成功");
            } else {
                return ResponseHelper.failed("调整层级关系失败");
            }
        } catch (Exception e) {
            log.error("调整局站层级失败", e);
            return ResponseHelper.failed("调整层级关系失败: " + e.getMessage());
        }
    }
}


