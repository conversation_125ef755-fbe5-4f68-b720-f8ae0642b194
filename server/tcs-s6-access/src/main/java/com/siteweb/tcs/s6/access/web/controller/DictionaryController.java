package com.siteweb.tcs.s6.access.web.controller;

import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.s6.access.util.ResponseUtil;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @program: tcs2
 * @description: 字典控制区
 * @author: xsx
 * @create: 2025-07-22 12:57
 **/
@Slf4j
@RestController
@RequestMapping(value = "/dictionary")
public class DictionaryController {
    @Autowired
    @Qualifier(value = "s6SitewebPersistentService")
    private SitewebPersistentService sitewebPersistentService;
    // 获取设备类型
    @GetMapping(value = "/equipment-type")
    public ResponseEntity<Map<String, Object>> getEquipmentType() {
        try {
            List<DataItem> equipmentTypeList = sitewebPersistentService.getConfigAPI().findByEntryIdForDataItem(DataEntryEnum.EQUIPMENT_CATEGORY.getValue());
            return ResponseUtil.success(equipmentTypeList);
        } catch (Exception e) {
            log.error("查询设备类型", e);
            return ResponseUtil.error("查询失败");
        }
    }
}
