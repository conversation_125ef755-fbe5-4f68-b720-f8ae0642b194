package com.siteweb.tcs.s6.access.connector.process;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.common.o11y.ProbeActor;
import com.siteweb.tcs.hub.domain.letter.enums.SubscriptionTypeEnum;
import com.siteweb.tcs.hub.domain.v2.letter.DeviceSignalChange;
import com.siteweb.tcs.hub.domain.v2.letter.SignalChange;
import com.siteweb.tcs.hub.util.TCSTopicUtil;
import com.siteweb.tcs.s6.access.connector.letter.SetRedisItemAction;
import com.siteweb.tcs.s6.access.dal.entity.GatewayMap;
import com.siteweb.tcs.s6.access.dal.entity.IdMap;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.cluster.pubsub.DistributedPubSub;
import org.apache.pekko.cluster.pubsub.DistributedPubSubMediator;
import org.apache.pekko.japi.pf.ReceiveBuilder;

import java.util.List;
import java.util.Map;

/**
 * @program: tcs2
 * @description: 实时信号变化处理器
 * @author: xsx
 * @create: 2025-08-28 10:00
 **/

public class NorthS6SignalChangeProcessor extends ProbeActor {

    private final ActorRef mediator;

    private ActorRef realTimeRedisSink;

    private IdMap<IdMap> idMap;

    public static Props props(GatewayMap gatewayMap, ActorRef realTimeRedisSink){
        return Props.create(NorthS6SignalChangeProcessor.class,gatewayMap,realTimeRedisSink);
    }


    private NorthS6SignalChangeProcessor(GatewayMap gatewayMap,ActorRef realTimeRedisSink){
        this.mediator = DistributedPubSub.get(getContext().system()).mediator();
        this.realTimeRedisSink = realTimeRedisSink;
        handlerSubscribe(gatewayMap.getGatewayId());
        handleInit(gatewayMap);
    }

    private void handlerSubscribe(Long gatewayGuid) {
        String topicName = TCSTopicUtil.getTopicName(SubscriptionTypeEnum.REAL_DATA, gatewayGuid);
        mediator.tell(new DistributedPubSubMediator.Subscribe(topicName, getSelf()), getSelf());
    }

    private void handleInit(GatewayMap gatewayMap) {
        //初始化数据
        idMap = new IdMap();
        idMap.setHubId(gatewayMap.getGatewayId());
        idMap.setNorthId(gatewayMap.getNorthMonitorUnitId());
        if(CollectionUtil.isNotEmpty(gatewayMap.getDeviceMapList())){
            gatewayMap.getDeviceMapList().forEach(e ->{
                IdMap deviceIdMap = new IdMap();
                deviceIdMap.setHubId(e.getDeviceId());
                deviceIdMap.setNorthId(e.getNorthEquipmentId());
                if(CollectionUtil.isNotEmpty(e.getSignalMapList())){
                    e.getSignalMapList().forEach(s -> {
                        IdMap signalIdMap = new IdMap();
                        signalIdMap.setHubId(s.getSignalId());
                        signalIdMap.setNorthId(s.getNorthSignalId());
                        deviceIdMap.getSubIdMap().put(s.getSignalId(),signalIdMap);
                    });
                }
                idMap.getSubIdMap().put(e.getDeviceId(),deviceIdMap);
            });
        }
    }

    @Override
    public Receive createReceive() {
        Receive receive = ReceiveBuilder.create().match(DeviceSignalChange.class, this::onSignalChange).build();
        return super.createReceive().orElse(receive);
    }

    /**
     * 写实时数据
     * @param deviceSignalChange
     */
    private void onSignalChange(DeviceSignalChange deviceSignalChange) {
        Map<Long, IdMap> deviceIdMap = idMap.getSubIdMap();
        if(!deviceIdMap.keySet().contains(deviceSignalChange.getDeviceId())){
            //不存在
            probe.info("不存在设备："+deviceSignalChange.getDeviceId());
        }
        if(CollectionUtil.isEmpty(deviceSignalChange.getSignalChangeList())){
            probe.info("设备"+deviceSignalChange.getDeviceId()+"实时数据包中为空数据");
        }
        Integer equipmentId = deviceIdMap.get(deviceSignalChange.getDeviceId()).getNorthId();
        List<SignalChange> signalChangeList = deviceSignalChange.getSignalChangeList();
        Map<Long, IdMap> signalIdMap = deviceIdMap.get(deviceSignalChange.getDeviceId()).getSubIdMap();
        SetRedisItemAction setRedisItemAction = new SetRedisItemAction();
        signalChangeList.forEach( s ->{
            if(signalIdMap.containsKey(s.getSignalId())){
                Integer northId = signalIdMap.get(s.getSignalId()).getNorthId();
                String redisKey = s.getRedisKey(equipmentId, northId);
                String redisValue = s.getRedisValue(equipmentId, northId);
                setRedisItemAction.addItem(redisKey,redisValue);
            }
        });
        realTimeRedisSink.tell(setRedisItemAction,getSelf());
    }
}
