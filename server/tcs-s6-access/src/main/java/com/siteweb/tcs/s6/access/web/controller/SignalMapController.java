package com.siteweb.tcs.s6.access.web.controller;

import com.siteweb.tcs.s6.access.dal.entity.SignalMap;
import com.siteweb.tcs.s6.access.util.ResponseUtil;
import com.siteweb.tcs.s6.access.web.service.ISignalMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 信号映射控制器
 */
@Slf4j
@RestController
@RequestMapping(value = "/signal-map")
public class SignalMapController {

    @Autowired
    private ISignalMapService signalMapService;

    /**
     * 根据设备ID查询
     * @param deviceId 设备ID
     * @return 列表
     */
    @GetMapping(value = "/device/{deviceId}")
    public ResponseEntity<Map<String, Object>> getByDeviceId(@PathVariable Long deviceId) {
        try {
            List<SignalMap> signalMaps = signalMapService.getByDeviceId(deviceId);
            return ResponseUtil.success(signalMaps);
        } catch (Exception e) {
            log.error("查询信号映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 创建
     * @param signalMap 信号映射
     * @return 结果
     */
    @PostMapping(value = "/create")
    public ResponseEntity<Map<String, Object>> create(@RequestBody SignalMap signalMap) {
        try {
            signalMap.setDeleted(false);
            boolean success = signalMapService.save(signalMap);
            return success ? ResponseUtil.success("创建成功") : ResponseUtil.error("创建失败");
        } catch (Exception e) {
            log.error("创建信号映射失败", e);
            return ResponseUtil.error("创建失败");
        }
    }

    /**
     * 批量创建
     * @param signalMaps 列表
     * @return 结果
     */
    @PostMapping(value = "/batch-create")
    public ResponseEntity<Map<String, Object>> batchCreate(@RequestBody List<SignalMap> signalMaps) {
        try {
            signalMaps.forEach(sm -> sm.setDeleted(false));
            boolean success = signalMapService.saveBatch(signalMaps);
            return success ? ResponseUtil.success("批量创建成功") : ResponseUtil.error("批量创建失败");
        } catch (Exception e) {
            log.error("批量创建信号映射失败", e);
            return ResponseUtil.error("批量创建失败");
        }
    }
    
    /**
     * 删除
     * @param deviceId 设备ID
     * @param signalId 信号ID
     * @param northSignalId 北向信号ID
     * @return 结果
     */
    @DeleteMapping(value = "/delete")
    public ResponseEntity<Map<String, Object>> delete(
            @RequestParam Long deviceId,
            @RequestParam Long signalId,
            @RequestParam Integer northSignalId) {
        try {
            boolean success = signalMapService.deleteByCompositeKey(deviceId, signalId, northSignalId);
            return success ? ResponseUtil.success("删除成功") : ResponseUtil.error("删除失败");
        } catch (Exception e) {
            log.error("删除信号映射失败", e);
            return ResponseUtil.error("删除失败");
        }
    }
} 