package com.siteweb.tcs.s6.access.web.controller;

import com.siteweb.tcs.s6.access.dal.entity.DeviceMap;
import com.siteweb.tcs.s6.access.util.ResponseUtil;
import com.siteweb.tcs.s6.access.web.service.IDeviceMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 设备映射控制器
 */
@Slf4j
@RestController
@RequestMapping(value = "/device-map")
public class DeviceMapController {

    @Autowired
    private IDeviceMapService deviceMapService;

    /**
     * 根据网关ID查询
     * @param gatewayId 网关ID
     * @return 列表
     */
    @GetMapping(value = "/gateway/{gatewayId}")
    public ResponseEntity<Map<String, Object>> getByGatewayId(@PathVariable Long gatewayId) {
        try {
            List<DeviceMap> deviceMaps = deviceMapService.getByGatewayId(gatewayId);
            return ResponseUtil.success(deviceMaps);
        } catch (Exception e) {
            log.error("查询设备映射失败", e);
            return ResponseUtil.error("查询失败");
        }
    }

    /**
     * 创建
     * @param deviceMap 设备映射
     * @return 结果
     */
    @PostMapping(value = "/create")
    public ResponseEntity<Map<String, Object>> create(@RequestBody DeviceMap deviceMap) {
        try {
            deviceMap.setDeleted(false);
            boolean success = deviceMapService.save(deviceMap);
            return success ? ResponseUtil.success("创建成功") : ResponseUtil.error("创建失败");
        } catch (Exception e) {
            log.error("创建设备映射失败", e);
            return ResponseUtil.error("创建失败");
        }
    }

    /**
     * 批量创建
     * @param deviceMaps 列表
     * @return 结果
     */
    @PostMapping(value = "/batch-create")
    public ResponseEntity<Map<String, Object>> batchCreate(@RequestBody List<DeviceMap> deviceMaps) {
        try {
            deviceMaps.forEach(dm -> dm.setDeleted(false));
            boolean success = deviceMapService.saveBatch(deviceMaps);
            return success ? ResponseUtil.success("批量创建成功") : ResponseUtil.error("批量创建失败");
        } catch (Exception e) {
            log.error("批量创建设备映射失败", e);
            return ResponseUtil.error("批量创建失败");
        }
    }

    /**
     * 删除
     * @param gatewayId 网关ID
     * @param deviceId 设备ID
     * @param northEquipmentId 北向设备ID
     * @return 结果
     */
    @DeleteMapping(value = "/delete")
    public ResponseEntity<Map<String, Object>> delete(
            @RequestParam Long gatewayId,
            @RequestParam Long deviceId,
            @RequestParam Integer northEquipmentId) {
        try {
            boolean success = deviceMapService.deleteByCompositeKey(gatewayId, deviceId, northEquipmentId);
            return success ? ResponseUtil.success("删除成功") : ResponseUtil.error("删除失败");
        } catch (Exception e) {
            log.error("删除设备映射失败", e);
            return ResponseUtil.error("删除失败");
        }
    }
} 