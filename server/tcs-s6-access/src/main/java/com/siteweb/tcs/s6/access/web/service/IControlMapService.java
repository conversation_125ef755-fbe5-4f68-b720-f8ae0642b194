package com.siteweb.tcs.s6.access.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.s6.access.dal.entity.ControlMap;

import java.util.List;

/**
 * 控制映射服务接口
 */
public interface IControlMapService extends IService<ControlMap> {

    /**
     * 根据设备ID查询控制映射
     * @param deviceId 设备ID
     * @return 控制映射列表
     */
    List<ControlMap> getByDeviceId(Long deviceId);

    /**
     * 批量保存控制映射
     * @param controlMaps 控制映射列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<ControlMap> controlMaps);
    
    /**
     * 根据复合主键删除
     * @param deviceId 设备ID
     * @param controlId 控制ID
     * @param northControlId 北向控制ID
     * @return boolean
     */
    boolean deleteByCompositeKey(Long deviceId, Long controlId, Integer northControlId);

    boolean deleteByDeviceId(Long deviceId);

    ControlMap getByControlId(Long controlId);

    boolean deleteByControlId(Long controlId);
}