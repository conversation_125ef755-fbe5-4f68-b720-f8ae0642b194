package com.siteweb.tcs.s6.access.dal.entity;

import com.siteweb.tcs.s6.access.connector.ConnectorDataHolder;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @ClassName: MonitorUnitRoute
 * @descriptions: 采集单元路由实体
 * @author: xsx
 * @date: 4/14/2025 10:00 AM
 **/
@Data
@AllArgsConstructor
public class MonitorUnitRoute {
    private static final String redisKey = "MuRoute:%s";

    private Integer monitorUnitId;

    private static final String redisValueFormat = "\"%s.%s\"";

    public String getRedisKey(){
        return String.format(redisKey,monitorUnitId);
    }

    public String getRedisValue(){
        String workStationId = ConnectorDataHolder.getInstance().getTcsWsId();
        return String.format(redisValueFormat,monitorUnitId,workStationId);
    }
}

