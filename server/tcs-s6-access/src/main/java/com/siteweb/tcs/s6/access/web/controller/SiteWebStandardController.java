package com.siteweb.tcs.s6.access.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @program: tcs2
 * @description: SiteWeb标准化控制器
 * @author: xsx
 * @create: 2025-08-15 16:43
 **/
@Slf4j
@RestController
@RequestMapping(value = "/siteweb-standard")
public class SiteWebStandardController {

    @Autowired
    @Qualifier(value = "s6SitewebPersistentService")
    private SitewebPersistentService sitewebPersistentService;

    @GetMapping(value = "/equipment-category")
    public ResponseEntity<ResponseResult> getSiteWebEquipmentCategory(){
        List<DataItem> byEntryIdForDataItem = sitewebPersistentService.getConfigAPI().findByEntryIdForDataItem(DataEntryEnum.EQUIPMENT_CATEGORY.getValue());
        return ResponseHelper.successful(byEntryIdForDataItem);
    }

    @GetMapping(value = "/station-category")
    public ResponseEntity<ResponseResult> getSiteWebStationCategory(){
        List<DataItem> byEntryIdForDataItem = sitewebPersistentService.getConfigAPI().findByEntryIdForDataItem(DataEntryEnum.STATION_CATEGORY.getValue());
        return ResponseHelper.successful(byEntryIdForDataItem);
    }
}
