package com.siteweb.tcs.s6.access.web.controller;

import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.dto.StructureTreeNodeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * @program: tcs2
 * @description: SiteWeb资源结构控制器
 * @author: xsx
 * @create: 2025-09-06 17:36
 **/
@Slf4j
@RestController
@RequestMapping(value = "/siteweb-resource-structure")
public class SiteWebResourceStructureController {


    @Autowired
    @Qualifier(value = "s6SitewebPersistentService")
    private SitewebPersistentService sitewebPersistentService;

    /**
     * 获取资源结构树
     * @param resourceStructureId 资源结构ID，默认为根节点
     * @param eqs 是否包含设备列表，默认不包含
     * @param maxDepth 递归深度，默认为最大深度
     * @return 资源结构树
     */
    @GetMapping(value = "/tree")
    public ResponseEntity<ResponseResult> getResourceStructureTree(
            @RequestParam(value = "resourceStructureId", required = false) Integer resourceStructureId,
            @RequestParam(value = "eqs", required = false,defaultValue = "false") Boolean eqs,
            @RequestParam(value = "maxDepth", required = false) Integer maxDepth) {
        try {
            StructureTreeNodeDTO tree = sitewebPersistentService.getConfigAPI().enumTreeBranch(resourceStructureId, eqs, maxDepth);
            return ResponseHelper.successful(tree);
        } catch (Exception e) {
            log.error("获取资源结构树失败", e);
            return ResponseHelper.failed("获取资源结构树失败: " + e.getMessage());
        }
    }

    /**
     * 调整资源结构层级关系
     * @param resourceStructureId 要移动的资源结构ID
     * @param newParentId 新的父级资源结构ID
     * @return 调整结果
     */
    @PostMapping(value = "/change-hierarchy")
    public ResponseEntity<ResponseResult> changeResourceStructureHierarchy(
            @RequestParam("resourceStructureId") Integer resourceStructureId,
            @RequestParam("newParentId") Integer newParentId) {
        try {
            boolean success = sitewebPersistentService.getConfigAPI().changeResourceStructureHierarchy(resourceStructureId, newParentId);
            if (success) {
                return ResponseHelper.successful("调整层级关系成功");
            } else {
                return ResponseHelper.failed("调整层级关系失败");
            }
        } catch (Exception e) {
            log.error("调整资源结构层级关系失败", e);
            return ResponseHelper.failed("调整层级关系失败: " + e.getMessage());
        }
    }
}
