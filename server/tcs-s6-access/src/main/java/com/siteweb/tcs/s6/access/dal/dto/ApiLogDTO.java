package com.siteweb.tcs.s6.access.dal.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * API日志数据传输对象
 */
@Data
public class ApiLogDTO {
    
    /**
     * 主键ID
     */
    private Long id;

    /**
     * API路径
     */
    private String apiPath;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 响应状态码
     */
    private Integer responseStatus;

    /**
     * 执行时间(毫秒)
     */
    private Long executionTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 