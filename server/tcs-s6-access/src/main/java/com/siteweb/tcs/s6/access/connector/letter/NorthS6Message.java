package com.siteweb.tcs.s6.access.connector.letter;

import com.siteweb.tcs.common.sharding.IGatewayShardingMessage;
import org.apache.pekko.actor.ActorRef;

/**
 * 北向S6接口消息基类
 */
public abstract class NorthS6Message implements IGatewayShardingMessage{

    private Long gatewayGuid;

    private ActorRef sender;

    public NorthS6Message(Long gatewayGuid,ActorRef sender){
        this.gatewayGuid = gatewayGuid;
        this.sender = sender;
    }

    @Override
    public String getGatewayId() {
        return String.valueOf(gatewayGuid);
    }

    @Override
    public ActorRef getSender() {
        return sender;
    }
}