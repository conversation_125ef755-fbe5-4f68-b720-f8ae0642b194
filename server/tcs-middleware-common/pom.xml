<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.siteweb</groupId>
        <artifactId>thing-connect-server</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>tcs-middleware-common</artifactId>
    <name>tcs-middleware-common</name>
    <description>Common interfaces for TCS Middleware platform</description>

    <properties>
        <spring-kafka.version>3.1.1</spring-kafka.version>
    </properties>

    <dependencies>
        <!-- 依赖tcs-common模块 -->
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-common</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <!-- 注意：tcs-common已包含以下依赖，无需重复引入 -->
        <!-- - lombok -->
        <!-- - spring-boot-starter -->
        <!-- - slf4j -->

        <!-- Kafka -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <version>${spring-kafka.version}</version>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Minio客户端 -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.5.7</version>
        </dependency>

        <!-- Zookeeper客户端 -->
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>5.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>5.1.0</version>
        </dependency>

        <!-- Consul客户端 -->
        <dependency>
            <groupId>com.ecwid.consul</groupId>
            <artifactId>consul-api</artifactId>
            <version>1.4.5</version>
        </dependency>

        <!-- InfluxDB客户端 -->
        <dependency>
            <groupId>com.influxdb</groupId>
            <artifactId>influxdb-client-java</artifactId>
            <version>6.10.0</version>
        </dependency>
        <!-- InfluxDB 1.x客户端 -->
        <dependency>
            <groupId>org.influxdb</groupId>
            <artifactId>influxdb-java</artifactId>
            <version>2.24</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
            <version>1.2.5</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-siteweb</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
