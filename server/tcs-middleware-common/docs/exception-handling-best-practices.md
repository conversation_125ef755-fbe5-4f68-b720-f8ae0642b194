# 中间件异常处理最佳实践

## 概述

本文档定义了TCS中间件模块的异常处理规范和最佳实践，确保异常处理的一致性和可维护性。

## 异常体系架构

### 异常类层次结构

```
MiddlewareException (抽象基类)
├── MiddlewareBusinessException (业务异常)
└── MiddlewareTechnicalException (技术异常)
```

### 异常类说明

| 异常类 | 用途 | 错误码类型 | 使用场景 |
|--------|------|------------|----------|
| `MiddlewareBusinessException` | 业务逻辑错误 | `MiddlewareBusinessErrorCode` | 配置验证失败、资源类型不匹配、操作不允许等 |
| `MiddlewareTechnicalException` | 技术实现错误 | `MiddlewareTechnicalErrorCode` | 连接失败、初始化失败、系统错误等 |

## 错误码规范

### 业务错误码 (MiddlewareBusinessErrorCode)

```java
// 资源相关
RESOURCE_NOT_FOUND("BIZ-RESOURCE-001", "资源未找到")
RESOURCE_TYPE_INVALID("BIZ-RESOURCE-002", "资源类型无效")
RESOURCE_CONFIG_INVALID("BIZ-RESOURCE-003", "资源配置无效")

// 服务相关
SERVICE_NOT_FOUND("BIZ-SERVICE-001", "服务未找到")
SERVICE_TYPE_INVALID("BIZ-SERVICE-002", "服务类型无效")
SERVICE_CONFIG_INVALID("BIZ-SERVICE-003", "服务配置无效")

// 操作相关
OPERATION_NOT_ALLOWED("BIZ-OPERATION-001", "操作不允许")
```

### 技术错误码 (MiddlewareTechnicalErrorCode)

```java
// 系统错误
SYSTEM_ERROR("TECH-SYSTEM-001", "系统错误")

// 资源相关
RESOURCE_INITIALIZATION_FAILED("TECH-RESOURCE-001", "资源初始化失败")
RESOURCE_START_FAILED("TECH-RESOURCE-002", "资源启动失败")
RESOURCE_STOP_FAILED("TECH-RESOURCE-003", "资源停止失败")
RESOURCE_DESTROY_FAILED("TECH-RESOURCE-004", "资源销毁失败")

// 服务相关
SERVICE_INITIALIZATION_FAILED("TECH-SERVICE-001", "服务初始化失败")
SERVICE_START_FAILED("TECH-SERVICE-002", "服务启动失败")
SERVICE_STOP_FAILED("TECH-SERVICE-003", "服务停止失败")
SERVICE_DESTROY_FAILED("TECH-SERVICE-004", "服务销毁失败")

// 连接相关
DATABASE_CONNECTION_FAILED("TECH-CONNECTION-001", "数据库连接失败")
MESSAGE_QUEUE_CONNECTION_FAILED("TECH-CONNECTION-002", "消息队列连接失败")
```

## 使用规范

### 1. 异常抛出

#### 业务异常示例
```java
// 配置验证失败
if (!validationResult.isValid()) {
    throw new MiddlewareBusinessException(
        MiddlewareBusinessErrorCode.RESOURCE_CONFIG_INVALID,
        "配置验证失败: " + String.join(", ", validationResult.getErrors())
    );
}

// 资源类型不匹配
if (!(resource instanceof RedisResource)) {
    throw new MiddlewareBusinessException(
        MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
        "资源类型不匹配，期望RedisResource，实际为" + resource.getClass().getName()
    );
}
```

#### 技术异常示例
```java
// 资源初始化失败
try {
    // 初始化逻辑
} catch (Exception e) {
    throw new MiddlewareTechnicalException(
        MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
        "创建Redis资源失败: " + e.getMessage(),
        e
    );
}

// 连接失败
if (!connectionTest.isSuccessful()) {
    throw new MiddlewareTechnicalException(
        MiddlewareTechnicalErrorCode.DATABASE_CONNECTION_FAILED,
        "数据库连接测试失败"
    );
}
```

### 2. 异常处理

#### 标准异常处理模式
```java
public Resource createResource(String id, String name, String description, Map<String, Object> config) 
        throws MiddlewareTechnicalException {
    try {
        // 业务逻辑
        return resource;
    } catch (MiddlewareBusinessException | MiddlewareTechnicalException e) {
        // 重新抛出中间件异常
        throw e;
    } catch (Exception e) {
        // 包装其他异常为技术异常
        throw new MiddlewareTechnicalException(
            MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
            "创建资源失败: " + e.getMessage(),
            e
        );
    }
}
```

### 3. 接口声明

#### 接口方法异常声明规范
```java
public interface ResourceProvider<T extends Resource> {
    // 创建和销毁方法抛出技术异常
    T createResource(String id, String name, String description, Map<String, Object> config) 
        throws MiddlewareTechnicalException;
    
    void destroyResource(Resource resource) throws MiddlewareTechnicalException;
}

public interface ResourceProviderFactory {
    // 查找方法抛出业务异常
    ResourceProvider<? extends Resource> getProvider(String type) 
        throws MiddlewareBusinessException;
}
```

## 开发指南

### 1. 异常选择原则

| 场景 | 异常类型 | 理由 |
|------|----------|------|
| 用户输入错误、配置错误 | `MiddlewareBusinessException` | 用户可以修正的错误 |
| 系统故障、网络问题、资源不足 | `MiddlewareTechnicalException` | 系统级别的技术问题 |
| 编程错误、空指针等 | `MiddlewareTechnicalException` | 技术实现问题 |

### 2. 错误消息规范

- **简洁明确**：描述具体的错误原因
- **包含上下文**：提供足够的调试信息
- **用户友好**：业务异常消息应该对用户有意义
- **技术详细**：技术异常可以包含技术细节

### 3. 异常链保持

```java
// ✅ 正确：保持异常链
catch (SQLException e) {
    throw new MiddlewareTechnicalException(
        MiddlewareTechnicalErrorCode.DATABASE_CONNECTION_FAILED,
        "数据库连接失败: " + e.getMessage(),
        e  // 保持原始异常
    );
}

// ❌ 错误：丢失异常链
catch (SQLException e) {
    throw new MiddlewareTechnicalException(
        MiddlewareTechnicalErrorCode.DATABASE_CONNECTION_FAILED,
        "数据库连接失败"
        // 缺少原始异常
    );
}
```

### 4. 日志记录

```java
try {
    // 业务逻辑
} catch (MiddlewareBusinessException e) {
    // 业务异常通常记录为WARN级别
    logger.warn("业务异常: {}", e.getMessage());
    throw e;
} catch (Exception e) {
    // 技术异常记录为ERROR级别
    logger.error("技术异常: {}", e.getMessage(), e);
    throw new MiddlewareTechnicalException(
        MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
        "系统错误: " + e.getMessage(),
        e
    );
}
```

## 迁移指南

### 旧异常类映射

| 旧异常类 | 新异常类 | 说明 |
|----------|----------|------|
| `ResourceException` | `MiddlewareTechnicalException` | 资源相关技术异常 |
| `ServiceException` | `MiddlewareTechnicalException` | 服务相关技术异常 |
| `ResourceNotFoundException` | `MiddlewareBusinessException` | 资源未找到业务异常 |
| `ServiceNotFoundException` | `MiddlewareBusinessException` | 服务未找到业务异常 |

### 快速替换模式

```java
// 旧代码
throw new ResourceException("RESOURCE-001", "错误消息", e);

// 新代码
throw new MiddlewareTechnicalException(
    MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
    "错误消息",
    e
);
```

## 注意事项

1. **不要吞噬异常**：总是要么处理异常，要么重新抛出
2. **避免过度包装**：不要无意义地包装已经是中间件异常的异常
3. **保持一致性**：在同一模块中使用相同的异常处理模式
4. **及时更新错误码**：添加新的错误场景时，及时添加对应的错误码
5. **文档同步**：修改异常处理时，同步更新相关文档

## 工具支持

- **错误码枚举**：使用IDE的自动完成功能选择合适的错误码
- **异常模板**：建议IDE中创建异常抛出的代码模板
- **静态检查**：可以配置静态代码检查规则，确保异常处理的一致性
