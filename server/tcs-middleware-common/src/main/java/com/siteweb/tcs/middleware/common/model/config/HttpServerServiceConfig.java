package com.siteweb.tcs.middleware.common.model.config;

import lombok.Data;

/**
 * HTTP服务器服务配置
 * 只包含Service层真正使用的配置项
 */
@Data
public class HttpServerServiceConfig {

    // ==================== 解析失败记录配置 ====================

    /**
     * 解析失败记录最大数量
     * 设置为0表示不限制，-1表示禁用错误记录
     */
    private int maxRequestFailureRecords = 10000;

    /**
     * 是否启用解析失败记录
     */
    private boolean enableRequestFailureRecording = true;

    /**
     * 解析失败记录保留时间（小时）
     * 超过此时间的记录将被自动清理
     */
    private int requestFailureRetentionHours = 24;
}
