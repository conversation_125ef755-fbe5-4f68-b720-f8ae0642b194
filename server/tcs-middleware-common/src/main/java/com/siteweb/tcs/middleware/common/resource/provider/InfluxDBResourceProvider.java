package com.siteweb.tcs.middleware.common.resource.provider;

import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.InfluxDBConfig;
import com.siteweb.tcs.middleware.common.resource.InfluxDBResource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * InfluxDB资源提供者
 * 使用InfluxDB 1.x客户端
 */
@Component
public class InfluxDBResourceProvider extends AbstractResourceProvider<InfluxDBResource, InfluxDBConfig> {

    @Override
    public String getType() {
        return ResourceType.INFLUXDB.getCode();
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        return null;
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        return null;
    }

    @Override
    protected Class<InfluxDBConfig> getConfigClass() {
        return InfluxDBConfig.class;
    }

    @Override
    protected void validateConfigObject(InfluxDBConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);
        
        List<String> errors = new ArrayList<>();

        // 验证URL
        if (!StringUtils.hasText(config.getUrl())) {
            errors.add("InfluxDB服务器URL不能为空");
        }

        // 验证用户名
        if (!StringUtils.hasText(config.getUser())) {
            errors.add("用户名不能为空");
        }

        // 验证密码
        if (!StringUtils.hasText(config.getPassword())) {
            errors.add("密码不能为空");
        }

        // 验证数据库名称
        if (!StringUtils.hasText(config.getDatabase())) {
            errors.add("数据库名称不能为空");
        }

        // 验证超时时间
        if (config.getConnectTimeout() <= 0) {
            errors.add("连接超时时间必须大于0");
        }

        if (config.getReadTimeout() <= 0) {
            errors.add("读取超时时间必须大于0");
        }

        if (config.getWriteTimeout() <= 0) {
            errors.add("写入超时时间必须大于0");
        }

        if (!errors.isEmpty()) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.CONFIGURATION_INVALID,
                "InfluxDB配置验证失败: " + String.join("; ", errors)
            );
        }
    }

    /**
     * 创建InfluxDB客户端
     *
     * @param config InfluxDB配置
     * @return InfluxDB客户端
     */
    private InfluxDB createInfluxDBClient(InfluxDBConfig config) {
        try {
            logger.debug("创建InfluxDB客户端: url={}, user={}, database={}", 
                config.getUrl(), config.getUser(), config.getDatabase());

            // 创建InfluxDB客户端 - 使用InfluxDB 1.x API
            InfluxDB influxDB = InfluxDBFactory.connect(
                config.getUrl(), 
                config.getUser(), 
                config.getPassword()
            );

            // 设置数据库
            influxDB.setDatabase(config.getDatabase());

            // 启用GZIP压缩
            if (config.isEnableGzip()) {
                influxDB.enableGzip();
            }

            // 启用批量写入
            if (config.isEnableBatching()) {
                influxDB.enableBatch(
                    config.getBatchSize(), 
                    config.getFlushInterval(), 
                    TimeUnit.MILLISECONDS
                );
            }

            logger.debug("InfluxDB客户端创建成功");
            return influxDB;

        } catch (Exception e) {
            logger.error("创建InfluxDB客户端失败", e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建InfluxDB客户端失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected InfluxDBResource doCreateResource(String id, String name, String description, InfluxDBConfig config) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建InfluxDB资源: id={}, name={}, url={}", 
                id, name, config.getUrl());

            // 记录配置信息（隐藏敏感信息）
            logConfig(config);

            // 创建InfluxDB客户端
            InfluxDB client = createInfluxDBClient(config);

            // 创建InfluxDB资源实例
            InfluxDBResource resource = new InfluxDBResource(id, getType(), name, description, client);
            
            logger.info("InfluxDB资源创建成功: id={}, name={}", id, name);
            return resource;
            
        } catch (Exception e) {
            logger.error("创建InfluxDB资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建InfluxDB资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected String getConfigString(InfluxDBConfig config) {
        // 隐藏敏感信息
        return String.format("InfluxDBConfig{url='%s', user='%s', database='%s', password='***', " +
                "connectTimeout=%d, readTimeout=%d, writeTimeout=%d}",
            config.getUrl(), config.getUser(), config.getDatabase(), 
            config.getConnectTimeout(), config.getReadTimeout(), config.getWriteTimeout());
    }
}
