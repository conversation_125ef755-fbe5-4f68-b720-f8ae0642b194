package com.siteweb.tcs.middleware.common.resource.provider;

import com.siteweb.tcs.middleware.common.model.config.MinioFileSystemConfig;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import com.siteweb.tcs.middleware.common.resource.MinioFileSystemResource;
import io.minio.BucketExistsArgs;
import io.minio.MinioClient;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Minio文件系统资源提供者
 */
@Component
public class MinioFileSystemResourceProvider extends AbstractResourceProvider<MinioFileSystemResource, MinioFileSystemConfig> {

    @Override
    public String getType() {
        return ResourceType.MINIO_FILESYSTEM.getCode();
    }

    @Override
    protected Class<MinioFileSystemConfig> getConfigClass() {
        return MinioFileSystemConfig.class;
    }

    @Override
    protected void validateConfigObject(MinioFileSystemConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);

        // 验证端点
        if (!StringUtils.hasText(config.getEndpoint())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Minio端点不能为空"
            );
        }

        // 验证访问密钥
        if (!StringUtils.hasText(config.getAccessKey())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Minio访问密钥不能为空"
            );
        }

        // 验证秘密密钥
        if (!StringUtils.hasText(config.getSecretKey())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Minio秘密密钥不能为空"
            );
        }

        // 验证存储桶名称
        if (!StringUtils.hasText(config.getBucketName())) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "Minio存储桶名称不能为空"
            );
        }

        // 验证超时时间
        if (config.getConnectTimeout() <= 0) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "连接超时时间必须大于0"
            );
        }

        if (config.getReadTimeout() <= 0) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "读取超时时间必须大于0"
            );
        }

        if (config.getWriteTimeout() <= 0) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "写入超时时间必须大于0"
            );
        }
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        try {
            MinioFileSystemConfig minioConfig = convertMapToConfig(config);
            validateConfigObject(minioConfig);
            return ValidationResult.valid();
        } catch (MiddlewareTechnicalException e) {
            logger.error("验证Minio文件系统配置失败", e);
            return ValidationResult.invalid(List.of(e.getMessage()));
        } catch (Exception e) {
            logger.error("验证Minio文件系统配置失败", e);
            return ValidationResult.invalid(List.of("配置格式错误: " + e.getMessage()));
        }
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            // 转换配置
            MinioFileSystemConfig minioConfig = convertMapToConfig(config);
            
            // 创建临时Minio客户端进行连接测试
            MinioClient testClient = MinioClient.builder()
                .endpoint(minioConfig.getEndpoint(), minioConfig.isUseHttps() ? 443 : 80, minioConfig.isUseHttps())
                .credentials(minioConfig.getAccessKey(), minioConfig.getSecretKey())
                .region(minioConfig.getRegion())
                .build();
            
            // 测试连接 - 检查存储桶是否存在
            boolean bucketExists = testClient.bucketExists(
                BucketExistsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .build()
            );
            
            if (bucketExists) {
                return ConnectionTestResult.success("Minio连接测试成功，存储桶已存在");
            } else {
                return ConnectionTestResult.success("Minio连接测试成功，存储桶将在启动时创建");
            }
            
        } catch (MiddlewareTechnicalException e) {
            logger.error("Minio连接测试失败", e);
            return ConnectionTestResult.failure("连接测试失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Minio连接测试异常", e);
            return ConnectionTestResult.failure("连接测试异常: " + e.getMessage());
        }
    }

    @Override
    protected MinioFileSystemResource doCreateResource(String id, String name, String description, 
                                                     MinioFileSystemConfig config) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建Minio文件系统资源: id={}, name={}", id, name);
            
            // 记录配置信息（隐藏敏感信息）
            logConfig(config);
            
            // 创建Minio文件系统资源
            MinioFileSystemResource resource = new MinioFileSystemResource(
                id, 
                name, 
                description,
                config
            );
            
            logger.info("Minio文件系统资源创建成功: id={}, name={}, endpoint={}, bucket={}", 
                id, name, config.getEndpoint(), config.getBucketName());
            
            return resource;
            
        } catch (Exception e) {
            logger.error("创建Minio文件系统资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建Minio文件系统资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public void destroyResource(Resource resource) throws MiddlewareTechnicalException {
        if (resource instanceof MinioFileSystemResource) {
            try {
                logger.info("开始销毁Minio文件系统资源: {}", resource.getId());
                // 调用资源的destroy方法
                resource.destroy();
                logger.info("Minio文件系统资源销毁成功: {}", resource.getId());
            } catch (Exception e) {
                logger.error("销毁Minio文件系统资源失败: {}", resource.getId(), e);
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_DESTRUCTION_FAILED,
                    "销毁Minio文件系统资源失败: " + e.getMessage(),
                    e
                );
            }
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "资源类型不匹配，期望MinioFileSystemResource，实际为" + resource.getClass().getName()
            );
        }
    }

    @Override
    protected String getConfigString(MinioFileSystemConfig config) {
        if (config == null) {
            return "null";
        }
        // 隐藏敏感信息，只显示关键配置
        return String.format("MinioFileSystemConfig{endpoint='%s', bucketName='%s', region='%s', useHttps=%s}", 
            config.getEndpoint(), config.getBucketName(), config.getRegion(), config.isUseHttps());
    }
}
