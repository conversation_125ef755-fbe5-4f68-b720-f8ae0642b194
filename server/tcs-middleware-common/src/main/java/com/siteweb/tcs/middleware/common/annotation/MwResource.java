package com.siteweb.tcs.middleware.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 资源注入注解
 */
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Deprecated
public @interface MwResource {
    
    /**
     * 资源ID
     */
    String value() default "";

    /**
     * 引用id
     */
    String referenceId() default "system";

    /**
     * 是否必须
     */
    boolean required() default true;
}
