package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.FileInfo;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.model.config.MinioFileSystemConfig;
import io.minio.*;
import io.minio.errors.ErrorResponseException;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * Minio文件系统资源实现
 */
public class MinioFileSystemResource extends FileSystemResource {

    private static final Logger logger = LoggerFactory.getLogger(MinioFileSystemResource.class);

    private final String endpoint;
    private final String accessKey;
    private final String secretKey;
    private final String bucketName;
    private final String region;
    private final boolean useHttps;
    private final int connectTimeout;
    private final int readTimeout;
    private final int writeTimeout;

    private MinioClient minioClient;

    public MinioFileSystemResource(String id, String name, String description,
                                   MinioFileSystemConfig config) {
        super(id, ResourceType.MINIO_FILESYSTEM.getCode(), name, description);
        this.endpoint = config.getEndpoint();
        this.accessKey = config.getAccessKey();
        this.secretKey = config.getSecretKey();
        this.bucketName = config.getBucketName();
        this.region = config.getRegion();
        this.useHttps = config.isUseHttps();
        this.connectTimeout = config.getConnectTimeout();
        this.readTimeout = config.getReadTimeout();
        this.writeTimeout = config.getWriteTimeout();
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化Minio文件系统资源: {}", getId());

        try {
            this.minioClient = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .region(region)
                .build();

            logger.info("Minio文件系统资源初始化成功: {}", getId());

        } catch (Exception e) {
            logger.error("初始化Minio文件系统资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "初始化Minio文件系统资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动Minio文件系统资源: {}", getId());

        try {
            // 验证存储桶名称有效性
            if (!isValidBucketName(bucketName)) {
                throw new IllegalArgumentException("无效的存储桶名称: " + bucketName);
            }

            // 创建MinIO客户端时指定正确的端点
            MinioClient minioClient = MinioClient.builder()
                    .endpoint(endpoint) // 确保这是正确的MinIO服务器地址
                    .credentials(accessKey, secretKey)
                    .build();

            // 检查存储桶是否存在
            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
                // 创建存储桶
                try {
                    minioClient.makeBucket(MakeBucketArgs.builder()
                            .bucket(bucketName)
                            .region(region)
                            .build());
                    logger.info("创建存储桶: {}", bucketName);
                } catch (ErrorResponseException e) {
                    // 处理区域相关错误
                    if ("InvalidRegion".equals(e.errorResponse().code())) {
                        logger.warn("指定的区域不被支持，尝试使用默认区域: {}", region);
                        minioClient.makeBucket(MakeBucketArgs.builder()
                                .bucket(bucketName)
                                .build());
                        logger.info("使用默认区域创建存储桶成功: {}", bucketName);
                    } else {
                        throw e;
                    }
                }
            }

            logger.info("Minio文件系统资源启动成功: {}", getId());
        } catch (Exception e) {
            logger.error("启动Minio文件系统资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                    "启动Minio文件系统资源失败: " + e.getMessage(),
                    e
            );
        }

// 辅助方法：验证存储桶名称是否符合要求

    }
    private boolean isValidBucketName(String bucketName) {
        if (bucketName == null || bucketName.length() < 3 || bucketName.length() > 63) {
            return false;
        }
        return bucketName.matches("^[a-z0-9][a-z0-9.-]+[a-z0-9]$");
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止Minio文件系统资源: {}", getId());
        // Minio客户端不需要特殊停止操作
        logger.info("Minio文件系统资源停止成功: {}", getId());
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁Minio文件系统资源: {}", getId());
        this.minioClient = null;
        logger.info("Minio文件系统资源销毁成功: {}", getId());
    }

    @Override
    public HealthStatus checkHealth() {
        try {
            minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            return HealthStatus.up("Minio文件系统正常");
        } catch (Exception e) {
            return HealthStatus.down("Minio连接失败: " + e.getMessage());
        }
    }

    @Override
    public <T> T getNativeResource() {
        return (T) minioClient;
    }

    @Override
    public boolean writeFile(String filePath, String fileName, byte[] content) throws MiddlewareTechnicalException {
        try {
            logger.debug("写入文件到Minio: {}/{}", filePath, fileName);

            String objectName = buildObjectName(filePath, fileName);

            try (InputStream inputStream = new ByteArrayInputStream(content)) {
                minioClient.putObject(
                    PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .stream(inputStream, content.length, -1)
                        .build()
                );
            }

            logger.debug("文件写入Minio成功: {}", objectName);
            return true;

        } catch (Exception e) {
            logger.error("写入文件到Minio失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "写入文件到Minio失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public byte[] readFile(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("从Minio读取文件: {}/{}", filePath, fileName);

            String objectName = buildObjectName(filePath, fileName);

            try (InputStream inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build())) {
                return inputStream.readAllBytes();
            }

        } catch (ErrorResponseException e) {
            if ("NoSuchKey".equals(e.errorResponse().code())) {
                return null; // 文件不存在
            }
            logger.error("从Minio读取文件失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "从Minio读取文件失败: " + e.getMessage(),
                e
            );
        } catch (Exception e) {
            logger.error("从Minio读取文件失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "从Minio读取文件失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean deleteFile(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("从Minio删除文件: {}/{}", filePath, fileName);

            String objectName = buildObjectName(filePath, fileName);

            minioClient.removeObject(
                RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build()
            );

            logger.debug("从Minio删除文件成功: {}", objectName);
            return true;

        } catch (Exception e) {
            logger.error("从Minio删除文件失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "从Minio删除文件失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public List<FileInfo> listFiles(String directoryPath) throws MiddlewareTechnicalException {
        try {
            logger.debug("列出Minio目录文件: {}", directoryPath);

            List<FileInfo> fileInfos = new ArrayList<>();
            String prefix = directoryPath.isEmpty() || directoryPath.equals("/") ? "" :
                           (directoryPath.endsWith("/") ? directoryPath : directoryPath + "/");

            Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                    .bucket(bucketName)
                    .prefix(prefix)
                    .recursive(false)
                    .build()
            );

            for (Result<Item> result : results) {
                Item item = result.get();
                FileInfo fileInfo = FileInfo.builder()
                    .fileName(extractFileName(item.objectName()))
                    .filePath(directoryPath)
                    .fileSize(item.size())
                    .isDirectory(item.isDir())
                    .modifyTime(LocalDateTime.ofInstant(item.lastModified().toInstant(), ZoneId.systemDefault()))
                    .build();
                fileInfos.add(fileInfo);
            }

            logger.debug("列出Minio目录文件成功: {}, 文件数: {}", directoryPath, fileInfos.size());
            return fileInfos;

        } catch (Exception e) {
            logger.error("列出Minio目录文件失败: {}", directoryPath, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "列出Minio目录文件失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean fileExists(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            String objectName = buildObjectName(filePath, fileName);

            try {
                minioClient.statObject(
                    StatObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build()
                );
                return true;
            } catch (ErrorResponseException e) {
                if ("NoSuchKey".equals(e.errorResponse().code())) {
                    return false;
                }
                throw e;
            }

        } catch (Exception e) {
            logger.error("检查Minio文件存在失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "检查Minio文件存在失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean createDirectory(String directoryPath) throws MiddlewareTechnicalException {
        // Minio是对象存储，不需要显式创建目录
        // 目录会在上传文件时自动创建
        logger.debug("Minio不需要显式创建目录: {}", directoryPath);
        return true;
    }

    @Override
    public boolean deleteDirectory(String directoryPath, boolean recursive) throws MiddlewareTechnicalException {
        try {
            logger.debug("删除Minio目录: {}, recursive: {}", directoryPath, recursive);

            if (recursive) {
                String prefix = directoryPath.isEmpty() || directoryPath.equals("/") ? "" :
                               (directoryPath.endsWith("/") ? directoryPath : directoryPath + "/");

                Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                        .bucket(bucketName)
                        .prefix(prefix)
                        .recursive(true)
                        .build()
                );

                List<DeleteObject> objectsToDelete = new ArrayList<>();
                for (Result<Item> result : results) {
                    objectsToDelete.add(new DeleteObject(result.get().objectName()));
                }

                if (!objectsToDelete.isEmpty()) {
                    minioClient.removeObjects(
                        RemoveObjectsArgs.builder()
                            .bucket(bucketName)
                            .objects(objectsToDelete)
                            .build()
                    );
                }
            }

            logger.debug("删除Minio目录成功: {}", directoryPath);
            return true;

        } catch (Exception e) {
            logger.error("删除Minio目录失败: {}", directoryPath, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "删除Minio目录失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public FileInfo getFileInfo(String filePath, String fileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("获取Minio文件信息: {}/{}", filePath, fileName);

            String objectName = buildObjectName(filePath, fileName);

            StatObjectResponse stat = minioClient.statObject(
                StatObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build()
            );

            return FileInfo.builder()
                .fileName(fileName)
                .filePath(filePath)
                .fileSize(stat.size())
                .isDirectory(false)
                .modifyTime(LocalDateTime.ofInstant(stat.lastModified().toInstant(), ZoneId.systemDefault()))
                .contentType(stat.contentType())
                .build();

        } catch (ErrorResponseException e) {
            if ("NoSuchKey".equals(e.errorResponse().code())) {
                return null; // 文件不存在
            }
            logger.error("获取Minio文件信息失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "获取Minio文件信息失败: " + e.getMessage(),
                e
            );
        } catch (Exception e) {
            logger.error("获取Minio文件信息失败: {}/{}", filePath, fileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "获取Minio文件信息失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public boolean copyFile(String sourcePath, String sourceFileName,
                          String targetPath, String targetFileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("复制Minio文件: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);

            String sourceObjectName = buildObjectName(sourcePath, sourceFileName);
            String targetObjectName = buildObjectName(targetPath, targetFileName);

            minioClient.copyObject(
                CopyObjectArgs.builder()
                    .bucket(bucketName)
                    .object(targetObjectName)
                    .source(CopySource.builder()
                        .bucket(bucketName)
                        .object(sourceObjectName)
                        .build())
                    .build()
            );

            logger.debug("复制Minio文件成功: {} -> {}", sourceObjectName, targetObjectName);
            return true;

        } catch (Exception e) {
            logger.error("复制Minio文件失败: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "复制Minio文件失败: " + e.getMessage(),
                e
            );
        }
    }

    private String buildObjectName(String filePath, String fileName) {
        if (filePath == null || filePath.isEmpty() || filePath.equals("/")) {
            return fileName;
        }
        String normalizedPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;
        normalizedPath = normalizedPath.endsWith("/") ? normalizedPath : normalizedPath + "/";
        return normalizedPath + fileName;
    }

    @Override
    public boolean moveFile(String sourcePath, String sourceFileName,
                          String targetPath, String targetFileName) throws MiddlewareTechnicalException {
        try {
            logger.debug("移动Minio文件: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);

            // 先复制文件
            boolean copySuccess = copyFile(sourcePath, sourceFileName, targetPath, targetFileName);
            if (!copySuccess) {
                return false;
            }

            // 再删除源文件
            boolean deleteSuccess = deleteFile(sourcePath, sourceFileName);
            if (!deleteSuccess) {
                // 如果删除失败，尝试删除已复制的目标文件
                try {
                    deleteFile(targetPath, targetFileName);
                } catch (Exception e) {
                    logger.warn("回滚复制的文件失败: {}/{}", targetPath, targetFileName, e);
                }
                return false;
            }

            logger.debug("移动Minio文件成功: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName);
            return true;

        } catch (Exception e) {
            logger.error("移动Minio文件失败: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                "移动Minio文件失败: " + e.getMessage(),
                e
            );
        }
    }

    private String extractFileName(String objectName) {
        int lastSlashIndex = objectName.lastIndexOf('/');
        return lastSlashIndex >= 0 ? objectName.substring(lastSlashIndex + 1) : objectName;
    }
}
