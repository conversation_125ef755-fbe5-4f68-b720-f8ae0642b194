package com.siteweb.tcs.middleware.common.service.udp.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;

/**
 * UDP流量统计信息
 * 
 * 用于统计特定来源IP:Port的UDP流量信息，包括数据包数量和总字节数
 * 
 * <AUTHOR> for UDP Middleware Framework
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UDPTrafficStats {

    /**
     * 来源地址（IP:Port格式）
     */
    private String sourceAddress;

    /**
     * 接收的数据包数量
     */
    @Builder.Default
    private AtomicLong packetCount = new AtomicLong(0);

    /**
     * 接收的总字节数
     */
    @Builder.Default
    private AtomicLong totalBytes = new AtomicLong(0);

    /**
     * 最后接收时间
     */
    private LocalDateTime lastReceivedTime;

    /**
     * 首次接收时间
     */
    private LocalDateTime firstReceivedTime;

    /**
     * 创建流量统计信息
     *
     * @param sourceAddress 来源地址
     * @return 流量统计信息
     */
    public static UDPTrafficStats create(String sourceAddress) {
        LocalDateTime now = LocalDateTime.now();
        return UDPTrafficStats.builder()
                .sourceAddress(sourceAddress)
                .firstReceivedTime(now)
                .lastReceivedTime(now)
                .build();
    }

    /**
     * 记录接收到的数据包
     *
     * @param dataSize 数据包大小（字节）
     */
    public void recordPacket(int dataSize) {
        packetCount.incrementAndGet();
        totalBytes.addAndGet(dataSize);
        lastReceivedTime = LocalDateTime.now();
        
        // 如果是第一次记录，设置首次接收时间
        if (firstReceivedTime == null) {
            firstReceivedTime = lastReceivedTime;
        }
    }

    /**
     * 获取数据包数量
     *
     * @return 数据包数量
     */
    public long getPacketCount() {
        return packetCount.get();
    }

    /**
     * 获取总字节数
     *
     * @return 总字节数
     */
    public long getTotalBytes() {
        return totalBytes.get();
    }

    /**
     * 获取平均数据包大小
     *
     * @return 平均数据包大小（字节）
     */
    public double getAveragePacketSize() {
        long packets = packetCount.get();
        if (packets == 0) {
            return 0.0;
        }
        return (double) totalBytes.get() / packets;
    }

    /**
     * 重置统计信息
     */
    public void reset() {
        packetCount.set(0);
        totalBytes.set(0);
        LocalDateTime now = LocalDateTime.now();
        firstReceivedTime = now;
        lastReceivedTime = now;
    }
}
