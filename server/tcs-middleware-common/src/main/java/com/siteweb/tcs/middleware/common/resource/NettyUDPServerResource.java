package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.model.config.NettyUDPServerResourceConfig;
import com.siteweb.tcs.middleware.common.service.udp.DatagramHandler;
import com.siteweb.tcs.middleware.common.service.udp.model.UDPDatagramInfo;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 基于Netty框架的UDP服务器资源实现
 * 
 * 使用Netty框架的Bootstrap和DatagramChannel实现高性能的UDP服务器。
 * 提供异步非阻塞的UDP数据包接收和处理能力。
 * 
 * <AUTHOR> for UDP Middleware Framework
 */
@Slf4j
public class NettyUDPServerResource extends UDPServerResource {

    private NettyUDPServerResourceConfig config;

    /**
     * 数据包处理器
     */
    private volatile DatagramHandler datagramHandler;

    /**
     * Netty Bootstrap
     */
    private Bootstrap bootstrap;

    /**
     * Netty EventLoopGroup
     */
    private EventLoopGroup eventLoopGroup;

    /**
     * Netty Channel
     */
    private Channel channel;

    /**
     * 接收的数据包计数器
     */
    private final AtomicLong receivedPacketCount = new AtomicLong(0);

    /**
     * 接收的字节计数器
     */
    private final AtomicLong receivedByteCount = new AtomicLong(0);

    /**
     * 性能监控任务Future
     */
    private CompletableFuture<Void> performanceMonitorFuture;

    /**
     * 上次统计时间
     */
    private volatile long lastStatsTime = System.currentTimeMillis();

    /**
     * 上次统计的数据包数量
     */
    private volatile long lastPacketCount = 0;

    /**
     * 上次统计的字节数量
     */
    private volatile long lastByteCount = 0;

    /**
     * 构造函数
     *
     * @param id 资源ID
     * @param name 资源名称
     * @param description 资源描述
     * @param config UDP服务器配置
     */
    public NettyUDPServerResource(String id, String name, String description, NettyUDPServerResourceConfig config) {
        super(id, ResourceType.NETTY_UDP_SERVER.getCode(), name, description);
        this.config = config;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getType() {
        return ResourceType.NETTY_UDP_SERVER.getCode();
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public ResourceStatus getStatus() {
        return status.get();
    }

    /**
     * 设置数据包处理器
     *
     * @param handler 数据包处理器
     * @throws MiddlewareTechnicalException 设置失败时抛出异常
     */
    public void setDatagramHandler(DatagramHandler handler) throws MiddlewareTechnicalException {
        if (handler == null) {
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.CONFIGURATION_INVALID,
                    "Datagram handler cannot be null for Netty UDP server resource: " + id
            );
        }
        this.datagramHandler = handler;
        log.debug("Datagram handler set for Netty UDP server resource: {}", id);
    }

    @Override
    public Object getConfig() {
        return config;
    }

    @Override
    public String getBindAddress() {
        return this.config.getBindAddress();
    }

    /**
     * 获取具体的Netty UDP配置
     * @return Netty UDP配置
     */
    public NettyUDPServerResourceConfig getNettyConfig() {
        return config;
    }

    /**
     * 检查是否已绑定
     *
     * @return 如果已绑定到端口则返回true，否则返回false
     */
    public boolean isBound() {
        return channel != null && channel.isActive();
    }

    /**
     * 获取接收到的数据包总数
     *
     * @return 接收到的数据包总数
     */
    public long getReceivedPacketCount() {
        return receivedPacketCount.get();
    }

    /**
     * 获取接收到的总字节数
     *
     * @return 接收到的总字节数
     */
    public long getReceivedByteCount() {
        return receivedByteCount.get();
    }

    /**
     * 发送UDP数据包
     *
     * @param targetIp 目标IP地址
     * @param targetPort 目标端口
     * @param data 要发送的数据
     * @throws MiddlewareTechnicalException 发送失败时抛出异常
     */
    public void sendDatagram(String targetIp, int targetPort, byte[] data) throws MiddlewareTechnicalException {
        if (!isBound()) {
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                    "Netty UDP server resource not bound: " + id
            );
        }

        try {
            InetSocketAddress targetAddress = new InetSocketAddress(targetIp, targetPort);
            ByteBuf buffer = channel.alloc().buffer(data.length);
            buffer.writeBytes(data);
            DatagramPacket packet = new DatagramPacket(buffer, targetAddress);
            channel.writeAndFlush(packet);
            log.debug("Sent UDP datagram from Netty UDP server resource {} to {}:{}", id, targetIp, targetPort);
        } catch (Exception e) {
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                    "Failed to send UDP datagram from Netty UDP server resource: " + id,
                    e
            );
        }
    }

    @Override
    public <T> T getNativeServer() {
        @SuppressWarnings("unchecked")
        T result = (T) channel;
        return result;
    }

    @Override
    public void clearStatistics() {
        receivedPacketCount.set(0);
        receivedByteCount.set(0);
        log.info("Statistics cleared for Netty UDP server resource: {}", id);
    }

    @Override
    public Map<String, Object> getStatisticsSummary() {
        Map<String, Object> summary = new java.util.HashMap<>();
        summary.put("receivedPacketCount", getReceivedPacketCount());
        summary.put("receivedByteCount", getReceivedByteCount());
        summary.put("isBound", isBound());
        summary.put("bindAddress", config != null ? config.getBindAddress() : "Not configured");
        summary.put("implementation", "Netty");
        summary.put("channelActive", channel != null && channel.isActive());
        summary.put("eventLoopGroupActive", eventLoopGroup != null && !eventLoopGroup.isShutdown());
        return summary;
    }

    @Override
    public <T> T getNativeResource() {
        @SuppressWarnings("unchecked")
        T result = (T) channel;
        return result;
    }

    @Override
    public boolean isHealthy() {
        return checkHealth().isUp();
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        log.info("Initializing Netty UDP server resource: {}", id);
        // Netty资源的初始化逻辑
        // 这里可以进行配置验证、依赖检查等
        log.info("Netty UDP server resource initialized successfully: {}", id);
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        log.info("Starting Netty UDP server resource: {}", id);

        try {
            // 根据配置创建EventLoopGroup，支持多线程
            int eventLoopThreads = config.getEventLoopThreads() > 0 ? config.getEventLoopThreads() : 1;
            eventLoopGroup = new NioEventLoopGroup(eventLoopThreads, r -> {
                Thread thread = new Thread(r, "NettyUDP-EventLoop-" + id + "-" + System.currentTimeMillis());
                thread.setDaemon(true);
                return thread;
            });

            // 创建Bootstrap
            bootstrap = new Bootstrap();
            bootstrap.group(eventLoopGroup)
                    .channel(NioDatagramChannel.class)
                    .option(ChannelOption.SO_BROADCAST, config.isBroadcast())
                    .option(ChannelOption.SO_REUSEADDR, config.isReuseAddress())
                    .option(ChannelOption.SO_RCVBUF, config.getReceiveBufferSize())
                    .option(ChannelOption.SO_SNDBUF, config.getSendBufferSize())
                    // 性能优化选项
                    .option(ChannelOption.ALLOCATOR, io.netty.buffer.PooledByteBufAllocator.DEFAULT)
                    .option(ChannelOption.RCVBUF_ALLOCATOR, new io.netty.channel.FixedRecvByteBufAllocator(config.getReceiveBufferSize()))
                    .option(ChannelOption.SO_KEEPALIVE, true)
                    .option(ChannelOption.TCP_NODELAY, true)
                    .handler(new UDPServerHandler());

            // 绑定地址
            String bindIp = (config.getBindIp() == null || config.getBindIp().isEmpty()) 
                    ? "0.0.0.0" : config.getBindIp();
            InetSocketAddress bindAddress = new InetSocketAddress(bindIp, config.getBindPort());
            
            ChannelFuture channelFuture = bootstrap.bind(bindAddress).sync();
            channel = channelFuture.channel();

            // 启动性能监控
            if (config.isEnablePerformanceMonitoring()) {
                startPerformanceMonitoring();
            }

            log.info("Netty UDP server resource started and bound to {}", config.getBindAddress());
        } catch (Exception e) {
            // 清理资源
            if (eventLoopGroup != null) {
                eventLoopGroup.shutdownGracefully();
            }
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                    "Failed to start Netty UDP server resource: " + id,
                    e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        log.info("Stopping Netty UDP server resource: {}", id);
        
        try {
            // 停止性能监控
            stopPerformanceMonitoring();

            // 关闭通道
            if (channel != null) {
                channel.close().sync();
            }

            // 关闭EventLoopGroup
            if (eventLoopGroup != null) {
                eventLoopGroup.shutdownGracefully().sync();
            }

            log.info("Netty UDP server resource stopped: {}", id);
        } catch (Exception e) {
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_STOP_FAILED,
                    "Failed to stop Netty UDP server resource: " + id,
                    e
            );
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        log.info("Destroying Netty UDP server resource: {}", id);
        
        try {
            // 清理资源
            channel = null;
            bootstrap = null;
            eventLoopGroup = null;
            config = null;

            log.info("Netty UDP server resource destroyed: {}", id);
        } catch (Exception e) {
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_DESTROY_FAILED,
                    "Failed to destroy Netty UDP server resource: " + id,
                    e
            );
        }
    }

    @Override
    public HealthStatus checkHealth() {
        try {
            if (channel == null || !channel.isActive()) {
                return HealthStatus.down("Channel not available or inactive");
            }

            if (eventLoopGroup == null || eventLoopGroup.isShutdown()) {
                return HealthStatus.down("EventLoopGroup not available or shutdown");
            }

            if (config != null && !isBound()) {
                return HealthStatus.down("Not bound to configured address");
            }

            return HealthStatus.up("Netty UDP server resource is healthy");
        } catch (Exception e) {
            log.error("Health check failed for Netty UDP server resource: {}", id, e);
            return HealthStatus.down("Health check failed: " + e.getMessage());
        }
    }

    /**
     * Netty UDP服务器处理器
     */
    private class UDPServerHandler extends SimpleChannelInboundHandler<DatagramPacket> {

        @Override
        protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket packet) throws Exception {
            try {
                // 提取数据
                ByteBuf content = packet.content();
                byte[] data = new byte[content.readableBytes()];
                content.readBytes(data);

                // 解析地址信息
                InetSocketAddress sourceAddress = packet.sender();
                String sourceIp = sourceAddress.getAddress().getHostAddress();
                int sourcePort = sourceAddress.getPort();

                // 获取本地地址信息
                InetSocketAddress localAddress = (InetSocketAddress) ctx.channel().localAddress();
                String targetIp = localAddress.getAddress().getHostAddress();
                int targetPort = localAddress.getPort();

                // 处理接收到的数据包
                handleReceivedDatagram(sourceIp, sourcePort, targetIp, targetPort, data);
            } catch (Exception e) {
                log.error("Error handling UDP datagram in Netty UDP server resource: {}", id, e);
            }
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            log.error("Exception caught in Netty UDP server resource: {}", id, cause);
        }

        @Override
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            log.info("Netty UDP server channel active for resource: {}", id);
            super.channelActive(ctx);
        }

        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            log.info("Netty UDP server channel inactive for resource: {}", id);
            super.channelInactive(ctx);
        }
    }

    /**
     * 处理接收到的原始UDP数据包
     */
    private void handleReceivedDatagram(String sourceIp, int sourcePort,
                                      String targetIp, int targetPort,
                                      byte[] rawData) {
        try {
            // 更新统计信息
            receivedPacketCount.incrementAndGet();
            receivedByteCount.addAndGet(rawData.length);

            // 解码为UTF-8字符串
            String data = new String(rawData, StandardCharsets.UTF_8);

            // 创建数据包信息
            UDPDatagramInfo datagramInfo = UDPDatagramInfo.create(
                    sourceIp, sourcePort, targetIp, targetPort, data, rawData);

            // 传递给处理器
            if (datagramHandler != null) {
                datagramHandler.handleDatagram(datagramInfo);
            } else {
                log.warn("No datagram handler set for Netty UDP server resource: {}", id);
            }
        } catch (Exception e) {
            log.error("Error handling received datagram from {}:{} for Netty UDP server resource: {}",
                    sourceIp, sourcePort, id, e);
        }
    }

    /**
     * 启动性能监控
     */
    private void startPerformanceMonitoring() {
        if (config.isEnablePerformanceMonitoring()) {
            performanceMonitorFuture = CompletableFuture.runAsync(this::performanceMonitorLoop,
                eventLoopGroup.next());
            log.info("Performance monitoring started for Netty UDP server resource: {}", id);
        }
    }

    /**
     * 停止性能监控
     */
    private void stopPerformanceMonitoring() {
        if (performanceMonitorFuture != null && !performanceMonitorFuture.isDone()) {
            performanceMonitorFuture.cancel(true);
            log.info("Performance monitoring stopped for Netty UDP server resource: {}", id);
        }
    }

    /**
     * 性能监控循环
     */
    private void performanceMonitorLoop() {
        log.info("Performance monitoring loop started for Netty UDP server resource: {}", id);

        while (channel != null && channel.isActive() && !Thread.currentThread().isInterrupted()) {
            try {
                Thread.sleep(config.getPerformanceStatsInterval() * 1000L);

                if (channel != null && channel.isActive()) {
                    logPerformanceStats();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("Error in performance monitoring loop for Netty UDP server resource: {}", id, e);
            }
        }

        log.info("Performance monitoring loop stopped for Netty UDP server resource: {}", id);
    }

    /**
     * 记录性能统计信息
     */
    private void logPerformanceStats() {
        long currentTime = System.currentTimeMillis();
        long currentPacketCount = receivedPacketCount.get();
        long currentByteCount = receivedByteCount.get();

        long timeDiff = currentTime - lastStatsTime;
        long packetDiff = currentPacketCount - lastPacketCount;
        long byteDiff = currentByteCount - lastByteCount;

        if (timeDiff > 0) {
            double packetsPerSecond = (double) packetDiff * 1000 / timeDiff;
            double bytesPerSecond = (double) byteDiff * 1000 / timeDiff;
            double mbPerSecond = bytesPerSecond / (1024 * 1024);

            log.info("Netty UDP Server [{}] Performance Stats: " +
                    "Packets/sec: {:.2f}, " +
                    "MB/sec: {:.2f}, " +
                    "Total Packets: {}, " +
                    "Total Bytes: {}, " +
                    "Avg Packet Size: {} bytes, " +
                    "EventLoop Threads: {}",
                    id,
                    packetsPerSecond,
                    mbPerSecond,
                    currentPacketCount,
                    currentByteCount,
                    currentPacketCount > 0 ? currentByteCount / currentPacketCount : 0,
                    config.getEventLoopThreads()
            );
        }

        // 更新上次统计数据
        lastStatsTime = currentTime;
        lastPacketCount = currentPacketCount;
        lastByteCount = currentByteCount;
    }
}
