package com.siteweb.tcs.middleware.common.resource.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.RedisConfig;
import com.siteweb.tcs.middleware.common.resource.RedisResource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Redis资源提供者
 * 使用统一的配置转换机制
 */
@Component
public class RedisResourceProvider extends AbstractResourceProvider<RedisResource, RedisConfig> {

    @Override
    public String getType() {
        return ResourceType.REDIS.getCode();
    }

    @Override
    protected Class<RedisConfig> getConfigClass() {
        return RedisConfig.class;
    }

    @Override
    protected void validateConfigObject(RedisConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);
        
        List<String> errors = new ArrayList<>();

        // 验证主机地址
        if (!StringUtils.hasText(config.getHost())) {
            errors.add("主机地址不能为空");
        }

        // 验证端口
        if (config.getPort() <= 0 || config.getPort() > 65535) {
            errors.add("端口必须在1-65535之间");
        }

        // 验证数据库索引
        if (config.getDatabase() < 0) {
            errors.add("数据库索引不能小于0");
        }

        // 验证连接超时时间
        if (config.getConnectionTimeout() <= 0) {
            errors.add("连接超时时间必须大于0");
        }

        // 验证命令超时时间
        if (config.getCommandTimeout() <= 0) {
            errors.add("命令超时时间必须大于0");
        }

        // 验证连接池参数
        if (config.getMaxTotal() <= 0) {
            errors.add("最大连接数必须大于0");
        }

        if (config.getMaxIdle() < 0) {
            errors.add("最大空闲连接数不能小于0");
        }

        if (config.getMinIdle() < 0) {
            errors.add("最小空闲连接数不能小于0");
        }

        if (config.getMinIdle() > config.getMaxIdle()) {
            errors.add("最小空闲连接数不能大于最大空闲连接数");
        }

        if (config.getMaxIdle() > config.getMaxTotal()) {
            errors.add("最大空闲连接数不能大于最大连接数");
        }

        if (!errors.isEmpty()) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_CONFIG_INVALID,
                "Redis配置验证失败: " + String.join(", ", errors)
            );
        }
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        try {
            RedisConfig redisConfig = convertMapToConfig(config);
            validateConfigObject(redisConfig);
            return ValidationResult.valid();
        } catch (MiddlewareTechnicalException e) {
            logger.error("验证Redis配置失败", e);
            return ValidationResult.invalid(List.of(e.getMessage()));
        } catch (Exception e) {
            logger.error("验证Redis配置失败", e);
            return ValidationResult.invalid(List.of("配置格式错误: " + e.getMessage()));
        }
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        RedisConnectionFactory connectionFactory = null;
        RedisConnection connection = null;
        
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                return ConnectionTestResult.failure("配置验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            RedisConfig redisConfig = convertMapToConfig(config);

            // 创建临时连接进行测试
            connectionFactory = createConnectionFactory(redisConfig);
            connection = connectionFactory.getConnection();
            
            // 测试连接
            String pingResult = connection.ping();
            
            Map<String, Object> details = new HashMap<>();
            details.put("host", redisConfig.getHost());
            details.put("port", redisConfig.getPort());
            details.put("database", redisConfig.getDatabase());
            details.put("pingResult", pingResult);

            return ConnectionTestResult.success("连接成功", details);
            
        } catch (Exception e) {
            logger.error("测试Redis连接失败", e);
            return ConnectionTestResult.failure("连接测试异常: " + e.getMessage());
        } finally {
            // 确保资源被正确释放
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    logger.warn("关闭Redis连接时发生异常", e);
                }
            }
            
            if (connectionFactory != null) {
                try {
                    // 销毁连接工厂，释放资源
                    if (connectionFactory instanceof LettuceConnectionFactory) {
                        ((LettuceConnectionFactory) connectionFactory).destroy();
                    } else if (connectionFactory instanceof AutoCloseable) {
                        ((AutoCloseable) connectionFactory).close();
                    }
                } catch (Exception e) {
                    logger.warn("销毁Redis连接工厂时发生异常", e);
                }
            }
        }
    }

    @Override
    protected RedisResource doCreateResource(String id, String name, String description, RedisConfig config) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建Redis资源: id={}, name={}, host={}:{}", 
                id, name, config.getHost(), config.getPort());

            // 创建连接工厂
            RedisConnectionFactory connectionFactory = createConnectionFactory(config);

            // 创建RedisTemplate
            RedisTemplate<String, Object> redisTemplate = createRedisTemplate(connectionFactory);

            // 创建Redis资源实例
            RedisResource resource = new RedisResource(id, getType(), name, description, connectionFactory, redisTemplate);
            
            logger.info("Redis资源创建成功: id={}, name={}", id, name);
            return resource;
            
        } catch (Exception e) {
            logger.error("创建Redis资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建Redis资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected String getConfigString(RedisConfig config) {
        // 隐藏敏感信息
        return String.format("RedisConfig{host='%s', port=%d, database=%d, password='***', maxTotal=%d}",
            config.getHost(), config.getPort(), config.getDatabase(), config.getMaxTotal());
    }

    /**
     * 创建Redis连接工厂
     *
     * @param config Redis配置
     * @return Redis连接工厂
     */
    protected RedisConnectionFactory createConnectionFactory(RedisConfig config) {
        // 创建Redis单机配置
        RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration();
        standaloneConfig.setHostName(config.getHost());
        standaloneConfig.setPort(config.getPort());
        standaloneConfig.setDatabase(config.getDatabase());
        
        if (StringUtils.hasText(config.getPassword())) {
            standaloneConfig.setPassword(config.getPassword());
        }

        // 使用Lettuce连接工厂（Spring Boot 2.x默认）
        LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(standaloneConfig);
        connectionFactory.afterPropertiesSet();

        return connectionFactory;
    }

    /**
     * 创建RedisTemplate
     *
     * @param connectionFactory Redis连接工厂
     * @return RedisTemplate
     */
    protected RedisTemplate<String, Object> createRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);

        // 设置序列化器
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();

        // 设置key和value的序列化器
        redisTemplate.setKeySerializer(stringSerializer);
        redisTemplate.setValueSerializer(jsonSerializer);
        redisTemplate.setHashKeySerializer(stringSerializer);
        redisTemplate.setHashValueSerializer(jsonSerializer);

        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }
}
