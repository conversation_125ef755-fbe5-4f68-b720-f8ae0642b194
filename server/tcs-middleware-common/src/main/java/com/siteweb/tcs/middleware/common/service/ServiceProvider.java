package com.siteweb.tcs.middleware.common.service;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.resource.Resource;

import java.util.Map;

/**
 * 服务提供者接口
 *
 * @param <T> 服务类型
 */
public interface ServiceProvider<T extends Service> {

    /**
     * 获取服务类型
     *
     * @return 服务类型
     */
    String getType();

    /**
     * 获取支持的资源类别
     *
     * @return 支持的资源类别
     */
    String getSupportedResourceCategory();

    /**
     * 验证服务配置
     *
     * @param config 服务配置
     * @return 验证结果，包含是否有效和错误信息
     */
    ValidationResult validateConfig(Map<String, Object> config);

    /**
     * 测试服务连接
     *
     * @param config 服务配置
     * @return 连接测试结果，包含是否成功和错误信息
     */
    ConnectionTestResult testConnection(Map<String, Object> config);

    /**
     * 创建服务实例
     *
     * @param id 服务ID
     * @param name 服务名称
     * @param description 服务描述
     * @param config 服务配置
     * @param resource 关联的资源
     * @return 服务实例
     * @throws MiddlewareTechnicalException 创建服务实例失败时抛出异常
     */
    T createService(String id, String name, String description, Map<String, Object> config, Resource resource) throws MiddlewareTechnicalException;

    /**
     * 销毁服务实例
     *
     * @param service 服务实例
     * @throws MiddlewareTechnicalException 销毁服务实例失败时抛出异常
     */
    void destroyService(Service service) throws MiddlewareTechnicalException;
}
