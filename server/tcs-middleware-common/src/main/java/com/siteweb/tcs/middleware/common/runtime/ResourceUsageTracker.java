package com.siteweb.tcs.middleware.common.runtime;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 资源使用追踪器
 * 用于追踪资源的使用情况，包括操作次数、使用时长等
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Component
public class ResourceUsageTracker {
    
    private final Map<String, ResourceUsageInfo> resourceUsageMap = new ConcurrentHashMap<>();
    private final MiddlewareLogCollectorManager logCollectorManager;
    
    public ResourceUsageTracker(MiddlewareLogCollectorManager logCollectorManager) {
        this.logCollectorManager = logCollectorManager;
    }
    
    /**
     * 记录资源操作开始
     * 
     * @param resourceId 资源ID
     * @param operation 操作类型
     * @param details 操作详情
     * @return 操作追踪ID
     */
    public String trackOperationStart(String resourceId, String operation, String details) {
        ResourceUsageInfo usageInfo = resourceUsageMap.computeIfAbsent(resourceId, k -> new ResourceUsageInfo(resourceId));
        
        String trackingId = generateTrackingId(resourceId, operation);
        OperationRecord record = new OperationRecord();
        record.setTrackingId(trackingId);
        record.setOperation(operation);
        record.setDetails(details);
        record.setStartTime(LocalDateTime.now());
        
        usageInfo.getActiveOperations().put(trackingId, record);
        usageInfo.getTotalOperations().incrementAndGet();
        
        // 记录到日志收集器
        logCollectorManager.appendResourceLog(resourceId, createUsageLogInfo(
            resourceId, "INFO", 
            String.format("[USAGE:START] %s - %s (ID: %s)", operation, details, trackingId)
        ));
        
        log.debug("Started tracking operation {} for resource {}: {}", operation, resourceId, trackingId);
        return trackingId;
    }
    
    /**
     * 记录资源操作结束
     * 
     * @param trackingId 操作追踪ID
     * @param success 是否成功
     * @param result 操作结果
     */
    public void trackOperationEnd(String trackingId, boolean success, String result) {
        String resourceId = extractResourceIdFromTrackingId(trackingId);
        ResourceUsageInfo usageInfo = resourceUsageMap.get(resourceId);
        
        if (usageInfo != null) {
            OperationRecord record = usageInfo.getActiveOperations().remove(trackingId);
            if (record != null) {
                record.setEndTime(LocalDateTime.now());
                record.setSuccess(success);
                record.setResult(result);
                
                // 更新统计信息
                if (success) {
                    usageInfo.getSuccessfulOperations().incrementAndGet();
                } else {
                    usageInfo.getFailedOperations().incrementAndGet();
                }
                
                // 记录到日志收集器
                String status = success ? "SUCCESS" : "FAILED";
                long duration = java.time.Duration.between(record.getStartTime(), record.getEndTime()).toMillis();
                
                logCollectorManager.appendResourceLog(resourceId, createUsageLogInfo(
                    resourceId, success ? "INFO" : "WARN",
                    String.format("[USAGE:END] %s - %s (%dms) - %s", 
                        record.getOperation(), status, duration, result)
                ));
                
                log.debug("Finished tracking operation {} for resource {}: {} ({}ms)", 
                    record.getOperation(), resourceId, status, duration);
            }
        }
    }
    
    /**
     * 记录资源连接事件
     * 
     * @param resourceId 资源ID
     * @param connected 是否连接
     * @param details 连接详情
     */
    public void trackConnection(String resourceId, boolean connected, String details) {
        ResourceUsageInfo usageInfo = resourceUsageMap.computeIfAbsent(resourceId, k -> new ResourceUsageInfo(resourceId));
        
        if (connected) {
            usageInfo.setLastConnectedTime(LocalDateTime.now());
            usageInfo.getConnectionCount().incrementAndGet();
        } else {
            usageInfo.setLastDisconnectedTime(LocalDateTime.now());
        }
        
        // 记录到日志收集器
        String event = connected ? "CONNECTED" : "DISCONNECTED";
        logCollectorManager.appendResourceLog(resourceId, createUsageLogInfo(
            resourceId, "INFO",
            String.format("[CONNECTION:%s] %s", event, details)
        ));
        
        log.debug("Resource {} {}: {}", resourceId, event.toLowerCase(), details);
    }
    
    /**
     * 记录资源维护操作
     * 
     * @param resourceId 资源ID
     * @param maintenanceType 维护类型（backup, restore, cleanup等）
     * @param details 维护详情
     */
    public void trackMaintenance(String resourceId, String maintenanceType, String details) {
        ResourceUsageInfo usageInfo = resourceUsageMap.computeIfAbsent(resourceId, k -> new ResourceUsageInfo(resourceId));
        usageInfo.setLastMaintenanceTime(LocalDateTime.now());
        usageInfo.getMaintenanceCount().incrementAndGet();
        
        // 记录到日志收集器
        logCollectorManager.appendResourceLog(resourceId, createUsageLogInfo(
            resourceId, "INFO",
            String.format("[MAINTENANCE:%s] %s", maintenanceType.toUpperCase(), details)
        ));
        
        log.info("Resource {} maintenance {}: {}", resourceId, maintenanceType, details);
    }
    
    /**
     * 获取资源使用信息
     * 
     * @param resourceId 资源ID
     * @return 使用信息
     */
    public ResourceUsageInfo getUsageInfo(String resourceId) {
        return resourceUsageMap.get(resourceId);
    }
    
    /**
     * 清理资源使用信息
     * 
     * @param resourceId 资源ID
     */
    public void clearUsageInfo(String resourceId) {
        resourceUsageMap.remove(resourceId);
        log.debug("Cleared usage info for resource: {}", resourceId);
    }
    
    private String generateTrackingId(String resourceId, String operation) {
        return String.format("%s-%s-%d", resourceId, operation, System.currentTimeMillis());
    }
    
    private String extractResourceIdFromTrackingId(String trackingId) {
        int firstDash = trackingId.indexOf('-');
        int secondDash = trackingId.indexOf('-', firstDash + 1);
        return trackingId.substring(0, secondDash);
    }
    
    private MiddlewareLogCollector.LogInfo createUsageLogInfo(String resourceId, String level, String message) {
        MiddlewareLogCollector.LogInfo logInfo = new MiddlewareLogCollector.LogInfo();
        logInfo.setTimeStamp(LocalDateTime.now());
        logInfo.setLevel(level);
        logInfo.setLoggerName("middleware.resource.usage");
        logInfo.setThread(Thread.currentThread().getName());
        logInfo.setComponentId(resourceId);
        logInfo.setComponentType(MiddlewareContextManager.TYPE_RESOURCE);
        logInfo.setMessage(message);
        return logInfo;
    }
    
    /**
     * 资源使用信息
     */
    @Data
    public static class ResourceUsageInfo {
        private final String resourceId;
        private final AtomicLong totalOperations = new AtomicLong(0);
        private final AtomicLong successfulOperations = new AtomicLong(0);
        private final AtomicLong failedOperations = new AtomicLong(0);
        private final AtomicLong connectionCount = new AtomicLong(0);
        private final AtomicLong maintenanceCount = new AtomicLong(0);
        private final Map<String, OperationRecord> activeOperations = new ConcurrentHashMap<>();
        
        private LocalDateTime createdTime = LocalDateTime.now();
        private LocalDateTime lastConnectedTime;
        private LocalDateTime lastDisconnectedTime;
        private LocalDateTime lastMaintenanceTime;
        
        public ResourceUsageInfo(String resourceId) {
            this.resourceId = resourceId;
        }
        
        public double getSuccessRate() {
            long total = totalOperations.get();
            return total > 0 ? (double) successfulOperations.get() / total * 100 : 0.0;
        }
    }
    
    /**
     * 操作记录
     */
    @Data
    public static class OperationRecord {
        private String trackingId;
        private String operation;
        private String details;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private boolean success;
        private String result;
        
        public long getDurationMillis() {
            return endTime != null ? 
                java.time.Duration.between(startTime, endTime).toMillis() : 
                java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
        }
    }
}
