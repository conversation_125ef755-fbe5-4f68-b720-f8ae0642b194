package com.siteweb.tcs.middleware.common.resource.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.DamengConfig;
import com.siteweb.tcs.middleware.common.resource.DamengResource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Dameng数据库资源提供者
 * 使用统一的配置转换机制
 */
@Component
public class DamengResourceProvider extends AbstractResourceProvider<DamengResource, DamengConfig> {

    @Override
    public String getType() {
        return ResourceType.DAMENG.getCode();
    }

    @Override
    protected Class<DamengConfig> getConfigClass() {
        return DamengConfig.class;
    }

    @Override
    protected void validateConfigObject(DamengConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);
        
        List<String> errors = new ArrayList<>();

        // 验证必要参数
        if (!StringUtils.hasText(config.getHost())) {
            errors.add("主机地址不能为空");
        }

        if (config.getPort() == null || config.getPort() <= 0) {
            errors.add("端口必须大于0");
        }

        if (!StringUtils.hasText(config.getSchema())) {
            errors.add("Schema不能为空");
        }

        if (!StringUtils.hasText(config.getUsername())) {
            errors.add("用户名不能为空");
        }

        // 验证连接池参数
        if (config.getMaxPoolSize() == null || config.getMaxPoolSize() <= 0) {
            errors.add("连接池大小必须大于0");
        }

        if (config.getMinPoolSize() == null || config.getMinPoolSize() < 0) {
            errors.add("最小连接池大小不能小于0");
        }

        if (config.getMinPoolSize() != null && config.getMaxPoolSize() != null && 
            config.getMinPoolSize() > config.getMaxPoolSize()) {
            errors.add("最小连接池大小不能大于最大连接池大小");
        }

        // 验证超时参数
        if (config.getConnectionTimeout() != null && config.getConnectionTimeout() <= 0) {
            errors.add("连接超时时间必须大于0");
        }

        if (config.getIdleTimeout() != null && config.getIdleTimeout() <= 0) {
            errors.add("空闲超时时间必须大于0");
        }

        if (config.getMaxLifetime() != null && config.getMaxLifetime() <= 0) {
            errors.add("连接最大生存时间必须大于0");
        }

        if (!errors.isEmpty()) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_CONFIG_INVALID,
                "Dameng配置验证失败: " + String.join(", ", errors)
            );
        }
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        try {
            DamengConfig damengConfig = convertMapToConfig(config);
            validateConfigObject(damengConfig);
            return ValidationResult.valid();
        } catch (MiddlewareTechnicalException e) {
            logger.error("验证Dameng配置失败", e);
            return ValidationResult.invalid(List.of(e.getMessage()));
        } catch (Exception e) {
            logger.error("验证Dameng配置失败", e);
            return ValidationResult.invalid(List.of("配置格式错误: " + e.getMessage()));
        }
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            logger.info("开始测试Dameng数据库连接");

            DamengConfig damengConfig = convertMapToConfig(config);

            // 使用DriverManager直接创建连接进行测试，避免资源泄露
            try (Connection connection = createTestConnection(damengConfig)) {
                // 测试连接是否有效
                if (connection.isValid(5)) {
                    Map<String, Object> details = new HashMap<>();
                    details.put("databaseProductName", connection.getMetaData().getDatabaseProductName());
                    details.put("databaseProductVersion", connection.getMetaData().getDatabaseProductVersion());
                    details.put("driverName", connection.getMetaData().getDriverName());
                    details.put("driverVersion", connection.getMetaData().getDriverVersion());

                    return ConnectionTestResult.success("连接成功", details);
                } else {
                    return ConnectionTestResult.failure("连接无效");
                }
            }
        } catch (Exception e) {
            logger.error("测试Dameng数据库连接失败", e);
            return ConnectionTestResult.failure("连接失败: " + e.getMessage());
        }
    }

    @Override
    protected DamengResource doCreateResource(String id, String name, String description, DamengConfig config) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建Dameng资源: id={}, name={}, host={}:{}", 
                id, name, config.getHost(), config.getPort());

            // 创建数据源
            DataSource dataSource = createDataSource(config);

            // 创建Dameng资源实例
            DamengResource resource = new DamengResource(id, getType(), name, description, dataSource);
            
            logger.info("Dameng资源创建成功: id={}, name={}", id, name);
            return resource;
            
        } catch (Exception e) {
            logger.error("创建Dameng资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建Dameng资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected String getConfigString(DamengConfig config) {
        // 隐藏敏感信息
        return String.format("DamengConfig{host='%s', port=%d, schema='%s', username='%s', password='***', maxPoolSize=%d}",
            config.getHost(), config.getPort(), config.getSchema(),
            config.getUsername(), config.getMaxPoolSize());
    }

    /**
     * 创建测试连接
     *
     * @param config Dameng配置
     * @return 数据库连接
     * @throws SQLException SQL异常
     */
    private Connection createTestConnection(DamengConfig config) throws SQLException {
        String url = config.getJdbcUrl();
        return DriverManager.getConnection(url, config.getUsername(), config.getPassword());
    }

    /**
     * 创建数据源
     *
     * @param config Dameng配置
     * @return 数据源
     */
    protected DataSource createDataSource(DamengConfig config) {
        HikariConfig hikariConfig = new HikariConfig();
        
        // 基本连接信息
        hikariConfig.setJdbcUrl(config.getJdbcUrl());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setDriverClassName(config.getDriverClassName());
        
        // 连接池配置
        hikariConfig.setMinimumIdle(config.getMinPoolSize());
        hikariConfig.setMaximumPoolSize(config.getMaxPoolSize());
        hikariConfig.setConnectionTimeout(config.getConnectionTimeout());
        hikariConfig.setIdleTimeout(config.getIdleTimeout());
        hikariConfig.setMaxLifetime(config.getMaxLifetime());
        hikariConfig.setLeakDetectionThreshold(config.getLeakDetectionThreshold());
        
        // 连接验证
        hikariConfig.setConnectionTestQuery(config.getValidationQuery());
        hikariConfig.setValidationTimeout(config.getValidationTimeout() * 1000); // 转换为毫秒
        
        // 连接池名称
        hikariConfig.setPoolName(config.getPoolName());
        
        // 自动提交
        hikariConfig.setAutoCommit(config.getAutoCommit());
        
        // JMX监控
        hikariConfig.setRegisterMbeans(config.getJmxEnabled());
        
        // 连接初始化SQL
        if (StringUtils.hasText(config.getConnectionInitSql())) {
            hikariConfig.setConnectionInitSql(config.getConnectionInitSql());
        }
        
        // 事务隔离级别
        if (StringUtils.hasText(config.getTransactionIsolation())) {
            hikariConfig.setTransactionIsolation(config.getTransactionIsolation());
        }

        return new HikariDataSource(hikariConfig);
    }
}
