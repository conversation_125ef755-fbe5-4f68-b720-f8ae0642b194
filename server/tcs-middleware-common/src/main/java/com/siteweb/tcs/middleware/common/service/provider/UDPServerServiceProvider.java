package com.siteweb.tcs.middleware.common.service.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.UDPServerServiceConfig;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.UDPServerResource;
import com.siteweb.tcs.middleware.common.service.Service;
import com.siteweb.tcs.middleware.common.service.ServiceProvider;
import com.siteweb.tcs.middleware.common.service.ServiceType;
import com.siteweb.tcs.middleware.common.service.udp.UDPServerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * UDP服务器服务提供者
 * 负责创建和配置UDP服务器服务实例
 * 
 * <AUTHOR> for UDP Middleware Framework
 */
@Component
public class UDPServerServiceProvider implements ServiceProvider<UDPServerService> {

    private static final Logger logger = LoggerFactory.getLogger(UDPServerServiceProvider.class);

    @Override
    public String getType() {
        return ServiceType.UDP_SERVER.getCode();
    }

    @Override
    public String getSupportedResourceCategory() {
        return ServiceType.UDP_SERVER.getSupportedResourceCategory();
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        List<String> errors = new ArrayList<>();

        try {
            UDPServerServiceConfig parsedConfig = parseConfig(config);
            if (!validateConfigInternal(parsedConfig)) {
                errors.add("UDP服务器服务配置验证失败");
            }
        } catch (Exception e) {
            errors.add("UDP服务器服务配置解析失败: " + e.getMessage());
        }

        return errors.isEmpty() ? ValidationResult.valid() : ValidationResult.invalid(errors);
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            UDPServerServiceConfig parsedConfig = parseConfig(config);
            // 连接测试逻辑在创建服务时进行
            return ConnectionTestResult.success("UDP服务器服务连接测试成功");
        } catch (Exception e) {
            return ConnectionTestResult.failure("UDP服务器服务连接测试失败: " + e.getMessage());
        }
    }

    @Override
    public UDPServerService createService(String id, String name, String description,
                                        Map<String, Object> config, Resource resource)
            throws MiddlewareTechnicalException {
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.SERVICE_CONFIG_INVALID,
                    "UDP服务器服务配置验证失败: " + String.join(", ", validationResult.getErrors())
                );
            }

            UDPServerServiceConfig parsedConfig = parseConfig(config);
            return doCreateService(id, name, description, parsedConfig, resource);
        } catch (MiddlewareBusinessException | MiddlewareTechnicalException e) {
            throw e;
        } catch (Exception e) {
            logger.error("创建UDP服务器服务失败", e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "创建UDP服务器服务失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public void destroyService(Service service) throws MiddlewareTechnicalException {
        if (service != null) {
            try {
                service.destroy();
            } catch (Exception e) {
                logger.error("销毁UDP服务器服务失败: {}", service.getId(), e);
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_DESTROY_FAILED,
                    "销毁UDP服务器服务失败: " + e.getMessage(),
                    e
                );
            }
        }
    }

    /**
     * 解析配置Map为UDPServerServiceConfig对象
     */
    private UDPServerServiceConfig parseConfig(Map<String, Object> configMap) throws MiddlewareTechnicalException {
        try {
            UDPServerServiceConfig config = new UDPServerServiceConfig();

            // ==================== 流量统计配置 ====================
            if (configMap.containsKey("maxTrafficStatsRecords")) {
                Object value = configMap.get("maxTrafficStatsRecords");
                if (value instanceof Number) {
                    config.setMaxTrafficStatsRecords(((Number) value).intValue());
                }
            }

            if (configMap.containsKey("enableTrafficStatsRecording")) {
                config.setEnableTrafficStatsRecording((Boolean) configMap.get("enableTrafficStatsRecording"));
            }

            if (configMap.containsKey("trafficStatsRetentionHours")) {
                Object value = configMap.get("trafficStatsRetentionHours");
                if (value instanceof Number) {
                    config.setTrafficStatsRetentionHours(((Number) value).intValue());
                }
            }

            // ==================== 错误记录配置 ====================
            if (configMap.containsKey("enableErrorLogging")) {
                config.setEnableErrorLogging((Boolean) configMap.get("enableErrorLogging"));
            }

            if (configMap.containsKey("errorLogRetentionHours")) {
                Object value = configMap.get("errorLogRetentionHours");
                if (value instanceof Number) {
                    config.setErrorLogRetentionHours(((Number) value).intValue());
                }
            }

            if (configMap.containsKey("maxErrorRecords")) {
                Object value = configMap.get("maxErrorRecords");
                if (value instanceof Number) {
                    config.setMaxErrorRecords(((Number) value).intValue());
                }
            }

            // ==================== 性能监控配置 ====================
            if (configMap.containsKey("enablePerformanceMonitoring")) {
                config.setEnablePerformanceMonitoring((Boolean) configMap.get("enablePerformanceMonitoring"));
            }

            if (configMap.containsKey("performanceStatsSampleIntervalSeconds")) {
                Object value = configMap.get("performanceStatsSampleIntervalSeconds");
                if (value instanceof Number) {
                    config.setPerformanceStatsSampleIntervalSeconds(((Number) value).intValue());
                }
            }

            if (configMap.containsKey("performanceStatsRetentionHours")) {
                Object value = configMap.get("performanceStatsRetentionHours");
                if (value instanceof Number) {
                    config.setPerformanceStatsRetentionHours(((Number) value).intValue());
                }
            }

            // ==================== 数据包处理配置 ====================
            if (configMap.containsKey("enablePacketSizeStats")) {
                config.setEnablePacketSizeStats((Boolean) configMap.get("enablePacketSizeStats"));
            }

            if (configMap.containsKey("packetSizeStatsGroupInterval")) {
                Object value = configMap.get("packetSizeStatsGroupInterval");
                if (value instanceof Number) {
                    config.setPacketSizeStatsGroupInterval(((Number) value).intValue());
                }
            }

            // ==================== IP过滤配置 ====================
            if (configMap.containsKey("enableSourceIpWhitelist")) {
                config.setEnableSourceIpWhitelist((Boolean) configMap.get("enableSourceIpWhitelist"));
            }

            if (configMap.containsKey("sourceIpWhitelist")) {
                @SuppressWarnings("unchecked")
                List<String> whitelist = (List<String>) configMap.get("sourceIpWhitelist");
                config.setSourceIpWhitelist(whitelist);
            }

            if (configMap.containsKey("enableSourceIpBlacklist")) {
                config.setEnableSourceIpBlacklist((Boolean) configMap.get("enableSourceIpBlacklist"));
            }

            if (configMap.containsKey("sourceIpBlacklist")) {
                @SuppressWarnings("unchecked")
                List<String> blacklist = (List<String>) configMap.get("sourceIpBlacklist");
                config.setSourceIpBlacklist(blacklist);
            }

            // ==================== 限流配置 ====================
            if (configMap.containsKey("enableGlobalRateLimit")) {
                config.setEnableGlobalRateLimit((Boolean) configMap.get("enableGlobalRateLimit"));
            }

            if (configMap.containsKey("globalMaxPacketsPerSecond")) {
                Object value = configMap.get("globalMaxPacketsPerSecond");
                if (value instanceof Number) {
                    config.setGlobalMaxPacketsPerSecond(((Number) value).intValue());
                }
            }

            if (configMap.containsKey("enablePerSourceIpRateLimit")) {
                config.setEnablePerSourceIpRateLimit((Boolean) configMap.get("enablePerSourceIpRateLimit"));
            }

            if (configMap.containsKey("perSourceIpMaxPacketsPerSecond")) {
                Object value = configMap.get("perSourceIpMaxPacketsPerSecond");
                if (value instanceof Number) {
                    config.setPerSourceIpMaxPacketsPerSecond(((Number) value).intValue());
                }
            }

            if (configMap.containsKey("rateLimitWindowSeconds")) {
                Object value = configMap.get("rateLimitWindowSeconds");
                if (value instanceof Number) {
                    config.setRateLimitWindowSeconds(((Number) value).intValue());
                }
            }

            return config;
        } catch (Exception e) {
            logger.error("解析UDP服务器服务配置失败", e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.CONFIGURATION_INVALID,
                "解析UDP服务器服务配置失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 创建UDP服务器服务实例
     */
    protected UDPServerService doCreateService(String id, String name, String description,
                                             UDPServerServiceConfig config, Resource resource)
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建UDP服务器服务: id={}, name={}", id, name);

            // 验证资源类型
            if (!(resource instanceof UDPServerResource)) {
                throw new MiddlewareTechnicalException(
                    MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID.getCode(),
                    "不支持的资源类型，需要UDPServerResource，实际类型: " + resource.getClass().getName()
                );
            }

            // 创建UDP服务器服务（使用新的构造函数，传入配置）
            UDPServerService service = new UDPServerService(id, name, description, resource, config);

            logger.info("UDP服务器服务创建成功: id={}, name={}", id, name);
            return service;

        } catch (Exception e) {
            logger.error("创建UDP服务器服务失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "创建UDP服务器服务失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 验证配置内部逻辑
     */
    private boolean validateConfigInternal(UDPServerServiceConfig config) {
        if (config == null) {
            logger.error("UDP服务器服务配置不能为空");
            return false;
        }

        // 验证流量统计配置
        if (config.getMaxTrafficStatsRecords() < -1) {
            logger.error("流量统计记录最大数量不能小于-1");
            return false;
        }

        if (config.getTrafficStatsRetentionHours() < 0) {
            logger.error("流量统计记录保留时间不能小于0");
            return false;
        }

        // 验证错误记录配置
        if (config.getMaxErrorRecords() < -1) {
            logger.error("错误记录最大数量不能小于-1");
            return false;
        }

        if (config.getErrorLogRetentionHours() < 0) {
            logger.error("错误日志保留时间不能小于0");
            return false;
        }

        // 验证性能监控配置
        if (config.getPerformanceStatsSampleIntervalSeconds() <= 0) {
            logger.error("性能统计采样间隔必须大于0");
            return false;
        }

        if (config.getPerformanceStatsRetentionHours() < 0) {
            logger.error("性能统计保留时间不能小于0");
            return false;
        }

        // 验证数据包处理配置
        if (config.getPacketSizeStatsGroupInterval() <= 0) {
            logger.error("数据包大小统计分组间隔必须大于0");
            return false;
        }

        // 验证限流配置
        if (config.getGlobalMaxPacketsPerSecond() <= 0) {
            logger.error("全局每秒最大数据包数必须大于0");
            return false;
        }

        if (config.getPerSourceIpMaxPacketsPerSecond() <= 0) {
            logger.error("每个来源IP每秒最大数据包数必须大于0");
            return false;
        }

        if (config.getRateLimitWindowSeconds() <= 0) {
            logger.error("限流窗口大小必须大于0");
            return false;
        }

        return true;
    }

    /**
     * 测试连接内部逻辑
     */
    private boolean testConnectionInternal(UDPServerServiceConfig config, Resource resource) {
        try {
            // 验证资源类型
            if (!(resource instanceof UDPServerResource)) {
                logger.error("资源类型不匹配，需要UDPServerResource");
                return false;
            }

            UDPServerResource udpResource = (UDPServerResource) resource;

            // 检查资源健康状态
            if (!udpResource.checkHealth().isUp()) {
                logger.error("UDP服务器资源不健康");
                return false;
            }

            logger.info("UDP服务器服务连接测试成功");
            return true;
        } catch (Exception e) {
            logger.error("UDP服务器服务连接测试失败", e);
            return false;
        }
    }

    // ==================== 预定义配置方法 ====================

    /**
     * 创建默认UDP服务器服务配置
     */
    public static UDPServerServiceConfig createDefaultConfig() {
        UDPServerServiceConfig config = new UDPServerServiceConfig();
        // 使用默认值，已在UDPServerServiceConfig中设置
        return config;
    }

    /**
     * 创建高性能UDP服务器服务配置
     */
    public static UDPServerServiceConfig createHighPerformanceConfig() {
        UDPServerServiceConfig config = new UDPServerServiceConfig();
        config.setMaxTrafficStatsRecords(50000);
        config.setEnableTrafficStatsRecording(true);
        config.setTrafficStatsRetentionHours(48);
        config.setEnableErrorLogging(true);
        config.setErrorLogRetentionHours(72);
        config.setMaxErrorRecords(10000);
        config.setEnablePerformanceMonitoring(true);
        config.setPerformanceStatsSampleIntervalSeconds(30);
        config.setPerformanceStatsRetentionHours(24);
        config.setEnablePacketSizeStats(true);
        config.setPacketSizeStatsGroupInterval(1024);
        return config;
    }

    /**
     * 创建企业级UDP服务器服务配置
     */
    public static UDPServerServiceConfig createEnterpriseConfig() {
        UDPServerServiceConfig config = new UDPServerServiceConfig();
        config.setMaxTrafficStatsRecords(100000);
        config.setEnableTrafficStatsRecording(true);
        config.setTrafficStatsRetentionHours(168); // 7天
        config.setEnableErrorLogging(true);
        config.setErrorLogRetentionHours(168); // 7天
        config.setMaxErrorRecords(20000);
        config.setEnablePerformanceMonitoring(true);
        config.setPerformanceStatsSampleIntervalSeconds(60);
        config.setPerformanceStatsRetentionHours(72);
        config.setEnablePacketSizeStats(true);
        config.setPacketSizeStatsGroupInterval(512);
        config.setEnableGlobalRateLimit(true);
        config.setGlobalMaxPacketsPerSecond(50000);
        config.setEnablePerSourceIpRateLimit(true);
        config.setPerSourceIpMaxPacketsPerSecond(1000);
        config.setRateLimitWindowSeconds(1);
        return config;
    }

    /**
     * 创建安全增强UDP服务器服务配置
     */
    public static UDPServerServiceConfig createSecurityEnhancedConfig() {
        UDPServerServiceConfig config = createDefaultConfig();
        config.setEnableSourceIpWhitelist(true);
        config.setEnableSourceIpBlacklist(true);
        config.setEnableGlobalRateLimit(true);
        config.setGlobalMaxPacketsPerSecond(10000);
        config.setEnablePerSourceIpRateLimit(true);
        config.setPerSourceIpMaxPacketsPerSecond(100);
        config.setRateLimitWindowSeconds(1);
        return config;
    }

    /**
     * 创建轻量级UDP服务器服务配置
     */
    public static UDPServerServiceConfig createLightweightConfig() {
        UDPServerServiceConfig config = new UDPServerServiceConfig();
        config.setMaxTrafficStatsRecords(1000);
        config.setEnableTrafficStatsRecording(true);
        config.setTrafficStatsRetentionHours(6);
        config.setEnableErrorLogging(true);
        config.setErrorLogRetentionHours(12);
        config.setMaxErrorRecords(500);
        config.setEnablePerformanceMonitoring(false);
        config.setEnablePacketSizeStats(false);
        config.setEnableGlobalRateLimit(false);
        config.setEnablePerSourceIpRateLimit(false);
        return config;
    }
}
