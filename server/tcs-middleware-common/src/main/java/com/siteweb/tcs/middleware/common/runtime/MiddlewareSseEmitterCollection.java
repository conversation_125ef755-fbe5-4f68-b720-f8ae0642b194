package com.siteweb.tcs.middleware.common.runtime;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 中间件SSE连接集合管理器
 * 用于管理Resource和Service日志流的SSE连接
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public class MiddlewareSseEmitterCollection {
    private final CopyOnWriteArrayList<SseEmitter> emitters = new CopyOnWriteArrayList<>();

    public void add(SseEmitter emitter) {
        emitters.add(emitter);
    }

    public void remove(SseEmitter emitter) {
        emitters.remove(emitter);
    }

    public void append(MiddlewareLogCollector.LogInfo logMessage) {
        for (SseEmitter emitter : emitters) {
            try {
                emitter.send(SseEmitter.event().name("append").data(logMessage));
            } catch (IOException e) {
                emitters.remove(emitter);
            }
        }
    }

    public boolean connected(SseEmitter emitter, Object logMessage) {
        try {
            emitter.send(SseEmitter.event().name("connected").data(logMessage));
            return true;
        } catch (IOException e) {
            emitters.remove(emitter);
            return false;
        }
    }

    public void clean() {
        for (SseEmitter emitter : emitters) {
            try {
                emitter.send(SseEmitter.event().name("clean").data(""));
            } catch (IOException e) {
                emitters.remove(emitter);
            }
        }
    }
}
