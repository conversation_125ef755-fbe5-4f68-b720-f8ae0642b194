package com.siteweb.tcs.middleware.common.resource.http;

import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.model.http.HttpServerRequest;
import com.siteweb.tcs.middleware.common.model.http.HttpServerResponse;

import com.siteweb.tcs.middleware.common.resource.ResourceStatus;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorSystem;
import org.apache.pekko.http.javadsl.Http;
import org.apache.pekko.http.javadsl.ServerBinding;
import org.apache.pekko.http.javadsl.model.*;
import org.apache.pekko.http.javadsl.server.Route;
import org.apache.pekko.stream.Materializer;
import org.apache.pekko.stream.javadsl.Flow;

import java.net.InetSocketAddress;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * HTTP服务器资源
 * 基于Pekko Akka HTTP实现的HTTP服务器资源
 * <AUTHOR>
 */
@Slf4j
public class AkkaHttpServerResource extends HttpServerResource {

    @Getter
    private ServerBinding serverBinding;
    @Getter
    private final String host;
    @Getter
    private final int port;

    @Getter
    private final Duration idleTimeout;

    @Getter
    private final int backlog;

    @Getter
    private final long requestTimeoutMs;

    // Pekko Akka相关组件
    private final ActorSystem actorSystem;
    private final Materializer materializer;
    private final Http http;

    // 路由管理器和统计管理器
    private final RouteManager routeManager;
    private final StatisticsManager statisticsManager;

    /**
     * 构造函数
     *
     * @param id 资源ID
     * @param type 资源类型
     * @param name 资源名称
     * @param description 资源描述
     * @param host 主机地址
     * @param port 端口
     * @param idleTimeout 空闲超时时间（秒）
     * @param backlog 连接队列大小
     * @param requestTimeoutMs 请求体读取超时时间（毫秒）
     */
    public AkkaHttpServerResource(String id, String type, String name, String description,
                                  String host, int port, int idleTimeout, int backlog, long requestTimeoutMs) {
        super(id, type, name, description);
        this.host = host;
        this.port = port;
        this.idleTimeout = Duration.ofSeconds(idleTimeout);
        this.backlog = backlog;
        this.requestTimeoutMs = requestTimeoutMs;

        // 从ClusterContext获取ActorSystem和相关组件
        this.actorSystem = ClusterContext.system();
        this.materializer = ClusterContext.getMaterializer();
        this.http = ClusterContext.getHttp();

        if (this.actorSystem == null) {
            throw new IllegalStateException("ActorSystem未初始化，请确保ClusterContext已正确配置");
        }

        // 初始化路由管理器和统计管理器
        this.routeManager = new RouteManager();
        this.statisticsManager = new StatisticsManager();
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        log.debug("初始化HTTP服务器资源: {}", getId());
        // 初始化阶段不需要特殊操作
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        log.debug("启动HTTP服务器资源: {}", getId());
        try {
            // 创建动态路由（支持多路由管理）
            Route dynamicRoute = createDynamicRoute();

            // 启动HTTP服务器（异步方式）
            CompletionStage<ServerBinding> bindingFuture = http.newServerAt(host, port)
                    .bind(dynamicRoute);

            // 使用CompletableFuture等待绑定完成，避免Thread.sleep
            CompletableFuture<ServerBinding> bindingCompletableFuture = bindingFuture.toCompletableFuture();

            // 转换为Void类型的CompletableFuture
            CompletableFuture<Void> startFuture = bindingCompletableFuture
                    .thenAccept(binding -> {
                        serverBinding = binding;
                        InetSocketAddress address = binding.localAddress();
                        log.info("HTTP服务器启动成功: {}:{} ({})", address.getHostString(), address.getPort(), getId());
                    })
                    .exceptionally(throwable -> {
                        log.error("启动HTTP服务器资源失败: {}", getId(), throwable);
                        throw new RuntimeException(throwable);
                    });

            // 等待启动完成，使用超时避免无限等待
            startFuture.get(30, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.error("启动HTTP服务器资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                    "启动HTTP服务器资源失败: " + e.getMessage(),
                    e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        log.debug("停止HTTP服务器资源: {}", getId());
        try {
            if (serverBinding != null) {
                // 异步优雅地停止HTTP服务器
                CompletableFuture<Void> stopFuture = serverBinding.terminate(Duration.ofSeconds(10))
                        .toCompletableFuture()
                        .thenAccept(terminated -> {
                            log.info("HTTP服务器停止成功: {}", getId());
                        })
                        .exceptionally(throwable -> {
                            log.error("停止HTTP服务器资源失败: {}", getId(), throwable);
                            return null;
                        });

                // 等待停止完成，但使用超时避免无限等待
                stopFuture.get(15, TimeUnit.SECONDS);
                serverBinding = null;
            }
        } catch (Exception e) {
            log.error("停止HTTP服务器资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_STOP_FAILED,
                    "停止HTTP服务器资源失败: " + e.getMessage(),
                    e
            );
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        log.debug("销毁HTTP服务器资源: {}", getId());
        try {
            // 确保服务器已停止
            if (serverBinding != null) {
                CompletableFuture<Void> destroyFuture = serverBinding.terminate(Duration.ofSeconds(5))
                        .toCompletableFuture()
                        .thenAccept(terminated -> {
                            log.info("HTTP服务器资源销毁成功: {}", getId());
                        })
                        .exceptionally(throwable -> {
                            log.error("销毁HTTP服务器资源失败: {}", getId(), throwable);
                            return null;
                        });

                // 等待销毁完成，但使用超时避免无限等待
                destroyFuture.get(10, TimeUnit.SECONDS);
                serverBinding = null;
            }

            // 关闭统计管理器
            if (statisticsManager != null) {
                statisticsManager.shutdown();
            }
        } catch (Exception e) {
            log.error("销毁HTTP服务器资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_DESTROY_FAILED,
                    "销毁HTTP服务器资源失败: " + e.getMessage(),
                    e
            );
        }
    }

    @Override
    public HealthStatus checkHealth() {
        if (serverBinding == null) {
            return HealthStatus.down("HTTP服务器未启动");
        }

        try {
            InetSocketAddress address = serverBinding.localAddress();
            Map<String, Object> details = new HashMap<>();
            details.put("host", address.getHostString());
            details.put("port", address.getPort());
            details.put("idleTimeout", idleTimeout.getSeconds() + "s");
            details.put("backlog", backlog);
            details.put("requestTimeoutMs", requestTimeoutMs + "ms");

            return HealthStatus.up("HTTP服务器运行正常", details);
        } catch (Exception e) {
            log.error("检查HTTP服务器健康状态失败: {}", getId(), e);
            return HealthStatus.down("检查健康状态失败: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getNativeResource() {
        return (T) serverBinding;
    }

    /**
     * 绑定自定义路由（旧版本方法，建议使用新的接口方法）
     *
     * @param route 自定义路由
     * @return 绑定完成的Future
     * @throws MiddlewareTechnicalException 如果绑定失败
     * @deprecated 使用 {@link #bindRoute(String, Function)} 替代
     */
    @Deprecated
    public CompletableFuture<ServerBinding> bindRoute(Route route) throws MiddlewareTechnicalException {
        if (getStatus() != ResourceStatus.STARTED) {
            throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                    "HTTP服务器未启动，无法绑定路由"
            );
        }

        // 异步停止当前绑定并创建新绑定
        CompletableFuture<Void> terminateFuture = CompletableFuture.completedFuture(null);
        if (serverBinding != null) {
            terminateFuture = serverBinding.terminate(Duration.ofSeconds(5))
                    .toCompletableFuture()
                    .thenApply(terminated -> null);
        }

        return terminateFuture.thenCompose(v -> {
            // 创建新的绑定
            CompletionStage<ServerBinding> bindingFuture = http.newServerAt(host, port)
                    .bind(route);

            return bindingFuture.toCompletableFuture();
        }).thenApply(binding -> {
            serverBinding = binding;
            InetSocketAddress address = binding.localAddress();
            log.info("HTTP服务器路由更新成功: {}:{} ({})", address.getHostString(), address.getPort(), getId());
            return binding;
        }).exceptionally(throwable -> {
            log.error("绑定HTTP服务器路由失败: {}", getId(), throwable);
            throw new RuntimeException(new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                    "绑定HTTP服务器路由失败: " + throwable.getMessage(),
                    throwable
            ));
        });
    }
    public CompletableFuture<Object> startServerAsync(Route route) {
        return http.newServerAt(host, port)
                .bind(route)
                .thenApply(binding -> {
                    this.serverBinding = binding;
                    log.info("HTTP服务器启动成功: {}", binding.localAddress());
                    return null;
                }).toCompletableFuture();
    }

    /**
     * 绑定自定义处理流（旧版本方法，建议使用新的接口方法）
     *
     * @param handler 自定义处理流
     * @return 绑定完成的Future
     * @throws MiddlewareTechnicalException 如果绑定失败
     * @deprecated 使用新的路由绑定方法替代
     */
    @Deprecated
    public CompletableFuture<ServerBinding> bindHandler(Flow<HttpRequest, HttpResponse, ?> handler) throws MiddlewareTechnicalException {
        if (getStatus() != ResourceStatus.STARTED) {
            throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                    "HTTP服务器未启动，无法绑定处理器"
            );
        }

        try {
            // 异步停止当前绑定
            CompletableFuture<Void> terminateFuture = CompletableFuture.completedFuture(null);
            if (serverBinding != null) {
                terminateFuture = serverBinding.terminate(Duration.ofSeconds(5))
                        .toCompletableFuture()
                        .thenApply(terminated -> null);
            }

            // 异步创建新的绑定
            return terminateFuture.thenCompose(v -> {
                CompletionStage<ServerBinding> bindingFuture = http.newServerAt(host, port)
                        .bindFlow(handler);

                return bindingFuture.toCompletableFuture();
            }).thenApply(binding -> {
                serverBinding = binding;
                InetSocketAddress address = binding.localAddress();
                log.info("HTTP服务器处理器更新成功: {}:{} ({})", address.getHostString(), address.getPort(), getId());
                return binding;
            }).exceptionally(throwable -> {
                log.error("绑定HTTP服务器处理器失败: {}", getId(), throwable);
                throw new RuntimeException(new MiddlewareTechnicalException(
                        MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                        "绑定HTTP服务器处理器失败: " + throwable.getMessage(),
                        throwable
                ));
            });
        } catch (Exception e) {
            log.error("绑定HTTP服务器处理器失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                    "绑定HTTP服务器处理器失败: " + e.getMessage(),
                    e
            );
        }
    }

    /**
     * 创建默认路由
     *
     * @return 默认路由
     */
    private Route createDefaultRoute() {
        return org.apache.pekko.http.javadsl.server.Directives.get(() ->
                org.apache.pekko.http.javadsl.server.Directives.path("", () ->
                        org.apache.pekko.http.javadsl.server.Directives.complete("HTTP Server is running")
                )
        );
    }


    // ==================== HttpServerResourceInterface 实现 ====================

    @Override
    public CompletableFuture<Void> bindRoute(String path, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> handler)
            throws MiddlewareTechnicalException {
        return bindRouteAsyncInternal(path, null, handler);
    }

    @Override
    public CompletableFuture<Void> bindPost(String path, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> handler)
            throws MiddlewareTechnicalException {
        return bindRouteAsyncInternal(path, "POST", handler);
    }

    @Override
    public CompletableFuture<Void> bindGet(String path, Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> handler)
            throws MiddlewareTechnicalException {
        return bindRouteAsyncInternal(path, "GET", handler);
    }

    @Override
    public CompletableFuture<Void> unbindRoute(String path) throws MiddlewareTechnicalException {
        if (getStatus() != ResourceStatus.STARTED) {
            throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                    "HTTP服务器未启动，无法移除路由"
            );
        }

        try {
            int removedCount = routeManager.unregisterAllRoutes(path);
            log.info("移除路由: path={}, removedCount={}", path, removedCount);
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("移除路由失败: path={}", path, e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                    "移除路由失败: " + e.getMessage(),
                    e
            );
        }
    }

    @Override
    public List<String> getBoundPaths() {
        return routeManager.getAllPaths();
    }

    @Override
    public InetSocketAddress getServerAddress() {
        if (serverBinding != null) {
            return serverBinding.localAddress();
        }
        return new InetSocketAddress(host, port);
    }

    @Override
    public long getRequestCount() {
        return statisticsManager.getTotalRequestCount();
    }

    @Override
    public Map<String, Long> getRequestCountBySourceIP() {
        return statisticsManager.getRequestCountBySourceIPMap();
    }

    @Override
    public List<String> getSourceIPs() {
        return statisticsManager.getSourceIPs();
    }

    @Override
    public long getRequestCountBySourceIP(String sourceIP) {
        return statisticsManager.getRequestCountBySourceIP(sourceIP);
    }

    // 注意：解析失败记录功能已移至Service层

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getNativeServer() {
        return (T) serverBinding;
    }

    @Override
    public void clearStatistics() {
        statisticsManager.clearStatistics();
    }

    @Override
    public Map<String, Object> getStatisticsSummary() {
        return statisticsManager.getStatisticsSummary();
    }

    // 注意：clearParsingFailureRecords方法已移至Service层

    // ==================== 私有辅助方法 ====================

    /**
     * 内部异步路由绑定方法
     */
    private CompletableFuture<Void> bindRouteAsyncInternal(String path, String method,
                                                           Function<HttpServerRequest, CompletableFuture<HttpServerResponse>> handler)
            throws MiddlewareTechnicalException {
        if (getStatus() != ResourceStatus.STARTED) {
            throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                    "HTTP服务器未启动，无法绑定路由"
            );
        }

        try {
            routeManager.registerAsyncRoute(path, method, handler);
            log.info("绑定路由: method={}, path={}", method != null ? method : "ALL", path);
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("绑定路由失败: method={}, path={}", method, path, e);
            throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SYSTEM_ERROR,
                    "绑定路由失败: " + e.getMessage(),
                    e
            );
        }
    }

    /**
     * 创建动态路由（支持多路由管理）
     */
    private Route createDynamicRoute() {
        return org.apache.pekko.http.javadsl.server.Directives.extractRequest(request ->
                org.apache.pekko.http.javadsl.server.Directives.extractClientIP(clientIP ->
                        org.apache.pekko.http.javadsl.server.Directives.extractMatchedPath(matchedPath -> {
                            try {
                                // 获取客户端IP地址
                                String sourceIP = clientIP.getAddress().isPresent() ? clientIP.getAddress().get().getHostAddress() : "unknown";
                                Integer sourcePort = clientIP.getPort();

                                // 计算请求大小
                                long requestBytes = calculateRequestSize(request);
                                statisticsManager.recordRequestWithTraffic(sourceIP, requestBytes);

                                // 查找匹配的路由
                                String requestPath = request.getUri().path();
                                String requestMethod = request.method().value();

                                RouteManager.RouteInfo routeInfo = routeManager.findRoute(requestPath, requestMethod);

                                if (routeInfo != null) {
                                    // 记录请求开始时间
                                    long requestStartTime = System.currentTimeMillis();

                                    // 创建远程和本地地址
                                    InetSocketAddress remoteAddr = new InetSocketAddress(sourceIP, sourcePort);
                                    InetSocketAddress localAddr = getServerAddress();

                                    // 使用请求处理器
                                    CompletableFuture<HttpResponse> responseFuture = AsyncHttpRequestProcessor.processAkkaRequest(
                                        request, remoteAddr, localAddr, requestTimeoutMs, materializer, routeInfo.getAsyncHandler()
                                    );

                                    // 使用 completeWithFuture 处理异步响应
                                    return org.apache.pekko.http.javadsl.server.Directives.completeWithFuture(
                                            responseFuture.whenComplete((response, throwable) -> {
                                                // 计算响应时间
                                                long responseTime = System.currentTimeMillis() - requestStartTime;

                                                if (throwable == null) {
                                                    // 成功响应，记录统计
                                                    long responseBytes = calculateResponseSize(response);
                                                    statisticsManager.recordResponse(responseBytes, responseTime);
                                                } else {
                                                    // 错误响应，记录错误统计
                                                    if (isTimeoutException(throwable)) {
                                                        statisticsManager.recordTimeoutError(sourceIP, requestPath, requestMethod,
                                                                                           responseTime, throwable.getMessage());
                                                    }
                                                    log.error("异步处理请求失败: path={}, method={}", requestPath, requestMethod, throwable);
                                                }
                                            })
                                    );
                                } else {
                                    // 没有找到匹配的路由，返回404
                                    return org.apache.pekko.http.javadsl.server.Directives.complete(
                                            HttpResponse.create().withStatus(404).withEntity("Not Found: " + requestPath)
                                    );
                                }
                            } catch (Exception e) {
                                log.error("处理请求失败: path={}, method={}", request.getUri().path(), request.method().value(), e);

                                // 注意：错误记录功能已移至Service层，这里只记录日志
                                return org.apache.pekko.http.javadsl.server.Directives.complete(
                                        HttpResponse.create().withStatus(500).withEntity("Internal Server Error")
                                );
                            }
                        })
                )
        );
    }

    /**
     * 计算请求大小（字节）
     */
    private long calculateRequestSize(HttpRequest request) {
        try {
            // 计算请求头大小
            long headerSize = 0;
            for (HttpHeader header : request.getHeaders()) {
                headerSize += header.name().length() + header.value().length() + 4; // +4 for ": " and "\r\n"
            }

            // 计算请求行大小
            String requestLine = request.method().value() + " " + request.getUri().toString() + " HTTP/1.1\r\n";
            long requestLineSize = requestLine.getBytes().length;

            // 计算请求体大小
            long bodySize = 0;
            if (request.entity() != null && !request.entity().isKnownEmpty()) {
                // 对于已知大小的实体，直接获取大小
                if (request.entity().getContentLengthOption().isPresent()) {
                    bodySize = request.entity().getContentLengthOption().getAsLong();
                } else {
                    // 对于未知大小的实体，估算为0（避免阻塞）
                    bodySize = 0;
                }
            }

            return requestLineSize + headerSize + bodySize;
        } catch (Exception e) {
            log.warn("计算请求大小失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 计算响应大小（字节）
     */
    private long calculateResponseSize(HttpServerResponse response) {
        try {
            // 计算响应头大小
            long headerSize = response.getHeaders().entrySet().stream()
                    .mapToLong(entry -> entry.getKey().length() + entry.getValue().length() + 4) // +4 for ": " and "\r\n"
                    .sum();

            // 计算状态行大小
            String statusLine = "HTTP/1.1 " + response.getStatus() + "\r\n";
            long statusLineSize = statusLine.getBytes().length;

            // 计算响应体大小
            long bodySize = 0;
            if (response.getBody() != null) {
                bodySize = response.getBody().getBytes().length;
            }

            return statusLineSize + headerSize + bodySize;
        } catch (Exception e) {
            log.warn("计算响应大小失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 计算响应大小（字节）- HttpResponse版本
     */
    private long calculateResponseSize(HttpResponse response) {
        try {
            // 计算响应头大小
            long headerSize = 0;
            for (HttpHeader header : response.getHeaders()) {
                headerSize += header.name().length() + header.value().length() + 4; // +4 for ": " and "\r\n"
            }

            // 计算状态行大小
            String statusLine = "HTTP/1.1 " + response.status().intValue() + " " + response.status().reason() + "\r\n";
            long statusLineSize = statusLine.getBytes().length;

            // 计算响应体大小
            long bodySize = response.entity().getContentLengthOption().orElse(0L);

            return statusLineSize + headerSize + bodySize;
        } catch (Exception e) {
            log.warn("计算响应大小失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 判断是否是超时异常
     */
    private boolean isTimeoutException(Throwable throwable) {
        if (throwable == null) {
            return false;
        }

        String message = throwable.getMessage();
        String className = throwable.getClass().getSimpleName();

        return message != null && (
                message.toLowerCase().contains("timeout") ||
                message.toLowerCase().contains("timed out") ||
                className.toLowerCase().contains("timeout")
        ) || throwable.getCause() != null && isTimeoutException(throwable.getCause());
    }




}
