package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.FileInfo;

import java.util.List;

/**
 * 文件系统资源抽象基类
 * 定义所有文件系统资源必须实现的方法
 */
public abstract class FileSystemResource extends BaseResource {

    protected FileSystemResource(String id, String type, String name, String description) {
        super(id, type, name, description);
    }

    /**
     * 写入文件
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param content 文件内容
     * @return 是否成功
     */
    public abstract boolean writeFile(String filePath, String fileName, byte[] content) throws MiddlewareTechnicalException;

    /**
     * 读取文件
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 文件内容
     */
    public abstract byte[] readFile(String filePath, String fileName) throws MiddlewareTechnicalException;

    /**
     * 删除文件
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 是否成功
     */
    public abstract boolean deleteFile(String filePath, String fileName) throws MiddlewareTechnicalException;

    /**
     * 列出文件
     * @param directoryPath 目录路径
     * @return 文件列表
     */
    public abstract List<FileInfo> listFiles(String directoryPath) throws MiddlewareTechnicalException;

    /**
     * 检查文件是否存在
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 是否存在
     */
    public abstract boolean fileExists(String filePath, String fileName) throws MiddlewareTechnicalException;

    /**
     * 创建目录
     * @param directoryPath 目录路径
     * @return 是否成功
     */
    public abstract boolean createDirectory(String directoryPath) throws MiddlewareTechnicalException;

    /**
     * 删除目录
     * @param directoryPath 目录路径
     * @param recursive 是否递归删除
     * @return 是否成功
     */
    public abstract boolean deleteDirectory(String directoryPath, boolean recursive) throws MiddlewareTechnicalException;

    /**
     * 获取文件信息
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 文件信息
     */
    public abstract FileInfo getFileInfo(String filePath, String fileName) throws MiddlewareTechnicalException;

    /**
     * 复制文件
     * @param sourcePath 源文件路径
     * @param sourceFileName 源文件名
     * @param targetPath 目标文件路径
     * @param targetFileName 目标文件名
     * @return 是否成功
     */
    public abstract boolean copyFile(String sourcePath, String sourceFileName, 
                                   String targetPath, String targetFileName) throws MiddlewareTechnicalException;

    /**
     * 移动文件
     * @param sourcePath 源文件路径
     * @param sourceFileName 源文件名
     * @param targetPath 目标文件路径
     * @param targetFileName 目标文件名
     * @return 是否成功
     */
    public abstract boolean moveFile(String sourcePath, String sourceFileName, 
                                   String targetPath, String targetFileName) throws MiddlewareTechnicalException;
}
