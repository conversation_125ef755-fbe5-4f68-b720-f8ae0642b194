package com.siteweb.tcs.middleware.common.service.impl;

import com.siteweb.tcs.common.service.IFileOperationService;
import com.siteweb.tcs.middleware.common.model.FileInfo;
import com.siteweb.tcs.middleware.common.service.FileSystemService;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件操作服务实现
 * 适配器模式，使用 FileSystemService 实现 IFileOperationService
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class FileOperationServiceImpl implements IFileOperationService {
    
    @Autowired
    private ServiceRegistry serviceRegistry;
    
    /**
     * 获取默认的文件系统服务
     * 可以根据配置选择不同的实现
     */
    @Bean
    private FileSystemService getFileSystemService() {
        try {
            // 根据配置获取特定的文件系统服务
            return (FileSystemService) serviceRegistry.get("auto-filesystem-service-main","tcs-middleware");
        } catch (Exception e) {
            log.error("Failed to get FileSystemService", e);
            return null;
        }
    }
    
    @Override
    public boolean writeFile(String path, String fileName, byte[] content) {
        FileSystemService service = getFileSystemService();
        if (service == null) {
            log.error("No FileSystemService available");
            return false;
        }
        
        try {
            var result = service.writeFile(path, fileName, content);
            return result.isSuccess();
        } catch (Exception e) {
            log.error("Write file failed: {}/{}", path, fileName, e);
            return false;
        }
    }
    
    @Override
    public byte[] readFile(String path, String fileName) {
        FileSystemService service = getFileSystemService();
        if (service == null) {
            log.error("No FileSystemService available");
            return null;
        }
        
        try {
            return service.readFile(path, fileName);
        } catch (Exception e) {
            log.error("Read file failed: {}/{}", path, fileName, e);
            return null;
        }
    }
    
    @Override
    public boolean deleteFile(String path, String fileName) {
        FileSystemService service = getFileSystemService();
        if (service == null) {
            log.error("No FileSystemService available");
            return false;
        }
        
        try {
            return service.deleteFile(path, fileName);
        } catch (Exception e) {
            log.error("Delete file failed: {}/{}", path, fileName, e);
            return false;
        }
    }
    
    @Override
    public boolean fileExists(String path, String fileName) {
        FileSystemService service = getFileSystemService();
        if (service == null) {
            log.error("No FileSystemService available");
            return false;
        }
        
        try {
            return service.fileExists(path, fileName);
        } catch (Exception e) {
            log.error("Check file exists failed: {}/{}", path, fileName, e);
            return false;
        }
    }
    
    @Override
    public boolean createDirectory(String path) {
        FileSystemService service = getFileSystemService();
        if (service == null) {
            log.error("No FileSystemService available");
            return false;
        }
        
        try {
            return service.createDirectory(path);
        } catch (Exception e) {
            log.error("Create directory failed: {}", path, e);
            return false;
        }
    }
    
    @Override
    public List<String> listFiles(String path) {
        FileSystemService service = getFileSystemService();
        if (service == null) {
            log.error("No FileSystemService available");
            return Collections.emptyList();
        }
        
        try {
            List<FileInfo> fileInfos = service.listFiles(path);
            return fileInfos.stream()
                .map(FileInfo::getFileName)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("List files failed: {}", path, e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public boolean copyFile(String sourcePath, String sourceFileName, String targetPath, String targetFileName) {
        FileSystemService service = getFileSystemService();
        if (service == null) {
            log.error("No FileSystemService available");
            return false;
        }
        
        try {
            return service.copyFile(sourcePath, sourceFileName, targetPath, targetFileName);
        } catch (Exception e) {
            log.error("Copy file failed: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName, e);
            return false;
        }
    }
    
    @Override
    public boolean moveFile(String sourcePath, String sourceFileName, String targetPath, String targetFileName) {
        FileSystemService service = getFileSystemService();
        if (service == null) {
            log.error("No FileSystemService available");
            return false;
        }
        
        try {
            return service.moveFile(sourcePath, sourceFileName, targetPath, targetFileName);
        } catch (Exception e) {
            log.error("Move file failed: {}/{} -> {}/{}", sourcePath, sourceFileName, targetPath, targetFileName, e);
            return false;
        }
    }
    
    @Override
    public boolean deleteDirectory(String path, boolean recursive) {
        FileSystemService service = getFileSystemService();
        if (service == null) {
            log.error("No FileSystemService available");
            return false;
        }
        
        try {
            return service.deleteDirectory(path, recursive);
        } catch (Exception e) {
            log.error("Delete directory failed: {}, recursive: {}", path, recursive, e);
            return false;
        }
    }
}
