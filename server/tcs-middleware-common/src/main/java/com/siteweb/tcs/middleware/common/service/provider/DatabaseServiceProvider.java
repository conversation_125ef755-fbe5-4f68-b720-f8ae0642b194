package com.siteweb.tcs.middleware.common.service.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import com.siteweb.tcs.middleware.common.service.DatabaseService;
import com.siteweb.tcs.middleware.common.service.Service;
import com.siteweb.tcs.middleware.common.service.ServiceProvider;
import com.siteweb.tcs.middleware.common.service.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据库服务提供者
 */
@Component
public class DatabaseServiceProvider implements ServiceProvider<Service> {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseServiceProvider.class);

    @Override
    public String getType() {
        return ServiceType.DATABASE.getCode();
    }

    @Override
    public String getSupportedResourceCategory() {
        return ServiceType.DATABASE.getSupportedResourceCategory();
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        List<String> errors = new ArrayList<>();

        // 验证配置
        if (config == null) {
            errors.add("配置不能为空");
            return ValidationResult.invalid(errors);
        }

        return errors.isEmpty() ? ValidationResult.valid() : ValidationResult.invalid(errors);
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                return ConnectionTestResult.failure("配置验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            // 数据库服务不需要额外的连接测试，因为它依赖于底层资源的连接
            return ConnectionTestResult.success("连接测试成功");
        } catch (Exception e) {
            logger.error("测试数据库服务连接失败", e);
            return ConnectionTestResult.failure("连接测试失败: " + e.getMessage());
        }
    }

    @Override
    public Service createService(String id, String name, String description, Map<String, Object> config, Resource resource) throws MiddlewareTechnicalException {
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.SERVICE_CONFIG_INVALID,
                    "配置验证失败: " + String.join(", ", validationResult.getErrors())
                );
            }

            // 验证资源类别
            String resourceType = resource.getType();
            if (!isSupportedResourceType(resourceType)) {
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                    "不支持的资源类型: " + resourceType
                );
            }

            // 创建数据库服务
            return new DatabaseService(id, name, description, resource);
        } catch (MiddlewareBusinessException | MiddlewareTechnicalException e) {
            throw e;
        } catch (Exception e) {
            logger.error("创建数据库服务失败", e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "创建数据库服务失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public void destroyService(Service service) throws MiddlewareTechnicalException {
        if (service instanceof DatabaseService) {
            try {
                // 服务销毁逻辑在Service接口的destroy方法中已实现
                // 这里可以添加额外的清理逻辑
                logger.info("数据库服务已销毁: {}", service.getId());
            } catch (Exception e) {
                logger.error("销毁数据库服务失败", e);
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_DESTROY_FAILED,
                    "销毁数据库服务失败: " + e.getMessage(),
                    e
                );
            }
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.SERVICE_TYPE_INVALID,
                "服务类型不匹配，期望DatabaseService，实际为" + service.getClass().getName()
            );
        }
    }

    /**
     * 判断资源类型是否支持
     *
     * @param resourceType 资源类型
     * @return 是否支持
     */
    private boolean isSupportedResourceType(String resourceType) {
        // 使用ResourceType枚举的fromCodeIgnoreCase方法，忽略大小写
        ResourceType type = ResourceType.fromCodeIgnoreCase(resourceType);

        // 支持的关系型数据库资源类型
        return type == ResourceType.MYSQL ||
               type == ResourceType.H2 ||
               type == ResourceType.POSTGRESQL;
    }
}
