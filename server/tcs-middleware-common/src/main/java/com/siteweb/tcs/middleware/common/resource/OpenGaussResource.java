package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.model.config.OpenGaussConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * OpenGauss数据库资源
 * 提供OpenGauss数据库连接和基本操作功能
 *
 * <AUTHOR>
 * @since 1.0
 */
public class OpenGaussResource extends BaseResource {

    private static final Logger logger = LoggerFactory.getLogger(OpenGaussResource.class);

    private final OpenGaussConfig config;
    private HikariDataSource dataSource;

    /**
     * 构造函数
     *
     * @param id OpenGauss资源ID
     * @param name OpenGauss资源名称
     * @param description OpenGauss资源描述
     * @param config OpenGauss配置
     */
    public OpenGaussResource(String id, String name, String description, OpenGaussConfig config) {
        super(id, "OPENGAUSS", name, description);
        this.config = config;
    }

    @Override
    public String getType() {
        return "OPENGAUSS";
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化OpenGauss资源: {}", getId());
        // 初始化阶段不需要特殊操作
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.info("启动OpenGauss资源: {}", getId());

        try {
            // 创建HikariCP配置
            HikariConfig hikariConfig = new HikariConfig();

            // 基本连接配置
            hikariConfig.setJdbcUrl(config.getJdbcUrl());
            hikariConfig.setUsername(config.getUsername());
            hikariConfig.setPassword(config.getPassword());
            hikariConfig.setDriverClassName(config.getDriverClassName());

            // 连接池配置
            hikariConfig.setMinimumIdle(config.getMinPoolSize());
            hikariConfig.setMaximumPoolSize(config.getMaxPoolSize());
            hikariConfig.setConnectionTimeout(config.getConnectionTimeout());
            hikariConfig.setIdleTimeout(config.getIdleTimeout());
            hikariConfig.setMaxLifetime(config.getMaxLifetime());
            hikariConfig.setLeakDetectionThreshold(config.getLeakDetectionThreshold());

            // 连接池名称
            hikariConfig.setPoolName(config.getPoolName() + "-" + getId());

            // 连接验证
            hikariConfig.setConnectionTestQuery(config.getValidationQuery());
            hikariConfig.setValidationTimeout(config.getValidationTimeout() * 1000L);

            // 连接初始化SQL
            if (config.getConnectionInitSql() != null && !config.getConnectionInitSql().trim().isEmpty()) {
                hikariConfig.setConnectionInitSql(config.getConnectionInitSql());
            }

            // 自动提交
            hikariConfig.setAutoCommit(config.getAutoCommit());

            // 事务隔离级别
            if (config.getTransactionIsolation() != null && !config.getTransactionIsolation().trim().isEmpty()) {
                hikariConfig.setTransactionIsolation(config.getTransactionIsolation());
            }

            // JMX监控
            hikariConfig.setRegisterMbeans(config.getJmxEnabled());

            // 创建数据源
            dataSource = new HikariDataSource(hikariConfig);

            // 测试连接
            testConnection();

            logger.info("OpenGauss资源启动成功: {}", getId());
        } catch (Exception e) {
            logger.error("启动OpenGauss资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                "启动OpenGauss资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.info("停止OpenGauss资源: {}", getId());

        try {
            if (dataSource != null && !dataSource.isClosed()) {
                dataSource.close();
                dataSource = null;
            }
            logger.info("OpenGauss资源停止成功: {}", getId());
        } catch (Exception e) {
            logger.error("停止OpenGauss资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_STOP_FAILED,
                "停止OpenGauss资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.info("销毁OpenGauss资源: {}", getId());

        try {
            if (dataSource != null && !dataSource.isClosed()) {
                dataSource.close();
                dataSource = null;
            }
            logger.info("OpenGauss资源销毁成功: {}", getId());
        } catch (Exception e) {
            logger.error("销毁OpenGauss资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_DESTROY_FAILED,
                "销毁OpenGauss资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public HealthStatus checkHealth() {
        try {
            if (dataSource == null || dataSource.isClosed()) {
                return HealthStatus.down("OpenGauss数据源未初始化或已关闭");
            }

            // 测试连接
            try (Connection connection = dataSource.getConnection()) {
                if (connection.isValid(5)) {
                    Map<String, Object> details = new HashMap<String, Object>();
                    details.put("activeConnections", dataSource.getHikariPoolMXBean().getActiveConnections());
                    details.put("idleConnections", dataSource.getHikariPoolMXBean().getIdleConnections());
                    details.put("totalConnections", dataSource.getHikariPoolMXBean().getTotalConnections());
                    details.put("threadsAwaitingConnection", dataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());

                    return HealthStatus.up("OpenGauss连接正常", details);
                } else {
                    return HealthStatus.down("OpenGauss连接无效");
                }
            }
        } catch (Exception e) {
            logger.error("检查OpenGauss资源健康状态失败: {}", getId(), e);
            return HealthStatus.down("OpenGauss健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 测试数据库连接
     *
     * @throws SQLException 连接失败时抛出异常
     */
    private void testConnection() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            if (!connection.isValid(5)) {
                throw new SQLException("OpenGauss连接无效");
            }
            logger.debug("OpenGauss连接测试成功: {}", getId());
        }
    }

    /**
     * 获取数据源
     *
     * @return 数据源
     */
    public DataSource getDataSource() {
        if (dataSource == null) {
            throw new IllegalStateException("OpenGauss资源未启动");
        }
        return dataSource;
    }

    /**
     * 获取OpenGauss配置
     *
     * @return OpenGauss配置
     */
    public OpenGaussConfig getConfig() {
        return config;
    }

    @Override
    public DataSource getNativeResource() {
        return getDataSource();
    }
}
