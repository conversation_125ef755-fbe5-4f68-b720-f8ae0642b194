<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.middleware.mapper.ResourceTypeMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.middleware.entity.ResourceTypeEntity">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="category" property="category" />
        <result column="description" property="description" />
        <result column="default_config" property="defaultConfig" typeHandler="com.siteweb.tcs.common.util.JsonMapTypeHandler" />
        <result column="ui_component" property="uiComponent" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, category, description, default_config, ui_component, create_time, update_time
    </sql>
    
</mapper>
