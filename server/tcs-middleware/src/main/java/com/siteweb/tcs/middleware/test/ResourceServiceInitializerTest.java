package com.siteweb.tcs.middleware.test;

//import com.siteweb.tcs.south.swap.bk.process.HeartBeatProcessor;
//import com.siteweb.tcs.south.swap.bk.process.RegisterRequestProcessor;
//import com.siteweb.tcs.south.swap.bk.protocol.SiteWebPacket;
//import com.siteweb.tcs.south.swap.bk.util.SiteWebPacketUtil;
import org.influxdb.InfluxDB;
import org.influxdb.dto.QueryResult;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.middleware.common.resource.InfluxDBResource;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.service.udp.DatagramHandler;
import com.siteweb.tcs.middleware.common.service.udp.UDPServerService;
import com.siteweb.tcs.middleware.common.service.udp.model.UDPDatagramInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// 使用tcs-south-swap模块中的Siteweb协议B标准实体类
//import com.siteweb.tcs.south.swap.bk.enums.MessageType;

// 使用新创建的协议解析和构建类


/**
 * 资源初始化服务
 * 用于测试中间件资源注入和功能
 */
@Component
public class ResourceServiceInitializerTest {

    private static final Logger log = LoggerFactory.getLogger(ResourceServiceInitializerTest.class);

    @Autowired
    private ResourceRegistry resourceRegistry;
    @Autowired
    private ServiceRegistry serviceRegistry;

    // 当前处理的UDP服务，用于发送响应
    private UDPServerService currentUdpService;

    private DatagramHandler handleDatagram = new DatagramHandler() {
        @Override
        public void handleDatagram(UDPDatagramInfo datagramInfo) {
            try {
                // 解析SWAP协议首部
//                SiteWebPacket siteWebPacket = SiteWebPacketUtil.unPackFromBytes(datagramInfo.getRawData());
//                log.info(siteWebPacket.getMessageType().name());
//                if (siteWebPacket.getMessageType() == MessageType.REGISTER_REQUEST){
//                    RegisterRequestProcessor processor = new RegisterRequestProcessor();
//                    byte[] bytes = processor.processRegisterRequestByte(siteWebPacket);
//                    currentUdpService.sendDatagram(datagramInfo.getSourceIp(), datagramInfo.getSourcePort(), bytes);
//                }
//                if (siteWebPacket.getMessageType() == MessageType.HEARTBEAT_REQUEST){
//                    HeartBeatProcessor processor = new HeartBeatProcessor();
//                    currentUdpService.sendDatagram(datagramInfo.getSourceIp(), datagramInfo.getSourcePort(), processor.createHeartBeatResponsePacket(siteWebPacket));
//                }

            } catch (Exception e) {
                log.error("协议解析失败", e);
                log.info("原始数据长度: {} 字节", datagramInfo.getRawData().length);
                log.info("来源: {}:{}", datagramInfo.getSourceIp(), datagramInfo.getSourcePort());
            }
        }
    };

    public void test() throws InterruptedException {
        // 测试UDP服务
        testUDPServices();
        
        // 测试InfluxDB资源
//        testInfluxDBResource();
    }

    /**
     * 测试UDP服务
     */
    private void testUDPServices() {
        try {
//            UDPServerService test = (UDPServerService) serviceRegistry.get("test-nio-udp-server-service-001", "test");
//            test.setDatagramHandler(handleDatagram);
//            this.currentUdpService = test; // 保存当前UDP服务引用

//            UDPServerService test2 = (UDPServerService) serviceRegistry.get("test-netty-udp-server-service-001", "test");
//            test2.setDatagramHandler(handleDatagram);
//
//            UDPServerService test3 = (UDPServerService) serviceRegistry.get("test-apache-udp-server-service-001", "test");
//            test3.setDatagramHandler(handleDatagram);

            log.info("UDP服务测试完成");
        } catch (Exception e) {
            log.error("UDP服务测试失败", e);
        }
    }

    /**
     * 测试InfluxDB资源
     */
    private void testInfluxDBResource() {
        try {
            log.info("开始测试InfluxDB资源...");
            
            // 获取InfluxDB资源
            Resource influxDBResource = resourceRegistry.get("test-influxdb-config-001");
            if (influxDBResource == null) {
                log.error("InfluxDB资源未找到: test-influxdb-config-001");
                return;
            }
            
            log.info("InfluxDB资源获取成功: {}", influxDBResource.getId());
            
            // 检查资源类型
            if (!(influxDBResource instanceof InfluxDBResource)) {
                log.error("资源类型不匹配，期望InfluxDBResource，实际: {}", influxDBResource.getClass().getSimpleName());
                return;
            }
            
            InfluxDBResource influxDB = (InfluxDBResource) influxDBResource;
            
            // 检查资源健康状态
            if (!influxDB.isHealthy()) {
                log.error("InfluxDB资源不健康");
                return;
            }
            
            log.info("InfluxDB资源健康状态检查通过");
            
            // 获取原生客户端
            InfluxDB client = influxDB.getNativeResource();
            if (client == null) {
                log.error("无法获取InfluxDB客户端");
                return;
            }
            
            log.info("InfluxDB客户端获取成功");
            
            // 执行查询测试
            testInfluxDBQuery(influxDB);
            
        } catch (Exception e) {
            log.error("InfluxDB资源测试失败", e);
        }
    }

    /**
     * 测试InfluxDB查询功能
     */
    private void testInfluxDBQuery(InfluxDBResource influxDBResource) {
        try {
            log.info("开始测试InfluxDB查询功能...");
            
            // 构建查询语句：查询最近1天的历史数据
            String queryString = "SELECT * FROM \"historydatas\" WHERE time > now() - 1d and time < now()";
            
            log.info("执行查询: {}", queryString);
            
            // 执行查询
            QueryResult result = influxDBResource.query(queryString);
            
            if (result != null && result.getResults() != null) {
                log.info("查询执行成功，返回结果数量: {}", result.getResults().size());
                
                // 输出查询结果信息
                for (int i = 0; i < result.getResults().size(); i++) {
                    QueryResult.Result queryResult = result.getResults().get(i);
                    if (queryResult.getSeries() != null) {
                        log.info("结果 {}: 系列数量={}", i + 1, queryResult.getSeries().size());
                        
                        for (QueryResult.Series series : queryResult.getSeries()) {
                            log.info("  系列: 名称={}, 列数={}, 行数={}", 
                                series.getName(), series.getColumns().size(), series.getValues().size());
                        }
                    }
                }
            } else {
                log.warn("查询返回空结果");
            }
            
            log.info("InfluxDB查询测试完成");
            
        } catch (Exception e) {
            log.error("InfluxDB查询测试失败", e);
        }
    }

    public void handleDatagram(UDPDatagramInfo datagramInfo){
        log.info("Received datagram from {}:{}", datagramInfo.getSourceAddress(), datagramInfo.getSourcePort());
    }








    /**
     * 发送响应数据包
     */
    private void sendResponse(byte[] responsePacket, UDPDatagramInfo originalDatagram) {
        if (currentUdpService == null) {
            log.error("UDP服务未初始化，无法发送响应");
            return;
        }

        try {
            // 发送响应到原始发送方
            currentUdpService.sendDatagram(originalDatagram.getSourceIp(),
                                         originalDatagram.getSourcePort(),
                                         responsePacket);

        } catch (Exception e) {
            log.error("发送响应失败", e);
        }
    }
}
