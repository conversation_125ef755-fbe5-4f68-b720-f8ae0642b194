//package com.siteweb.tcs.middleware.controller.runtime;
//
//import com.siteweb.tcs.common.response.ResponseResult;
//import com.siteweb.tcs.common.util.SpringBeanUtil;
//import com.siteweb.tcs.middleware.common.model.FileInfo;
//import com.siteweb.tcs.middleware.common.model.FileUploadResult;
//import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
//import com.siteweb.tcs.middleware.common.service.FileSystemService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.media.Content;
//import io.swagger.v3.oas.annotations.responses.ApiResponse;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.io.ByteArrayResource;
//import org.springframework.core.io.Resource;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.IOException;
//import java.net.URLEncoder;
//import java.nio.charset.StandardCharsets;
//import java.util.List;
//import java.util.concurrent.CompletableFuture;
//
///**
// * 文件系统服务控制器
// * 提供文件上传、下载、管理等接口
// */
//@RestController
//@RequestMapping("/middleware/filesystem")
//@Tag(name = "文件系统服务", description = "文件系统服务接口，支持文件上传、下载、列表等操作")
//public class FileSystemController {
//
//    private static final Logger logger = LoggerFactory.getLogger(FileSystemController.class);
//
//    @Autowired
//    private ServiceRegistry serviceRegistry;
//
//    private FileSystemService fileSystemService;
//
//    public FileSystemController() {
//        serviceRegistry = SpringBeanUtil.getBean("serviceRegistry", ServiceRegistry.class);
//        fileSystemService = (FileSystemService) serviceRegistry.get("test-local-filesystem-service-001","tcs-middleware");
//    }
//
//    /**
//     * 上传文件
//     *
//     * @param serviceConfigId 服务配置ID
//     * @param filePath 文件路径
//     * @param file 上传的文件
//     * @return 上传结果
//     */
//    @PostMapping("/upload")
//    @Operation(summary = "上传文件", description = "上传文件到指定的文件系统服务")
//    @ApiResponse(responseCode = "200", description = "上传成功")
//    @ApiResponse(responseCode = "400", description = "请求参数错误")
//    @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    public ResponseEntity<ResponseResult<FileUploadResult>> uploadFile(
//            @Parameter(description = "服务配置ID", required = true) @RequestParam String serviceConfigId,
//            @Parameter(description = "文件路径", required = true) @RequestParam String filePath,
//            @Parameter(description = "上传的文件", required = true) @RequestParam("file") MultipartFile file) {
//
//        try {
//            logger.info("开始上传文件: serviceConfigId={}, filePath={}, fileName={}",
//                serviceConfigId, filePath, file.getOriginalFilename());
//
//            // 获取文件系统服务实例
//
//            // 执行文件上传
//            CompletableFuture<FileUploadResult> uploadFuture = fileSystemService.uploadFileAsync(
//                filePath, file.getOriginalFilename(), file.getBytes());
//
//            FileUploadResult result = uploadFuture.get();
//
//            if (result.isSuccess()) {
//                logger.info("文件上传成功: {}/{}", filePath, file.getOriginalFilename());
//                return ResponseEntity.ok(ResponseResult.success(result));
//            } else {
//                logger.error("文件上传失败: {}", result.getErrorMessage());
//                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                    .body(ResponseResult.fail("文件上传失败: " + result.getErrorMessage()));
//            }
//
//        } catch (IOException e) {
//            logger.error("读取上传文件失败", e);
//            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
//                .body(ResponseResult.fail("读取上传文件失败: " + e.getMessage()));
//        } catch (Exception e) {
//            logger.error("上传文件异常", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                .body(ResponseResult.fail("上传文件异常: " + e.getMessage()));
//        }
//    }
//
//    /**
//     * 下载文件
//     *
//     * @param serviceConfigId 服务配置ID
//     * @param filePath 文件路径
//     * @param fileName 文件名
//     * @return 文件内容
//     */
//    @GetMapping("/download")
//    @Operation(summary = "下载文件", description = "从指定的文件系统服务下载文件")
//    @ApiResponse(responseCode = "200", description = "下载成功", content = @Content(mediaType = "application/octet-stream"))
//    @ApiResponse(responseCode = "404", description = "文件不存在")
//    @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    public ResponseEntity<Resource> downloadFile(
//            @Parameter(description = "服务配置ID", required = true) @RequestParam String serviceConfigId,
//            @Parameter(description = "文件路径", required = true) @RequestParam String filePath,
//            @Parameter(description = "文件名", required = true) @RequestParam String fileName) {
//
//        try {
//            logger.info("开始下载文件: serviceConfigId={}, filePath={}, fileName={}",
//                serviceConfigId, filePath, fileName);
//
//            // 获取文件系统服务实例
//
//            // 执行文件下载
//            CompletableFuture<byte[]> downloadFuture = fileSystemService.downloadFileAsync(filePath, fileName);
//            byte[] fileContent = downloadFuture.get();
//
//            if (fileContent == null) {
//                logger.warn("文件不存在: {}/{}", filePath, fileName);
//                return ResponseEntity.notFound().build();
//            }
//
//            // 构建响应
//            ByteArrayResource resource = new ByteArrayResource(fileContent);
//
//            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
//
//            HttpHeaders headers = new HttpHeaders();
//            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName);
//            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
//            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(fileContent.length));
//
//            logger.info("文件下载成功: {}/{}, size: {}", filePath, fileName, fileContent.length);
//
//            return ResponseEntity.ok()
//                .headers(headers)
//                .body(resource);
//
//        } catch (Exception e) {
//            logger.error("下载文件异常: {}/{}", filePath, fileName, e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//        }
//    }
//
//    /**
//     * 删除文件
//     *
//     * @param serviceConfigId 服务配置ID
//     * @param filePath 文件路径
//     * @param fileName 文件名
//     * @return 删除结果
//     */
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除文件", description = "从指定的文件系统服务删除文件")
//    @ApiResponse(responseCode = "200", description = "删除成功")
//    @ApiResponse(responseCode = "404", description = "文件不存在")
//    @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    public ResponseEntity<ResponseResult<Boolean>> deleteFile(
//            @Parameter(description = "服务配置ID", required = true) @RequestParam String serviceConfigId,
//            @Parameter(description = "文件路径", required = true) @RequestParam String filePath,
//            @Parameter(description = "文件名", required = true) @RequestParam String fileName) {
//
//        try {
//            logger.info("开始删除文件: serviceConfigId={}, filePath={}, fileName={}",
//                serviceConfigId, filePath, fileName);
//
//            // 获取文件系统服务实例
//
//            // 执行文件删除
//            CompletableFuture<Boolean> deleteFuture = fileSystemService.deleteFileAsync(filePath, fileName);
//            Boolean result = deleteFuture.get();
//
//            if (result) {
//                logger.info("文件删除成功: {}/{}", filePath, fileName);
//                return ResponseEntity.ok(ResponseResult.success(true));
//            } else {
//                logger.warn("文件不存在或删除失败: {}/{}", filePath, fileName);
//                return ResponseEntity.status(HttpStatus.NOT_FOUND)
//                    .body(ResponseResult.fail("文件不存在或删除失败"));
//            }
//
//        } catch (Exception e) {
//            logger.error("删除文件异常: {}/{}", filePath, fileName, e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                .body(ResponseResult.fail("删除文件异常: " + e.getMessage()));
//        }
//    }
//
//    /**
//     * 列出目录文件
//     *
//     * @param serviceConfigId 服务配置ID
//     * @param directoryPath 目录路径
//     * @return 文件列表
//     */
//    @GetMapping("/list")
//    @Operation(summary = "列出目录文件", description = "列出指定目录下的文件")
//    @ApiResponse(responseCode = "200", description = "获取成功")
//    @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    public ResponseEntity<ResponseResult<List<FileInfo>>> listFiles(
//            @Parameter(description = "服务配置ID", required = true) @RequestParam String serviceConfigId,
//            @Parameter(description = "目录路径", required = true) @RequestParam String directoryPath) {
//
//        try {
//            logger.info("开始列出目录文件: serviceConfigId={}, directoryPath={}",
//                serviceConfigId, directoryPath);
//
//            // 获取文件系统服务实例
//
//            // 执行文件列表获取
//            CompletableFuture<List<FileInfo>> listFuture = fileSystemService.listFilesAsync(directoryPath);
//            List<FileInfo> files = listFuture.get();
//
//            logger.info("列出目录文件成功: {}, 文件数: {}", directoryPath, files.size());
//            return ResponseEntity.ok(ResponseResult.success(files));
//
//        } catch (Exception e) {
//            logger.error("列出目录文件异常: {}", directoryPath, e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                .body(ResponseResult.fail("列出目录文件异常: " + e.getMessage()));
//        }
//    }
//
//    /**
//     * 检查文件是否存在
//     *
//     * @param serviceConfigId 服务配置ID
//     * @param filePath 文件路径
//     * @param fileName 文件名
//     * @return 是否存在
//     */
//    @GetMapping("/exists")
//    @Operation(summary = "检查文件是否存在", description = "检查指定文件是否存在")
//    @ApiResponse(responseCode = "200", description = "检查成功")
//    @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    public ResponseEntity<ResponseResult<Boolean>> fileExists(
//            @Parameter(description = "服务配置ID", required = true) @RequestParam String serviceConfigId,
//            @Parameter(description = "文件路径", required = true) @RequestParam String filePath,
//            @Parameter(description = "文件名", required = true) @RequestParam String fileName) {
//
//        try {
//            logger.debug("检查文件是否存在: serviceConfigId={}, filePath={}, fileName={}",
//                serviceConfigId, filePath, fileName);
//
//            // 获取文件系统服务实例
//
//            // 执行文件存在检查
//            CompletableFuture<Boolean> existsFuture = fileSystemService.fileExistsAsync(filePath, fileName);
//            Boolean exists = existsFuture.get();
//
//            return ResponseEntity.ok(ResponseResult.success(exists));
//
//        } catch (Exception e) {
//            logger.error("检查文件存在异常: {}/{}", filePath, fileName, e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                .body(ResponseResult.fail("检查文件存在异常: " + e.getMessage()));
//        }
//    }
//
//    /**
//     * 获取文件信息
//     *
//     * @param serviceConfigId 服务配置ID
//     * @param filePath 文件路径
//     * @param fileName 文件名
//     * @return 文件信息
//     */
//    @GetMapping("/info")
//    @Operation(summary = "获取文件信息", description = "获取指定文件的详细信息")
//    @ApiResponse(responseCode = "200", description = "获取成功")
//    @ApiResponse(responseCode = "404", description = "文件不存在")
//    @ApiResponse(responseCode = "500", description = "服务器内部错误")
//    public ResponseEntity<ResponseResult<FileInfo>> getFileInfo(
//            @Parameter(description = "服务配置ID", required = true) @RequestParam String serviceConfigId,
//            @Parameter(description = "文件路径", required = true) @RequestParam String filePath,
//            @Parameter(description = "文件名", required = true) @RequestParam String fileName) {
//
//        try {
//            logger.debug("获取文件信息: serviceConfigId={}, filePath={}, fileName={}",
//                serviceConfigId, filePath, fileName);
//
//
//            // 执行文件信息获取
//            CompletableFuture<FileInfo> infoFuture = fileSystemService.getFileInfoAsync(filePath, fileName);
//            FileInfo fileInfo = infoFuture.get();
//
//            if (fileInfo == null) {
//                return ResponseEntity.status(HttpStatus.NOT_FOUND)
//                    .body(ResponseResult.fail("文件不存在"));
//            }
//
//            return ResponseEntity.ok(ResponseResult.success(fileInfo));
//
//        } catch (Exception e) {
//            logger.error("获取文件信息异常: {}/{}", filePath, fileName, e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
//                .body(ResponseResult.fail("获取文件信息异常: " + e.getMessage()));
//        }
//    }
//}
