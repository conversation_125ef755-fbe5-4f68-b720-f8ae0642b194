package com.siteweb.tcs.middleware.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * 自定义MyBatis类型处理器，用于将数据库中存储的JSON字符串与Java的Map<String,Object>相互转换。
 * 支持处理带有多层引号包裹和转义字符的字符串，适应JSON字符串被多次序列化的情况。
 */
@MappedTypes(Map.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class MapStringObjectTypeHandler extends BaseTypeHandler<Map<String, Object>> {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<String, Object> parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = OBJECT_MAPPER.writeValueAsString(parameter);
            ps.setString(i, json);
        } catch (Exception e) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.DATABASE_UPDATE_FAILED,
                "Failed to convert Map to JSON string: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJsonToMap(json);
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJsonToMap(json);
    }

    @Override
    public Map<String, Object> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJsonToMap(json);
    }

    /**
     * 解析数据库中的JSON字符串为Map对象。
     * 支持多层转义的JSON字符串：
     *  - 反复去除外层的双引号包裹
     *  - 反转义内层的转义引号(\")
     *  - 去除换行、制表符等控制字符
     */
    private Map<String, Object> parseJsonToMap(String json) throws SQLException {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            // 去除首尾空白字符和换行符，避免解析异常
            json = json.trim()
                    .replace("\\r\\n", "")   // 替换转义的\r\n为无（或者换成空格）
                    .replace("\\n", "")
                    .replace("\\r", "")
                    .replace("\\t", "")
                    .replace("\\\"", "\"")
                    .replace("\\\\", "\\");


            // 循环去除外层多余的双引号，并反转义内部的转义字符
            while (json.startsWith("\"") && json.endsWith("\"")) {
                json = json.substring(1, json.length() - 1);
                // 反转义转义引号
                json = json.replace("\\\"", "\"");
                // 反转义反斜杠
                json = json.replace("\\\\", "\\");
            }

            return OBJECT_MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            System.err.println("解析JSON失败，内容：" + json);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.DATABASE_QUERY_FAILED,
                "Failed to parse JSON string to Map: " + e.getMessage(),
                e
            );
        }
    }
}
