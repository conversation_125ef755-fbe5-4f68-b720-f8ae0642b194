package com.siteweb.tcs.middleware.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.middleware.entity.ServiceTypeEntity;
import com.siteweb.tcs.middleware.mapper.ServiceTypeMapper;
import com.siteweb.tcs.middleware.service.ServiceTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 服务类型服务实现类
 */
@Service
public class ServiceTypeServiceImpl extends ServiceImpl<ServiceTypeMapper, ServiceTypeEntity> implements ServiceTypeService {
    
    @Override
    public IPage<ServiceTypeEntity> pageServiceTypes(Page<ServiceTypeEntity> page, String name) {
        LambdaQueryWrapper<ServiceTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(name)) {
            queryWrapper.like(ServiceTypeEntity::getName, name);
        }
        queryWrapper.orderByDesc(ServiceTypeEntity::getCreateTime);
        
        return page(page, queryWrapper);
    }
    
    @Override
    public ServiceTypeEntity getServiceTypeById(String id) {
        return getById(id);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceTypeEntity createServiceType(ServiceTypeEntity serviceType) {
        // 生成ID
        serviceType.setId(UUID.randomUUID().toString().replace("-", ""));
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        serviceType.setCreateTime(now);
        serviceType.setUpdateTime(now);
        
        // 保存实体
        save(serviceType);
        
        return serviceType;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceTypeEntity updateServiceType(String id, ServiceTypeEntity serviceType) {
        ServiceTypeEntity entity = getById(id);
        if (entity == null) {
            return null;
        }
        
        // 更新属性
        serviceType.setId(id); // 确保ID不变
        serviceType.setUpdateTime(LocalDateTime.now());
        
        // 更新实体
        updateById(serviceType);
        
        return serviceType;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteServiceType(String id) {
        return removeById(id);
    }
    
    @Override
    public List<ServiceTypeEntity> listServiceTypesBySupportedResourceCategory(String supportedResourceCategory) {
        LambdaQueryWrapper<ServiceTypeEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(supportedResourceCategory)) {
            queryWrapper.like(ServiceTypeEntity::getSupportedResourceCategory, supportedResourceCategory);
        }
        queryWrapper.orderByAsc(ServiceTypeEntity::getName);
        
        return list(queryWrapper);
    }
}
