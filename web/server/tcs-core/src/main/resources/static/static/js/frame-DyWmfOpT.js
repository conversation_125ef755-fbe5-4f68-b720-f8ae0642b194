import{d as _,a5 as v,p as c,B as p,u as t,K as h,y as g,j as x,w as I,c as R,o as k,e as B,a0 as L,_ as S}from"./index-CbJ7h3mt.js";const w=["element-loading-text"],y=["src"],E=_({name:"LayFrame",__name:"frame",props:{frameInfo:{}},setup(i){const r=i,{t:m}=v(),s=c(!0),a=p(),n=c(""),l=c(null);t(a.meta)?.frameSrc&&(n.value=t(a.meta)?.frameSrc),t(a.meta)?.frameLoading===!1&&o();function o(){s.value=!1}function u(){L(()=>{const e=t(l);if(!e)return;const f=e;f.attachEvent?f.attachEvent("onload",()=>{o()}):e.onload=()=>{o()}})}return h(()=>a.fullPath,e=>{a.name==="Redirect"&&e.includes(r.frameInfo?.fullPath)&&(n.value=e,s.value=!0),r.frameInfo?.fullPath===e&&(n.value=r.frameInfo?.frameSrc)}),g(()=>{u()}),(e,f)=>{const d=x("loading");return I((k(),R("div",{class:"frame","element-loading-text":t(m)("status.pureLoad")},[B("iframe",{ref_key:"frameRef",ref:l,src:n.value,class:"frame-iframe"},null,8,y)],8,w)),[[d,s.value]])}}}),D=S(E,[["__scopeId","data-v-5296aa19"]]);export{D as default};
