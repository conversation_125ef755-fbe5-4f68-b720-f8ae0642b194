import{d as y,t as b,p as h,aB as u,r as l,c,o as m,e as n,b as o,h as t,S as C,T as g,Z as w,R as S,N as k,I as x,aC as B}from"./index-CbJ7h3mt.js";const V={class:"card-header"},U=y({name:"PermissionPage",__name:"index",setup(N){const d=b(()=>({width:"85vw",justifyContent:"start"})),a=h(u()?.username),i=[{value:"admin",label:"管理员角色"},{value:"common",label:"普通角色"}];function p(){u().loginByUsername({username:a.value,password:"admin123"}).then(r=>{r.success&&(k().removeItem("async-routes"),x().clearAllCachePage(),B())})}return(r,s)=>{const v=l("el-option"),_=l("el-select"),f=l("el-card");return m(),c("div",null,[s[1]||(s[1]=n("p",{class:"mb-2!"}," 模拟后台根据不同角色返回对应路由，观察左侧菜单变化（管理员角色可查看系统管理菜单、普通角色不可查看系统管理菜单） ",-1)),o(f,{shadow:"never",style:S(d.value)},{header:t(()=>[n("div",V,[n("span",null,"当前角色："+w(a.value),1)])]),default:t(()=>[o(_,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=e=>a.value=e),class:"w-[160px]!",onChange:p},{default:t(()=>[(m(),c(C,null,g(i,e=>o(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1},8,["style"])])}}});export{U as default};
