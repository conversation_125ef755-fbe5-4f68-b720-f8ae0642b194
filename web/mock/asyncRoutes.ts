// 模拟后端动态生成路由
import { defineFakeRoute } from "vite-plugin-fake-server/client";

/**
 * roles：页面级别权限，这里模拟二种 "admin"、"common"
 * admin：管理员角色
 * common：普通角色
 */
const permissionRouter = {
  path: "/fighting",
  meta: {
    title: "加油"
  },
  children: [
    {
      // path随便写，但前面必须有个 `/`
      path: "/anything",
      // component对应的值前不需要加 / 值对应的是实际业务 `.vue` 或 `.tsx` 代码路径
      component: "system-management/role-management/index",
      name: "Fighting",
      meta: {
        title: "加油"
      }
    }
  ]
};

export default defineFakeRoute([
  {
    url: "/get-async-routes",
    method: "get",
    response: () => {
      return {
        success: true,
        data: [
          {
              "path": "/system-management",
              "name": "系统管理",
              "meta": {
                  "title": "系统管理",
                  "icon": "ep:monitor",
                  "rank": 10,
                  "showLink": true,
                  "showParent": false,
                  "activePath": null
              },
              "redirect": "/system-management/plugin-management",
              "children": [
                  {
                      "path": "/system-management/role-management",
                      "name": "角色管理",
                      "component": "system-management/role-management/index",
                      "meta": {
                          "title": "角色管理",
                          "icon": "ep:user-filled",
                          "showLink": true,
                          "showParent": true,
                          "activePath": null
                      }
                  },
                  {
                      "path": "/system-management/plugin-management",
                      "name": "插件管理",
                      "component": "system-management/plugin-management/index",
                      "meta": {
                          "title": "插件管理",
                          "icon": "mingcute:plugin-2-line",
                          "showLink": true,
                          "showParent": true,
                          "activePath": null
                      }
                  },
                  {
                      "path": "/system-management/plugin-management/detail/:pluginId",
                      "name": "插件详情",
                      "component": "system-management/plugin-management/detail/index",
                      "meta": {
                          "title": "插件详情",
                          "icon": null,
                          "showLink": false,
                          "showParent": false,
                          "activePath": "/system-management/plugin-management"
                      }
                  },
                  {
                      "path": "/system-management/middleware-management",
                      "name": "中间件管理",
                      "component": "system-management/middleware-management/index",
                      "meta": {
                          "title": "中间件管理",
                          "icon": "mingcute:package-2-line",
                          "showLink": true,
                          "showParent": true,
                          "activePath": null
                      },
                      "children": [
                          {
                              "path": "/system-management/middleware-management/resource-management",
                              "name": "资源管理",
                              "component": "system-management/middleware-management/resource-management/index",
                              "meta": {
                                  "title": "资源管理",
                                  "icon": null,
                                  "showLink": true,
                                  "showParent": false,
                                  "activePath": null
                              }
                          },
                          {
                              "path": "/system-management/middleware-management/resource-management/new",
                              "name": "新增资源配置",
                              "component": "system-management/middleware-management/resource-management/form",
                              "meta": {
                                  "title": "新增资源配置",
                                  "icon": null,
                                  "showLink": false,
                                  "showParent": false,
                                  "activePath": "/system-management/middleware-management/resource-management"
                              }
                          },
                          {
                              "path": "/system-management/middleware-management/resource-management/:id/edit",
                              "name": "编辑资源配置",
                              "component": "system-management/middleware-management/resource-management/form",
                              "meta": {
                                  "title": "编辑资源配置",
                                  "icon": null,
                                  "showLink": false,
                                  "showParent": false,
                                  "activePath": "/system-management/middleware-management/resource-management"
                              }
                          },
                          {
                              "path": "/system-management/middleware-management/resource-management/:id/detail",
                              "name": "资源详情",
                              "component": "system-management/middleware-management/resource-management/detail",
                              "meta": {
                                  "title": "资源详情",
                                  "icon": null,
                                  "showLink": false,
                                  "showParent": false,
                                  "activePath": "/system-management/middleware-management/resource-management"
                              }
                          },
                          {
                              "path": "/system-management/middleware-management/service-management",
                              "name": "服务管理",
                              "component": "system-management/middleware-management/service-management/index",
                              "meta": {
                                  "title": "服务管理",
                                  "icon": null,
                                  "showLink": true,
                                  "showParent": false,
                                  "activePath": null
                              }
                          },
                          {
                              "path": "/system-management/middleware-management/service-management/new",
                              "name": "新增服务配置",
                              "component": "system-management/middleware-management/service-management/form",
                              "meta": {
                                  "title": "新增服务配置",
                                  "icon": null,
                                  "showLink": false,
                                  "showParent": false,
                                  "activePath": "/system-management/middleware-management/service-management"
                              }
                          },
                          {
                              "path": "/system-management/middleware-management/service-management/:id/edit",
                              "name": "编辑服务配置",
                              "component": "system-management/middleware-management/service-management/form",
                              "meta": {
                                  "title": "编辑服务配置",
                                  "icon": null,
                                  "showLink": false,
                                  "showParent": false,
                                  "activePath": "/system-management/middleware-management/service-management"
                              }
                          },
                          {
                              "path": "/system-management/middleware-management/service-management/:id/detail",
                              "name": "服务详情",
                              "component": "system-management/middleware-management/service-management/detail",
                              "meta": {
                                  "title": "服务详情",
                                  "icon": null,
                                  "showLink": false,
                                  "showParent": false,
                                  "activePath": "/system-management/middleware-management/service-management"
                              }
                          }
                      ]
                  },
                  {
                      "path": "/system-management/user-management",
                      "name": "用户管理",
                      "component": "system-management/user-management/index",
                      "meta": {
                          "title": "用户管理",
                          "icon": "ep:user",
                          "showLink": true,
                          "showParent": true,
                          "activePath": null
                      }
                  },
                  {
                      "path": "/system-management/department-management",
                      "name": "部门管理",
                      "component": "system-management/department-management/index",
                      "meta": {
                          "title": "部门管理",
                          "icon": "ep:office-building",
                          "showLink": true,
                          "showParent": true,
                          "activePath": null
                      }
                  }
              ]
          },
          {
              "path": "/stream-computation",
              "name": "流计算",
              "meta": {
                  "title": "流计算",
                  "icon": "material-symbols:stream",
                  "rank": 11,
                  "showLink": true,
                  "showParent": false,
                  "activePath": null
              },
              "redirect": "/stream-computation/stream-plugin-management",
              "children": [
                  {
                      "path": "/stream-computation/stream-plugin-management",
                      "name": "图元库管理",
                      "component": "stream-computation/stream-plugin-management/index",
                      "meta": {
                          "title": "图元库管理",
                          "icon": "mingcute:plugin-2-line",
                          "showLink": true,
                          "showParent": false,
                          "activePath": null
                      }
                  },
                  {
                      "path": "/stream-computation/plugin-detail/:id?",
                      "name": "插件详情",
                      "component": "stream-computation/stream-plugin-management/detail",
                      "meta": {
                          "title": "插件详情",
                          "icon": null,
                          "showLink": false,
                          "showParent": false,
                          "activePath": "/stream-computation/stream-plugin-management"
                      }
                  },
                  {
                      "path": "/stream-computation/computation-graph",
                      "name": "计算图管理",
                      "component": "stream-computation/computation-graph/index",
                      "meta": {
                          "title": "计算图管理",
                          "icon": "material-symbols:account-tree-outline",
                          "showLink": true,
                          "showParent": false,
                          "activePath": null
                      }
                  },
                  {
                      "path": "/stream-computation/computation-graph/instances/:graphId",
                      "name": "计算图实例",
                      "component": "stream-computation/computation-graph/instances",
                      "meta": {
                          "title": "计算图实例",
                          "icon": null,
                          "showLink": false,
                          "showParent": false,
                          "activePath": "/stream-computation/computation-graph"
                      }
                  }
              ]
          }
      ]
      };
    }
  }
]);
