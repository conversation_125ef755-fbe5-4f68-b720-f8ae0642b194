需求：创建新的页面，页面菜单是系统管理-插件管理
插件管理页面布局 顶部是标题栏 标题栏左边是页面名称， 右边是上传插件按钮


然后主页面是卡片式的插件列表，能看到插件名称，插件id 插件版本 启动时间 启用状态 运行状态，然后卡片中有详情和操作按钮，操作按钮 是一个下拉按钮 有停止 禁用 卸载按钮

所有的操作目前不需要接口 
插件列表获取的假数据是：[
    {
        "pluginId": "south-ctcc-plugin",
        "pluginName": "电信B接口插件",
        "version": "1.2.3",
        "provider": "Undefined",
        "description": "",
        "className": "com.siteweb.tcs.south.ctcc.SouthCTCCPlugin",
        "buildTime": "2025-05-20 09:14:38",
        "fileName": "tcs-south-ctcc-0.0.1-SNAPSHOT.jar",
        "location": "plugins/south-ctcc-plugin",
        "loaded": false,
        "state": "STARTED",
        "enabled": 1,
        "applicationName": null,
        "uploadDate": "2025-05-20 09:34:02",
        "updateDate": "2025-05-20 09:34:02",
        "bootTime": "2025-05-20 09:34:05",
        "downTime": null
    }
]

然后插件标题的旁边有一个图标 鼠标悬浮到详情图标上，可以查看插件详情信息：
应用名称：未显示（但标题为“电信B接口插件”）

插件位置：plugins/north-s6-plugin

文件名称：tcs-north-s6-0.0.1-SNAPSHOT.jar

插件类名：com.siteweb.tcs.north.cmcc.NorthCMCCPlugin

技术支持：

上传时间：2025-05-08 15:21:00