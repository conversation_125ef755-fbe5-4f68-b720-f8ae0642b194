import type { RouteRecordRaw } from "vue-router";
import type { SystemConfig } from "./config";

// 存储插件组件映射
const pluginComponentMap: Record<string, any> = {};

// 扩展接口，保持向后兼容
export interface MicroFrontendRegistry {
  // 原有的路由注册方法（保持接口不变，但行为改变）
  registerRoute: (routeConfig: RouteRecordRaw) => void;
  registerRoutes: (routeConfigs: RouteRecordRaw[]) => void;
  getRouteModules: () => RouteRecordRaw[];
  
  // 组件注册方法
  registerComponent: (path: string, component: any) => void;
  registerComponents: (components: Record<string, any>) => void;
  hasComponent: (path: string) => boolean;
  getComponent: (path: string) => any;
  getAvailableComponents: () => string[];
}

// 声明全局对象
declare global {
  interface Window {
    microFrontendRegistry: MicroFrontendRegistry;
    APP?: SystemConfig;
  }
}

/**
 * 初始化微前端插件注册功能
 */
export function initPluginRegistry() {
  // 创建全局注册对象
  const globalRegistry: MicroFrontendRegistry = {
    // === 修改后的方法：从注册路由改为注册组件映射 ===
    
    // 注册单个路由 -> 改为注册组件映射（优先使用name字段）
    registerRoute: routeConfig => {
      try {
        // 提取组件并注册到组件映射中
        if (routeConfig.component) {
          // 优先使用name作为组件映射的key
          if (routeConfig.name) {
            pluginComponentMap[routeConfig.name as string] = routeConfig.component;
            console.log("[Plugin] 注册组件映射（name）", routeConfig.name, routeConfig.component);
          }
          
          // 如果有path，也注册一个path映射作为备用
          if (routeConfig.path) {
            const componentKey = routeConfig.path.replace(/^\//, "").replace(/\/$/, "");
            pluginComponentMap[componentKey] = routeConfig.component;
            console.log("[Plugin] 注册组件映射（path）", componentKey, routeConfig.component);
          }
        }
        
        // 递归处理子路由
        if (routeConfig.children && routeConfig.children.length > 0) {
          routeConfig.children.forEach(child => {
            globalRegistry.registerRoute(child);
          });
        }
        
        console.log("[Plugin] 路由组件注册完成", routeConfig.path || routeConfig.name);
      } catch (error) {
        console.error("[Plugin] 注册路由组件失败", error);
      }
    },

    // 注册路由数组 -> 改为批量注册组件映射
    registerRoutes: routeConfigs => {
      try {
        routeConfigs.forEach(routeConfig => {
          globalRegistry.registerRoute(routeConfig);
        });
        console.log("[Plugin] 批量注册组件映射，数量:", routeConfigs.length);
      } catch (error) {
        console.error("[Plugin] 批量注册组件映射失败", error);
      }
    },

    // 获取所有插件路由模块 -> 改为返回空数组（因为不再直接注册路由）
    getRouteModules: () => {
      return [];
    },

    // === 原有的组件相关方法 ===
    
    // 注册单个组件
    registerComponent: (path, component) => {
      pluginComponentMap[path] = component;
      console.log("[Plugin] 注册组件映射", path);
    },

    // 批量注册组件
    registerComponents: (components) => {
      Object.assign(pluginComponentMap, components);
      console.log("[Plugin] 批量注册组件", Object.keys(components));
    },

    // 检查组件是否存在
    hasComponent: (path) => {
      return pluginComponentMap.hasOwnProperty(path);
    },

    // 获取组件
    getComponent: (path) => {
      return pluginComponentMap[path];
    },

    // 获取所有可用组件路径
    getAvailableComponents: () => {
      return Object.keys(pluginComponentMap);
    }
  };

  // 将注册函数挂载到全局
  window.microFrontendRegistry = globalRegistry;
}

/**
 * 检查插件组件是否存在
 */
export function hasPluginComponent(path: string): boolean {
  return pluginComponentMap.hasOwnProperty(path);
}

/**
 * 获取插件组件
 */
export function getPluginComponent(path: string) {
  return pluginComponentMap[path];
}

/**
 * 获取所有可用的插件组件路径
 */
export function getAvailablePluginComponents(): string[] {
  return Object.keys(pluginComponentMap);
}
