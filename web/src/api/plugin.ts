import { http } from "@/utils/http";

export interface PluginInfo {
  pluginId: string;
  pluginName: string;
  version: string;
  provider: string;
  location: string;
  className: string;
  fileName: string;
  state: string;
  enabled: number;
  uploadDate: string;
  updateDate: string;
  bootTime: string;
  downTime: string;
  loaded: boolean;
  applicationName: string;
  description?: string;
}

export interface PluginDetailInfo extends PluginInfo {
  workspaceSize: string;
  runTime: number;
  deviceCount?: number;
  collectorCount?: number;
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  thread?: string;
  logger?: string;
}

export interface FileInfo {
  name: string;
  path: string;
  size: number;
  type: "file" | "directory";
  lastModified: string;
  permissions?: string;
}

export interface ConfigFile {
  name: string;
  content: string;
  type: string;
}

export type ApiResponse<T> = {
  state: boolean;
  timestamp: number;
  data: T;
  err_msg: string | null;
  err_code: string | null;
};

/**
 * 获取插件列表
 */
export const getPluginList = () => {
  return http.request<ApiResponse<PluginInfo[]>>(
    "get",
    "/api/thing/plugins/list"
  );
};

/**
 * 获取插件详细信息
 */
export const getPluginDetail = (pluginId: string) => {
  return http.request<ApiResponse<PluginDetailInfo>>(
    "get",
    `/api/thing/plugins/detail?pluginId=${pluginId}`
  );
};

/**
 * 获取插件基本信息
 */
export const getPluginInfo = (pluginId: string) => {
  return http.request<ApiResponse<PluginInfo>>(
    "get",
    `/api/thing/plugins/info?pluginId=${pluginId}`
  );
};

/**
 * 获取插件工作目录大小
 */
export const getPluginWorkspaceSize = (pluginId: string) => {
  return http.request<ApiResponse<string>>(
    "get",
    `/api/thing/plugins/workspace-size?pluginId=${pluginId}`
  );
};

/**
 * 获取插件日志
 */
export const getPluginLogs = (
  pluginId: string,
  lines: number = 100,
  level?: string
) => {
  const params = new URLSearchParams({
    pluginId,
    lines: lines.toString()
  });
  if (level) {
    params.append("level", level);
  }
  return http.request<ApiResponse<LogEntry[]>>(
    "get",
    `/api/thing/plugins/logs?${params.toString()}`
  );
};

/**
 * 获取插件实时日志流
 */
export const getPluginLogStream = (pluginId: string) => {
  return `/api/thing/plugins/logs/stream?pluginId=${pluginId}`;
};

/**
 * 获取插件文件列表
 */
export const getPluginFiles = (pluginId: string, path: string = "/") => {
  return http.request<ApiResponse<FileInfo[]>>(
    "get",
    `/api/thing/plugins/files?pluginId=${pluginId}&path=${encodeURIComponent(path)}`
  );
};

/**
 * 读取插件文件内容
 */
export const getPluginFileContent = (pluginId: string, filePath: string) => {
  return http.request<ApiResponse<string>>(
    "get",
    `/api/thing/plugins/files/content?pluginId=${pluginId}&path=${encodeURIComponent(filePath)}`
  );
};

/**
 * 保存插件文件内容
 */
export const savePluginFileContent = (
  pluginId: string,
  filePath: string,
  content: string
) => {
  return http.request<ApiResponse<boolean>>(
    "post",
    `/api/thing/plugins/files/content`,
    {
      data: {
        pluginId,
        path: filePath,
        content
      }
    }
  );
};

/**
 * 删除插件文件
 */
export const deletePluginFile = (pluginId: string, filePath: string) => {
  return http.request<ApiResponse<boolean>>(
    "delete",
    `/api/thing/plugins/files?pluginId=${pluginId}&path=${encodeURIComponent(filePath)}`
  );
};

/**
 * 创建插件目录
 */
export const createPluginDirectory = (pluginId: string, dirPath: string) => {
  return http.request<ApiResponse<boolean>>(
    "post",
    `/api/thing/plugins/files/directory`,
    {
      data: {
        pluginId,
        path: dirPath
      }
    }
  );
};

/**
 * 获取插件配置文件
 */
export const getPluginConfig = (pluginId: string) => {
  return http.request<ApiResponse<string>>(
    "get",
    `/api/thing/plugins/config?pluginId=${pluginId}`
  );
};

/**
 * 保存插件配置文件
 */
export const savePluginConfig = (pluginId: string, yaml: string) => {
  return http.request<ApiResponse<boolean>>(
    "put",
    `/api/thing/plugins/config`,
    {
      data: {
        pluginId,
        yaml
      }
    }
  );
};

/**
 * 上传插件
 */
export const uploadPlugin = (file: File) => {
  const formData = new FormData();
  formData.append("file", file);
  return http.request<ApiResponse<any>>("post", "/api/thing/plugins/install", {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

/**
 * 启动插件
 */
export const startPlugin = (pluginId: string) => {
  return http.request<ApiResponse<boolean>>(
    "get",
    `/api/thing/plugins/start?pluginId=${pluginId}`
  );
};

/**
 * 停止插件
 */
export const stopPlugin = (pluginId: string) => {
  return http.request<ApiResponse<boolean>>(
    "get",
    `/api/thing/plugins/stop?pluginId=${pluginId}`
  );
};

/**
 * 启用插件
 */
export const enablePlugin = (pluginId: string) => {
  return http.request<ApiResponse<boolean>>(
    "post",
    `/api/thing/plugins/enable?pluginId=${pluginId}`
  );
};

/**
 * 禁用插件
 */
export const disablePlugin = (pluginId: string) => {
  return http.request<ApiResponse<boolean>>(
    "post",
    `/api/thing/plugins/disable?pluginId=${pluginId}`
  );
};

/**
 * 设置插件日志级别
 */
export const setPluginLogLevel = (pluginId: string, level: string) => {
  return http.request<ApiResponse<string>>(
    "put",
    `/api/thing/plugins/log-level?pluginId=${pluginId}&logLevel=${level}`,
    {
      data: undefined
    }
  );
};

/**
 * 获取插件日志级别
 */
export const getPluginLogLevel = (pluginId: string) => {
  return http.request<ApiResponse<string>>(
    "get",
    `/api/thing/plugins/log-level?pluginId=${pluginId}`
  );
};

/**
 * 卸载插件
 */
export const uninstallPlugin = (pluginId: string) => {
  return http.request<ApiResponse<boolean>>(
    "post",
    `/api/thing/plugins/uninstall/${pluginId}`
  );
};
