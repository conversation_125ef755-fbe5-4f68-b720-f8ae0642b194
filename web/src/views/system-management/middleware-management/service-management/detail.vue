<template>
  <div class="detail-container">
    <div class="header">
      <h2>{{ $t('middleware.detailPage') }}: {{ serviceData.id }} - {{ serviceData.name }}</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleEdit" :disabled="isInstantiated">
          <el-icon><Edit /></el-icon>
          {{ $t('middleware.button.edit') }}
        </el-button>
        <el-button @click="handleBack">
          <el-icon><ArrowLeft /></el-icon>
          {{ $t('middleware.button.back') }}
        </el-button>
      </div>
    </div>

    <el-card shadow="never" v-loading="loading">
      <!-- Tab导航 -->
      <el-tabs v-model="activeTab">
        <!-- 基础配置信息 -->
        <el-tab-pane :label="$t('middleware.detail.basicConfig')" name="basic">
          <div class="detail-section">
            <h3>{{ $t('middleware.form.basicInfo') }}</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item :label="$t('middleware.table.id')">
                {{ serviceData.id }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('middleware.table.name')">
                {{ serviceData.name }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('middleware.table.serviceType')">
                {{ getServiceTypeName(serviceData.serviceId) }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('middleware.table.status')">
                <el-tag :type="serviceData.status === 'ENABLED' ? 'success' : 'info'">
                  {{ serviceData.status === 'ENABLED' ? $t('middleware.status.enabled') : $t('middleware.status.disabled') }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item :label="$t('middleware.table.resourceConfig')">
                {{ serviceData.resourceConfigurationId + ' (' + getResourceConfigName(serviceData.resourceConfigurationId) + ')' }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('middleware.table.createTime')">
                {{ new Date(serviceData.createTime).toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('middleware.table.createdBy')">
                {{ serviceData.createdBy }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('middleware.table.updateTime')">
                {{ new Date(serviceData.updateTime).toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('middleware.table.updatedBy')">
                {{ serviceData.updatedBy }}
              </el-descriptions-item>
              <el-descriptions-item :label="$t('middleware.table.description')" :span="2">
                {{ serviceData.description || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 配置信息 -->
          <div class="detail-section">
            <h3>{{ $t('middleware.form.config') }}</h3>
            <component
              :is="configComponent"
              v-if="configComponent && serviceData.config"
              :model-value="serviceData.config"
              :default-config="defaultConfig"
              :is-detail="true"
            />
            <el-card class="config-card" v-else>
              <pre class="config-json">{{ formatConfig(serviceData.config) }}</pre>
            </el-card>
          </div>

          <!-- 健康信息 -->
          <div class="detail-section" v-if="isInstantiated">
            <div class="section-header">
              <h3>{{ $t('middleware.detail.healthInfo') }}</h3>
              <el-button size="small" @click="refreshHealthInfo" :loading="loadingHealth">
                <el-icon><Refresh /></el-icon>
                {{ $t('middleware.button.refresh') }}
              </el-button>
            </div>

            <el-card class="health-card" v-if="healthData">
              <template #header>
                <div class="flex items-center justify-between">
                  <span class="font-medium">健康检查结果</span>
                </div>
              </template>

              <el-descriptions :column="2" border>
                <el-descriptions-item label="健康状态">
                  <el-tag :type="getHealthStatusType(healthData.status)" size="large">
                    {{ getHealthStatusText(healthData.status) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="检查时间">
                  {{ formatDateTime(healthData.checkTime) }}
                </el-descriptions-item>
                <el-descriptions-item label="状态消息" :span="2">
                  <span class="text-gray-600">{{ healthData.message || '无消息' }}</span>
                </el-descriptions-item>
              </el-descriptions>

              <!-- 健康详情 -->
              <div v-if="healthData.details && Object.keys(healthData.details).length > 0" class="details-section mt-4">
                <h4 class="text-base font-medium mb-3">详细信息</h4>
                <el-row :gutter="20">
                  <el-col :span="6" v-for="(value, key) in healthData.details" :key="key">
                    <el-statistic
                      :title="getDetailLabel(key)"
                      :value="formatDetailValue(value)"
                      :precision="getDetailPrecision(key, value)"
                    />
                  </el-col>
                </el-row>
              </div>

              <!-- 原始数据展示 -->
              <div class="raw-data-section mt-4">
                <el-collapse>
                  <el-collapse-item title="查看原始数据" name="rawData">
                    <pre class="text-sm bg-gray-50 p-3 rounded">{{ JSON.stringify(healthData, null, 2) }}</pre>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </el-card>

            <el-empty v-else description="暂无健康信息" />
          </div>
        </el-tab-pane>

        <!-- 日志查看 Tab -->
        <el-tab-pane
          v-if="isInstantiated"
          :label="$t('middleware.detail.logs')"
          name="logs"
        >
          <div class="log-view-section">
            <div class="section-header">
              <h3>{{ $t('middleware.detail.logs') }}</h3>
              <el-button size="small" @click="fetchLogs" :loading="loadingLogs">
                <el-icon><Refresh /></el-icon>
                {{ $t('middleware.button.refresh') }}
              </el-button>
            </div>
            <!-- 日志显示区域 -->
            <div class="log-output">
              <div v-if="logs.length === 0" class="no-logs">
                {{ $t('middleware.detail.noLogs') }}
              </div>
              <div v-else>
                <div v-for="(log, index) in logs" :key="index" class="log-entry">
                  <span :class="['log-level', (log.level || 'INFO').toLowerCase()]">
                    {{ log.level || 'INFO' }}
                  </span>
                  <span class="log-timestamp">[{{ log.timeStamp || log.timestamp }}]</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 维护管理 -->
        <el-tab-pane :label="$t('middleware.detail.maintenanceManagement')" name="maintenance">
          <div class="maintenance-section">
            <template v-if="isInstantiated && maintenanceComponent != null">
              <component :is="maintenanceComponent" :service-id="serviceData.id" ref="maintenanceRef" />
            </template>
            <template v-else>
              <el-empty :description="$t('middleware.detail.comingSoon')" />
            </template>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted, computed, defineAsyncComponent, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
import { ArrowLeft, Refresh, Edit } from "@element-plus/icons-vue";
import { http } from "@/utils/http";
import {
  getServiceConfigById,
  getServiceTypeList,
  getResourceConfigList,
  isServiceInstantiated,
  checkServiceHealth,
  type ServiceConfiguration,
  type ServiceType,
  type ResourceConfiguration,
  type HealthStatus,
  type ApiResponse
} from "@/api/system/middleware";

defineOptions({
  name: "ServiceDetail",
});

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 状态
const loading = ref(false);
const loadingHealth = ref(false);
const loadingLogs = ref(false);
const activeTab = ref("basic");
const isInstantiated = ref(false);

// 数据
const serviceData = ref<ServiceConfiguration>({} as ServiceConfiguration);
const serviceTypes = ref<ServiceType[]>([]);
const resourceConfigs = ref<ResourceConfiguration[]>([]);
const healthData = ref<HealthStatus | null>(null);
const logs = ref<any[]>([]); // 用于存储日志数据

const defaultConfig = ref<Record<string, any>>({});

// 动态配置组件加载
const configComponent = computed(() => {
  try {
    const type = serviceTypes.value.find((t) => t.id === serviceData.value.serviceId);
    if (!type || !type.uiComponent) return null;
    return defineAsyncComponent(() =>
      import(`../components/config/service/${type.uiComponent}.vue`).catch(() => null)
    );
  } catch (error) {
    return null;
  }
});

// 动态维护组件加载
const maintenanceComponent = computed(() => {
  if (!isInstantiated.value) return null;
  const type = serviceTypes.value.find((t) => t.id === serviceData.value.serviceId);
  if (!type || !type.uiComponent) return null;
  try {
    return defineAsyncComponent(() =>
      import(`../components/maintenance/${type.uiComponent}.vue`).catch(() => null)
    );
  } catch (e) {
    return null;
  }
});

// 维护tab刷新逻辑
const maintenanceRef = ref();
watch(activeTab, async (tab) => {
  if (tab === 'maintenance' && maintenanceRef.value && maintenanceRef.value.refresh) {
    await nextTick();
    maintenanceRef.value.refresh();
  }
});

// 获取服务类型名称
const getServiceTypeName = (serviceId: string) => {
  const type = serviceTypes.value.find((t) => t.id === serviceId);
  return type ? type.name : serviceId;
};

// 获取资源配置名称
const getResourceConfigName = (resourceConfigId: string) => {
  if (!resourceConfigId) return "-";
  const config = resourceConfigs.value.find((c) => c.id === resourceConfigId);
  return config ? config.name : resourceConfigId;
};

// 格式化配置
const formatConfig = (config: any) => {
  if (!config) return "";
  return JSON.stringify(config, null, 2);
};

// 获取健康状态类型
const getHealthStatusType = (status: string) => {
  switch (status?.toUpperCase()) {
    case "UP":
      return "success";
    case "DOWN":
      return "danger";
    case "UNKNOWN":
      return "warning";
    default:
      return "info";
  }
};

// 获取健康状态文本
const getHealthStatusText = (status: string) => {
  switch (status?.toUpperCase()) {
    case "UP":
      return "健康";
    case "DOWN":
      return "异常";
    case "UNKNOWN":
      return "未知";
    default:
      return status || "未知";
  }
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  try {
    return new Date(dateTime).toLocaleString('zh-CN');
  } catch (error) {
    return dateTime;
  }
};

// 获取详情标签
const getDetailLabel = (key: string) => {
  const labels: Record<string, string> = {
    connectionCount: "连接数",
    memoryUsage: "内存使用率",
    cpuUsage: "CPU使用率",
    diskUsage: "磁盘使用率",
    responseTime: "响应时间",
    requestCount: "请求数量",
    errorRate: "错误率",
    throughput: "吞吐量",
    uptime: "运行时间",
    version: "版本",
    lastConnectTime: "最后连接时间"
  };
  return labels[key] || key;
};

// 格式化详情值
const formatDetailValue = (value: any) => {
  if (typeof value === 'number') {
    return value;
  }
  if (typeof value === 'boolean') {
    return value ? '是' : '否';
  }
  return String(value);
};

// 获取详情精度
const getDetailPrecision = (key: string, value: any) => {
  if (typeof value !== 'number') return 0;
  if (key.includes('Usage') || key.includes('Rate')) {
    return 2; // 百分比保留2位小数
  }
  if (key.includes('Time')) {
    return 0; // 时间不保留小数
  }
  return 2; // 默认保留2位小数
};



// 获取服务类型列表
const fetchServiceTypes = async () => {
  try {
    const res = await getServiceTypeList();
    if (res.state) {
      serviceTypes.value = res.data;
    }
  } catch (error) {
    console.error("获取服务类型列表失败:", error);
  }
};

// 获取资源配置列表
const fetchResourceConfigs = async () => {
  try {
    const res = await getResourceConfigList();
    if (res.state) {
      resourceConfigs.value = res.data;
    }
  } catch (error) {
    console.error("获取资源配置列表失败:", error);
  }
};

// 获取服务配置详情
const fetchServiceDetail = async (id: string) => {
  loading.value = true;
  try {
    const res = await getServiceConfigById(id);
    if (res.state) {
      serviceData.value = res.data;

      // 设置默认配置
      const type = serviceTypes.value.find(t => t.id === serviceData.value.serviceId);
      if (type?.defaultConfig) {
        defaultConfig.value = type.defaultConfig;
      }

      // 检查是否已实例化
      const instantiatedRes = await isServiceInstantiated(id);
      if (instantiatedRes.state) {
        isInstantiated.value = instantiatedRes.data;

        // 如果已实例化，获取健康信息
        if (isInstantiated.value) {
          await fetchHealthInfo(id);
        }
      }
    }
  } catch (error) {
    console.error("获取服务详情失败:", error);
    ElMessage.error(t("middleware.message.operationFailed"));
  } finally {
    loading.value = false;
  }
};

// 获取健康信息
const fetchHealthInfo = async (id: string) => {
  loadingHealth.value = true;
  try {
    const res = await checkServiceHealth(id);
    if (res.state) {
      healthData.value = res.data;
    } else {
      healthData.value = null;
    }
  } catch (error) {
    console.error("获取健康信息失败:", error);
    healthData.value = null;
  } finally {
    loadingHealth.value = false;
  }
};

// 刷新健康信息
const refreshHealthInfo = () => {
  if (serviceData.value.id) {
    fetchHealthInfo(serviceData.value.id);
  }
};

// 返回
const handleBack = () => {
  router.push("/system-management/middleware-management/service-management");
};

// 编辑
const handleEdit = () => {
  router.push(`/system-management/middleware-management/service-management/${serviceData.value.id}/edit`);
};

// 在组件加载时获取详情和日志
onMounted(() => {
  const id = route.params.id as string;
  if (id) {
    fetchServiceTypes();
    fetchResourceConfigs();
    fetchServiceDetail(id);
  }
});

// 替换 startLogStream 函数为 fetchLogs 函数
const fetchLogs = async () => {
  if (!serviceData.value.id) return;

  loadingLogs.value = true;
  try {
    const url = `/api/thing/middleware/service-runtime/logs/${serviceData.value.id}`;
    console.log("Fetching logs from:", url);

    const result = await http.request<ApiResponse<any[]>>("get", url);
    console.log("Fetched logs response:", result);

    if (result.state && Array.isArray(result.data)) {
      logs.value = result.data;
      console.log("Updated logs array length:", logs.value.length);
    } else {
      console.warn("Invalid logs response format:", result);
      logs.value = [];
    }
  } catch (error) {
    console.error("Error fetching logs:", error);
    ElMessage.error(t("middleware.detail.logStreamError"));
    logs.value = [];
  } finally {
    loadingLogs.value = false;
  }
};

// 修改 watch activeTab 的逻辑
watch(activeTab, (newValue) => {
  if (newValue === "logs" && isInstantiated.value && serviceData.value.id) {
    fetchLogs();
  }
});
</script>

<style lang="scss" scoped>
@use "../styles/detail.scss";
</style>
