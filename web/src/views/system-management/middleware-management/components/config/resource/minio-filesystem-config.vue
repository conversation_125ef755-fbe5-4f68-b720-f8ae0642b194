<template>
  <div class="minio-filesystem-config">
    <el-form :model="configData" label-width="120px">
      <!-- 基本配置 -->
      <el-card shadow="never" class="config-section base-section">
        <div slot="header" class="section-header"><b>基本配置</b></div>
        
        <el-form-item label="服务端点" required>
          <el-input
            v-model="configData.endpoint"
            placeholder="http://localhost:9000"
            :readonly="isDetail"
            @input="handleConfigChange"
          />
          <div class="form-tip">Minio服务器的访问地址</div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="访问密钥" required>
              <el-input
                v-model="configData.accessKey"
                placeholder="minioadmin"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="秘密密钥" required>
              <el-input
                v-model="configData.secretKey"
                type="password"
                placeholder="请输入秘密密钥"
                show-password
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="存储桶名称" required>
              <el-input
                v-model="configData.bucketName"
                placeholder="default"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域">
              <el-input
                v-model="configData.region"
                placeholder="us-east-1"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="使用HTTPS">
          <el-switch
            v-model="configData.useHttps"
            :disabled="isDetail"
            @change="handleConfigChange"
          />
          <div class="form-tip">是否使用HTTPS协议连接</div>
        </el-form-item>
      </el-card>

      <!-- 高级配置 -->
      <el-card shadow="never" class="config-section advanced-section">
        <template #header>
          <div class="advanced-header" @click="showAdvanced = !showAdvanced">
            <span class="advanced-title">高级配置</span>
            <span class="advanced-desc" v-if="!isDetail">（展开进行配置）</span>
            <span class="advanced-desc" v-if="isDetail">（展开查看配置）</span>
            <el-icon class="advanced-arrow" :class="{ 'is-active': showAdvanced }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <el-collapse-transition>
          <div v-show="showAdvanced">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="连接超时(秒)">
                  <el-input-number
                    v-model="configData.connectTimeout"
                    :min="1"
                    :max="300"
                    placeholder="30"
                    style="width: 100%"
                    :readonly="isDetail"
                    @change="handleConfigChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="读取超时(秒)">
                  <el-input-number
                    v-model="configData.readTimeout"
                    :min="1"
                    :max="300"
                    placeholder="30"
                    style="width: 100%"
                    :readonly="isDetail"
                    @change="handleConfigChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="写入超时(秒)">
                  <el-input-number
                    v-model="configData.writeTimeout"
                    :min="1"
                    :max="300"
                    placeholder="30"
                    style="width: 100%"
                    :readonly="isDetail"
                    @change="handleConfigChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { ArrowDown } from '@element-plus/icons-vue';

defineOptions({
  name: "MinioFilesystemConfig"
});

const { t } = useI18n();

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
  isDetail?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({}),
  isDetail: false
});

// 显示高级配置
const showAdvanced = ref(false);

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端default_config保持一致
const configData = reactive({
  endpoint: 'http://localhost:9000',
  accessKey: 'minioadmin',
  secretKey: 'minioadmin',
  bucketName: 'default',
  region: 'us-east-1',
  useHttps: false,
  connectTimeout: 30,
  readTimeout: 30,
  writeTimeout: 30
});

// 处理配置变化
const handleConfigChange = () => {
  const config = { ...configData };
  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { immediate: true, deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // 只在当前配置为空时应用默认配置
    if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
      Object.assign(configData, newValue);
      handleConfigChange();
    }
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化时触发一次配置变化
  handleConfigChange();
});
</script>

<style scoped>
@import './common-config.scss';

.minio-filesystem-config {
  padding: 0;
}
</style>
