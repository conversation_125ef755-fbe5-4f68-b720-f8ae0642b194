<template>
  <div class="http-server-config">
    <el-form :model="configData" label-width="120px">
      <!-- 基本配置 -->
      <el-card shadow="never" class="config-section base-section">
        <div slot="header" class="section-header"><b>基本配置</b></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.host')" required>
              <el-input
                v-model="configData.host"
                placeholder="0.0.0.0"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.port')" required>
              <el-input-number
                v-model="configData.port"
                :min="1"
                :max="65535"
                placeholder="8080"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="连接队列大小">
              <el-input-number
                v-model="configData.backlog"
                :min="1"
                :max="1000"
                placeholder="128"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="空闲超时(ms)">
              <el-input-number
                v-model="configData.idleTimeout"
                :min="1000"
                :max="300000"
                placeholder="30000"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="请求体大小限制(MB)">
          <el-input-number
            v-model="configData.maxRequestSize"
            :min="1"
            :max="1024"
            placeholder="10"
            style="width: 200px"
            :readonly="isDetail"
            @change="handleConfigChange"
          />
        </el-form-item>

        <!-- HTTPS配置 -->
        <el-form-item label="启用HTTPS">
          <el-switch
            v-model="configData.enableHttps"
            :disabled="isDetail"
            @change="handleConfigChange"
          />
        </el-form-item>

        <template v-if="configData.enableHttps">
          <el-form-item label="证书文件路径">
            <el-input
              v-model="configData.certPath"
              placeholder="/path/to/cert.pem"
              :readonly="isDetail"
              @input="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="私钥文件路径">
            <el-input
              v-model="configData.keyPath"
              placeholder="/path/to/key.pem"
              :readonly="isDetail"
              @input="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="私钥密码">
            <el-input
              v-model="configData.keyPassword"
              type="password"
              placeholder="请输入私钥密码（可选）"
              show-password
              :readonly="isDetail"
              @input="handleConfigChange"
            />
          </el-form-item>
        </template>
      </el-card>

      <!-- 高级配置 -->
      <el-card shadow="never" class="config-section advanced-section">
        <template #header>
          <div class="advanced-header" @click="showAdvanced = !showAdvanced">
            <span class="advanced-title">高级配置</span>
            <span class="advanced-desc" v-if="!isDetail">（展开进行配置）</span>
            <span class="advanced-desc" v-if="isDetail">（展开查看配置）</span>
            <el-icon class="advanced-arrow" :class="{ 'is-active': showAdvanced }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <el-collapse-transition>
          <div v-show="showAdvanced">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="启用压缩">
                  <el-switch
                    v-model="configData.enableCompression"
                    :disabled="isDetail"
                    @change="handleConfigChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="启用CORS">
                  <el-switch
                    v-model="configData.enableCors"
                    :disabled="isDetail"
                    @change="handleConfigChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- CORS配置 -->
            <template v-if="configData.enableCors">
              <div class="sub-section">
                <h4 class="sub-title">CORS配置</h4>

                <el-form-item label="允许的来源">
                  <el-input
                    v-model="configData.corsAllowedOrigins"
                    placeholder="*"
                    :readonly="isDetail"
                    @input="handleConfigChange"
                  />
                  <div class="form-tip">允许的跨域来源，多个用逗号分隔，* 表示允许所有</div>
                </el-form-item>

                <el-form-item label="允许的HTTP方法">
                  <el-input
                    v-model="configData.corsAllowedMethods"
                    placeholder="GET,POST,PUT,DELETE,OPTIONS"
                    :readonly="isDetail"
                    @input="handleConfigChange"
                  />
                  <div class="form-tip">允许的HTTP方法，多个用逗号分隔</div>
                </el-form-item>

                <el-form-item label="允许的请求头">
                  <el-input
                    v-model="configData.corsAllowedHeaders"
                    placeholder="Content-Type,Authorization"
                    :readonly="isDetail"
                    @input="handleConfigChange"
                  />
                  <div class="form-tip">允许的请求头，多个用逗号分隔</div>
                </el-form-item>

                <el-form-item label="预检请求缓存时间(秒)">
                  <el-input-number
                    v-model="configData.corsMaxAge"
                    :min="0"
                    :max="86400"
                    placeholder="3600"
                    style="width: 200px"
                    :readonly="isDetail"
                    @change="handleConfigChange"
                  />
                </el-form-item>
              </div>
            </template>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { ArrowDown } from '@element-plus/icons-vue';

defineOptions({
  name: "HttpServerConfig"
});

const { t } = useI18n();

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
  isDetail?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({}),
  isDetail: false
});

// 显示高级配置
const showAdvanced = ref(false);

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端HttpServerConfig的15个属性保持一致
const configData = reactive({
  host: '0.0.0.0',
  port: 8080,
  backlog: 128,
  idleTimeout: 30000,
  maxRequestSize: 10,
  enableHttps: false,
  certPath: '',
  keyPath: '',
  keyPassword: '',
  enableCompression: true,
  enableCors: true,
  corsAllowedOrigins: '*',
  corsAllowedMethods: 'GET,POST,PUT,DELETE,OPTIONS',
  corsAllowedHeaders: 'Content-Type,Authorization',
  corsMaxAge: 3600
});

// 处理配置变化
const handleConfigChange = () => {
  const config = { ...configData };

  // 构建服务器URL
  const protocol = config.enableHttps ? 'https' : 'http';
  config.serverUrl = `${protocol}://${config.host}:${config.port}`;

  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { immediate: true, deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // 只在当前配置为空时应用默认配置
    if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
      Object.assign(configData, newValue);
      handleConfigChange();
    }
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化时触发一次配置变化
  handleConfigChange();
});
</script>

<style scoped>
@import './common-config.scss';

.http-server-config {
  padding: 0;
}
</style>
