<template>
  <div class="netty-http-server-config.vue">
    <el-form :model="configData" label-width="120px">
      <!-- 基本配置 -->
      <el-card shadow="never" class="config-section base-section">
        <div slot="header" class="section-header"><b>基本配置</b></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.host')" required>
              <el-input
                v-model="configData.host"
                placeholder="0.0.0.0"
                :readonly="isDetail"
                @input="handleConfigChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('middleware.config.port')" required>
              <el-input-number
                v-model="configData.port"
                :min="1"
                :max="65535"
                placeholder="8080"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="连接队列大小">
              <el-input-number
                v-model="configData.backlog"
                :min="1"
                :max="1000"
                placeholder="100"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
              <div class="form-tip">服务器套接字的连接队列大小</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="空闲超时(秒)">
              <el-input-number
                v-model="configData.idleTimeout"
                :min="1"
                :max="3600"
                placeholder="60"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
              <div class="form-tip">连接空闲超时时间，单位：秒</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";

defineOptions({
  name: "AkkaHttpServerConfig"
});

const { t } = useI18n();

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
  isDetail?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({}),
  isDetail: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端default_config保持一致
const configData = reactive({
  host: '0.0.0.0',
  port: 8080,
  idleTimeout: 60,
  backlog: 100
});

// 处理配置变化
const handleConfigChange = () => {
  const config = { ...configData };
  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { immediate: true, deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // 只在当前配置为空时应用默认配置
    if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
      Object.assign(configData, newValue);
      handleConfigChange();
    }
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化时触发一次配置变化
  handleConfigChange();
});
</script>

<style scoped>
@import './common-config.scss';

.netty-http-server-config.vue {
  padding: 0;
}
</style>
