<template>
  <div class="udp-server-service-config">
    <el-form :model="configData" label-width="160px">
      <!-- 流量统计配置 -->
      <el-card shadow="never" class="config-section traffic-section">
        <div slot="header" class="section-header"><b>流量统计配置</b></div>

        <el-form-item label="启用流量统计记录">
          <el-switch
            v-model="configData.enableTrafficStatsRecording"
            :disabled="isDetail"
            @change="handleConfigChange"
          />
          <div class="form-tip">是否记录UDP数据包的流量统计信息</div>
        </el-form-item>

        <!-- 流量统计相关配置 - 仅在启用时显示 -->
        <template v-if="configData.enableTrafficStatsRecording">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="最大流量统计记录数">
                <el-input-number
                  v-model="configData.maxTrafficStatsRecords"
                  :min="100"
                  :max="100000"
                  placeholder="10000"
                  style="width: 100%"
                  :readonly="isDetail"
                  @change="handleConfigChange"
                />
                <div class="form-tip">系统最多保存的流量统计记录数量</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="流量统计保留时间(小时)">
                <el-input-number
                  v-model="configData.trafficStatsRetentionHours"
                  :min="1"
                  :max="168"
                  placeholder="24"
                  style="width: 100%"
                  :readonly="isDetail"
                  @change="handleConfigChange"
                />
                <div class="form-tip">流量统计记录在系统中的保留时间</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-card>

      <!-- 错误日志配置 -->
      <el-card shadow="never" class="config-section error-section">
        <div slot="header" class="section-header"><b>错误日志配置</b></div>

        <el-form-item label="启用错误日志记录">
          <el-switch
            v-model="configData.enableErrorLogging"
            :disabled="isDetail"
            @change="handleConfigChange"
          />
          <div class="form-tip">是否记录UDP通信过程中的错误信息</div>
        </el-form-item>

        <!-- 错误日志相关配置 - 仅在启用时显示 -->
        <template v-if="configData.enableErrorLogging">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="错误日志保留时间(小时)">
                <el-input-number
                  v-model="configData.errorLogRetentionHours"
                  :min="1"
                  :max="168"
                  placeholder="48"
                  style="width: 100%"
                  :readonly="isDetail"
                  @change="handleConfigChange"
                />
                <div class="form-tip">错误日志在系统中的保留时间</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-card>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, onMounted } from "vue";

defineOptions({
  name: "UdpServerServiceConfig"
});

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
  isDetail?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({}),
  isDetail: false
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端default_config保持一致
const configData = reactive({
  maxTrafficStatsRecords: 10000,
  enableTrafficStatsRecording: true,
  trafficStatsRetentionHours: 24,
  enableErrorLogging: true,
  errorLogRetentionHours: 48
});

// 处理配置变化
const handleConfigChange = () => {
  const config = { ...configData };
  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { deep: true });

// 组件挂载时初始化
onMounted(() => {
  if (props.modelValue && Object.keys(props.modelValue).length > 0) {
    Object.assign(configData, props.modelValue);
  } else if (props.defaultConfig && Object.keys(props.defaultConfig).length > 0) {
    Object.assign(configData, props.defaultConfig);
  }
});
</script>

<style lang="scss" scoped>
@use "../resource/common-config.scss" as *;

.udp-server-service-config {
  .config-section {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-header {
    font-size: 14px;
    color: var(--el-text-color-primary);
  }

  .form-tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
    line-height: 1.4;
  }
}
</style> 