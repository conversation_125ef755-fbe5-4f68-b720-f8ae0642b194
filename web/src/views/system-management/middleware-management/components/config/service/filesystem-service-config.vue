<template>
  <div class="filesystem-service-config">
    <el-form :model="configData" label-width="140px">
      <!-- 基本配置 -->
      <el-card shadow="never" class="config-section base-section">
        <div slot="header" class="section-header"><b>基本配置</b></div>

        <el-form-item label="根路径" required>
          <el-input
            v-model="configData.rootPath"
            placeholder="/data"
            :readonly="isDetail"
            @input="handleConfigChange"
          />
          <div class="form-tip">文件系统服务的根路径</div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="异步模式">
              <el-switch
                v-model="configData.asyncMode"
                :disabled="isDetail"
                @change="handleConfigChange"
              />
              <div class="form-tip">启用异步模式，创建线程池进行异步处理</div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="configData.asyncMode">
            <el-form-item label="线程池大小">
              <el-input-number
                v-model="configData.threadPoolSize"
                :min="1"
                :max="100"
                placeholder="10"
                style="width: 100%"
                :readonly="isDetail"
                @change="handleConfigChange"
              />
              <div class="form-tip">处理文件操作的线程池大小</div>
            </el-form-item>
          </el-col>
          
        </el-row>
      </el-card>

      <!-- 高级配置 -->
      <el-card shadow="never" class="config-section advanced-section"  v-if="false">
        <template #header>
          <div class="advanced-header" @click="showAdvanced = !showAdvanced">
            <span class="advanced-title">高级配置</span>
            <span class="advanced-desc" v-if="!isDetail">（展开进行配置）</span>
            <span class="advanced-desc" v-if="isDetail">（展开查看配置）</span>
            <el-icon class="advanced-arrow" :class="{ 'is-active': showAdvanced }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <el-collapse-transition>
          <div v-show="showAdvanced">
            <!-- 压缩配置 -->
            <el-form-item label="启用压缩">
              <el-switch
                v-model="configData.enableCompression"
                :disabled="isDetail"
                @change="handleConfigChange"
              />
              <div class="form-tip">是否对文件进行压缩存储</div>
            </el-form-item>

            <!-- 加密配置 -->
            <el-form-item label="启用加密">
              <el-switch
                v-model="configData.enableEncryption"
                :disabled="isDetail"
                @change="handleConfigChange"
              />
              <div class="form-tip">是否对文件进行加密存储</div>
            </el-form-item>

            <!-- 加密密钥配置 - 仅在启用加密时显示 -->
            <template v-if="configData.enableEncryption">
              <el-form-item label="加密密钥" required>
                <el-input
                  v-model="configData.encryptionKey"
                  type="password"
                  placeholder="请输入加密密钥"
                  show-password
                  :readonly="isDetail"
                  @input="handleConfigChange"
                />
                <div class="form-tip">用于文件加密的密钥</div>
              </el-form-item>
            </template>

            <!-- 版本控制配置 -->
            <el-form-item label="启用版本控制">
              <el-switch
                v-model="configData.enableVersionControl"
                :disabled="isDetail"
                @change="handleConfigChange"
              />
              <div class="form-tip">是否启用文件版本控制</div>
            </el-form-item>

            <!-- 版本控制相关配置 - 仅在启用时显示 -->
            <template v-if="configData.enableVersionControl">
              <el-form-item label="最大版本数">
                <el-input-number
                  v-model="configData.maxVersions"
                  :min="1"
                  :max="100"
                  placeholder="5"
                  style="width: 200px"
                  :readonly="isDetail"
                  @change="handleConfigChange"
                />
                <div class="form-tip">每个文件保留的最大版本数</div>
              </el-form-item>
            </template>

            <!-- 缓存配置 -->
            <el-form-item label="启用缓存">
              <el-switch
                v-model="configData.enableCache"
                :disabled="isDetail"
                @change="handleConfigChange"
              />
              <div class="form-tip">是否启用文件缓存</div>
            </el-form-item>

            <!-- 缓存相关配置 - 仅在启用时显示 -->
            <template v-if="configData.enableCache">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="缓存大小(MB)">
                    <el-input-number
                      v-model="configData.cacheSize"
                      :min="1"
                      :max="10240"
                      placeholder="256"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="缓存过期时间(秒)">
                    <el-input-number
                      v-model="configData.cacheExpireTime"
                      :min="60"
                      :max="86400"
                      placeholder="3600"
                      style="width: 100%"
                      :readonly="isDetail"
                      @change="handleConfigChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { ArrowDown } from '@element-plus/icons-vue';

defineOptions({
  name: "FilesystemServiceConfig"
});

const { t } = useI18n();

// Props
interface Props {
  modelValue?: Record<string, any>;
  defaultConfig?: Record<string, any>;
  isDetail?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultConfig: () => ({}),
  isDetail: false
});

// 显示高级配置
const showAdvanced = ref(false);

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>];
  'test-connection': [];
}>();

// 配置数据 - 与后端default_config保持一致
const configData = reactive({
  rootPath: '/data',
  threadPoolSize: 10,
  asyncMode: true,
  enableCompression: false,
  enableEncryption: false,
  encryptionKey: '',
  enableVersionControl: false,
  maxVersions: 5,
  enableCache: true,
  cacheSize: 256,
  cacheExpireTime: 3600
});

// 处理配置变化
const handleConfigChange = () => {
  const config = { ...configData };
  emit('update:modelValue', config);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(configData, newValue);
  }
}, { immediate: true, deep: true });

// 监听默认配置变化
watch(() => props.defaultConfig, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // 只在当前配置为空时应用默认配置
    if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
      Object.assign(configData, newValue);
      handleConfigChange();
    }
  }
}, { immediate: true, deep: true });

onMounted(() => {
  // 初始化时触发一次配置变化
  handleConfigChange();
});
</script>

<style scoped>
.filesystem-service-config {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
}

.config-section {
  margin-bottom: 20px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.base-section {
  border: 1px solid #e4e7ed;
}

.advanced-section {
  border: 1px solid #e4e7ed;
}

.section-header {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.advanced-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.advanced-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.advanced-desc {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.advanced-arrow {
  margin-left: auto;
  transition: transform 0.3s ease;
}

.advanced-arrow.is-active {
  transform: rotate(180deg);
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-input, .el-input-number, .el-select {
  width: 100%;
}

.el-switch {
  display: flex;
  align-items: center;
}
</style>
