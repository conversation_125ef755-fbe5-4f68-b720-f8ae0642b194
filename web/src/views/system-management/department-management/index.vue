<template>
  <div class="main">
    <div class="w-full p-4">
      <!-- 搜索工具栏 -->
      <div class="flex justify-between items-center mb-4">
        <div class="flex gap-4 items-center">
          <el-input
            v-model="searchForm.name"
            placeholder="搜索部门名称"
            style="width: 300px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select
            v-model="searchForm.status"
            placeholder="启用状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="启用" :value="1" />
            <el-option label="停用" :value="0" />
          </el-select>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
        <div class="flex gap-2">
          <Auth value="system:dept:add">
            <el-button
              type="primary"
              @click="handleAdd"
            >
              <el-icon><Plus /></el-icon>
              新增部门
            </el-button>
          </Auth>
          <el-button @click="handleExpand">
            <el-icon><Expand /></el-icon>
            {{ isExpandAll ? '折叠' : '展开' }}
          </el-button>
        </div>
      </div>

      <!-- 部门树表格 -->
      <el-table
        ref="tableRef"
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
        row-key="id"
        border
        :default-expand-all="false"
        
      >
        <el-table-column prop="name" label="部门名称" min-width="200">
          <template #default="{ row }">
            <span class="font-medium">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="部门编码" width="150" />
        <el-table-column prop="leader" label="负责人" width="120" />
        <el-table-column prop="phone" label="联系电话" width="140" />
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column label="启用状态" width="100" align="center">
          <template #default="{ row }">
            <Auth value="system:dept:toggle-status">
              <el-switch
                v-model="row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleToggleStatus(row)"
                :loading="row._switching"
              />
            </Auth>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80" align="center" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <Auth value="system:dept:add">
              <el-button
                link
                type="primary"
                @click="handleAddChild(row)"
              >
                新增
              </el-button>
            </Auth>
            <Auth value="system:dept:edit">
              <el-button
                link
                type="primary"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
            </Auth>
            <Auth value="system:dept:delete">
              <el-button
                link
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </Auth>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 部门表单对话框 -->
    <DepartmentFormDialog
      :visible="formDialogVisible"
      :department-data="currentDepartment"
      :parent-department="parentDepartment"
      :is-edit="isEdit"
      @update:visible="(val: boolean) => formDialogVisible = val"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox, type ElTable } from "element-plus";
import { Search, Refresh, Plus, Expand } from "@element-plus/icons-vue";
import { 
  getDepartmentTree,
  updateDepartmentStatus,
  deleteDepartment,
  hasChildren,
  type DepartmentInfo 
} from "@/api/department";
import DepartmentFormDialog from "./components/DepartmentFormDialog.vue";

defineOptions({
  name: "DepartmentManagement"
});

// 响应式数据
const tableRef = ref<InstanceType<typeof ElTable>>();
const isExpandAll = ref(false);
const tableLoading = ref(false);
const tableData = ref<DepartmentInfo[]>([]);
const formDialogVisible = ref(false);
const currentDepartment = ref<DepartmentInfo | null>(null);
const parentDepartment = ref<DepartmentInfo | null>(null);
const isEdit = ref(false);

// 搜索表单
const searchForm = reactive({
  name: "",
  status: ""
});

// 清理数据格式，处理children字段
const cleanDepartmentData = (data: DepartmentInfo[]): any[] => {
  return data.map(item => {
    // 创建一个新对象，只包含需要的字段
    const cleanedItem: any = {
      id: item.id,
      name: item.name,
      code: item.code,
      parentId: item.parentId,
      parentName: item.parentName,
      leader: item.leader,
      phone: item.phone,
      email: item.email,
      status: item.status,
      sort: item.sort,
      createTime: item.createTime,
      updateTime: item.updateTime,
      remark: item.remark
    };
    
    // 只有当确实有子节点时，才添加children字段
    if (item.children && Array.isArray(item.children) && item.children.length > 0) {
      cleanedItem.children = cleanDepartmentData(item.children);
    }
    
    // 不添加hasChildren字段，让Element Plus自动判断
    
    return cleanedItem;
  });
};

// 过滤树状数据，保持父级结构
const filterTreeData = (data: any[], searchName: string): any[] => {
  if (!searchName) return data;
  
  const filtered: any[] = [];
  
  for (const item of data) {
    // 检查当前节点是否匹配搜索条件
    const isMatch = item.name.toLowerCase().includes(searchName.toLowerCase());
    
    // 递归过滤子节点
    let filteredChildren: any[] = [];
    if (item.children && item.children.length > 0) {
      filteredChildren = filterTreeData(item.children, searchName);
    }
    
    // 如果当前节点匹配或者有匹配的子节点，则保留该节点
    if (isMatch || filteredChildren.length > 0) {
      const filteredItem = { ...item };
      if (filteredChildren.length > 0) {
        filteredItem.children = filteredChildren;
      } else {
        // 如果没有子节点，删除children属性
        delete filteredItem.children;
      }
      filtered.push(filteredItem);
    }
  }
  
  return filtered;
};

// 获取部门树数据
const getTableData = async () => {
  try {
    tableLoading.value = true;
    const params: { status?: number } = {};
    
    // 只传递状态参数给后端，前端处理名称搜索
    if (searchForm.status !== "") {
      params.status = Number(searchForm.status);
    }
    
    const response = await getDepartmentTree(params);
    console.log("原始数据:", response);
    if (response.state) {
      const cleanedData = cleanDepartmentData(response.data || []);
      console.log("清理后数据:", cleanedData);
      
      // 如果有名称搜索条件，进行前端过滤
      if (searchForm.name && searchForm.name.trim()) {
        const filteredData = filterTreeData(cleanedData, searchForm.name.trim());
        console.log("过滤后数据:", filteredData);
        tableData.value = filteredData;
      } else {
        tableData.value = cleanedData;
      }
    } else {
      ElMessage.error(response.err_msg || "获取部门列表失败");
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取部门列表失败:", error);
    ElMessage.error("获取部门列表失败");
    tableData.value = [];
  } finally {
    tableLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  getTableData();
};

// 重置搜索
const handleReset = () => {
  searchForm.name = "";
  searchForm.status = "";
  getTableData();
};


// 新增根部门
const handleAdd = () => {
  currentDepartment.value = null;
  parentDepartment.value = null;
  isEdit.value = false;
  formDialogVisible.value = true;
};

// 新增子部门
const handleAddChild = (department: DepartmentInfo) => {
  currentDepartment.value = null;
  parentDepartment.value = department;
  isEdit.value = false;
  formDialogVisible.value = true;
};

// 编辑部门
const handleEdit = (department: DepartmentInfo) => {
  currentDepartment.value = { ...department };
  parentDepartment.value = null;
  isEdit.value = true;
  formDialogVisible.value = true;
};

// 切换部门状态
const handleToggleStatus = async (department: DepartmentInfo) => {
  try {
    department._switching = true;
    const newStatus = department.status;
    
    // 如果是停用操作，询问是否级联子部门
    let cascadeChildren = false;
    if (newStatus === 0 && department.children && department.children.length > 0) {
      try {
        await ElMessageBox.confirm(
          "是否同时停用该部门下的所有子部门？",
          "确认操作",
          {
            confirmButtonText: "是",
            cancelButtonText: "否",
            distinguishCancelAndClose: true,
            type: "warning"
          }
        );
        cascadeChildren = true;
      } catch (action) {
        if (action === "cancel") {
          cascadeChildren = false;
        } else {
          // 用户点击了关闭按钮，取消操作
          department.status = department.status === 1 ? 0 : 1; // 回滚状态
          return;
        }
      }
    }
    
    const response = await updateDepartmentStatus({
      id: department.id,
      status: newStatus,
      cascadeChildren
    });
    
    if (response.state) {
      ElMessage.success(`${newStatus === 1 ? '启用' : '停用'}部门成功`);
      // 如果级联更新了子部门，重新加载数据
      if (cascadeChildren) {
        getTableData();
      }
    } else {
      department.status = department.status === 1 ? 0 : 1; // 回滚状态
      ElMessage.error(response.err_msg || "操作失败");
    }
  } catch (error) {
    department.status = department.status === 1 ? 0 : 1; // 回滚状态
    console.error("切换部门状态失败:", error);
    ElMessage.error("操作失败");
  } finally {
    department._switching = false;
  }
};

// 删除部门
const handleDelete = async (department: DepartmentInfo) => {
  try {
    // 检查是否有子部门
    if (department.children && department.children.length > 0) {
      ElMessage.warning("该部门下存在子部门，请先删除子部门");
      return;
    }
    
    await ElMessageBox.confirm(
      `确认删除部门 "${department.name}" 吗？此操作不可逆！`,
      "确认删除",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    const response = await deleteDepartment(department.id);
    if (response.state) {
      ElMessage.success("删除部门成功");
      getTableData();
    } else {
      ElMessage.error(response.err_msg || "删除部门失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除部门失败:", error);
      ElMessage.error("删除部门失败");
    }
  }
};

// 展开/折叠功能
const handleExpand = () => {
  isExpandAll.value = !isExpandAll.value;
  
  // 获取表格实例
  const table = tableRef.value;
  if (!table) return;
  
  // 递归处理所有行的展开/折叠状态
  const toggleRowExpansion = (data: any[]) => {
    data.forEach(row => {
      if (row.children && row.children.length > 0) {
        table.toggleRowExpansion(row, isExpandAll.value);
        // 递归处理子节点
        toggleRowExpansion(row.children);
      }
    });
  };
  
  // 延迟执行，确保表格已渲染
  setTimeout(() => {
    toggleRowExpansion(tableData.value);
  }, 100);
};

// 刷新数据
const handleRefresh = () => {
  getTableData();
};

// 页面挂载时获取数据
onMounted(() => {
  getTableData();
});
</script>

<style scoped>
.main {
  background: var(--el-bg-color);
  height: 100%;
}
</style>