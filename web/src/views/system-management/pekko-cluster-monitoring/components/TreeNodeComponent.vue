<template>
  <div class="space-y-1">
    <div
      class="flex items-center gap-2 py-1 px-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
      @click="toggleExpanded"
    >
      <el-icon 
        size="16" 
        class="text-gray-400 transition-transform duration-200"
        :class="{ 'transform rotate-90': isExpanded }"
      >
        <ArrowRight />
      </el-icon>
      <el-icon v-if="icon" size="16" class="text-gray-600 dark:text-gray-400">
        <component :is="icon" />
      </el-icon>
      <div class="flex-1">
        <slot name="label">
          <span>{{ label }}</span>
        </slot>
      </div>
    </div>
    <div 
      v-show="isExpanded" 
      class="ml-6 space-y-1 border-l border-gray-200 dark:border-gray-600 pl-4"
    >
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ArrowRight } from "@element-plus/icons-vue";

interface TreeNodeProps {
  icon?: any;
  label?: string;
  defaultExpanded?: boolean;
}

const props = withDefaults(defineProps<TreeNodeProps>(), {
  defaultExpanded: false
});

const isExpanded = ref(props.defaultExpanded);

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};
</script>