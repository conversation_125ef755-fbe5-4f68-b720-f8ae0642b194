<template>
  <svg class="custom-connection" data-testid="connection">
    <path 
      :d="path || data.path" 
      class="connection-path"
      :class="{ 'connection-selected': data.selected }"
      fill="none"
      stroke="var(--el-color-primary)"
      stroke-width="3"
    />
  </svg>
</template>

<script setup lang="ts">
interface ConnectionData {
  selected?: boolean;
  [key: string]: any;
}

interface Props {
  data: ConnectionData;
  emit?: Function;
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  path: string;
}

const props = defineProps<Props>();
</script>

<style scoped>
.custom-connection {
  overflow: visible !important;
  position: absolute;
  pointer-events: none;
  width: 9999px;
  height: 9999px;
  z-index: 1;
}

.connection-path {
  fill: none;
  stroke-width: 3px;
  stroke: var(--el-color-primary);
  stroke-linecap: round;
  stroke-linejoin: round;
  pointer-events: auto;
  cursor: pointer;
  /* 移除transition避免拖拽连线时的延迟 */
  /* transition: all 0.2s ease-in-out; */
  opacity: 0.8;
}

.connection-path:hover {
  stroke: var(--el-color-success);
  stroke-width: 4px;
  opacity: 1;
}

.connection-selected {
  stroke: var(--el-color-success) !important;
  stroke-width: 4px !important;
  opacity: 1 !important;
}

/* 确保连接线在明亮主题下可见 */
@media (prefers-color-scheme: light) {
  .connection-path {
    stroke: #409eff;
    stroke-width: 3px;
  }
}

/* 暗黑模式适配 */
.dark .connection-path {
  stroke: var(--el-color-primary);
}

/* 强制显示连接线 */
svg path {
  stroke: var(--el-color-primary) !important;
  stroke-width: 3px !important;
  fill: none !important;
}
</style> 