<template>
  <teleport to="body">
    <div
      v-if="visible && element"
      class="fixed z-50 pointer-events-none"
      :style="{ left: position.x + 'px', top: position.y + 'px' }"
    >
      <div
        class="bg-[var(--el-bg-color)] border border-[var(--el-border-color)] rounded-lg shadow-lg p-4 max-w-sm"
        style="box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1)"
      >
        <!-- 头部信息 -->
        <div class="flex items-center mb-3">
          <div 
            class="w-10 h-10 rounded-lg flex items-center justify-center mr-3 flex-shrink-0"
            :style="{ backgroundColor: element.color + '20', color: element.color }"
          >
            <el-icon size="18">
              <component :is="element.icon" />
            </el-icon>
          </div>
          <div class="flex-1 min-w-0">
            <h4 class="text-base font-semibold text-[var(--el-text-color-primary)] truncate">
              {{ element.name }}
            </h4>
            <p class="text-sm text-[var(--el-text-color-secondary)]">
              {{ element.description }}
            </p>
          </div>
        </div>

        <!-- 类型标签 -->
        <div class="mb-3">
          <el-tag :type="getTagType(element.type)" size="small" effect="light">
            {{ getTypeLabel(element.type) }}
          </el-tag>
        </div>

        <!-- 输入端口 -->
        <div v-if="element.inputPorts.length > 0" class="mb-3">
          <div class="text-xs font-medium text-[var(--el-text-color-secondary)] mb-1">
            输入端口
          </div>
          <div class="space-y-1">
            <div
              v-for="port in element.inputPorts"
              :key="port.name"
              class="flex items-center justify-between text-xs"
            >
              <span class="text-[var(--el-text-color-primary)]">{{ port.name }}</span>
              <span class="text-[var(--el-text-color-placeholder)]">{{ port.type }}</span>
            </div>
          </div>
        </div>

        <!-- 输出端口 -->
        <div v-if="element.outputPorts.length > 0" class="mb-3">
          <div class="text-xs font-medium text-[var(--el-text-color-secondary)] mb-1">
            输出端口
          </div>
          <div class="space-y-1">
            <div
              v-for="port in element.outputPorts"
              :key="port.name"
              class="flex items-center justify-between text-xs"
            >
              <span class="text-[var(--el-text-color-primary)]">{{ port.name }}</span>
              <span class="text-[var(--el-text-color-placeholder)]">{{ port.type }}</span>
            </div>
          </div>
        </div>

        <!-- 属性配置 -->
        <div v-if="element.properties.length > 0">
          <div class="text-xs font-medium text-[var(--el-text-color-secondary)] mb-1">
            配置属性
          </div>
          <div class="space-y-1">
            <div
              v-for="property in element.properties"
              :key="property.name"
              class="text-xs"
            >
              <div class="flex items-center justify-between">
                <span class="text-[var(--el-text-color-primary)]">{{ property.name }}</span>
                <span class="text-[var(--el-text-color-placeholder)]">{{ property.type }}</span>
              </div>
              <div class="text-[var(--el-text-color-secondary)] mt-0.5">
                {{ property.description }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// 属性定义
interface GraphElement {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  type: string;
  inputPorts: Array<{ name: string; type: string }>;
  outputPorts: Array<{ name: string; type: string }>;
  properties: Array<{ name: string; type: string; description: string }>;
}

interface Props {
  visible: boolean;
  element: GraphElement | null;
  position: { x: number; y: number };
}

const props = defineProps<Props>();

// 获取标签类型
const getTagType = (type: string) => {
  switch (type) {
    case 'source':
      return 'success';
    case 'processor':
      return 'warning';
    case 'output':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取类型标签
const getTypeLabel = (type: string) => {
  switch (type) {
    case 'source':
      return '数据源';
    case 'processor':
      return '处理器';
    case 'output':
      return '输出';
    default:
      return '未知';
  }
};
</script>

<style scoped>
/* 自定义样式 */
</style> 