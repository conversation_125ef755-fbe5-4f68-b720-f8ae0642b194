<template>
  <div
    class="custom-socket"
    :class="[`socket-${data.side}`, { 'socket-connected': isConnected }]"
    :title="data.payload?.name || `${data.side} socket`"
    :data-testid="`socket-${data.side}`"
  >
  </div>
</template>

<script setup lang="ts">
interface SocketData {
  side: 'input' | 'output';
  payload?: any;
  name?: string;
}

interface Props {
  data: SocketData;
  emit?: Function;
}

const props = defineProps<Props>();

// 简单判断是否已连接（这里可以根据实际需求调整）
const isConnected = false; // 实际应用中可以从 payload 或其他地方获取连接状态
</script>

<style scoped>
.custom-socket {
  display: inline-block;
  cursor: pointer;
  border: 2px solid var(--el-border-color);
  width: 14px;
  height: 14px;
  border-radius: 50%;
  vertical-align: middle;
  background: var(--el-bg-color);
  z-index: 2;
  box-sizing: border-box;
  /* 移除transition避免拖拽时的延迟 */
  /* transition: all 0.2s ease-in-out; */
  position: relative;
}

.custom-socket:hover {
  background: var(--el-fill-color-light);
  border-color: var(--el-color-primary);
}

.socket-connected {
  background: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.socket-connected::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--el-bg-color);
}

/* 输入端口样式 - 一半在节点外 */
.socket-input {
  margin-left: -7px;
  position: relative;
  left: -7px;
}

.socket-input:not(.socket-connected) {
  background: var(--el-color-info);
  border-color: var(--el-color-info);
}

.socket-input:hover {
  background: var(--el-color-info);
  border-color: var(--el-color-info);
}

/* 输出端口样式 - 一半在节点外 */
.socket-output {
  margin-right: -7px;
  position: relative;
  right: -7px;
}

.socket-output:not(.socket-connected) {
  background: var(--el-color-warning);
  border-color: var(--el-color-warning);
}

.socket-output:hover {
  background: var(--el-color-warning);
  border-color: var(--el-color-warning);
}

/* 暗黑模式适配 */
.dark .custom-socket {
  border-color: var(--el-border-color-dark);
}
</style> 