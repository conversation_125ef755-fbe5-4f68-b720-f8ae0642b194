# GEMINI.md: Project "Thing Connect Server"

This document provides a comprehensive overview of the "Thing Connect Server" project, its architecture, and development conventions to be used as instructional context for future interactions.

## Project Overview

This is a full-stack application consisting of a Java backend and a TypeScript/Vue.js frontend. The project is named "Thing Connect Server" (`thing-connect-server`), and the frontend is `tcs-hub`.

The application appears to be a monitoring and management platform for IoT devices or other systems. The name "Thing Connect" and the plugin-based architecture strongly suggest its purpose is to integrate with various "southbound" systems (like CMCC, CTCC) and provide a unified "northbound" interface and UI.

### Backend (`server/`)

*   **Technology**: Java 17, Spring Boot 3.2.4
*   **Architecture**:
    *   **Multi-module Maven Project**: The backend is split into numerous modules (e.g., `tcs-core`, `tcs-common`, `tcs-hub`).
    *   **Plugin-Based**: It uses the **PF4J (Plugin Framework for Java)**. This is a core architectural feature, allowing for modular extension. Modules like `tcs-south-cmcc` and `tcs-south-ctcc` are likely plugins for specific integrations.
    *   **Reactive Programming**: The use of **<PERSON><PERSON><PERSON> (a fork of Akka)** suggests a reactive, actor-based model for handling concurrency and distribution.
*   **Data Persistence**:
    *   **Relational**: Uses **MyBatis** for SQL persistence.
    *   **Graph**: Integrates with **Neo4j** for graph data.
    *   **Caching**: Uses **Redis**.
*   **Database Migrations**: **Flyway** is used to manage database schema changes.

### Frontend (`web/`)

*   **Technology**: Vue.js 3, Vite, TypeScript
*   **UI Framework**: Element Plus, with Tailwind CSS for styling.
*   **State Management**: Pinia
*   **Key Features**:
    *   **Data Visualization**: Uses **ECharts**.
    *   **Node-Based Editor**: A significant feature is the use of **Rete.js**, a framework for creating visual, node-based editors. This is likely used for configuring data flows, rules, or logic within the application.
*   **Package Manager**: The project is configured to use **pnpm**.

## Building and Running

### Backend

The backend is a standard Maven project.

*   **Build**: From the `server/` directory, run:
    ```bash
    mvn clean install
    ```
    *Note: The configuration currently has `maven.test.skip` set to `true`.*

*   **Run**: The primary application entry point is likely within the `tcs-hub` or `tcs-siteweb` module. It can be run as a standard Spring Boot application.

### Frontend

The frontend is managed with `pnpm`.

*   **Install Dependencies**: From the `web/` directory, run:
    ```bash
    pnpm install
    ```

*   **Run Development Server**:
    ```bash
    pnpm dev
    ```

*   **Build for Production**:
    ```bash
    pnpm build
    ```

### Docker

The project includes Docker configuration for containerized deployment.

*   **Build Docker Images**:
    ```bash
    # See docker/build.sh for details
    ./docker/build.sh
    ```
*   **Run with Docker Compose**:
    ```bash
    docker-compose up -d
    ```

## Development Conventions

*   **Backend**: Follows standard Java and Spring Boot conventions. The modular, plugin-based architecture is central, and new functionality for southbound integrations should be added as new PF4J plugins.
*   **Frontend**:
    *   Uses **Vue 3 Composition API** with `<script setup>`.
    *   Code style is enforced by **ESLint**, **Prettier**, and **Stylelint**. Run `pnpm lint` to format and check the code.
    *   **TypeScript** is used for type safety. Run `pnpm typecheck` to verify types.
    *   Commits should follow the **Conventional Commits** specification, as indicated by the `commitlint` configuration.
*   **Package Management**: The frontend strictly uses `pnpm`. Do not use `npm` or `yarn`.
