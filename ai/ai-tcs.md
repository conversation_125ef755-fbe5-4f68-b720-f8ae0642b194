# Thing Connect Server 设计文档

## 1. 系统概述

Thing Connect Server (TCS) 是一个基于JDK 17和Spring Boot 3开发的物联网接入平台，采用插件化架构设计，用于连接、管理和处理各种物联网设备的数据。系统通过南向插件接入各类设备数据，通过北向插件将数据输出到其他系统或应用。

## 2. 系统架构

### 2.1 整体架构

TCS采用模块化、插件化的架构设计，主要由以下几个部分组成：

- **核心框架**：提供基础功能和插件管理，tcs-common，tcs-core
- **南向插件**：负责设备数据接入，比如：tcs-south-seed
- **北向插件**：负责数据输出和对外接口
- **Hub**：数据总线，进行南向数据到北向数据的中转和处理
- **Backend Core**：提供后端核心功能

### 2.2 模块结构

- **tcs-common**：公共组件和基础框架，存放各种基类定义
- **tcs-core**：服务启动module
- **tcs-hub**：数据中转和处理中心，负责数据pipleline的维护
- **tcs-backend-core**：tcs的web后端核心功能，
- **tcs-south-seed**：南向插件模板，创建南向插件可参考此模板进行空白插件创建
- **tcs-north-s6**：北向S6接口插件，北向插件样例，可参考此模板进行北向插件创建
- **tcs-health**：系统健康监控

## 3. 核心组件

### 3.1 TCS级插件系统

系统基于PF4J框架实现插件机制，主要组件包括：

- **ThingConnectPlugin**：插件基类，提供插件生命周期管理和通用功能
- **SouthPlugin**：南向插件基类，负责数据接入
- **NorthPlugin**：北向插件基类，负责数据输出
- **PluginContext**：插件上下文，提供插件运行环境和资源访问
- **ThingConnectPluginDescriptor**：插件描述信息
- **ThingConnectPluginWrapper**：插件包装器

#### 3.1.1 插件生命周期

1. **加载**：系统启动时加载插件
2. **初始化**：创建插件实例和上下文
3. **启动**：调用插件的start()方法，执行onStart()回调
4. **运行**：插件正常运行
5. **停止**：调用插件的stop()方法，执行onStop()回调
6. **卸载**：从系统中卸载插件

#### 3.1.2 插件开发流程

1. 继承SouthPlugin或NorthPlugin基类
2. 实现onStart()和onStop()方法
3. 创建根Actor处理数据
4. 实现数据处理逻辑(南向：实现数据采集和处理逻辑，北向：实现数据订阅和输出逻辑)

#### 3.1.3 南向插件模板

南向插件模板(tcs-south-seed)提供了开发南向插件的基础框架：

- **SouthSeedPlugin**：南向插件实现类
- **SeedGuard**：根Actor，处理所有消息
- **ConnectorDataHolder**：存储插件ID和根Actor引用
- **HelloSeedService**：示例服务，演示与Actor通信

#### 3.1.4 北向插件示例

北向S6插件(tcs-north-s6)提供了开发北向插件的示例：

- **NorthS6Plugin**：北向插件实现类
- **NorthS6Guard**：根Actor，处理所有消息
- **订阅机制**：通过subscribe()和unSubscribe()方法实现数据订阅
   
### 3.2 Actor系统

系统使用Akka Actor模型实现组件间通信和并发处理：

- **PluginsActor**：插件Actor管理器，负责创建和管理插件Actor
- **GuardActor**：监护Actor，负责监控和管理子Actor
- **SeedGuard**：南向插件模板的根Actor
- **NorthS6Guard**：北向S6插件的根Actor

组件间通信：

- **Tell模式**：异步消息发送，不等待响应
- **Ask模式**：同步消息发送，等待响应
- **订阅/发布模式**：通过SubscribeAction和UnsubscribeAction实现

### 3.3 数据总线

在hub中process目录下：

采用XXAdapter->XXStateStore->XXSpout三个类通过actor传递消息组装成数据总线Pipeline

- **XXAdapter**：对象XX的数据适配器，负责将南向插件ID映射为总线对象ID
- **XXStateStore**：对象XX的数据缓冲器，负责将南向插件ID传入的总线对象进行数据治理，比如：缓存，清晰，降采样
- **XXSpout**：对象XX的数据发送器，负责将清洗好的数据传给订阅者，消费者（Watcher）可订阅来侦听南向插件ID映射为总线对象ID

数据流：

1. **南向数据流**：设备 -> 南向插件 -> Hub (Adapter->StateStore->Spout）-> 北向插件 -> 外部系统
2. **北向数据流**：外部系统 <- 北向插件 <- Hub (Spout<-StateStore<-Adapter) <- 南向插件 -> 设备

## 4. 非功能设计

### 4.1 扩展性
系统通过以下机制实现良好的扩展性：

- **插件化架构**：支持动态加载和卸载插件
- **Actor模型**：支持高并发和分布式处理
- **依赖注入**：使用Spring实现组件解耦
- **事件机制**：通过ThingConnectPluginEvent实现插件事件通知

### 4.2 安全性

- **认证授权**：通过SecurityConfig实现
- **异常处理**：全局异常处理机制
- **日志收集**：插件日志独立收集和管理

### 4.3 部署架构

系统支持多种部署方式：

- **单机部署**：所有组件部署在同一服务器
- **分布式部署**：各组件可分布在不同服务器
- **容器化部署**：支持Docker容器部署

## 5 开发指南

### 5.1 技术栈 
- **后端**：JDK 17、Spring Boot 3.2.4、Akka 2.9.3、MyBatis-Plus 3.5.6
- **前端**：TypeScript、Vue、Vite
- **数据库**：MySQL/PostgreSQL
- **插件框架**：PF4J
- **通信**：Akka Actor模型、HTTP/WebSocket

### 5.2 开发规范

#### 5.2 命名规范
#### 5.2.1 tcs-common
- **com.siteweb.tcs.common**：公共组件根包
  - **actions**：Actor通信动作类，如SubscribeAction、UnsubscribeAction
  - **configuration**：全局配置类，如RedisConfig、ObjectMapperConfiguration
  - **exception**：异常处理类，如BusinessException、GlobalExceptionHandler
  - **iot**：物联网基础模型，如Thing
  - **net**：网络通信相关类，如HttpFlowServer、SimpleHttpServer
  - **o11y**：可观测性相关类，如ActorProbe、TraceGraph
  - **response**：统一响应类，如ResponseResult、PageResponseResult
  - **runtime**：插件运行时核心类，如ThingConnectPlugin、SouthPlugin、NorthPlugin
  - **security**：安全配置类，如SecurityConfig
  - **util**：工具类，如DateUtil、StringUtils

#### 5.2.2 tcs-hub
- **com.siteweb.tcs.hub**：数据中转中心根包
  - **dal**：数据访问层
    - **dto**：数据传输对象
    - **entity**：实体类
    - **mapper**：MyBatis映射接口
    - **service**：数据服务类
  - **domain**：领域模型
    - **process**：数据处理管道，包含Adapter、StateStore、Spout三层结构
      - **XXAdapter**：数据适配器，如DeviceSignalChangeAdapter
      - **XXStateStore/XXKeeper**：数据状态存储，如EquipmentSignalStateStore
      - **XXSpout**：数据发送器，如EquipmentAlarmSpout
  - **security**：安全认证相关类

#### 5.2.3 tcs-backend-core
- **com.siteweb.tcs.backend**：后端核心功能根包
  - **admin**：系统管理相关类
  - **config**：系统配置类
  - **controller**：REST API控制器
  - **dto**：数据传输对象
  - **entity**：实体类
  - **mapper**：MyBatis映射接口
  - **plugin**：插件管理相关类
  - **service**：业务服务类
  - **utils**：工具类

#### 5.2.4 Web
- **src**：前端源码目录
  - **assets**：静态资源文件
  - **common**：公共组件和函数
  - **components**：Vue组件
  - **directives**：Vue指令
  - **i18n**：国际化资源
  - **store**：状态管理
  - **utils**：工具函数
  - **views**：页面视图组件
  - **router.ts**：路由配置
  - **index.ts**：应用入口

#### 5.2.5 tcs-south-seed
- **com.siteweb.tcs.south.seed**：南向插件模板根包
  - **SouthSeedPlugin**：插件入口类
  - **config**：插件配置类
  - **connector**：设备连接相关类
    - **ConnectorDataHolder**：连接数据持有者
    - **process**：数据处理相关类
  - **web**：插件Web接口
    - **controller**：REST API控制器
    - **service**：业务服务类


### 5.3 版本管理规范
tcs-core 版本号规范

格式：主版本.次版本.补丁版本（如3.2.1）。

- 主版本（x）：重大架构变更（如 3.x 基于 Spring Framework 6，需 Java 17+）。
- 次版本（y）：新增功能（如 2.x 引入响应式编程）。
- 补丁版本（z）：安全修复和小改进（优先使用最新补丁版）。

当软件git提交时，提示用户需要更新版本。