# CMCC查询API使用文档

## 接口概述

为CMCCDevice、CMCCAlarm、CMCCSignal、CMCCControl四个实体提供查询功能。

**路由前缀**: `/api/thing/south-cmcc-plugin`

## 1. 设备查询 (CMCCDevice)

### 1.1 查询设备列表
```
GET /api/thing/south-cmcc-plugin/cmcc-device/list?fsuId={fsuId}
```
**参数**: `fsuId` (可选) - FSU ID


返回值 ：{
    "state": true,
    "timestamp": 1753949257105,
    "data": [
        {
            "id": 1,
            "fsuId": "10000000001",
            "deviceId": "170200000001017",
            "deviceHubId": 556644941692928,
            "deviceName": "A03_1F_量子实验室_102设备区湿度1#_维谛",
            "siteName": "江苏_苏州_虎丘区_苏州虎丘区苏研A03楼一层量子实验室机房",
            "roomName": "A03一楼量子实验室动力间",
            "siteId": null,
            "roomId": null,
            "deviceType": "17",
            "deviceSubType": "02",
            "model": "机房环境",
            "brand": "维谛技术",
            "ratedCapacity": null,
            "version": "",
            "beginRunTime": "2025-03-18 13:08:19",
            "devDescribe": "",
            "description": null,
            "signalList": [],
            "alarmList": [],
            "controlList": []
        }
    ],
    "code": 0,
    "err_msg": null,
    "err_code": null
}

### 1.2 分页查询设备
```
GET /api/thing/south-cmcc-plugin/cmcc-device/page?current=1&size=10&fsuId={fsuId}
```
**参数**: 
- `current` (默认1) - 页码
- `size` (默认10) - 页大小  
- `fsuId` (可选) - FSU ID

### 1.3 根据ID查询设备
```
GET /api/thing/south-cmcc-plugin/cmcc-device/{id}
```


{
    "state": true,
    "timestamp": 1754017098417,
    "data": {
        "id": 2,
        "fsuId": "10000000001",
        "deviceId": "170200000001018",
        "deviceHubId": 556644941692929,
        "deviceName": "A03_1F_量子实验室_105电源设备区湿度1#_维谛",
        "siteName": "江苏_苏州_虎丘区_苏州虎丘区苏研A03楼一层量子实验室机房",
        "roomName": "A03一楼量子实验室动力间",
        "siteId": null,
        "roomId": null,
        "deviceType": "17",
        "deviceSubType": "02",
        "model": "机房环境",
        "brand": "维谛技术",
        "ratedCapacity": null,
        "version": "",
        "beginRunTime": "2025-03-18 13:10:13",
        "devDescribe": "",
        "description": null,
        "signalList": [],
        "alarmList": [],
        "controlList": []
    },
    "code": 0,
    "err_msg": null,
    "err_code": null
}

## 2. 告警查询 (CMCCAlarm)

### 2.1 查询告警列表
```
GET /api/thing/south-cmcc-plugin/cmcc-alarm/list?fsuId={fsuId}&deviceId={deviceId}
```

{
    "state": true,
    "timestamp": 1753951241073,
    "data": [
        {
            "id": 489,
            "fsuId": "10000000001",
            "deviceId": "920100000001007",
            "spId": "092001000",
            "originSpId": "092001",
            "spHubId": 556644941693893,
            "spType": 0,
            "alarmLevel": 2,
            "alarmName": "市电停电",
            "signalNumber": "000",
            "nmAlarmId": "602-092-00-092001",
            "threshold": 0.0,
            "unit": null,
            "meaning": null
        }
    ],
    "code": 0,
    "err_msg": null,
    "err_code": null
}
**参数**: 
- `fsuId` (可选) - FSU ID
- `deviceId` (可选) - 设备ID

### 2.2 分页查询告警
```
GET /api/thing/south-cmcc-plugin/cmcc-alarm/page?current=1&size=10&fsuId={fsuId}&deviceId={deviceId}
```

### 2.3 根据ID查询告警
```
GET /api/thing/south-cmcc-plugin/cmcc-alarm/{id}
```

## 3. 信号查询 (CMCCSignal)

### 3.1 查询信号列表
```
GET /api/thing/south-cmcc-plugin/cmcc-signal/list?fsuId={fsuId}&deviceId={deviceId}
```

{
    "state": true,
    "timestamp": 1753951231703,
    "data": [
        {
            "id": 433,
            "fsuId": "10000000001",
            "deviceId": "920100000001007",
            "spId": "092301000",
            "originSpId": "092301",
            "spHubId": 556644941693871,
            "signalName": "相电压Ua",
            "spType": 3,
            "signalNumber": "000",
            "meanings": {},
            "visible": null,
            "unit": null
        }
    ],
    "code": 0,
    "err_msg": null,
    "err_code": null
}

### 3.2 分页查询信号
```
GET /api/thing/south-cmcc-plugin/cmcc-signal/page?current=1&size=10&fsuId={fsuId}&deviceId={deviceId}
```

### 3.3 根据ID查询信号
```
GET /api/thing/south-cmcc-plugin/cmcc-signal/{id}
```

## 4. 控制查询 (CMCCControl)

### 4.1 查询控制列表
```
GET /api/thing/south-cmcc-plugin/cmcc-control/list?fsuId={fsuId}&deviceId={deviceId}
```


{
    "state": true,
    "timestamp": 1753951121806,
    "data": [
        {
            "id": 1,
            "fsuId": "10000000001",
            "deviceId": "760300000001002",
            "spType": 1,
            "spId": "076101000",
            "originSpId": "076101",
            "spHubId": 556644941693144,
            "controlName": "FSU复位",
            "meanings": {},
            "signalNumber": "000",
            "maxValue": null,
            "minValue": null
        }
    ],
    "code": 0,
    "err_msg": null,
    "err_code": null
}

### 4.2 分页查询控制
```
GET /api/thing/south-cmcc-plugin/cmcc-control/page?current=1&size=10&fsuId={fsuId}&deviceId={deviceId}
```

### 4.3 根据ID查询控制
```
GET /api/thing/south-cmcc-plugin/cmcc-control/{id}
```

## 响应格式

所有接口统一返回格式：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 列表查询: 数组
    // 分页查询: {records: [], total: 100, current: 1, size: 10}
    // ID查询: 对象
  }
}
```