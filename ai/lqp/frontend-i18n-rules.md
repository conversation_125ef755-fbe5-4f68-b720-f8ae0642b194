# Hub-Web 国际化规范

## 国际化规则

### 基本原则

- **所有用户可见文本必须使用国际化**
- **业务模块翻译放在对应模块目录**，而非公共国际化包
- **翻译键名采用模块.功能.字段的命名方式**
- **多语言保持一致性**，新增文本需同时添加所有语言版本

### 翻译文件结构

国际化翻译文件位于各应用的`src/locales/langs/`目录下：

```
src/locales/langs/
├── zh-CN/ # 中文翻译文件夹
│ ├── common.json # 通用翻译
│ ├── menu.json # 菜单翻译
│ └── module1.json # 模块翻译
└── en-US/ # 英文翻译文件夹
  ├── common.json
  ├── menu.json
  └── module1.json
```

### 添加新翻译步骤

#### 1. 为新页面创建翻译文件

若开发了新模块，需在各语言目录下创建对应的JSON文件：

```json
// src/locales/langs/zh-CN/my-module.json
{
  "title": "我的模块",
  "description": "这是模块描述",
  "table": {
    "header": {
      "name": "名称",
      "status": "状态",
      "operation": "操作"
    }
  },
  "form": {
    "placeholders": {
      "enterName": "请输入名称",
      "selectStatus": "请选择状态"
    },
    "buttons": {
      "submit": "提交",
      "reset": "重置"
    }
  }
}

// src/locales/langs/en-US/my-module.json
{
  "title": "My Module",
  "description": "This is module description",
  "table": {
    "header": {
      "name": "Name",
      "status": "Status",
      "operation": "Operation"
    }
  },
  "form": {
    "placeholders": {
      "enterName": "Please enter name",
      "selectStatus": "Please select status"
    },
    "buttons": {
      "submit": "Submit",
      "reset": "Reset"
    }
  }
}
```

#### 2. 在页面中使用翻译

```vue
<script setup lang="ts">
import { $t } from '#/locales';
</script>

<template>
  <h1>{{ $t('my-module.title') }}</h1>
  <p>{{ $t('my-module.description') }}</p>

  <table>
    <thead>
      <tr>
        <th>{{ $t('my-module.table.header.name') }}</th>
        <th>{{ $t('my-module.table.header.status') }}</th>
        <th>{{ $t('my-module.table.header.operation') }}</th>
      </tr>
    </thead>
  </table>

  <form>
    <input :placeholder="$t('my-module.form.placeholders.enterName')" />
    <select :placeholder="$t('my-module.form.placeholders.selectStatus')"></select>

    <button>{{ $t('my-module.form.buttons.submit') }}</button>
    <button>{{ $t('my-module.form.buttons.reset') }}</button>
  </form>
</template>
```

### 路由国际化

在路由配置中使用国际化：

```ts
import type { RouteRecordRaw } from 'vue-router';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    path: '/module-name',
    name: 'ModuleName',
    meta: {
      icon: 'icon-name',
      title: $t('module.title'), // 国际化标题
      order: 100,
    },
    redirect: '/module-name/page1',
    children: [
      {
        path: '/module-name/page1',
        name: 'ModuleNamePage1',
        component: () => import('#/views/module-name/page1.vue'),
        meta: {
          title: $t('module.page1'), // 国际化标题
          icon: 'page1-icon',
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
```

### 按需加载翻译

对于大型应用，可以按需加载翻译文件：

```ts
// 按需加载特定模块的翻译
await import(`#/locales/langs/${locale}/my-module.json`).then(module => {
  addMessages(locale, 'my-module', module.default);
});
```

### 最佳实践

- **建议先完成界面开发**，再进行国际化处理
- **保持翻译文件结构与组件结构一致**，便于维护
- **使用有意义的翻译键名**，反映实际用途
- **避免在代码中硬编码文本**，所有文本应使用`$t()`获取
- **确保所有语言版本内容一致**，避免某些语言缺失翻译 
