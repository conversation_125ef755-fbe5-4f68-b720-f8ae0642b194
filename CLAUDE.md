# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

TCS2（Thing Connect Server）是一个基于微服务架构的物联网接入平台，采用前后端分离设计，支持插件化扩展的物联网数据处理系统。

### 技术架构
- **后端**: Spring Boot 3.2.4 + JDK 17 + Pekko Actor + MyBatis Plus + PF4J插件系统
- **前端**: Vue 3.5.13 + TypeScript + Element Plus + Vite + Pinia + Tailwind CSS
- **数据库**: 支持 MySQL、PostgreSQL、H2、SQLite、OpenGauss、达梦、Neo4j
- **容器化**: Docker + Docker Compose

## 常用开发命令

### 后端开发
```bash
# 进入后端目录
cd server

# 编译项目
mvn clean compile

# 打包项目
mvn clean package -DskipTests

# 运行主程序（开发模式）
mvn spring-boot:run -pl tcs-core

# 运行测试
mvn test

# 清理缓存
mvn clean
```

### 前端开发
```bash
# 进入前端目录
cd web

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 类型检查
pnpm typecheck

# 代码格式化和检查
pnpm lint
```

### Docker 容器
```bash
# 进入 docker 目录
cd docker

# 构建镜像
./build.sh

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

## 核心模块架构

### 后端模块结构
- **tcs-core**: 主启动模块，应用程序入口点
- **tcs-common**: 公共组件、基础框架、工具类
- **tcs-backend-core**: Web后端核心功能、REST API
- **tcs-hub**: 数据总线，负责南北向数据中转
- **stream-***: 流计算引擎相关模块（common/core/service/plugin-defaults）
- **tcs-south-***: 南向插件（设备接入）- CMCC/CUCC/CRCC/OMC等
- **tcs-north-***: 北向插件（数据输出）- ETL/S6等
- **tcs-middleware**: 中间件服务

### 前端架构特点
- **插件化UI**: 支持动态加载插件界面
- **流式编辑器**: 基于 Rete.js 的可视化流程编辑
- **多主题支持**: 明暗主题切换，使用 CSS 变量系统
- **国际化**: 支持中英文切换（但开发时禁用国际化功能）

### Actor系统架构
基于 Pekko Actor 模型：
- **集群支持**: 多节点集群部署
- **分片机制**: 支持数据分片处理
- **容错设计**: 自动重启和错误恢复
- **消息路由**: 南北向数据消息路由

## 插件系统

### 插件类型
- **南向插件**: 设备协议适配（各运营商、工业协议）
- **北向插件**: 数据输出接口（API、文件、数据库）
- **流计算插件**: 数据处理和计算逻辑
- **中间件插件**: 业务逻辑处理

### 插件开发
- 基于 PF4J 框架
- 支持热插拔（运行时加载/卸载）
- 独立类加载器隔离
- 配置管理和Web界面集成

## 数据库配置

### 支持的数据库
```yaml
# 默认使用 H2（开发）
spring.profiles.active: h2

# 生产环境可选择
# mysql | postgresql | opengauss | sqlite
```

### 数据库迁移
- 使用 Flyway 进行版本控制
- 迁移脚本位置: `server/tcs-core/src/main/resources/db/`
- 支持多数据库方言的独立脚本

## 重要配置

### 服务端口
- **主服务**: 8550
- **Pekko集群**: 2551
- **健康检查**: /api/thing/manage
- **前端开发**: 默认由Vite分配

### 关键配置文件
- `server/tcs-core/src/main/resources/application.yml`: 主配置
- `web/vite.config.ts`: 前端构建配置
- `docker/docker-compose.yml`: 容器编排
- `.cursor/rules/frontend-develop-guide.mdc`: 前端开发规范

## 开发规范

### 前端开发约束
1. **禁止使用国际化**: 直接使用中文硬编码，不使用 `$t()` 函数
2. **颜色使用规范**: 禁止硬编码颜色值，必须使用主题变量或 Element Plus 变量
3. **样式优先级**: Tailwind CSS > Element Plus > 自定义CSS
4. **API响应处理**: 使用 `res.state` 判断成功，`res.err_msg` 获取错误信息

### 后端开发特点
- 基于 Actor 模型的异步消息处理
- 插件化架构，支持动态扩展
- 多数据库支持，使用 Flyway 管理迁移
- RESTful API 设计，统一响应格式

## 测试和调试

### 开发模式
- 插件开发模式: `spring.plugins.runtime-mode: development`
- 可在 `application.yml` 中配置 `dev-path` 指定调试插件

### 健康检查
- Spring Boot Actuator: `/api/thing/manage/health`
- 自定义健康检查指标
- Docker 容器健康检查

## 常见问题

### 前端构建问题
- Node.js 版本要求: `^18.18.0 || ^20.9.0 || >=22.0.0`
- 使用 pnpm 作为包管理器（版本 >=9）
- 内存不足时可调整 `NODE_OPTIONS=--max-old-space-size=4096`

### 后端启动问题
- JDK 版本必须是 17
- 确保数据库连接配置正确
- 检查插件目录权限和路径配置
- 集群模式需要配置正确的种子节点

### 插件开发问题
- 插件目录默认为 `./plugins`
- 开发模式下可直接在 IDE 中调试
- 注意插件依赖版本兼容性