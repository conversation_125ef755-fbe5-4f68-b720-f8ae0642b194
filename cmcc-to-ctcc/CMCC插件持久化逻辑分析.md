# CMCC插件持久化逻辑分析

本文档旨在详细阐述 `tcs-south-cmcc` 插件在处理FSU（现场监控单元）连接和数据交换过程中的核心持久化逻辑和需求。

## 1. FSU登录流程持久化

FSU的登录处理逻辑根据FSU是首次接入还是已注册设备而有所不同。

### 场景一：新FSU首次接入

当一个在数据库中尚未注册的FSU首次连接到服务器时，其处理流程由 `CmccFsuAccessServiceImpl` 服务负责。

#### 1.1. 创建待审批记录 (Pending FSU)

- **触发**: 任何未知的FSU发起连接。
- **动作**: 系统首先将FSU的登录信息（包括FSU ID, IP地址, MAC地址, 版本号等）封装成一个 `CMCCPendingFsu` 对象。
- **持久化逻辑**:
    - **手动入网模式 (`autoAccess=false`)**:
        - 调用 `fsuProvider.savePending()` 方法。
        - **数据库操作**: 向 `cmcc_pending_fsu` 数据表中 **INSERT** 一条新记录。
        - 流程暂时中止，等待平台管理员进行人工审批。
    - **自动入网模式 (`autoAccess=true`)**:
        - 待审批记录仅存在于内存中，不写入数据库，直接进入下一步的“批准入网”流程。

#### 1.2. 批准FSU入网 (Approved FSU)

此流程可由人工审批触发，或在自动入网模式下无缝衔接。

- **触发**: 管理员在UI上批准，或系统自动执行。
- **持久化逻辑**:
    1.  **记录操作日志**:
        - 调用 `operationLogProvider.record()`。
        - **数据库操作**: 向 `cmcc_operation_log` 表 **INSERT** 一条“批准入网”的操作日志。
    2.  **创建流图 (Stream Graph)**:
        - 调用 `streamGraphMapper.insert()`。
        - **数据库操作**: 向 `stream_graph` 表 **INSERT** 一条记录，用于定义该FSU的数据处理流程。
    3.  **创建FSU主记录**:
        - 调用 `fsuProvider.saveFsu()`。
        - **数据库操作**: 向 `cmcc_fsu` 主数据表 **INSERT** 一条完整的FSU记录。这是最核心的持久化步骤。
    4.  **清理待审批记录**:
        - 调用 `fsuProvider.deletePendingFsu()`。
        - **数据库操作**: 从 `cmcc_pending_fsu` 表中 **DELETE** 对应的待审批记录。

### 场景二：已注册FSU登录

对于已经存在于 `cmcc_fsu` 表中的设备，其日常的上线登录流程由 `CmccFSUEntity` Actor处理。

- **触发**: 已注册的FSU发起连接。
- **持久化逻辑**:
    - **更新FSU状态**:
        - 调用 `fsuProvider.updateFsu()` 或类似方法。
        - **数据库操作**: 对 `cmcc_fsu` 表执行 **UPDATE** 操作，更新字段如 `online_status` (变为ONLINE), `last_online_time`, `ip_address` 等。

---

## 2. 核心业务数据流持久化

除了登录过程，插件在运行中会持续接收来自FSU的各类业务数据，这些数据流也需要持久化。

### 2.1. 告警上报 (Alarm)

- **描述**: FSU监测到异常情况时上报的告警信息。
- **持久化逻辑**:
    - 调用 `CMCCAlarmMapper` 接口。
    - **数据库操作**: 向 `cmcc_alarm` 表 **INSERT** 新的告警记录。

### 2.2. 信号/遥测上报 (Signal)

- **描述**: FSU定时上报的设备遥测（如温度、电压）和遥信（如开关状态）数据。
- **持久化逻辑**:
    - 调用 `CMCCSignalMapper` 接口。
    - **数据库操作**: 向 `cmcc_signal` 表 **INSERT** 新的信号数据记录。

### 2.3. 事件上报 (Event)

- **描述**: FSU上报的其他类型事件，例如设备自检信息、通信状态变化等。
- **持久化逻辑**:
    - 调用 `CMCCEventMapper` 接口。
    - **数据库操作**: 向 `cmcc_event` 表 **INSERT** 新的事件记录。

### 2.4. 操作日志 (Operation Log)

- **描述**: 记录平台与FSU之间的所有重要交互，如用户通过平台下发指令（远程重启、参数设置）的动作和结果。
- **持久化逻辑**:
    - 调用 `CMCCOperationLogMapper` 接口。
    - **数据库操作**: 向 `cmcc_operation_log` 表 **INSERT** 操作记录，用于审计和问题追溯。
